[{"eventType": "DELAYED_MESSAGE_POSTED", "timestamp": 1528792571982, "actionCode": -1, "delayTime": 3597466, "canScrollTo": false, "elementDescriptors": []}, {"eventType": "VIEW_CLICKED", "timestamp": 1528792586590, "replacementText": "", "actionCode": -1, "delayTime": 0, "canScrollTo": false, "elementDescriptors": [{"className": "android.support.v7.widget.AppCompatImageButton", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "com.hqt.datvemaybay:id/Search", "contentDescription": "", "text": ""}, {"className": "android.widget.LinearLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "", "contentDescription": "", "text": ""}, {"className": "android.widget.LinearLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "com.hqt.datvemaybay:id/btnSearch", "contentDescription": "", "text": ""}]}, {"eventType": "DELAYED_MESSAGE_POSTED", "timestamp": 1528792588529, "actionCode": -1, "delayTime": 3587603, "canScrollTo": false, "elementDescriptors": []}, {"eventType": "VIEW_CLICKED", "timestamp": 1528792591184, "replacementText": "", "actionCode": -1, "delayTime": 0, "canScrollTo": false, "elementDescriptors": [{"className": "android.support.v7.widget.AppCompatTextView", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 1, "resourceId": "com.hqt.datvemaybay:id/diemDen", "contentDescription": "", "text": ""}, {"className": "android.widget.LinearLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 1, "resourceId": "", "contentDescription": "", "text": ""}, {"className": "android.widget.LinearLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "", "contentDescription": "", "text": ""}]}, {"eventType": "DELAYED_MESSAGE_POSTED", "timestamp": 1528792592519, "actionCode": -1, "delayTime": 3596397, "canScrollTo": false, "elementDescriptors": []}, {"eventType": "VIEW_CLICKED", "timestamp": 1528792596394, "replacementText": "", "actionCode": -1, "delayTime": 0, "canScrollTo": false, "elementDescriptors": [{"className": "android.widget.RelativeLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": 5, "groupViewChildPosition": -1, "resourceId": "", "contentDescription": "", "text": ""}, {"className": "android.widget.ListView", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 2, "resourceId": "com.hqt.datvemaybay:id/listview", "contentDescription": "", "text": ""}, {"className": "android.widget.LinearLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "", "contentDescription": "", "text": ""}]}, {"eventType": "DELAYED_MESSAGE_POSTED", "timestamp": 1528792597909, "actionCode": -1, "delayTime": 3594842, "canScrollTo": false, "elementDescriptors": []}, {"eventType": "VIEW_CLICKED", "timestamp": 1528792599809, "replacementText": "", "actionCode": -1, "delayTime": 0, "canScrollTo": false, "elementDescriptors": [{"className": "android.support.v7.widget.AppCompatCheckBox", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 1, "resourceId": "com.hqt.datvemaybay:id/checkXemGiaRe", "contentDescription": "", "text": ""}, {"className": "android.widget.LinearLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "", "contentDescription": "", "text": ""}, {"className": "android.widget.LinearLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 7, "resourceId": "", "contentDescription": "", "text": ""}]}, {"eventType": "VIEW_CLICKED", "timestamp": 1528792601922, "replacementText": "<PERSON><PERSON><PERSON>", "actionCode": -1, "delayTime": 0, "canScrollTo": false, "elementDescriptors": [{"className": "android.support.v7.widget.AppCompatButton", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 1, "resourceId": "com.hqt.datvemaybay:id/checkKhuHoi", "contentDescription": "", "text": "<PERSON><PERSON><PERSON>"}, {"className": "android.widget.RadioGroup", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "com.hqt.datvemaybay:id/radio", "contentDescription": "", "text": ""}, {"className": "android.widget.LinearLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "", "contentDescription": "", "text": ""}]}, {"eventType": "DELAYED_MESSAGE_POSTED", "timestamp": 1528792603314, "actionCode": -1, "delayTime": 10000, "canScrollTo": false, "elementDescriptors": []}, {"eventType": "VIEW_CLICKED", "timestamp": 1528792604372, "replacementText": " TÌM CHUYẾN BAY", "actionCode": -1, "delayTime": 0, "canScrollTo": true, "elementDescriptors": [{"className": "android.support.v7.widget.AppCompatButton", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 8, "resourceId": "com.hqt.datvemaybay:id/button1", "contentDescription": "", "text": " TÌM CHUYẾN BAY"}, {"className": "android.widget.LinearLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "", "contentDescription": "", "text": ""}, {"className": "android.support.v7.widget.CardView", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "com.hqt.datvemaybay:id/card_view", "contentDescription": "", "text": ""}]}, {"eventType": "DELAYED_MESSAGE_POSTED", "timestamp": 1528792605630, "actionCode": -1, "delayTime": 3592355, "canScrollTo": false, "elementDescriptors": []}, {"eventType": "VIEW_CLICKED", "timestamp": 1528792610802, "replacementText": "TIẾP TỤC »", "actionCode": -1, "delayTime": 0, "canScrollTo": false, "elementDescriptors": [{"className": "android.support.v7.widget.AppCompatButton", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "com.hqt.datvemaybay:id/btnBookVe", "contentDescription": "", "text": "TIẾP TỤC »"}, {"className": "android.widget.LinearLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 2, "resourceId": "com.hqt.datvemaybay:id/quickView", "contentDescription": "", "text": ""}, {"className": "android.widget.RelativeLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "", "contentDescription": "", "text": ""}]}, {"eventType": "DELAYED_MESSAGE_POSTED", "timestamp": 1528792612105, "actionCode": -1, "delayTime": 3593897, "canScrollTo": false, "elementDescriptors": []}, {"eventType": "VIEW_CLICKED", "timestamp": 1528792620729, "replacementText": "", "actionCode": -1, "delayTime": 0, "canScrollTo": false, "elementDescriptors": [{"className": "android.widget.FrameLayout", "recyclerViewChildPosition": 6, "adapterViewChildPosition": -1, "groupViewChildPosition": -1, "resourceId": "", "contentDescription": "", "text": ""}, {"className": "android.support.v7.widget.RecyclerView", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "com.hqt.datvemaybay:id/recyclerView", "contentDescription": "", "text": ""}, {"className": "android.support.v4.view.ViewPager", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "com.hqt.datvemaybay:id/materialviewpager_viewpager", "contentDescription": "", "text": ""}]}, {"eventType": "VIEW_CLICKED", "timestamp": 1528792624139, "replacementText": "OK", "actionCode": -1, "delayTime": 0, "canScrollTo": true, "elementDescriptors": [{"className": "android.support.v7.widget.AppCompatButton", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 3, "resourceId": "android:id/button1", "contentDescription": "", "text": "OK"}, {"className": "android.support.v7.widget.ButtonBarLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "", "contentDescription": "", "text": ""}, {"className": "android.widget.ScrollView", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 3, "resourceId": "com.hqt.datvemaybay:id/buttonPanel", "contentDescription": "", "text": ""}]}, {"eventType": "VIEW_CLICKED", "timestamp": 1528792626597, "replacementText": "<PERSON><PERSON><PERSON><PERSON> về", "actionCode": -1, "delayTime": 0, "canScrollTo": false, "elementDescriptors": [{"className": "android.support.v7.widget.AppCompatTextView", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 1, "resourceId": "com.hqt.datvemaybay:id/psts_tab_title", "contentDescription": "", "text": "<PERSON><PERSON><PERSON><PERSON> về"}, {"className": "android.widget.LinearLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "", "contentDescription": "", "text": ""}, {"className": "com.astuetz.PagerSlidingTabStrip", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "com.hqt.datvemaybay:id/materialviewpager_pagerTitleStrip", "contentDescription": "", "text": ""}]}, {"eventType": "VIEW_CLICKED", "timestamp": 1528792629351, "replacementText": "", "actionCode": -1, "delayTime": 0, "canScrollTo": false, "elementDescriptors": [{"className": "android.widget.FrameLayout", "recyclerViewChildPosition": 1, "adapterViewChildPosition": -1, "groupViewChildPosition": -1, "resourceId": "", "contentDescription": "", "text": ""}, {"className": "android.support.v7.widget.RecyclerView", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 1, "resourceId": "com.hqt.datvemaybay:id/recyclerView", "contentDescription": "", "text": ""}, {"className": "android.support.v4.view.ViewPager", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "com.hqt.datvemaybay:id/materialviewpager_viewpager", "contentDescription": "", "text": ""}]}, {"eventType": "VIEW_CLICKED", "timestamp": 1528792632512, "replacementText": "OK", "actionCode": -1, "delayTime": 0, "canScrollTo": true, "elementDescriptors": [{"className": "android.support.v7.widget.AppCompatButton", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 3, "resourceId": "android:id/button1", "contentDescription": "", "text": "OK"}, {"className": "android.support.v7.widget.ButtonBarLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "", "contentDescription": "", "text": ""}, {"className": "android.widget.ScrollView", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 3, "resourceId": "com.hqt.datvemaybay:id/buttonPanel", "contentDescription": "", "text": ""}]}, {"eventType": "VIEW_CLICKED", "timestamp": 1528792635029, "replacementText": "TIẾP TỤC »", "actionCode": -1, "delayTime": 0, "canScrollTo": false, "elementDescriptors": [{"className": "android.support.v7.widget.AppCompatButton", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "com.hqt.datvemaybay:id/btnBookVe", "contentDescription": "", "text": "TIẾP TỤC »"}, {"className": "android.widget.LinearLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 1, "resourceId": "", "contentDescription": "", "text": ""}, {"className": "android.widget.LinearLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 1, "resourceId": "com.hqt.datvemaybay:id/quickView", "contentDescription": "", "text": ""}]}, {"eventType": "DELAYED_MESSAGE_POSTED", "timestamp": 1528792636359, "actionCode": -1, "delayTime": 3576117, "canScrollTo": false, "elementDescriptors": []}, {"eventType": "VIEW_CLICKED", "timestamp": 1528792649079, "replacementText": "", "actionCode": -1, "delayTime": 0, "canScrollTo": false, "elementDescriptors": [{"className": "android.support.v7.widget.AppCompatImageButton", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 2, "resourceId": "", "contentDescription": "", "text": ""}, {"className": "android.support.v7.widget.Toolbar", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "com.hqt.datvemaybay:id/toolbar", "contentDescription": "", "text": ""}, {"className": "android.support.design.widget.AppBarLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "", "contentDescription": "", "text": ""}]}, {"eventType": "DELAYED_MESSAGE_POSTED", "timestamp": 1528792650190, "actionCode": -1, "delayTime": 3586722, "canScrollTo": false, "elementDescriptors": []}, {"eventType": "VIEW_CLICKED", "timestamp": 1528792652599, "replacementText": "", "actionCode": -1, "delayTime": 0, "canScrollTo": false, "elementDescriptors": [{"className": "android.support.v7.widget.AppCompatImageButton", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "", "contentDescription": "Navigate up", "text": ""}, {"className": "android.support.v7.widget.Toolbar", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 1, "resourceId": "com.hqt.datvemaybay:id/toolbar", "contentDescription": "", "text": ""}, {"className": "android.widget.RelativeLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 2, "resourceId": "com.hqt.datvemaybay:id/toolbar_layout", "contentDescription": "", "text": ""}]}, {"eventType": "DELAYED_MESSAGE_POSTED", "timestamp": 1528792653709, "actionCode": -1, "delayTime": 3596597, "canScrollTo": false, "elementDescriptors": []}, {"eventType": "PRESSED_BACK", "timestamp": 1528792655363, "actionCode": -1, "delayTime": 0, "canScrollTo": false, "elementDescriptors": []}, {"eventType": "DELAYED_MESSAGE_POSTED", "timestamp": 1528792655421, "actionCode": -1, "delayTime": 3598391, "canScrollTo": false, "elementDescriptors": []}, {"eventType": "VIEW_CLICKED", "timestamp": 1528792657336, "replacementText": "", "actionCode": -1, "delayTime": 0, "canScrollTo": false, "elementDescriptors": [{"className": "android.support.v7.widget.AppCompatImageButton", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 2, "resourceId": "", "contentDescription": "", "text": ""}, {"className": "android.support.v7.widget.Toolbar", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "com.hqt.datvemaybay:id/toolbar", "contentDescription": "", "text": ""}, {"className": "android.support.design.widget.AppBarLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "", "contentDescription": "", "text": ""}]}, {"eventType": "DELAYED_MESSAGE_POSTED", "timestamp": 1528792658415, "actionCode": -1, "delayTime": 3597085, "canScrollTo": false, "elementDescriptors": []}, {"eventType": "VIEW_CLICKED", "timestamp": 1528792663615, "replacementText": "", "actionCode": -1, "delayTime": 0, "canScrollTo": false, "elementDescriptors": [{"className": "android.support.v7.widget.AppCompatButton", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "com.hqt.datvemaybay:id/Promo", "contentDescription": "", "text": ""}, {"className": "android.widget.LinearLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "", "contentDescription": "", "text": ""}, {"className": "android.widget.LinearLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "com.hqt.datvemaybay:id/btnPromo", "contentDescription": "", "text": ""}]}, {"eventType": "DELAYED_MESSAGE_POSTED", "timestamp": 1528792664964, "actionCode": -1, "delayTime": 3594614, "canScrollTo": false, "elementDescriptors": []}, {"eventType": "PRESSED_BACK", "timestamp": 1528792675493, "actionCode": -1, "delayTime": 0, "canScrollTo": false, "elementDescriptors": []}, {"eventType": "DELAYED_MESSAGE_POSTED", "timestamp": 1528792675555, "actionCode": -1, "delayTime": 3595574, "canScrollTo": false, "elementDescriptors": []}, {"eventType": "PRESSED_BACK", "timestamp": 1528792677518, "actionCode": -1, "delayTime": 0, "canScrollTo": false, "elementDescriptors": []}, {"eventType": "DELAYED_MESSAGE_POSTED", "timestamp": 1528792677547, "actionCode": -1, "delayTime": 3598010, "canScrollTo": false, "elementDescriptors": []}, {"eventType": "VIEW_CLICKED", "timestamp": 1528792682966, "replacementText": "", "actionCode": -1, "delayTime": 0, "canScrollTo": false, "elementDescriptors": [{"className": "android.support.v7.widget.CardView", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "com.hqt.datvemaybay:id/checkin", "contentDescription": "", "text": ""}, {"className": "android.widget.LinearLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 1, "resourceId": "", "contentDescription": "", "text": ""}, {"className": "android.widget.LinearLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 2, "resourceId": "", "contentDescription": "", "text": ""}]}, {"eventType": "DELAYED_MESSAGE_POSTED", "timestamp": 1528792684182, "actionCode": -1, "delayTime": 3593854, "canScrollTo": false, "elementDescriptors": []}, {"eventType": "VIEW_CLICKED", "timestamp": 1528792686437, "replacementText": " Mới: LẤY GHẾ ĐẦU VIETJET", "actionCode": -1, "delayTime": 0, "canScrollTo": false, "elementDescriptors": [{"className": "android.support.v7.widget.AppCompatButton", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 5, "resourceId": "com.hqt.datvemaybay:id/VJCHECKIN", "contentDescription": "", "text": " Mới: LẤY GHẾ ĐẦU VIETJET"}, {"className": "android.widget.LinearLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "", "contentDescription": "", "text": ""}, {"className": "android.support.v7.widget.CardView", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "com.hqt.datvemaybay:id/card_view", "contentDescription": "", "text": ""}]}, {"eventType": "DELAYED_MESSAGE_POSTED", "timestamp": 1528792687943, "actionCode": -1, "delayTime": 3596626, "canScrollTo": false, "elementDescriptors": []}, {"eventType": "PRESSED_BACK", "timestamp": 1528792693739, "actionCode": -1, "delayTime": 0, "canScrollTo": false, "elementDescriptors": []}, {"eventType": "DELAYED_MESSAGE_POSTED", "timestamp": 1528792693769, "actionCode": -1, "delayTime": 3594537, "canScrollTo": false, "elementDescriptors": []}, {"eventType": "VIEW_CLICKED", "timestamp": 1528792696419, "replacementText": "VietJet", "actionCode": -1, "delayTime": 0, "canScrollTo": false, "elementDescriptors": [{"className": "android.support.v7.widget.AppCompatRadioButton", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 2, "resourceId": "com.hqt.datvemaybay:id/checkVietJet", "contentDescription": "", "text": "VietJet"}, {"className": "android.widget.RadioGroup", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "com.hqt.datvemaybay:id/radio", "contentDescription": "", "text": ""}, {"className": "android.widget.LinearLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "", "contentDescription": "", "text": ""}]}, {"eventType": "DELAYED_MESSAGE_POSTED", "timestamp": 1528792698078, "actionCode": -1, "delayTime": 10000, "canScrollTo": false, "elementDescriptors": []}, {"eventType": "VIEW_TEXT_CHANGED", "timestamp": 1528792699469, "replacementText": "HO", "actionCode": -1, "delayTime": 0, "canScrollTo": false, "elementDescriptors": [{"className": "android.support.v7.widget.AppCompatEditText", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 1, "resourceId": "com.hqt.datvemaybay:id/lName", "contentDescription": "", "text": ""}, {"className": "android.widget.LinearLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "", "contentDescription": "", "text": ""}, {"className": "android.widget.LinearLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 1, "resourceId": "", "contentDescription": "", "text": ""}]}, {"eventType": "VIEW_TEXT_CHANGED", "timestamp": 1528792704393, "replacementText": "THIN", "actionCode": -1, "delayTime": 0, "canScrollTo": false, "elementDescriptors": [{"className": "android.support.v7.widget.AppCompatEditText", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 2, "resourceId": "com.hqt.datvemaybay:id/fName", "contentDescription": "", "text": ""}, {"className": "android.widget.LinearLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "", "contentDescription": "", "text": ""}, {"className": "android.widget.LinearLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 1, "resourceId": "", "contentDescription": "", "text": ""}]}, {"eventType": "PRESSED_EDITOR_ACTION", "timestamp": 1528792709986, "replacementText": "THIN", "actionCode": 5, "delayTime": 0, "canScrollTo": false, "elementDescriptors": [{"className": "android.support.v7.widget.AppCompatEditText", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 2, "resourceId": "com.hqt.datvemaybay:id/fName", "contentDescription": "", "text": "THIN"}, {"className": "android.widget.LinearLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "", "contentDescription": "", "text": ""}, {"className": "android.widget.LinearLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 1, "resourceId": "", "contentDescription": "", "text": ""}]}, {"eventType": "VIEW_TEXT_CHANGED", "timestamp": 1528792713504, "replacementText": "123", "actionCode": -1, "delayTime": 0, "canScrollTo": false, "elementDescriptors": [{"className": "android.support.v7.widget.AppCompatEditText", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 1, "resourceId": "com.hqt.datvemaybay:id/pnr", "contentDescription": "", "text": ""}, {"className": "android.widget.LinearLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "", "contentDescription": "", "text": ""}, {"className": "android.widget.LinearLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 3, "resourceId": "", "contentDescription": "", "text": ""}]}, {"eventType": "VIEW_CLICKED", "timestamp": 1528792718505, "replacementText": "", "actionCode": -1, "delayTime": 0, "canScrollTo": false, "elementDescriptors": [{"className": "android.support.v7.widget.AppCompatTextView", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 1, "resourceId": "com.hqt.datvemaybay:id/diemDi", "contentDescription": "", "text": ""}, {"className": "android.widget.LinearLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "", "contentDescription": "", "text": ""}, {"className": "android.widget.LinearLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 2, "resourceId": "", "contentDescription": "", "text": ""}]}, {"eventType": "DELAYED_MESSAGE_POSTED", "timestamp": 1528792719761, "actionCode": -1, "delayTime": 3574104, "canScrollTo": false, "elementDescriptors": []}, {"eventType": "VIEW_CLICKED", "timestamp": 1528792721584, "replacementText": "", "actionCode": -1, "delayTime": 0, "canScrollTo": false, "elementDescriptors": [{"className": "android.widget.RelativeLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": 0, "groupViewChildPosition": -1, "resourceId": "", "contentDescription": "", "text": ""}, {"className": "android.widget.ListView", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 2, "resourceId": "com.hqt.datvemaybay:id/listview", "contentDescription": "", "text": ""}, {"className": "android.widget.LinearLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "", "contentDescription": "", "text": ""}]}, {"eventType": "DELAYED_MESSAGE_POSTED", "timestamp": 1528792723055, "actionCode": -1, "delayTime": 3596911, "canScrollTo": false, "elementDescriptors": []}, {"eventType": "VIEW_CLICKED", "timestamp": 1528792725347, "replacementText": " LÀM THỦ TỤC", "actionCode": -1, "delayTime": 0, "canScrollTo": false, "elementDescriptors": [{"className": "android.support.v7.widget.AppCompatButton", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 4, "resourceId": "com.hqt.datvemaybay:id/button1", "contentDescription": "", "text": " LÀM THỦ TỤC"}, {"className": "android.widget.LinearLayout", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "", "contentDescription": "", "text": ""}, {"className": "android.support.v7.widget.CardView", "recyclerViewChildPosition": -1, "adapterViewChildPosition": -1, "groupViewChildPosition": 0, "resourceId": "com.hqt.datvemaybay:id/card_view", "contentDescription": "", "text": ""}]}, {"eventType": "DELAYED_MESSAGE_POSTED", "timestamp": 1528792726671, "actionCode": -1, "delayTime": 3596415, "canScrollTo": false, "elementDescriptors": []}]