package com.prolificinteractive.materialcalendarview;

import android.animation.Animator;

class AnimatorListener implements Animator.AnimatorListener {
  @Override
  public void onAnimationStart(Animator animator) {
  }

  @Override
  public void onAnimationEnd(Animator animator) {
  }

  @Override
  public void onAnimationCancel(Animator animator) {
  }

  @Override
  public void onAnimationRepeat(Animator animator) {
  }
}
