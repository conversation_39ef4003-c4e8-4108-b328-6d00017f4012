package com.prolificinteractive.materialcalendarview.spans;

import android.content.res.Resources;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Typeface;
import android.text.TextPaint;
import android.text.style.LineBackgroundSpan;

import com.prolificinteractive.materialcalendarview.CalendarDay;
import com.prolificinteractive.materialcalendarview.LunarCalendarUtil;
import com.prolificinteractive.materialcalendarview.YMD;

import java.util.Calendar;
import java.util.concurrent.TimeUnit;

/**
 * Created by NT on 4/15/2016.
 */
public class LunarSpan implements LineBackgroundSpan {

    /**
     * Default text used
     */
    public static final String DEFAULT_text = "";

    private final CalendarDay day;

    /**
     * Create a span to draw a dot using default text and color
     *
     * @see #TextSpan(String, int)
     * @see #DEFAULT_text
     */
    public LunarSpan() {
        this.day = null;
    }

    /**
     * Create a span to draw a dot using a specified text and color
     *
     * @param text text for the dot
     * @param color  color of the dot
     */
    public LunarSpan(CalendarDay date) {
        this.day = date;

    }

    @Override
    public void drawBackground(
            Canvas canvas, Paint paint,
            int left, int right, int top, int baseline, int bottom,
            CharSequence charSequence,
            int start, int end, int lineNum
    ) {
        TextPaint p = new TextPaint(TextPaint.ANTI_ALIAS_FLAG);
        int oldColor = paint.getColor();

        Calendar cal = Calendar.getInstance();
        cal.set(day.getYear(), day.getMonth() + 1, day.getDay());

        float tzz = TimeUnit.HOURS.convert(cal.getTimeZone().getRawOffset(), TimeUnit.MILLISECONDS);
        YMD tmp = LunarCalendarUtil.convertSolar2Lunar(day.getYear(), day.getMonth(), day.getDay(), tzz);

        String ngayAm = tmp.day+"";
        p.setColor(Color.parseColor("#2980b9"));
        if(tmp.day == 1){
            ngayAm = tmp.day+"/"+tmp.month;
            p.setColor(Color.RED);
        }

        if(tmp.day == 15){
            ngayAm = tmp.day+"";
            p.setColor(Color.RED);
        }

        paint.setColor(Color.parseColor("#2980b9"));
        float MYTEXTSIZE = 10.0f;
// Get the screen's density scale
        final float scale = Resources.getSystem().getDisplayMetrics().density;
// Convert the dps to pixels, based on density scale

        float tz = (int) (MYTEXTSIZE * scale + 0.1f);
        p.setStyle(Paint.Style.FILL);
        p.setTextAlign(Paint.Align.LEFT);
        p.setTypeface(Typeface.SERIF);
        p.setTextSize(tz);

        //canvas.drawCircle((left + right) / 2, bottom + text, text, paint);
        canvas.drawText(ngayAm, (left + right) / 2, bottom+tz/2, p);
        paint.setColor(oldColor);
    }
}
