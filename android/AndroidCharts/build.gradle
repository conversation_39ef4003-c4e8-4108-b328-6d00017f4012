apply plugin: 'com.android.library'

ext {
    bintrayRepo = 'maven'
    bintrayName = 'AndroidCharts'

    publishedGroupId = 'im.dacer'
    libraryName = 'AndroidCharts'
    artifact = 'AndroidCharts'

    libraryDescription = 'An easy-to-use Android charts library with animation.'

    siteUrl = '//github.com/HackPlan/AndroidCharts'
    gitUrl = '//github.com/HackPlan/AndroidCharts'

    libraryVersion = '1.0.4'

    developerId = 'Dacer'
    developerName = 'Ding Wenhao'
    developerEmail = '<EMAIL>'

    licenseName = 'The MIT License (MIT)'
    licenseUrl = '//opensource.org/licenses/MIT'
    allLicenses = ["MIT"]
}

buildscript {
    repositories {
        jcenter()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:2.3.3'
    }
}

repositories {
    mavenCentral()
}

android {
    compileSdkVersion 28
    buildToolsVersion "25.0.1"

    defaultConfig {
        minSdkVersion 14
        targetSdkVersion 28
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFile 'proguard-rules.txt'
            proguardFile getDefaultProguardFile('proguard-android-optimize.txt')
        }
    }
}
//apply from: 'https://raw.githubusercontent.com/nuuneoi/JCenter/master/installv1.gradle'
//apply from: 'https://raw.githubusercontent.com/nuuneoi/JCenter/master/bintrayv1.gradle'