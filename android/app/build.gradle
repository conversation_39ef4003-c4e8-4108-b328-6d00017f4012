

buildscript {
    repositories {
        maven { url 'https://maven.fabric.io/public' }
    }
    dependencies {

    }
}


apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'
apply plugin: 'dagger.hilt.android.plugin'


if (!project.hasProperty("disable-performance-plugin")) {
    apply plugin: 'com.google.firebase.firebase-perf'
}


repositories {
    google()
    mavenCentral()
}

android {
    buildFeatures {
        dataBinding true
        viewBinding true
        buildConfig true
    }
    bundle {
        storeArchive {
            enable = false
        }
    }
    buildFeatures.dataBinding = true
    defaultConfig.applicationId = "com.hqt.datvemaybay"
    compileSdkVersion 35
    useLibrary 'org.apache.http.legacy'


    defaultConfig {
        multiDexEnabled true
        applicationId "com.hqt.datvemaybay"
        vectorDrawables.useSupportLibrary = true
        minSdkVersion 28
        targetSdkVersion 35
        compileOptions {
            coreLibraryDesugaringEnabled true
            sourceCompatibility JavaVersion.VERSION_11
            targetCompatibility JavaVersion.VERSION_11
        }
        resConfigs "vi", "en"
    }

    buildTypes {
        release {
            resValue 'string', 'merchant_id', '823'
            resValue 'string', 'protocol_scheme', 'payoosdk823'
            resValue 'string', 'secret_key', 'v9XkFj7eY2KnViM8SX0+UtfKOtx1BRbfiwd+cwO4dLN7ybQVjd4+qZqYVzhx/fNW1nDorFscRkoNd01xoEyKGQ=='
            resValue 'string', 'build_type', ''


            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.txt'
            debuggable false

        }
        debug {
//            resValue 'string', 'build_type', '"D"'
//            resValue 'string', 'merchant_id', '621'
//            resValue 'string', 'protocol_scheme', 'payoosdk621'
//            resValue 'string', 'secret_key', '0aa21e9e51bfb74793881e5780d29ae8'
//
            resValue 'string', 'merchant_id', '823'
            resValue 'string', 'protocol_scheme', 'payoosdk823'
            resValue 'string', 'secret_key', 'v9XkFj7eY2KnViM8SX0+UtfKOtx1BRbfiwd+cwO4dLN7ybQVjd4+qZqYVzhx/fNW1nDorFscRkoNd01xoEyKGQ=='
            resValue 'string', 'build_type', ''

            minifyEnabled false
            ext.enableCrashlytics = false
            ext.alwaysUpdateBuildId = false
            firebaseCrashlytics {
                // If you don't need crash reporting for your debug build,
                // you can speed up your build by disabling mapping file uploading.
                mappingFileUploadEnabled false
            }

        }
    }

    lintOptions {
        disable 'InvalidPackage'
        checkReleaseBuilds false
        abortOnError true
    }
    packagingOptions {
        exclude 'META-INF/services/javax.annotation.processing.Processor'
    }
//    buildToolsVersion = '29.0.3'
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = '11'
    }
    buildToolsVersion '32.0.0'
    ndkVersion '24.0.8215888'
    namespace 'com.hqt.datvemaybay'
    packagingOptions {
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/license.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/notice.txt'
        exclude 'META-INF/ASL2.0'
    }

}
kapt {
    //generateStubs = true
}
dependencies {
    implementation platform('com.google.firebase:firebase-bom:31.2.1')

    implementation 'androidx.navigation:navigation-fragment-ktx:2.3.4'
    implementation 'androidx.navigation:navigation-ui-ktx:2.3.4'

    implementation(name: 'payoo-payment-sdk-release-3.0.0-RC16', ext: 'aar') { transitive = true }
    implementation(name: 'payoo-core-release-1.0.4', ext: 'aar') { transitive = true }
    implementation 'androidx.browser:browser:1.8.0'


    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.6.1'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1'
    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    implementation 'androidx.activity:activity:1.10.1'


    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:1.1.9'
    implementation 'com.github.kizitonwose:CalendarView:1.0.1'
    implementation 'com.google.firebase:firebase-perf'
    implementation 'com.google.code.gson:gson:2.9.0'
    implementation 'com.makeramen:roundedimageview:2.3.0'
    implementation 'com.github.qstumn:BadgeView:1.1.3'
    //implementation 'com.github.ittianyu:BottomNavigationViewEx:2.0.4'


    implementation 'com.github.rubensousa:gravitysnaphelper:2.2.2'

    implementation platform('com.google.firebase:firebase-bom:32.0.0') // add this

    implementation 'com.google.firebase:firebase-perf'
    implementation 'com.firebaseui:firebase-ui-auth:7.2.0'
    implementation 'com.google.firebase:firebase-config'
    implementation 'com.google.firebase:firebase-analytics-ktx'
    implementation 'com.google.firebase:firebase-messaging:23.1.2'
    implementation 'com.google.firebase:firebase-iid:21.1.0'


    implementation 'com.facebook.shimmer:shimmer:0.5.0'
    implementation 'com.github.deano2390:MaterialShowcaseView:1.3.4@aar'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'com.github.tiper:MaterialSpinner:1.4.1'
    implementation 'com.github.smarteist:Android-Image-Slider:1.4.0'
    implementation 'com.github.evrencoskun:TableView:*******'
    implementation 'androidx.annotation:annotation:1.6.0'
    // implementation 'com.suddenh4x.ratingdialog:awesome-app-rating:2.2.1'
    // implementation 'eu.dkaratzas:android-inapp-update:1.0.5'


    implementation 'com.facebook.android:facebook-android-sdk:13.0.0'
    implementation("com.github.bumptech.glide:glide:4.16.0") {
        exclude group: "com.android.support"
    }
    implementation 'com.github.clans:fab:1.6.4'
    implementation 'com.diogobernardino:williamchart:3.10.1'

    implementation project(':LunarCalendar')
    implementation project(':charts')
    implementation project(':materialviewpager')


    implementation 'com.jakewharton.threetenabp:threetenabp:1.1.0'
    implementation 'com.google.android.material:material:1.7.0' // SKIP UPDATE
    implementation 'com.google.firebase:firebase-dynamic-links'
    implementation 'com.google.android.flexbox:flexbox:3.0.0'

    implementation 'com.vipulasri:ticketview:1.0.7'

    implementation 'de.hdodenhof:circleimageview:2.1.0'

    implementation "com.mikepenz:iconics-core:5.3.3"
    implementation "com.mikepenz:iconics-views:5.3.3"
    implementation 'com.mikepenz:fontawesome-typeface:*******-kotlin@aar'
    implementation 'com.mikepenz:google-material-typeface:*******-kotlin@aar'

    implementation 'com.android.volley:volley:1.2.1'

    implementation 'com.github.delight-im:Android-AdvancedWebView:v3.0.0'

    androidTestImplementation 'androidx.test.espresso:espresso-core:3.1.0', {
        exclude group: 'com.android.support', module: 'support-annotations'
    }
    implementation 'com.airbnb.android:lottie:3.4.4'
    androidTestImplementation 'androidx.test.espresso:espresso-contrib:3.1.0', {
        exclude group: 'com.android.support', module: 'support-annotations'
        exclude group: 'com.android.support', module: 'support-v4'
        exclude group: 'com.android.support', module: 'design'
        exclude group: 'com.android.support', module: 'recyclerview-v7'
    }

    implementation 'androidx.coordinatorlayout:coordinatorlayout:1.2.0'
    implementation 'androidx.appcompat:appcompat:1.4.2'


    implementation "androidx.core:core-ktx:1.9.0"
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.21"


    implementation 'com.google.android.gms:play-services-maps:18.1.0'
    implementation 'com.caverock:androidsvg-aar:1.4'
    implementation 'com.google.firebase:firebase-crashlytics'
    implementation 'com.google.firebase:firebase-inappmessaging-display-ktx'
    implementation 'com.google.android.gms:play-services-location:21.0.1'
    implementation 'com.android.installreferrer:installreferrer:2.2'

    implementation 'io.nlopez.smartlocation:library:3.3.3', {
        transitive = false
    }

    implementation 'com.squareup.okhttp3:okhttp:3.14.9'
    implementation 'com.squareup.okhttp3:logging-interceptor:3.12.6'

//    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
//    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.retrofit2:adapter-rxjava2:2.6.4'
    implementation 'com.github.MikeOrtiz:TouchImageView:3.6'

    implementation 'com.google.android.play:integrity:1.4.0'


    //PAYMEE
    //implementation 'com.github.PayME-Tech:PayME-SDK-Android:0.9.36'
    implementation 'com.github.aabhasr1:OtpView:v1.1.2-ktx'
    implementation 'com.github.cachapa:ExpandableLayout:2.9.2'

    implementation 'com.google.firebase:firebase-appcheck-playintegrity'
    implementation 'com.google.firebase:firebase-appcheck-debug:18.0.0'


    // DAGGER HILT ROOM
    implementation "com.google.dagger:hilt-android:$daggerVersion"
    kapt "com.google.dagger:hilt-compiler:$daggerVersion"


    implementation 'com.squareup.okhttp3:logging-interceptor:4.9.0'

    implementation "com.squareup.retrofit2:retrofit:$retrofitVersion"
    implementation "com.squareup.retrofit2:adapter-rxjava3:$retrofitVersion"
    implementation "com.squareup.retrofit2:converter-gson:$retrofitVersion"


    implementation "androidx.security:security-crypto:1.1.0-alpha06"
    implementation "androidx.navigation:navigation-fragment-ktx:$navigationVersion"



    //GOOGLE
    implementation("com.google.android.libraries.identity.googleid:googleid:1.1.1")


}
configurations.all {
    exclude group: 'com.google.android.gms', module: 'play-services-safetynet'
    exclude group: "com.google.android.play", module: "core"
}

