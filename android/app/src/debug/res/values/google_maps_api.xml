<resources>
    <!--
    TODO: Before you run your application, you need a Google Maps API key.

    To get one, follow this link, follow the directions and press "Create" at the end:

    https://console.developers.google.com/flows/enableapi?apiid=maps_android_backend&keyType=CLIENT_SIDE_ANDROID&r=39:CF:EE:C3:90:2E:61:40:8B:41:70:E2:6F:33:EF:F6:EE:35:24:33%3Bcom.hqt.datvemaybay

    You can also add your credentials to an existing key, using these values:

    Package name:
    com.hqt.datvemaybay

    SHA-1 certificate fingerprint:
    39:CF:EE:C3:90:2E:61:40:8B:41:70:E2:6F:33:EF:F6:EE:35:24:33

    Alternatively, follow the directions here:
    https://developers.google.com/maps/documentation/android/start#get-key

    Once you have your key (it starts with "<PERSON>za"), replace the "google_maps_key"
    string in this file.
    -->
    <string name="google_maps_key" templateMergeStrategy="preserve" translatable="false">AIzaSyAsroKtWTeqltMTjer1gOkQcbAKnO7VKsk</string>
</resources>