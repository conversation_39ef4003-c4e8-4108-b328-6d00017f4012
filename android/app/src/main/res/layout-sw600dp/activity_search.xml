<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.hqt.viewmodel.BookingViewModel" />

        <import type="com.hqt.datvemaybay.Common" />

        <import type="android.view.View" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true"
        tools:ignore="MissingPrefix"
        android:id="@+id/coordinatorLayout"
        tools:context="com.hqt.view.ui.SearchActivity">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/primary"
            android:fitsSystemWindows="true"
            android:orientation="vertical">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                app:elevation="0dp"
                app:layout_collapseMode="pin"
                app:layout_scrollFlags="scroll|exitUntilCollapsed"
                android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
                app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

            <ScrollView
                android:id="@+id/scrollView1"
                android:layout_alignParentTop="true"
                android:scrollbars="none"
                android:background="@color/gbgray"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/bg_gradient"
                        android:contentDescription="@string/app_name"
                        android:weightSum="3"
                        android:scaleType="centerCrop">

                        <RelativeLayout
                            android:id="@+id/header"
                            android:layout_width="match_parent"
                            android:adjustViewBounds="true"
                            android:layout_height="wrap_content">

                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:scaleType="center"
                                android:layout_alignTop="@+id/select"
                                android:layout_alignBottom="@+id/select"
                                app:srcCompat="@drawable/world" />

                            <LinearLayout

                                android:id="@+id/change"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center_vertical|center_horizontal"
                                android:layout_alignTop="@+id/select"
                                android:layout_alignBottom="@+id/select"
                                android:orientation="vertical"
                                android:paddingTop="?attr/actionBarSize"
                                android:paddingRight="10dp"
                                android:paddingLeft="10dp"
                                android:paddingBottom="30dp">

                                <View
                                    android:layout_width="1dp"
                                    android:layout_height="60dp"
                                    android:background="@color/diver_color" />

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    app:iiv_color="@color/white"
                                    app:iiv_icon="gmd_airplanemode_active"
                                    android:rotation="180" />

                                <View
                                    android:layout_width="1dp"
                                    android:layout_height="fill_parent"
                                    android:background="@color/diver_color" />
                            </LinearLayout>

                            <LinearLayout

                                android:id="@+id/select"
                                android:layout_width="fill_parent"
                                android:layout_height="wrap_content"
                                android:layout_alignParentTop="true"
                                android:layout_toRightOf="@+id/change"
                                android:orientation="vertical"
                                android:layout_marginLeft="20dp"
                                android:paddingBottom="40dp"
                                android:paddingTop="?attr/actionBarSize">

                                <LinearLayout
                                    android:id="@+id/select_origin"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_toRightOf="@+id/change"
                                    android:orientation="vertical"
                                    android:visibility="visible">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textColor="@color/white"
                                        android:text="@string/txt_dep_from" />

                                    <TextView
                                        android:id="@+id/originCode"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textColor="@color/white"
                                        android:textStyle="bold"
                                        android:textSize="20sp"
                                        android:text="Chọn nơi đi" />

                                    <TextView
                                        android:id="@+id/originName"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginRight="10dp"
                                        android:textColor="@color/white"
                                        android:text="" />
                                </LinearLayout>

                                <LinearLayout
                                    android:id="@+id/select_destination"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentTop="true"
                                    android:layout_toRightOf="@+id/change"
                                    android:layout_marginTop="15dp"
                                    android:orientation="vertical"
                                    android:visibility="visible">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textColor="@color/white"
                                        android:text="@string/txt_dep_to" />

                                    <TextView
                                        android:id="@+id/destinationCode"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textColor="@color/white"
                                        android:textStyle="bold"
                                        android:textSize="20sp"
                                        android:text="Chọn nơi đến" />

                                    <TextView
                                        android:id="@+id/destinationName"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginRight="10dp"
                                        android:textColor="@color/white"
                                        android:text="" />
                                </LinearLayout>
                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/swapRouteLayout"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentRight="true"
                                android:layout_alignTop="@+id/select"
                                android:layout_alignBottom="@+id/select"
                                android:gravity="center_vertical|center_horizontal"
                                android:orientation="vertical"
                                android:paddingTop="?attr/actionBarSize"
                                android:paddingRight="10dp"
                                android:paddingLeft="10dp"
                                android:paddingBottom="30dp">

                                <View
                                    android:layout_width="1dp"
                                    android:layout_height="60dp"
                                    android:background="@color/diver_color" />

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:id="@+id/swapRoute"
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    app:iiv_color="@color/white"
                                    app:iiv_icon="gmd_import_export" />

                                <View
                                    android:layout_width="1dp"
                                    android:layout_height="fill_parent"
                                    android:background="@color/diver_color" />
                            </LinearLayout>

                        </RelativeLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_marginTop="-25dp"
                        android:paddingLeft="20dp"
                        android:paddingRight="20dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="right">

                            <RadioGroup
                                android:id="@+id/radio"
                                android:layout_width="0dp"
                                android:orientation="horizontal"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:background="@drawable/corner_full"
                                android:layout_marginBottom="5dp">

                                <Button
                                    style="@style/MyApp.Button.Big"
                                    android:id="@+id/checkOneWay"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="0.5"
                                    android:checked="true"
                                    android:textColor="#FFFFFF"
                                    android:background="@drawable/button_one_way"
                                    android:textAllCaps="true"
                                    android:capitalize="characters"
                                    android:text="@string/txt_one_way_up" />

                                <Button
                                    style="@style/MyApp.Button.Big"
                                    android:id="@+id/checkRoundtrip"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="0.5"
                                    android:textColor="#00a2e3"
                                    android:textAllCaps="true"
                                    android:capitalize="characters"
                                    android:background="@color/fui_transparent"
                                    android:text="@string/txt_round_trip_up" />
                            </RadioGroup>

                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_marginTop="10dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingLeft="20dp"
                        android:paddingRight="20dp"
                        android:weightSum="1"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:id="@+id/selectDepDate"
                            android:layout_width="0dp"
                            android:layout_weight="0.5"
                            android:layout_marginRight="5dp"
                            android:background="@drawable/corner_full"
                            android:layout_height="wrap_content">

                            <LinearLayout
                                android:padding="10dp"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <TextView
                                    android:textSize="12sp"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/txt_dep_date" />

                                <LinearLayout
                                    android:layout_width="fill_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:id="@+id/txtDepDate"
                                        android:layout_gravity="center"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textSize="24dp"
                                        android:textStyle="bold"
                                        android:text="14/09" />

                                    <LinearLayout

                                        android:gravity="left"
                                        android:paddingLeft="10dp"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:orientation="vertical">

                                        <TextView
                                            android:id="@+id/txtDepDayofweek"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="Thứ s" />

                                        <TextView
                                            android:id="@+id/txtDepYear"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="2018" />
                                    </LinearLayout>
                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="fill_parent"
                                    android:layout_height="wrap_content">

                                    <TextView
                                        android:id="@+id/txtDepLunarDate"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textSize="12sp"
                                        android:text="Âm lịch 20/11" />

                                    <LinearLayout
                                        android:layout_width="fill_parent"
                                        android:gravity="right|end"
                                        android:layout_height="wrap_content">

                                        <com.mikepenz.iconics.view.IconicsImageView

                                            android:paddingTop="6dp"
                                            android:layout_width="12dp"
                                            android:layout_height="12dp"
                                            app:iiv_color="@color/input_register_hint"
                                            app:iiv_icon="faw-caret_down" />
                                    </LinearLayout>
                                </LinearLayout>


                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout

                            android:layout_width="0dp"
                            android:layout_weight="0.5"
                            android:layout_marginLeft="5dp"
                            android:background="@drawable/corner_full"
                            android:layout_height="wrap_content">

                            <LinearLayout
                                android:id="@+id/selectRetDate"
                                android:padding="10dp"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"

                                android:orientation="vertical">

                                <TextView
                                    android:textSize="12sp"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/txt_ret_date" />

                                <LinearLayout
                                    android:layout_width="fill_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:id="@+id/txtRetDate"
                                        android:layout_gravity="center"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textSize="24sp"
                                        android:textStyle="bold"
                                        android:text="" />

                                    <LinearLayout
                                        android:gravity="left"
                                        android:paddingLeft="10dp"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:orientation="vertical">

                                        <TextView
                                            android:id="@+id/txtRetDayOfWeek"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content" />

                                        <TextView
                                            android:id="@+id/txtRetYear"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content" />
                                    </LinearLayout>
                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="fill_parent"
                                    android:layout_height="wrap_content">

                                    <TextView
                                        android:id="@+id/txtRetLunarDate"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textSize="12sp" />

                                    <LinearLayout
                                        android:layout_width="fill_parent"
                                        android:gravity="right|end"
                                        android:layout_height="wrap_content">

                                        <com.mikepenz.iconics.view.IconicsImageView
                                            android:id="@+id/txtRetCaret"
                                            android:paddingTop="6dp"
                                            android:layout_width="12dp"
                                            android:layout_height="12dp"
                                            app:iiv_color="@color/input_register_hint"
                                            app:iiv_icon="faw-caret_down"
                                            android:visibility="gone" />
                                    </LinearLayout>
                                </LinearLayout>

                            </LinearLayout>

                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/select_pax_button"
                        android:layout_width="match_parent"
                        android:layout_marginTop="10dp"
                        android:layout_marginLeft="20dp"
                        android:layout_marginRight="20dp"
                        android:background="@drawable/corner_full"
                        android:layout_height="wrap_content">

                        <LinearLayout
                            android:padding="10dp"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="12sp"
                                android:text="@string/txt_pax" />

                            <LinearLayout
                                android:padding="5dp"
                                android:gravity="center_horizontal"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:weightSum="3"
                                android:orientation="horizontal">

                                <LinearLayout
                                    android:id="@+id/showPaxSelect"
                                    android:layout_width="0dp"
                                    android:layout_weight="1"
                                    android:gravity="center"
                                    android:layout_height="wrap_content">

                                    <TextView
                                        android:id="@+id/adultCount"
                                        android:textAlignment="center"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textStyle="bold"
                                        android:textSize="14sp"
                                        android:text="1" />

                                    <TextView
                                        android:paddingLeft="5dp"
                                        android:textAlignment="center"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textStyle="bold"
                                        android:textSize="14sp"
                                        android:text="@string/txt_adult" />
                                </LinearLayout>

                                <View
                                    android:layout_width="1dp"
                                    android:layout_height="fill_parent"
                                    android:layout_marginRight="10dp"
                                    android:layout_marginLeft="10dp"
                                    android:background="#dddddd" />

                                <LinearLayout
                                    android:layout_width="0dp"
                                    android:gravity="center"
                                    android:layout_weight="1"
                                    android:layout_height="wrap_content">

                                    <TextView
                                        android:id="@+id/childCount"
                                        android:textAlignment="center"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textStyle="bold"
                                        android:textSize="14sp"
                                        android:text="0" />

                                    <TextView
                                        android:paddingLeft="5dp"
                                        android:textAlignment="center"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textStyle="bold"
                                        android:textSize="14sp"
                                        android:text="@string/txt_child" />
                                </LinearLayout>

                                <View
                                    android:layout_width="1dp"
                                    android:layout_height="fill_parent"
                                    android:layout_marginRight="10dp"
                                    android:layout_marginLeft="10dp"
                                    android:background="#dddddd" />

                                <LinearLayout
                                    android:gravity="center"
                                    android:layout_width="0dp"
                                    android:layout_weight="1"
                                    android:layout_height="wrap_content">

                                    <TextView
                                        android:id="@+id/infantCount"
                                        android:textAlignment="center"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textStyle="bold"
                                        android:textSize="14sp"
                                        android:text="0" />

                                    <TextView
                                        android:paddingLeft="5dp"
                                        android:textAlignment="center"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textStyle="bold"
                                        android:textSize="14sp"
                                        android:text="@string/txt_infant" />
                                </LinearLayout>


                            </LinearLayout>

                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_marginTop="10dp"
                        android:layout_marginLeft="20dp"
                        android:layout_marginRight="20dp"
                        android:background="@drawable/corner_full"
                        android:layout_height="wrap_content">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="fill_parent"
                                android:background="@drawable/border_left"
                                android:padding="10dp">

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    app:iiv_color="@color/white"
                                    app:iiv_icon="gmd_timeline" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_marginLeft="5dp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@color/white"
                                android:padding="5dp"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/textDark"
                                    android:text="@string/txt_find_cheap" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textSize="12sp"
                                    android:text="@string/txt_find_cheap_description" />

                            </LinearLayout>

                            <Switch
                                android:id="@+id/cheapSearch"
                                android:layout_gravity="center_horizontal|center_vertical"
                                android:layout_width="fill_parent"
                                android:layout_height="fill_parent" />

                        </LinearLayout>

                    </LinearLayout>

                    <androidx.appcompat.widget.AppCompatButton
                        android:layout_marginTop="10dp"
                        android:layout_marginLeft="25dp"
                        android:layout_marginRight="25dp"
                        android:layout_marginBottom="10dp"
                        android:id="@+id/search"
                        android:background="@drawable/button_gradient"
                        android:textColor="#FFFFFF"
                        android:layout_width="wrap_content"
                        android:layout_gravity="center"
                        android:paddingLeft="50dp"
                        android:paddingRight="50dp"
                        android:textAlignment="gravity"
                        android:textSize="16sp"
                        android:layout_height="wrap_content"
                        android:gravity="center_horizontal|center_vertical"
                        android:text="TÌM CHUYẾN BAY" />

                </LinearLayout>
            </ScrollView>

        </RelativeLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>