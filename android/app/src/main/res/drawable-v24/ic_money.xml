<vector xmlns:android="http://schemas.android.com/apk/res/android" xmlns:aapt="http://schemas.android.com/aapt"
    android:viewportWidth="496"
    android:viewportHeight="496"
    android:width="24dp"
    android:height="24dp">
    <group
        android:translateX="-0"
        android:translateY="64">
        <path
            android:pathData="M456 0h-384c-22.082031 0.0273438 -39.972656 17.917969 -40 40v8.800781c-18.613281 3.828125 -31.9765625 20.199219 -32 39.199219v240c0.0273438 22.082031 17.917969 39.972656 40 40h384c22.082031 -0.027344 39.972656 -17.917969 40 -40v-8.800781c18.613281 -3.828125 31.976562 -20.199219 32 -39.199219v-240c-0.027344 -22.082031 -17.917969 -39.9726562 -40 -40zm-8 328c0 13.253906 -10.746094 24 -24 24h-384c-13.253906 0 -24 -10.746094 -24 -24v-240c0 -13.253906 10.746094 -24 24 -24h384c13.253906 0 24 10.746094 24 24zm32 -48c-0.042969 10.132812 -6.445312 19.148438 -16 22.527344v-214.527344c-0.027344 -22.082031 -17.917969 -39.972656 -40 -40h-376v-8c0 -13.253906 10.746094 -24 24 -24h384c13.253906 0 24 10.746094 24 24zm0 0">
            <aapt:attr
                name="android:fillColor">
                <gradient
                    android:startX="-2.312"
                    android:startY="434.312"
                    android:endX="498.304"
                    android:endY="-66.304"
                    android:tileMode="clamp">
                    <item
                        android:color="@color/gradient_start"
                        android:offset="0" />
                    <item
                        android:color="@color/gradient_center"
                        android:offset="0.518" />
                    <item
                        android:color="@color/gradient_end"
                        android:offset="1" />
                </gradient>
            </aapt:attr>
        </path>
        <path
            android:pathData="M128 80c-8.433594 0.003906 -16.714844 2.246094 -24 6.496094 -20.171875 -11.71875 -45.84375 -7.238282 -60.855469 10.617187 -15.007812 17.855469 -15.007812 43.917969 0 61.773438 15.011719 17.855469 40.683594 22.335937 60.855469 10.617187 17.472656 10.152344 39.433594 8.273438 54.929688 -4.703125 15.492187 -12.972656 21.203124 -34.261719 14.277343 -53.246093 -6.925781 -18.988282 -25 -31.601563 -45.207031 -31.554688zm-24 68.945312c-10.667969 -11.925781 -10.667969 -29.964843 0 -41.890624 10.667969 11.925781 10.667969 29.964843 0 41.890624zm-56 -20.945312c0 -17.671875 14.328125 -32 32 -32 3.628906 0.039062 7.21875 0.699219 10.625 1.953125 -14.164062 17.527344 -14.164062 42.566406 0 60.09375 -3.40625 1.253906 -6.996094 1.914063 -10.625 1.953125 -17.671875 0 -32 -14.328125 -32 -32zm80 32c-3.628906 -0.039062 -7.21875 -0.699219 -10.625 -1.953125 14.164062 -17.527344 14.164062 -42.566406 0 -60.09375 3.40625 -1.253906 6.996094 -1.914063 10.625 -1.953125 17.671875 0 32 14.328125 32 32s-14.328125 32 -32 32zm0 0">
            <aapt:attr
                name="android:fillColor">
                <gradient
                    android:startX="-102.312"
                    android:startY="334.312"
                    android:endX="398.304"
                    android:endY="-166.304"
                    android:tileMode="clamp">
                    <item
                        android:color="@color/gradient_start"
                        android:offset="0" />
                    <item
                        android:color="@color/gradient_center"
                        android:offset="0.518" />
                    <item
                        android:color="@color/gradient_end"
                        android:offset="1" />
                </gradient>
            </aapt:attr>
        </path>
        <path
            android:pathData="M168 240h-128c-4.417969 0 -8 3.582031 -8 8v80c0 4.417969 3.582031 8 8 8h128c4.417969 0 8 -3.582031 8 -8v-80c0 -4.417969 -3.582031 -8 -8 -8zm-8 80h-112v-64h112zm0 0">
            <aapt:attr
                name="android:fillColor">
                <gradient
                    android:startX="-22.312"
                    android:startY="414.312"
                    android:endX="478.304"
                    android:endY="-86.304"
                    android:tileMode="clamp">
                    <item
                        android:color="@color/gradient_start"
                        android:offset="0" />
                    <item
                        android:color="@color/gradient_center"
                        android:offset="0.518" />
                    <item
                        android:color="@color/gradient_end"
                        android:offset="1" />
                </gradient>
            </aapt:attr>
        </path>
        <path
            android:pathData="M376 240h-168c-4.417969 0 -8 3.582031 -8 8s3.582031 8 8 8h168c4.417969 0 8 -3.582031 8 -8s-3.582031 -8 -8 -8zm0 0">
            <aapt:attr
                name="android:fillColor">
                <gradient
                    android:startX="51.688"
                    android:startY="488.312"
                    android:endX="552.304"
                    android:endY="-12.304"
                    android:tileMode="clamp">
                    <item
                        android:color="@color/gradient_start"
                        android:offset="0" />
                    <item
                        android:color="@color/gradient_center"
                        android:offset="0.518" />
                    <item
                        android:color="@color/gradient_end"
                        android:offset="1" />
                </gradient>
            </aapt:attr>
        </path>
        <path
            android:pathData="M376 280h-168c-4.417969 0 -8 3.582031 -8 8s3.582031 8 8 8h168c4.417969 0 8 -3.582031 8 -8s-3.582031 -8 -8 -8zm0 0">
            <aapt:attr
                name="android:fillColor">
                <gradient
                    android:startX="71.688"
                    android:startY="508.312"
                    android:endX="572.304"
                    android:endY="7.696"
                    android:tileMode="clamp">
                    <item
                        android:color="@color/gradient_start"
                        android:offset="0" />
                    <item
                        android:color="@color/gradient_center"
                        android:offset="0.518" />
                    <item
                        android:color="@color/gradient_end"
                        android:offset="1" />
                </gradient>
            </aapt:attr>
        </path>
        <path
            android:pathData="M328 320h-120c-4.417969 0 -8 3.582031 -8 8s3.582031 8 8 8h120c4.417969 0 8 -3.582031 8 -8s-3.582031 -8 -8 -8zm0 0">
            <aapt:attr
                name="android:fillColor">
                <gradient
                    android:startX="79.688"
                    android:startY="516.312"
                    android:endX="580.304"
                    android:endY="15.696"
                    android:tileMode="clamp">
                    <item
                        android:color="@color/gradient_start"
                        android:offset="0" />
                    <item
                        android:color="@color/gradient_center"
                        android:offset="0.518" />
                    <item
                        android:color="@color/gradient_end"
                        android:offset="1" />
                </gradient>
            </aapt:attr>
        </path>
        <path
            android:pathData="M360 320h-8c-4.417969 0 -8 3.582031 -8 8s3.582031 8 8 8h8c4.417969 0 8 -3.582031 8 -8s-3.582031 -8 -8 -8zm0 0">
            <aapt:attr
                name="android:fillColor">
                <gradient
                    android:startX="123.688"
                    android:startY="560.312"
                    android:endX="624.304"
                    android:endY="59.696"
                    android:tileMode="clamp">
                    <item
                        android:color="@color/gradient_start"
                        android:offset="0" />
                    <item
                        android:color="@color/gradient_center"
                        android:offset="0.518" />
                    <item
                        android:color="@color/gradient_end"
                        android:offset="1" />
                </gradient>
            </aapt:attr>
        </path>
        <path
            android:pathData="M408 80h-64c-4.417969 0 -8 3.582031 -8 8v64c0 4.417969 3.582031 8 8 8h64c4.417969 0 8 -3.582031 8 -8v-64c0 -4.417969 -3.582031 -8 -8 -8zm-8 64h-48v-48h48zm0 0">
            <aapt:attr
                name="android:fillColor">
                <gradient
                    android:startX="29.688"
                    android:startY="466.312"
                    android:endX="530.304"
                    android:endY="-34.304"
                    android:tileMode="clamp">
                    <item
                        android:color="@color/gradient_start"
                        android:offset="0" />
                    <item
                        android:color="@color/gradient_center"
                        android:offset="0.518" />
                    <item
                        android:color="@color/gradient_end"
                        android:offset="1" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
</vector>