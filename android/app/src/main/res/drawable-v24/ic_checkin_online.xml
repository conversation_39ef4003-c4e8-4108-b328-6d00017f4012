<vector xmlns:android="http://schemas.android.com/apk/res/android" xmlns:aapt="http://schemas.android.com/aapt"
    android:viewportWidth="512"
    android:viewportHeight="512"
    android:width="24dp"
    android:height="24dp">
    <path
        android:pathData="M360 304.4v-264.4a32.036 32.036 0 0 0 -32 -32h-208a32.036 32.036 0 0 0 -32 32v432a32.036 32.036 0 0 0 32 32h208a32.036 32.036 0 0 0 32 -32v-8.4a80 80 0 0 0 0 -159.2zm-98.25 -280.4l-4 16h-67.5l-4 -16zm82.25 448a16.021 16.021 0 0 1 -16 16h-208a16.021 16.021 0 0 1 -16 -16v-432a16.021 16.021 0 0 1 16 -16h49.75l6.49 25.94a8 8 0 0 0 7.76 6.06h80a8 8 0 0 0 7.76 -6.06l6.49 -25.94h49.75a16.021 16.021 0 0 1 16 16v264.4a80 80 0 0 0 0 159.2zm8 -24a64 64 0 1 1 64 -64 64.072 64.072 0 0 1 -64 64z">
        <aapt:attr
            name="android:fillColor">
            <gradient
                android:startX="3.81"
                android:startY="478.113"
                android:endX="498.035"
                android:endY="-16.112"
                android:tileMode="clamp">
                <item
                    android:color="@color/gradient_start"
                    android:offset="0" />
                <item
                    android:color="@color/gradient_center"
                    android:offset="0.518" />
                <item
                    android:color="@color/gradient_end"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:pathData="M269.87 174.45l19.79 -19.79a24.975 24.975 0 0 0 -35.32 -35.32l-19.79 19.79 -46.76 -25.17a7.989 7.989 0 0 0 -9.45 1.38l-16 16a8.009 8.009 0 0 0 0.59 11.85l37.16 30.4 -21.59 21.59 -23.48 -9.59a8.028 8.028 0 0 0 -8.68 1.75l-16 16a8.008 8.008 0 0 0 1.54 12.52l38.29 22.97 22.97 38.29a8.008 8.008 0 0 0 5.88 3.82 8.18 8.18 0 0 0 0.98 0.06 8 8 0 0 0 5.66 -2.34l16 -16a8.041 8.041 0 0 0 1.75 -8.69l-9.59 -23.47 21.59 -21.59 30.4 37.16a8.009 8.009 0 0 0 11.85 0.59l16 -16a8 8 0 0 0 1.38 -9.45zm2.72 54.64l-30.4 -37.16a8.009 8.009 0 0 0 -11.85 -0.59l-31.6 31.6a8.028 8.028 0 0 0 -1.75 8.68l9.59 23.48 -4.99 5 -18.73 -31.22a8.16 8.16 0 0 0 -2.74 -2.74l-31.22 -18.73 5 -4.99 23.48 9.59a8.028 8.028 0 0 0 8.68 -1.75l31.6 -31.6a8.009 8.009 0 0 0 -0.59 -11.85l-37.16 -30.4 5.54 -5.54 46.76 25.17a8 8 0 0 0 9.45 -1.38l24 -24a8.966 8.966 0 1 1 12.68 12.68l-24 24a8 8 0 0 0 -1.38 9.45l25.17 46.76z">
        <aapt:attr
            name="android:fillColor">
            <gradient
                android:startX="-32.651"
                android:startY="441.651"
                android:endX="461.573"
                android:endY="-52.573"
                android:tileMode="clamp">
                <item
                    android:color="@color/gradient_start"
                    android:offset="0" />
                <item
                    android:color="@color/gradient_center"
                    android:offset="0.518" />
                <item
                    android:color="@color/gradient_end"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:pathData="M389 353.753a8 8 0 0 0 -11.247 1.247l-27.418 34.272 -17.9 -11.931a8 8 0 0 0 -8.875 13.312l24 16a8 8 0 0 0 10.687 -1.653l32 -40a8 8 0 0 0 -1.247 -11.247z">
        <aapt:attr
            name="android:fillColor">
            <gradient
                android:startX="128.848"
                android:startY="603.151"
                android:endX="623.073"
                android:endY="108.926"
                android:tileMode="clamp">
                <item
                    android:color="@color/gradient_start"
                    android:offset="0" />
                <item
                    android:color="@color/gradient_center"
                    android:offset="0.518" />
                <item
                    android:color="@color/gradient_end"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:pathData="M224 296h-80a8 8 0 0 0 0 16h80a8 8 0 0 0 0 -16z">
        <aapt:attr
            name="android:fillColor">
            <gradient
                android:startX="6.849"
                android:startY="481.151"
                android:endX="501.073"
                android:endY="-13.073"
                android:tileMode="clamp">
                <item
                    android:color="@color/gradient_start"
                    android:offset="0" />
                <item
                    android:color="@color/gradient_center"
                    android:offset="0.518" />
                <item
                    android:color="@color/gradient_end"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:pathData="M224 320h-80a8 8 0 0 0 0 16h80a8 8 0 0 0 0 -16z">
        <aapt:attr
            name="android:fillColor">
            <gradient
                android:startX="18.849"
                android:startY="493.151"
                android:endX="513.073"
                android:endY="-1.073"
                android:tileMode="clamp">
                <item
                    android:color="@color/gradient_start"
                    android:offset="0" />
                <item
                    android:color="@color/gradient_center"
                    android:offset="0.518" />
                <item
                    android:color="@color/gradient_end"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:pathData="M176 344h-32a8 8 0 0 0 0 16h32a8 8 0 0 0 0 -16z">
        <aapt:attr
            name="android:fillColor">
            <gradient
                android:startX="18.849"
                android:startY="493.151"
                android:endX="513.073"
                android:endY="-1.073"
                android:tileMode="clamp">
                <item
                    android:color="@color/gradient_start"
                    android:offset="0" />
                <item
                    android:color="@color/gradient_center"
                    android:offset="0.518" />
                <item
                    android:color="@color/gradient_end"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:pathData="M208 344h-8a8 8 0 0 0 0 16h8a8 8 0 0 0 0 -16z">
        <aapt:attr
            name="android:fillColor">
            <gradient
                android:startX="40.849"
                android:startY="515.151"
                android:endX="535.073"
                android:endY="20.927"
                android:tileMode="clamp">
                <item
                    android:color="@color/gradient_start"
                    android:offset="0" />
                <item
                    android:color="@color/gradient_center"
                    android:offset="0.518" />
                <item
                    android:color="@color/gradient_end"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
</vector>