<vector xmlns:android="http://schemas.android.com/apk/res/android" xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:tools="http://schemas.android.com/tools"
    android:viewportWidth="512"
    android:viewportHeight="512"
    android:width="24dp"
    android:height="24dp">
    <path
        android:pathData="M248 416H80a8 8 0 0 0 0 16H248a8 8 0 0 0 0 -16Z">
        <aapt:attr
            name="android:fillColor">
            <gradient
                android:startX="46.959"
                android:startY="541.041"
                android:endX="560.619"
                android:endY="27.381"
                android:tileMode="clamp">
                <item
                    android:color="@color/gradient_start"
                    android:offset="0" />
                <item
                    android:color="@color/gradient_center"
                    android:offset="0.518" />
                <item
                    android:color="@color/gradient_end"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:pathData="M288 416H272a8 8 0 0 0 0 16h16a8 8 0 0 0 0 -16Z">
        <aapt:attr
            name="android:fillColor">
            <gradient
                android:startX="104.959"
                android:startY="599.041"
                android:endX="618.619"
                android:endY="85.381"
                android:tileMode="clamp">
                <item
                    android:color="@color/gradient_start"
                    android:offset="0" />
                <item
                    android:color="@color/gradient_center"
                    android:offset="0.518" />
                <item
                    android:color="@color/gradient_end"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:pathData="M344 176h32a8 8 0 0 0 8 -8V80a8 8 0 0 0 -8 -8H344a8 8 0 0 0 -8 8v88A8 8 0 0 0 344 176Zm8 -88h16v72H352Z">
        <aapt:attr
            name="android:fillColor">
            <gradient
                android:startX="-5.041"
                android:startY="489.041"
                android:endX="508.619"
                android:endY="-24.619"
                android:tileMode="clamp">
                <item
                    android:color="@color/gradient_start"
                    android:offset="0" />
                <item
                    android:color="@color/gradient_center"
                    android:offset="0.518" />
                <item
                    android:color="@color/gradient_end"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:pathData="M344 232h32a8 8 0 0 0 8 -8V192a8 8 0 0 0 -8 -8H344a8 8 0 0 0 -8 8v32A8 8 0 0 0 344 232Zm8 -32h16v16H352Z">
        <aapt:attr
            name="android:fillColor">
            <gradient
                android:startX="36.959"
                android:startY="531.041"
                android:endX="550.619"
                android:endY="17.381"
                android:tileMode="clamp">
                <item
                    android:color="@color/gradient_start"
                    android:offset="0" />
                <item
                    android:color="@color/gradient_center"
                    android:offset="0.518" />
                <item
                    android:color="@color/gradient_end"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:pathData="M472 152A111.931 111.931 0 0 0 294.04 61.55 151.148 151.148 0 0 0 255.9 45.32a39.987 39.987 0 0 0 -79.8 0.06C111.58 63.11 64 122.75 64 193.38V377.12a16.007 16.007 0 0 1 -2.43 8.48l-13.4 21.44A31.991 31.991 0 0 0 75.3 456h85.28a55.994 55.994 0 0 0 110.84 0H356.7a31.991 31.991 0 0 0 27.13 -48.96l-13.4 -21.44a16.007 16.007 0 0 1 -2.43 -8.48V263.71A112.141 112.141 0 0 0 472 152ZM216 24a24.044 24.044 0 0 1 23.18 17.78A153.017 153.017 0 0 0 215.98 40h-0.05a150.561 150.561 0 0 0 -23.11 1.78A24.044 24.044 0 0 1 216 24Zm0 464a40.069 40.069 0 0 1 -39.2 -32h78.4A40.069 40.069 0 0 1 216 488Zm154.27 -72.48A16 16 0 0 1 356.7 440H75.3a16 16 0 0 1 -13.57 -24.48l13.4 -21.44A31.883 31.883 0 0 0 80 377.12V193.38C80 117.67 140.98 56.04 215.93 56h0.05A135.5 135.5 0 0 1 281.1 72.58 111.945 111.945 0 0 0 352 263.71V377.12a31.883 31.883 0 0 0 4.87 16.96ZM360 248a96 96 0 1 1 96 -96A96.115 96.115 0 0 1 360 248Z"
        tools:ignore="VectorPath">
        <aapt:attr
            name="android:fillColor">
            <gradient
                android:startX="7.852"
                android:startY="501.935"
                android:endX="521.512"
                android:endY="-11.726"
                android:tileMode="clamp">
                <item
                    android:color="@color/gradient_start"
                    android:offset="0" />
                <item
                    android:color="@color/gradient_center"
                    android:offset="0.518" />
                <item
                    android:color="@color/gradient_end"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
</vector>