<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:viewportWidth="128"
    android:viewportHeight="128"
    android:width="24dp"
    android:height="24dp">
    <path android:pathData="M84 95.5a6 6 0 0 0 12 0z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:startX="90"
                android:startY="84.15"
                android:endX="90"
                android:endY="90.32"
                android:tileMode="clamp">
                <item
                    android:color="#808080"
                    android:offset="0" />
                <item
                    android:color="#333333"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path android:pathData="M32 95.5a6 6 0 0 0 12 0z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:startX="38"
                android:startY="84.15"
                android:endX="38"
                android:endY="90.32"
                android:tileMode="clamp">
                <item
                    android:color="#808080"
                    android:offset="0" />
                <item
                    android:color="#333333"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <group
        android:rotation="-0"
        android:scaleX="-1"
        android:scaleY="-1"
        android:translateX="128"
        android:translateY="143">
        <path android:pathData="M25 47.5H103A6 6 0 0 1 109 53.5V89.5A6 6 0 0 1 103 95.5H25A6 6 0 0 1 19 89.5V53.5A6 6 0 0 1 25 47.5Z">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startX="64"
                    android:startY="105.8"
                    android:endX="64"
                    android:endY="51.86"
                    android:tileMode="clamp">
                    <item
                        android:color="#F3F7FF"
                        android:offset="0" />
                    <item
                        android:color="#CDD9FF"
                        android:offset="1" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <path android:pathData="M103 47.5h-78a6 6 0 0 0 -6 6v2a6 6 0 0 1 6 -6h78a6 6 0 0 1 6 6v-2a6 6 0 0 0 -6 -6z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:startX="64"
                android:startY="89.46"
                android:endX="64"
                android:endY="48.42"
                android:tileMode="clamp">
                <item
                    android:color="#F3F7FF"
                    android:offset="0" />
                <item
                    android:color="#CDD9FF"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path android:pathData="M109 79c-12 6.73 -40.89 18.1 -90 10.29v0.21a6 6 0 0 0 6 6h78a6 6 0 0 0 6 -6z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:startX="70.87"
                android:startY="92.13"
                android:endX="68.94"
                android:endY="82.7"
                android:tileMode="clamp">
                <item
                    android:color="#F3F7FF"
                    android:offset="0" />
                <item
                    android:color="#CDD9FF"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path android:pathData="M25 95.5h78a6 6 0 0 0 6 -6v-8.38h-90v8.38a6 6 0 0 0 6 6z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:startX="64"
                android:startY="23.92"
                android:endX="64"
                android:endY="110.63"
                android:tileMode="clamp">
                <item
                    android:color="#1488CC"
                    android:offset="0" />
                <item
                    android:color="#2B32B2"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path android:pathData="M109 81.12h-4c-13.84 6.66 -41.68 15.21 -86 8.17v0.21a6 6 0 0 0 6 6h78a6 6 0 0 0 6 -6z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:startX="61.63"
                android:startY="28.46"
                android:endX="64.6"
                android:endY="99.76"
                android:tileMode="clamp">
                <item
                    android:color="#1488CC"
                    android:offset="0" />
                <item
                    android:color="#2B32B2"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path android:pathData="M95 95.5h-17v-33.35a5.51 5.51 0 0 1 5.5 -5.5h6a5.51 5.51 0 0 1 5.5 5.5z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:startX="84.36"
                android:startY="27.51"
                android:endX="87.34"
                android:endY="98.81"
                android:tileMode="clamp">
                <item
                    android:color="#1488CC"
                    android:offset="0" />
                <item
                    android:color="#2B32B2"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path android:pathData="M50 95.5h-17v-33.35a5.51 5.51 0 0 1 5.5 -5.5h6a5.51 5.51 0 0 1 5.5 5.5z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:startX="39.44"
                android:startY="29.38"
                android:endX="42.41"
                android:endY="100.68"
                android:tileMode="clamp">
                <item
                    android:color="#1488CC"
                    android:offset="0" />
                <item
                    android:color="#2B32B2"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path android:pathData="M89.51 58.15h-6a4 4 0 0 0 -4 4v33.35h14v-33.35a4 4 0 0 0 -4 -4z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:startX="86.54"
                android:startY="70.87"
                android:endX="86.01"
                android:endY="180.8"
                android:tileMode="clamp">
                <item
                    android:color="#1488CC"
                    android:offset="0" />
                <item
                    android:color="#2B32B2"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path android:pathData="M44.51 58.15h-6a4 4 0 0 0 -4 4v33.35h14v-33.35a4 4 0 0 0 -4 -4z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:startX="41.54"
                android:startY="70.66"
                android:endX="41.01"
                android:endY="180.58"
                android:tileMode="clamp">
                <item
                    android:color="#1488CC"
                    android:offset="0" />
                <item
                    android:color="#2B32B2"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path android:pathData="M19 58.15h4a4 4 0 0 1 4 4v6a4 4 0 0 1 -4 4h-4a0 0 0 0 1 0 0v-14a0 0 0 0 1 0 0z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:startX="23.18"
                android:startY="56.14"
                android:endX="22.48"
                android:endY="83.93"
                android:tileMode="clamp">
                <item
                    android:color="#00C6FF"
                    android:offset="0" />
                <item
                    android:color="#0072FF"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path android:pathData="M19 64.56l5.9 -5.9a3.83 3.83 0 0 0 -1.9 -0.51h-0.56l-3.44 3.44z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:startX="21.78"
                android:startY="64.51"
                android:endX="21.3"
                android:endY="76.41"
                android:tileMode="clamp">
                <item
                    android:color="#00C6FF"
                    android:offset="0" />
                <item
                    android:color="#0072FF"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path android:pathData="M27 62.15c0 -0.09 0 -0.18 0 -0.28l-8 8v2.31h2.85l5.15 -5.18z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:startX="22.86"
                android:startY="72.05"
                android:endX="22.09"
                android:endY="91.26"
                android:tileMode="clamp">
                <item
                    android:color="#00C6FF"
                    android:offset="0" />
                <item
                    android:color="#0072FF"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <group
        android:rotation="-0"
        android:scaleX="-1"
        android:scaleY="-1"
        android:translateX="210"
        android:translateY="130.31">
        <path android:pathData="M101 58.15h4a4 4 0 0 1 4 4v6a4 4 0 0 1 -4 4h-4a0 0 0 0 1 0 0v-14a0 0 0 0 1 0 0z">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startX="104.85"
                    android:startY="74.28"
                    android:endX="105.37"
                    android:endY="35.84"
                    android:tileMode="clamp">
                    <item
                        android:color="#00C6FF"
                        android:offset="0" />
                    <item
                        android:color="#0072FF"
                        android:offset="1" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <path android:pathData="M101 68.03l8 -8v-1.88h-3.53l-4.47 4.47z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:startX="104.74"
                android:startY="67.93"
                android:endX="104"
                android:endY="86.42"
                android:tileMode="clamp">
                <item
                    android:color="#00C6FF"
                    android:offset="0" />
                <item
                    android:color="#0072FF"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path android:pathData="M109 65.27l-6.21 6.21a4 4 0 0 0 2.21 0.67h0.58l3.42 -3.41z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:startX="105.82"
                android:startY="72.02"
                android:endX="105.31"
                android:endY="84.79"
                android:tileMode="clamp">
                <item
                    android:color="#00C6FF"
                    android:offset="0" />
                <item
                    android:color="#0072FF"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <group
        android:rotation="-0"
        android:scaleX="-1"
        android:scaleY="-1"
        android:translateX="128"
        android:translateY="130.31">
        <path android:pathData="M60 58.15H68A4 4 0 0 1 72 62.15V68.15A4 4 0 0 1 68 72.15H60A4 4 0 0 1 56 68.15V62.15A4 4 0 0 1 60 58.15Z">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startX="64"
                    android:startY="88.92"
                    android:endX="64"
                    android:endY="16.37"
                    android:tileMode="clamp">
                    <item
                        android:color="#00C6FF"
                        android:offset="0" />
                    <item
                        android:color="#0072FF"
                        android:offset="1" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <path android:pathData="M68 58.15h-5.71l-6.29 6.29v3.71a4 4 0 0 0 0.93 2.54l12.31 -12.32a3.89 3.89 0 0 0 -1.24 -0.22z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:startX="62.26"
                android:startY="70.58"
                android:endX="61.32"
                android:endY="94.1"
                android:tileMode="clamp">
                <item
                    android:color="#00C6FF"
                    android:offset="0" />
                <item
                    android:color="#0072FF"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path android:pathData="M65 72.15l7 -7v-3a3.44 3.44 0 0 0 0 -0.45l-10.5 10.45z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:startX="66.61"
                android:startY="72.09"
                android:endX="65.83"
                android:endY="91.79"
                android:tileMode="clamp">
                <item
                    android:color="#00C6FF"
                    android:offset="0" />
                <item
                    android:color="#0072FF"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:pathData="M68 72.65h-8a4.51 4.51 0 0 1 -4.5 -4.5v-6a4.51 4.51 0 0 1 4.5 -4.5h8a4.51 4.51 0 0 1 4.5 4.5v6a4.51 4.51 0 0 1 -4.5 4.5zm-8 -14a3.5 3.5 0 0 0 -3.5 3.5v6a3.5 3.5 0 0 0 3.5 3.5h8a3.5 3.5 0 0 0 3.5 -3.5v-6a3.5 3.5 0 0 0 -3.5 -3.5z"
        android:fillColor="#FFFFFF" />
    <path
        android:pathData="M23 57.65h-4v1h4a3.5 3.5 0 0 1 3.5 3.5v6a3.5 3.5 0 0 1 -3.5 3.5h-4v1h4a4.51 4.51 0 0 0 4.5 -4.5v-6a4.51 4.51 0 0 0 -4.5 -4.5z"
        android:fillColor="#FFFFFF" />
    <path
        android:pathData="M105 71.65a3.5 3.5 0 0 1 -3.5 -3.5v-6a3.5 3.5 0 0 1 3.5 -3.5h4v-1h-4a4.51 4.51 0 0 0 -4.5 4.5v6a4.51 4.51 0 0 0 4.5 4.5h4v-1z"
        android:fillColor="#FFFFFF" />
    <path
        android:pathData="M112 103.5h-96a2 2 0 0 1 0 -4h96a2 2 0 0 1 0 4z"
        android:fillColor="#333333" />
    <path
        android:pathData="M38.79 67.46a1 1 0 0 1 -1 -1v-2.46a1 1 0 0 1 2 0v2.46a1 1 0 0 1 -1 1z"
        android:fillColor="#FFFFFF" />
    <path
        android:pathData="M38.79 77.83a1 1 0 0 1 -1 -1v-7.26a1 1 0 0 1 2 0v7.26a1 1 0 0 1 -1 1z"
        android:fillColor="#FFFFFF" />
    <path
        android:pathData="M84 67.46a1 1 0 0 1 -1 -1v-2.46a1 1 0 0 1 2 0v2.46a1 1 0 0 1 -1 1z"
        android:fillColor="#FFFFFF" />
    <path
        android:pathData="M84 77.83a1 1 0 0 1 -1 -1v-7.26a1 1 0 0 1 2 0v7.26a1 1 0 0 1 -1 1z"
        android:fillColor="#FFFFFF" />
    <path
        android:pathData="M60.71 65.19a1 1 0 0 1 -1 -1v-1.33a1 1 0 0 1 2 0v1.33a1 1 0 0 1 -1 1z"
        android:fillColor="#FFFFFF" />
    <path
        android:pathData="M22.78 65.19a1 1 0 0 1 -1 -1v-1.33a1 1 0 0 1 2 0v1.33a1 1 0 0 1 -1 1z"
        android:fillColor="#FFFFFF" />
    <path
        android:pathData="M104.53 65.19a1 1 0 0 1 -1 -1v-1.33a1 1 0 1 1 2 0v1.33a1 1 0 0 1 -1 1z"
        android:fillColor="#FFFFFF" />
</vector>