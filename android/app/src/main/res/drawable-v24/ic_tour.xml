<vector xmlns:android="http://schemas.android.com/apk/res/android" xmlns:aapt="http://schemas.android.com/aapt"
    android:viewportWidth="62"
    android:viewportHeight="62.414"
    android:width="24dp"
    android:height="24dp">
    <path
        android:pathData="M23.105 34.967l1.789 0.895c-0.788 1.574 -2.371 2.553 -4.131 2.553h-4.763v-2h4.764c0.998 -0.001 1.895 -0.556 2.341 -1.448zm-21.617 -11.64l-0.261 -0.653c-0.814 -2.034 -1.227 -4.177 -1.227 -6.37 0 -3.397 0.996 -6.687 2.88 -9.513l1.679 -2.518 4.04 1.01 3.103 -4.136 4.102 2.051 3.196 -3.198 3.197 3.197 4.102 -2.051 3.103 4.136 4.04 -1.01 1.679 2.518c1.883 2.827 2.879 6.116 2.879 9.514 0 2.193 -0.413 4.336 -1.227 6.37l-0.261 0.653c0.9 0.734 1.488 1.838 1.488 3.087 0 2.206 -1.794 4 -4 4h-0.051c-0.352 5.297 -3.45 9.851 -7.895 12.231 3.594 1.852 5.946 5.575 5.946 9.769v1.586l0.707 -0.707c0.566 -0.566 1.32 -0.879 2.121 -0.879h0.172c0.351 0 0.686 0.065 1 0.176v-13.825c0 -1.296 1.055 -2.351 2.351 -2.351h0.176c0.532 0 1.054 0.183 1.469 0.515l9.123 7.299c0.56 0.448 0.881 1.117 0.881 1.835 0 1.296 -1.055 2.351 -2.351 2.351h-9.649v6c2.206 0 4 1.794 4 4s-1.794 4 -4 4h-5.825c-0.059 0 -0.116 -0.005 -0.175 -0.006v0.006h-20c-2.206 0 -4 -1.794 -4 -4v-8c0 -1.544 0.889 -2.871 2.173 -3.538 0.325 -1.546 1.125 -2.939 2.279 -3.988 -4.71 -2.298 -8.036 -6.981 -8.401 -12.474h-0.051c-2.206 0 -4 -1.794 -4 -4 0 -1.25 0.588 -2.353 1.488 -3.087zm36.512 23.087h9.649c0.193 0 0.351 -0.157 0.351 -0.351 0 -0.105 -0.049 -0.208 -0.131 -0.273l-9.124 -7.299c-0.062 -0.05 -0.14 -0.077 -0.219 -0.077h-0.176c-0.193 0 -0.351 0.157 -0.351 0.351v7.649zm-34 -18v-4c-1.103 0 -2 0.897 -2 2s0.897 2 2 2zm4.7 -11.403l-1.444 0.577c-0.763 0.306 -1.256 1.035 -1.256 1.857v1.153c0.314 -0.112 0.648 -0.184 1 -0.184h8c1.654 0 3 1.346 3 3v1h2v-1c0 -1.654 1.346 -3 3 -3h8c0.352 0 0.686 0.072 1 0.184v-1.153c0 -0.823 -0.493 -1.552 -1.257 -1.857l-1.443 -0.577c-0.634 0.847 -1.635 1.403 -2.771 1.403h-15.057c-1.136 0 -2.138 -0.556 -2.772 -1.403zm23.3 12.403v-6c0 -0.551 -0.448 -1 -1 -1h-8c-0.552 0 -1 0.449 -1 1v6c0 0.551 0.448 1 1 1h8c0.552 0 1 -0.449 1 -1zm-25 1h8c0.552 0 1 -0.449 1 -1v-6c0 -0.551 -0.448 -1 -1 -1h-8c-0.552 0 -1 0.449 -1 1v6c0 0.551 0.448 1 1 1zm7.617 13.343c-1.021 0.625 -1.807 1.558 -2.238 2.657h2.067c0.349 -0.881 0.891 -1.664 1.573 -2.299 -0.476 -0.097 -0.943 -0.217 -1.402 -0.358zm4.942 11.416l6.227 -4.151 -1.428 -3.808c-0.628 -1.675 -2.252 -2.8 -4.041 -2.8 -2.38 0 -4.316 1.937 -4.316 4.316v0.328c0 0.72 0.183 1.435 0.527 2.067 0.823 1.511 1.851 2.865 3.031 4.048zm-5.559 -2.759h-4v4h4zm-4 -2h4v-1.355 -0.329c0 -0.107 0.011 -0.211 0.016 -0.316h-2.016c-1.103 0 -2 0.898 -2 2zm2 10h2v-2h-4c0 1.103 0.897 2 2 2zm11.512 0c-2.972 -1.444 -5.565 -3.623 -7.512 -6.378v6.378zm8.562 -3.66l-5.128 -4.102 -5.851 3.901c3.099 2.459 6.984 3.862 11.079 3.862h5.826c1.103 0 2 -0.897 2 -2s-0.897 -2 -2 -2h-2v-1c0 -0.552 -0.448 -1 -1 -1h-0.172c-0.263 0 -0.521 0.106 -0.707 0.293zm-7.357 -12.537c0.654 0.635 1.181 1.41 1.513 2.295l1.609 4.294 2.161 1.728v-0.12c0 -3.593 -2.107 -6.764 -5.283 -8.197zm6.94 -11.88c-0.212 0.048 -0.43 0.077 -0.657 0.077h-8c-1.654 0 -3 -1.346 -3 -3v-3h-2v3c0 1.654 -1.346 3 -3 3h-8c-0.226 0 -0.445 -0.03 -0.657 -0.077 1.332 5.763 6.495 10.077 12.657 10.077s11.325 -4.314 12.657 -10.077zm4.343 -5.923c0 -1.103 -0.897 -2 -2 -2v4c1.103 0 2 -0.897 2 -2zm-32.694 -3.93c0.226 -0.04 0.456 -0.07 0.694 -0.07v-2.969c0 -1.645 0.987 -3.103 2.515 -3.714l1.504 -0.602c-0.003 -0.063 -0.019 -0.123 -0.019 -0.187 0 -1.324 0.735 -2.514 1.919 -3.105l0.634 -0.317 0.895 1.789 -0.634 0.317c-0.502 0.25 -0.814 0.755 -0.814 1.316 0 0.812 0.66 1.472 1.472 1.472h15.057c0.812 0 1.472 -0.661 1.472 -1.472 0 -0.561 -0.312 -1.065 -0.813 -1.316l-0.634 -0.317 0.895 -1.789 0.634 0.317c1.182 0.591 1.917 1.781 1.917 3.105 0 0.064 -0.016 0.124 -0.019 0.188l1.504 0.602c1.528 0.61 2.515 2.068 2.515 3.713v2.969c0.238 0 0.468 0.03 0.694 0.07l0.222 -0.554c0.72 -1.796 1.084 -3.689 1.084 -5.626 0 -3.001 -0.88 -5.907 -2.544 -8.403l-0.897 -1.345 -3.96 0.99 -2.897 -3.864 -3.898 1.949 -2.804 -2.803 -2.803 2.803 -3.898 -1.949 -2.898 3.863 -3.96 -0.99 -0.897 1.345c-1.664 2.497 -2.544 5.403 -2.544 8.404 0 1.937 0.364 3.83 1.084 5.626zm8.249 5.098l-3 -2 -1.109 1.664 3 2zm3 -2l-3 -2 -1.109 1.664 3 2zm8.89 1.664l3 2 1.109 -1.664 -3 -2zm3 -2l3 2 1.109 -1.664 -3 -2zm23.555 9.168h-2v2h2zm2 0v2h2v-2zm4 0v2h2v-2zm6 -21c0 2.813 -0.991 5.552 -2.792 7.713l-8.208 9.849 -8.208 -9.849c-1.801 -2.161 -2.792 -4.9 -2.792 -7.713 0 -6.065 4.935 -11 11 -11s11 4.935 11 11zm-2 0c0 -4.962 -4.037 -9 -9 -9s-9 4.038 -9 9c0 2.346 0.827 4.63 2.329 6.433l6.671 8.005 6.671 -8.005c1.502 -1.803 2.329 -4.087 2.329 -6.433zm-2 0c0 3.86 -3.141 7 -7 7s-7 -3.14 -7 -7 3.141 -7 7 -7 7 3.14 7 7zm-2 0c0 -2.757 -2.243 -5 -5 -5s-5 2.243 -5 5 2.243 5 5 5 5 -2.243 5 -5z">
        <aapt:attr
            name="android:fillColor">
            <gradient
                android:startX="31"
                android:startY="62.414"
                android:endX="31"
                android:tileMode="clamp">
                <item
                    android:color="@color/gradient_start"
                    android:offset="0" />
                <item
                    android:color="@color/gradient_center"
                    android:offset="0.518" />
                <item
                    android:color="@color/gradient_end"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
</vector>