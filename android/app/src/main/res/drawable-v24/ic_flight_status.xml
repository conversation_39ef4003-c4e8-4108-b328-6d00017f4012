<vector xmlns:android="http://schemas.android.com/apk/res/android" xmlns:aapt="http://schemas.android.com/aapt"
    android:viewportWidth="64"
    android:viewportHeight="64"
    android:width="24dp"
    android:height="24dp">
    <path
        android:pathData="M6.213 46.674a1 1 0 0 0 0.946 1.326h2.894a1 1 0 0 0 0.88 -0.525l0.8 -1.475h2.4l-0.409 4.917a1 1 0 0 0 1 1.083h2a1 1 0 0 0 0.944 -0.67l1.854 -5.33h2.363a2.466 2.466 0 0 0 1.764 -0.73 2.494 2.494 0 0 0 0.649 -2.431 2.529 2.529 0 0 0 -2.482 -1.839h-2.294l-1.865 -5.33a1 1 0 0 0 -0.944 -0.67h-2a1 1 0 0 0 -1 1.083l0.413 4.917h-2.4l-0.795 -1.475a1 1 0 0 0 -0.88 -0.525h-2.892a1 1 0 0 0 -0.965 1.264l0.611 2.236 0.31 1.239 -0.285 1.148zm3.243 -5.674l0.794 1.475a1 1 0 0 0 0.881 0.525h4.082a1 1 0 0 0 1 -1.083l-0.413 -4.917h0.2l1.865 5.33a1 1 0 0 0 0.944 0.67h3a0.56 0.56 0 0 1 0.548 0.345 0.5 0.5 0 0 1 -0.13 0.511 0.483 0.483 0 0 1 -0.349 0.144h-3.065a1 1 0 0 0 -0.944 0.67l-1.869 5.33h-0.2l0.41 -4.917a1 1 0 0 0 -1 -1.083h-4.079a1 1 0 0 0 -0.881 0.525l-0.794 1.475h-0.9l0.557 -2.024a1.02 1.02 0 0 0 0 -0.485l-0.644 -2.491z">
        <aapt:attr
            name="android:fillColor">
            <gradient
                android:startX="-1.799"
                android:startY="59.235"
                android:endX="65.448"
                android:endY="-8.012"
                android:tileMode="clamp">
                <item
                    android:color="@color/gradient_start"
                    android:offset="0" />
                <item
                    android:color="@color/gradient_center"
                    android:offset="0.518" />
                <item
                    android:color="@color/gradient_end"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:pathData="M28 42h13a1 1 0 0 0 1 -1v-4a1 1 0 0 0 -1 -1h-13a1 1 0 0 0 -1 1v4a1 1 0 0 0 1 1zm1 -4h11v2h-11z">
        <aapt:attr
            name="android:fillColor">
            <gradient
                android:startX="6.233"
                android:startY="67.267"
                android:endX="73.48"
                android:endY="0.02"
                android:tileMode="clamp">
                <item
                    android:color="@color/gradient_start"
                    android:offset="0" />
                <item
                    android:color="@color/gradient_center"
                    android:offset="0.518" />
                <item
                    android:color="@color/gradient_end"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:pathData="M44 42h13a1 1 0 0 0 1 -1v-4a1 1 0 0 0 -1 -1h-13a1 1 0 0 0 -1 1v4a1 1 0 0 0 1 1zm1 -4h11v2h-11z">
        <aapt:attr
            name="android:fillColor">
            <gradient
                android:startX="14.233"
                android:startY="75.267"
                android:endX="81.48"
                android:endY="8.02"
                android:tileMode="clamp">
                <item
                    android:color="@color/gradient_start"
                    android:offset="0" />
                <item
                    android:color="@color/gradient_center"
                    android:offset="0.518" />
                <item
                    android:color="@color/gradient_end"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:pathData="M41 43h-9a1 1 0 0 0 0 2h9a1 1 0 0 0 0 -2z">
        <aapt:attr
            name="android:fillColor">
            <gradient
                android:startX="9.733"
                android:startY="70.767"
                android:endX="76.98"
                android:endY="3.52"
                android:tileMode="clamp">
                <item
                    android:color="@color/gradient_start"
                    android:offset="0" />
                <item
                    android:color="@color/gradient_center"
                    android:offset="0.518" />
                <item
                    android:color="@color/gradient_end"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:pathData="M28 48h13a1 1 0 0 0 0 -2h-13a1 1 0 0 0 0 2z">
        <aapt:attr
            name="android:fillColor">
            <gradient
                android:startX="10.233"
                android:startY="71.267"
                android:endX="77.48"
                android:endY="4.02"
                android:tileMode="clamp">
                <item
                    android:color="@color/gradient_start"
                    android:offset="0" />
                <item
                    android:color="@color/gradient_center"
                    android:offset="0.518" />
                <item
                    android:color="@color/gradient_end"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:pathData="M28 51h13a1 1 0 0 0 0 -2h-13a1 1 0 0 0 0 2z">
        <aapt:attr
            name="android:fillColor">
            <gradient
                android:startX="11.733"
                android:startY="72.767"
                android:endX="78.98"
                android:endY="5.52"
                android:tileMode="clamp">
                <item
                    android:color="@color/gradient_start"
                    android:offset="0" />
                <item
                    android:color="@color/gradient_center"
                    android:offset="0.518" />
                <item
                    android:color="@color/gradient_end"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:pathData="M44 45h13a1 1 0 0 0 0 -2h-13a1 1 0 0 0 0 2z">
        <aapt:attr
            name="android:fillColor">
            <gradient
                android:startX="16.733"
                android:startY="77.767"
                android:endX="83.98"
                android:endY="10.52"
                android:tileMode="clamp">
                <item
                    android:color="@color/gradient_start"
                    android:offset="0" />
                <item
                    android:color="@color/gradient_center"
                    android:offset="0.518" />
                <item
                    android:color="@color/gradient_end"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:pathData="M44 48h13a1 1 0 0 0 0 -2h-13a1 1 0 0 0 0 2z">
        <aapt:attr
            name="android:fillColor">
            <gradient
                android:startX="18.233"
                android:startY="79.267"
                android:endX="85.48"
                android:endY="12.02"
                android:tileMode="clamp">
                <item
                    android:color="@color/gradient_start"
                    android:offset="0" />
                <item
                    android:color="@color/gradient_center"
                    android:offset="0.518" />
                <item
                    android:color="@color/gradient_end"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:pathData="M44 51h9a1 1 0 0 0 0 -2h-9a1 1 0 0 0 0 2z">
        <aapt:attr
            name="android:fillColor">
            <gradient
                android:startX="18.733"
                android:startY="79.767"
                android:endX="85.98"
                android:endY="12.52"
                android:tileMode="clamp">
                <item
                    android:color="@color/gradient_start"
                    android:offset="0" />
                <item
                    android:color="@color/gradient_center"
                    android:offset="0.518" />
                <item
                    android:color="@color/gradient_end"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:pathData="M28 45h1a1 1 0 0 0 0 -2h-1a1 1 0 0 0 0 2z">
        <aapt:attr
            name="android:fillColor">
            <gradient
                android:startX="5.733"
                android:startY="66.767"
                android:endX="72.98"
                android:endY="-0.48"
                android:tileMode="clamp">
                <item
                    android:color="@color/gradient_start"
                    android:offset="0" />
                <item
                    android:color="@color/gradient_center"
                    android:offset="0.518" />
                <item
                    android:color="@color/gradient_end"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:pathData="M57 49h-1a1 1 0 0 0 0 2h1a1 1 0 0 0 0 -2z">
        <aapt:attr
            name="android:fillColor">
            <gradient
                android:startX="22.733"
                android:startY="83.767"
                android:endX="89.98"
                android:endY="16.52"
                android:tileMode="clamp">
                <item
                    android:color="@color/gradient_start"
                    android:offset="0" />
                <item
                    android:color="@color/gradient_center"
                    android:offset="0.518" />
                <item
                    android:color="@color/gradient_end"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:pathData="M62 29a1 1 0 0 0 1 -1v-11a1 1 0 0 0 -1 -1h-5v-2h3a1 1 0 0 0 1 -1v-4a1 1 0 0 0 -1 -1h-8a1 1 0 0 0 -1 1v4a1 1 0 0 0 1 1h3v2h-46v-2h3a1 1 0 0 0 1 -1v-4a1 1 0 0 0 -1 -1h-8a1 1 0 0 0 -1 1v4a1 1 0 0 0 1 1h3v2h-5a1 1 0 0 0 -1 1v11a1 1 0 0 0 1 1h5v3h-5a1 1 0 0 0 -1 1v21a1 1 0 0 0 1 1h60a1 1 0 0 0 1 -1v-21a1 1 0 0 0 -1 -1h-5v-3zm-9 -17v-2h6v2zm-48 0v-2h6v2zm-2 15v-9h58v9zm58 7v19h-58v-19zm-6 -2h-46v-3h46z">
        <aapt:attr
            name="android:fillColor">
            <gradient
                android:startX="1.733"
                android:startY="62.767"
                android:endX="68.98"
                android:endY="-4.48"
                android:tileMode="clamp">
                <item
                    android:color="@color/gradient_start"
                    android:offset="0" />
                <item
                    android:color="@color/gradient_center"
                    android:offset="0.518" />
                <item
                    android:color="@color/gradient_end"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:pathData="M41 20h-35a1 1 0 0 0 0 2h35a1 1 0 0 0 0 -2z">
        <aapt:attr
            name="android:fillColor">
            <gradient
                android:startX="-8.267"
                android:startY="52.767"
                android:endX="58.98"
                android:endY="-14.48"
                android:tileMode="clamp">
                <item
                    android:color="@color/gradient_start"
                    android:offset="0" />
                <item
                    android:color="@color/gradient_center"
                    android:offset="0.518" />
                <item
                    android:color="@color/gradient_end"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:pathData="M41 23h-35a1 1 0 0 0 0 2h35a1 1 0 0 0 0 -2z">
        <aapt:attr
            name="android:fillColor">
            <gradient
                android:startX="-6.767"
                android:startY="54.267"
                android:endX="60.48"
                android:endY="-12.98"
                android:tileMode="clamp">
                <item
                    android:color="@color/gradient_start"
                    android:offset="0" />
                <item
                    android:color="@color/gradient_center"
                    android:offset="0.518" />
                <item
                    android:color="@color/gradient_end"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:pathData="M58 20h-14a1 1 0 0 0 -1 1v3a1 1 0 0 0 1 1h14a1 1 0 0 0 1 -1v-3a1 1 0 0 0 -1 -1zm-1 3h-12v-1h12z">
        <aapt:attr
            name="android:fillColor">
            <gradient
                android:startX="6.233"
                android:startY="67.267"
                android:endX="73.48"
                android:endY="0.02"
                android:tileMode="clamp">
                <item
                    android:color="@color/gradient_start"
                    android:offset="0" />
                <item
                    android:color="@color/gradient_center"
                    android:offset="0.518" />
                <item
                    android:color="@color/gradient_end"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
</vector>