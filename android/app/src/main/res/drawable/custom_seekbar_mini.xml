<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item
        android:id="@android:id/background"
        android:top="10dp"
        android:bottom="10dp">
        <shape>
            <corners android:radius="0dip" />
            <solid android:color="@color/light_primary" />
        </shape>
    </item>
    <item
        android:id="@android:id/progress"
        android:top="8dp"
        android:bottom="8dp">
        <clip>
            <shape android:shape="rectangle">
                <corners android:radius="2dp" />
                <solid android:color="@color/primary" />

                <gradient
                    android:angle="180"
                    android:centerColor="#FB953B"
                    android:endColor="#FB953B"
                    android:startColor="#e66363" />
            </shape>
        </clip>
    </item>
</layer-list>