<?xml version="1.0" encoding="utf-8"?>
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    android:color="@color/ripple_material_dark">
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#e66363" />
            <corners android:radius="20dp" />
        </shape>
    </item>
    <item android:id="@android:id/background">
<shape android:shape="rectangle" >
    <corners
        android:radius="20dp"
        />
    <gradient
        android:angle="45"
        android:startColor="#e66363"
        android:endColor="#FB953B"
        android:type="linear"
        />
    <padding
        android:left="10dp"
        android:top="0dp"
        android:right="10dp"
        android:bottom="0dp"
        />

</shape>
    </item>
</ripple>