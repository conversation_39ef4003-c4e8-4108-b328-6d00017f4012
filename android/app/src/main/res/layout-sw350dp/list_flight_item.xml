<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <FrameLayout
        android:id="@+id/viewItem"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.cardview.widget.CardView
            android:id="@+id/card_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/corner_full"
            app:cardCornerRadius="2dp"
            app:cardElevation="0dp"
            android:layout_marginBottom="@dimen/cardMarginVertical"
            android:layout_marginLeft="@dimen/cardMarginHorizontal"
            android:layout_marginRight="@dimen/cardMarginHorizontal"
            android:layout_marginTop="@dimen/cardMarginVertical"
            app:cardPreventCornerOverlap="false">

            <LinearLayout
                android:id="@+id/container"
                android:background="@drawable/background_selector"
                android:layout_width="match_parent"
                android:gravity="center_horizontal|center_vertical"
                android:layout_height="match_parent"
                android:padding="5dp">

                <com.hqt.util.AspectRatioImageView
                    android:id="@+id/logo"
                    android:layout_marginLeft="5dp"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    app:srcCompat="@drawable/logo_vn" />

                <View
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:layout_marginRight="5dp"
                    android:layout_marginLeft="5dp"
                    android:background="@drawable/gradientdiv" />

                <LinearLayout
                    android:id="@+id/main"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <com.mikepenz.iconics.view.IconicsTextView
                            app:emojiCompatEnabled="false"
                            android:id="@+id/soHieu"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/textDark"
                            android:text="VN10" />

                        <View
                            android:layout_width="1dp"
                            android:layout_height="match_parent"
                            android:layout_marginRight="5dp"
                            android:layout_marginLeft="5dp"
                            android:background="@drawable/gradientdiv" />

                        <TextView
                            android:textColor="@color/textDark"
                            android:id="@+id/hangBay"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="VietNam Airline" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_margin="4dp">

                        <LinearLayout

                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:layout_gravity="center_vertical"
                            android:orientation="vertical">

                            <com.hqt.datvemaybay.RobotoTextView
                                android:id="@+id/thoiGianBay"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:singleLine="true"
                                android:text="10:10"
                                android:textColor="@color/textDark"
                                android:textSize="12sp"
                                android:textStyle="bold" />


                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:orientation="vertical">

                            <com.hqt.datvemaybay.RobotoTextView
                                android:id="@+id/txtDuration"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:text="1h15p"
                                android:textColor="@color/textDark"
                                android:textSize="11sp"
                                android:textStyle="bold" />

                            <View
                                android:layout_width="100dp"
                                android:layout_height="1dp"
                                android:layout_marginLeft="7dp"
                                android:layout_marginRight="7dp"
                                android:background="@color/diver_color" />

                            <com.hqt.datvemaybay.RobotoTextView
                                android:id="@+id/stop"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:text="Bay Thẳng"
                                android:textColor="@color/textDark"
                                android:textSize="11sp"
                                android:textStyle="bold" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:layout_gravity="center_vertical"
                            android:orientation="vertical">

                            <com.hqt.datvemaybay.RobotoTextView
                                android:id="@+id/thoiGianDen"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:singleLine="true"
                                android:text="17:00"
                                android:textColor="@color/textDark"
                                android:textSize="12sp"
                                android:textStyle="bold" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:gravity="right"
                    android:layout_marginRight="5dp"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/giaVe"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="#da281c"
                        android:text="200,000 vnd" />

                    <TextView
                        android:id="@+id/gioBay"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="#003A6F"
                        android:text="27/10/2018" />

                    <TextView
                        android:id="@+id/hangGhe"
                        android:layout_width="0dp"
                        android:layout_height="0dp" />

                    <TextView
                        android:id="@+id/giaPhi"
                        android:layout_width="0dp"
                        android:layout_height="0dp" />

                    <TextView
                        android:id="@+id/thuePhi"
                        android:layout_width="0dp"
                        android:layout_height="0dp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="0dp" />

                    <TextView
                        android:id="@+id/thueSanBay"
                        android:layout_width="0dp"
                        android:layout_height="0dp" />

                    <TextView
                        android:id="@+id/pos"
                        android:layout_width="0dp"
                        android:layout_height="0dp" />

                    <TextView
                        android:id="@+id/promo"
                        android:layout_width="0dp"
                        android:layout_height="0dp" />
                </LinearLayout>

            </LinearLayout>
        </androidx.cardview.widget.CardView>
    </FrameLayout>
</layout>