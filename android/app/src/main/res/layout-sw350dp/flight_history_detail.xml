<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="flightHistory"
            type="com.hqt.view.ui.flighthistory.data.model.FlightHistoryItem" />

        <import type="com.hqt.datvemaybay.Common" />

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@color/gbgray"
        app:cardBackgroundColor="@color/gbgray">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                android:background="@color/primary"
                android:padding="5dp"
                android:layout_width="match_parent"
                android:elevation="2dp"
                android:layout_height="wrap_content">

                <TextView
                    android:textColor="@color/white"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Chuyến bay: " />

                <TextView
                    android:textColor="@color/white"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@{flightHistory.number}" />

                <TextView
                    android:id="@+id/closeSheet"
                    android:textColor="@color/white"
                    android:gravity="end"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:text="Đóng" />
            </LinearLayout>

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <RelativeLayout
                        android:layout_width="wrap_content"
                        android:layout_height="200dp">

                        <com.hqt.util.AspectRatioImageView
                            android:id="@+id/noimage"
                            android:layout_centerInParent="true"
                            android:layout_width="120dp"
                            android:layout_height="120dp"
                            android:background="@drawable/logo_trans" />

                        <com.smarteist.autoimageslider.SliderView
                            android:layout_alignParentTop="true"
                            android:layout_alignParentStart="true"
                            android:id="@+id/imageSlider"
                            android:layout_width="match_parent"
                            android:layout_height="200dp"
                            app:sliderAnimationDuration="1500"
                            app:sliderAutoCycleDirection="back_and_forth"
                            app:sliderAutoCycleEnabled="false"
                            app:sliderIndicatorAnimationDuration="600"
                            app:sliderIndicatorGravity="center_horizontal|bottom"
                            app:sliderIndicatorMargin="15dp"
                            app:sliderIndicatorOrientation="horizontal"
                            app:sliderIndicatorPadding="3dp"
                            app:sliderIndicatorRadius="1dp"
                            app:sliderIndicatorSelectedColor="#FFF"
                            app:sliderIndicatorUnselectedColor="#5A5A5A"
                            app:sliderScrollTimeInSec="2"
                            app:sliderStartAutoCycle="false" />
                    </RelativeLayout>

                    <LinearLayout
                        android:background="@drawable/corner_full"
                        android:padding="5dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:layout_marginBottom="5dp"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="10dp"

                        android:weightSum="9">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="3"
                            android:gravity="center"
                            android:orientation="vertical">

                            <com.hqt.util.AspectRatioImageView

                                android:layout_width="wrap_content"
                                android:layout_height="50dp"
                                android:layout_gravity="center"
                                android:layout_marginStart="10dp"
                                android:layout_marginEnd="10dp"
                                android:paddingLeft="10dp"
                                android:paddingRight="10dp"
                                android:scaleType="centerInside"
                                bind:imageUrl="@{flightHistory.airline.logo}" />

                            <TextView
                                android:id="@+id/txtDiHangBay"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="VietNam Airline"
                                android:textColor="#000000"
                                android:textSize="11sp"
                                android:visibility="gone" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="3"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:text="Số hiệu"
                                android:textColor="#000000"
                                android:textSize="14sp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{flightHistory.aircraft.registration}"
                                android:layout_gravity="center"
                                android:textColor="#000000"
                                android:maxLines="1"
                                android:textSize="16sp"
                                android:textStyle="bold" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="3"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Máy bay"
                                android:textColor="#000000"
                                android:textSize="14sp" />

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@{flightHistory.aircraft.model.text}"
                                    android:singleLine="true"
                                    android:ellipsize="end"
                                    android:textColor="#000000"
                                    android:textSize="16sp"
                                    android:textStyle="bold" />
                            </LinearLayout>


                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:background="@drawable/corner_full"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="5dp"
                        android:layout_marginBottom="5dp"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="10dp"
                        android:orientation="horizontal"
                        android:weightSum="8">

                        <LinearLayout
                            android:padding="5dp"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="3"
                            android:gravity="center_horizontal|center_vertical"
                            android:orientation="vertical">

                            <TextView
                                android:layout_gravity="center_horizontal|center_vertical"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@drawable/code_rounded"
                                android:paddingLeft="3dp"
                                android:paddingTop="2dp"
                                android:paddingRight="3dp"
                                android:paddingBottom="2dp"
                                android:singleLine="true"
                                android:text="@{flightHistory.airport.origin.code}"
                                android:textColor="#7C7C7C"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_gravity="center_horizontal|center_vertical"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{flightHistory.airport.origin.city}"
                                android:textColor="#000000"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_gravity="center_horizontal|center_vertical"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{`UTC` +flightHistory.airport.origin.tz+`:00`}"
                                android:textSize="12sp" />


                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="2"
                            android:gravity="center_horizontal|center_vertical"
                            android:layout_gravity="center_vertical"
                            android:orientation="vertical">

                            <ImageView

                                android:contentDescription="@string/app_name"
                                android:layout_width="50dp"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_horizontal|center_vertical"
                                app:srcCompat="@drawable/flight_history_icon" />

                            <TextView
                                android:id="@+id/txtDiDuration"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_horizontal|center_vertical"
                                android:text="@{flightHistory.time.duration}"
                                android:maxLines="1"
                                android:textColor="#000000"
                                android:textSize="14sp"
                                android:textStyle="bold" />
                        </LinearLayout>

                        <LinearLayout
                            android:padding="5dp"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="3"
                            android:gravity="center_horizontal|center_vertical"
                            android:orientation="vertical">

                            <TextView
                                android:layout_gravity="center_horizontal|center_vertical"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@drawable/code_rounded"
                                android:paddingLeft="3dp"
                                android:paddingTop="2dp"
                                android:paddingRight="3dp"
                                android:paddingBottom="2dp"
                                android:singleLine="true"
                                android:text="@{flightHistory.airport.destination.code}"
                                android:textColor="#7C7C7C"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_gravity="center_horizontal|center_vertical"
                                android:id="@+id/txtDiThoiGianDen"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{flightHistory.airport.destination.city}"
                                android:textColor="#000000"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_gravity="center_horizontal|center_vertical"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{`UTC` +flightHistory.airport.destination.tz+`:00`}"
                                android:textSize="12sp" />


                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_marginTop="5dp"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="10dp"
                        android:layout_margin="10dp"
                        android:layout_width="match_parent"
                        android:weightSum="10"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:baselineAligned="false">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_weight="5"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="5dp"
                                android:background="@drawable/corner_full"
                                android:gravity="center_horizontal"
                                android:orientation="vertical"
                                android:padding="10dp">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Ngày khởi hành" />

                                <TextView
                                    style="@style/Text"
                                    android:maxLines="1"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textStyle="bold"
                                    android:text="@{Common.dateToString(flightHistory.time.scheduled.departure,`dd/MM/yyyy`)}"
                                    android:textSize="16sp" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_marginTop="10dp"
                                android:background="@drawable/corner_full"
                                android:padding="10dp"
                                android:layout_marginEnd="5dp"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:gravity="center_horizontal"
                                    android:text="Giờ khởi hành" />


                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal">

                                    <TextView

                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Lịch trình: " />

                                    <TextView
                                        android:gravity="end"
                                        android:layout_width="fill_parent"
                                        android:layout_height="wrap_content"
                                        style="@style/Text"
                                        android:textSize="16sp"
                                        android:textStyle="bold"
                                        android:text="@{Common.dateToString(flightHistory.time.scheduled.departure,`HH:mm`)}" />


                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal">

                                    <TextView

                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Thực tế: " />

                                    <TextView
                                        style="@style/Text"
                                        android:gravity="end"
                                        android:textSize="16sp"
                                        android:textStyle="bold"
                                        android:layout_width="fill_parent"
                                        android:layout_height="wrap_content"
                                        android:text="@{Common.dateToString(flightHistory.time.real.departure,`HH:mm`)}" />


                                </LinearLayout>
                            </LinearLayout>
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_weight="5"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="5dp"
                                android:background="@drawable/corner_full"
                                android:gravity="center_horizontal"
                                android:orientation="vertical"
                                android:padding="10dp">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Trạng thái" />

                                <TextView
                                    android:textStyle="bold"
                                    style="@style/Text"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@{flightHistory.status.getIconColor()}"
                                    android:text="@{flightHistory.status.getFlightStatus()}"
                                    android:textSize="16sp" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_marginTop="10dp"
                                android:background="@drawable/corner_full"
                                android:padding="10dp"
                                android:layout_marginStart="5dp"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:gravity="center_horizontal"
                                    android:text="Giờ hạ cánh" />

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal">

                                    <TextView

                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Lịch trình: " />

                                    <TextView
                                        style="@style/Text"
                                        android:gravity="end"
                                        android:textSize="16sp"
                                        android:textStyle="bold"
                                        android:layout_width="fill_parent"
                                        android:layout_height="wrap_content"
                                        android:text="@{Common.dateToString(flightHistory.time.scheduled.arrival,`HH:mm`)}" />


                                </LinearLayout>

                                <LinearLayout
                                    bind:visibility="@{!flightHistory.status.live}"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal">

                                    <TextView

                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Thực tế: " />

                                    <TextView
                                        style="@style/Text"
                                        android:layout_width="fill_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="end"
                                        android:text="@{Common.dateToString(flightHistory.time.real.arrival,`HH:mm`)}"
                                        android:textSize="16sp"
                                        android:textStyle="bold" />


                                </LinearLayout>

                                <LinearLayout
                                    android:visibility="gone"
                                    bind:visibility="@{flightHistory.status.live}"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Dự kiến: " />

                                    <View
                                        android:layout_width="10dp"
                                        android:layout_height="10dp"
                                        bind:viewColor="@{flightHistory.status.getIconColor()}" />

                                    <TextView
                                        style="@style/Text"
                                        android:gravity="end"
                                        android:textSize="16sp"
                                        android:textStyle="bold"
                                        android:layout_width="fill_parent"
                                        android:layout_height="wrap_content"
                                        android:text="@{Common.dateToString(flightHistory.time.estimated.arrival,`HH:mm`)}" />


                                </LinearLayout>
                            </LinearLayout>
                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_marginBottom="10dp"
                        android:background="@drawable/corner_full"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="10dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:padding="5dp"
                            android:animateLayoutChanges="true"
                            android:orientation="vertical">

                            <SeekBar
                                android:id="@+id/flightProgress"
                                android:layout_width="fill_parent"
                                android:layout_height="wrap_content"
                                android:max="100"
                                android:progress="@{flightHistory.getProgress()}"
                                android:progressDrawable="@drawable/custom_seekbar"
                                android:thumb="@drawable/flight_history_icon" />


                            <LinearLayout
                                bind:visibility="@{flightHistory.status.live}"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@{flightHistory.live.getFlightText()}" />

                                <LinearLayout
                                    android:layout_width="fill_parent"
                                    android:layout_height="match_parent"
                                    android:layoutDirection="rtl"
                                    android:layout_alignParentRight="true">

                                    <TextView
                                        android:gravity="end"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="@{flightHistory.live.getFlightRemainText()}" />

                                    <LinearLayout
                                        android:layout_width="fill_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="center_horizontal|center_vertical">

                                        <LinearLayout
                                            android:background="@drawable/button_round_yellow"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:orientation="horizontal"
                                            tools:ignore="UselessParent">

                                            <com.mikepenz.iconics.view.IconicsImageView
                                                android:layout_marginStart="10dp"
                                                android:layout_width="15dp"
                                                android:layout_height="15dp"
                                                android:rotation="45"
                                                android:layout_gravity="center_vertical"
                                                app:iiv_size="12dp"
                                                app:iiv_color="@color/white"
                                                app:iiv_icon="gmd_airplanemode_active"
                                                tools:ignore="TooDeepLayout" />

                                            <TextView
                                                android:id="@+id/liveIcon"
                                                android:gravity="center_horizontal|center_vertical"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:textColor="@color/white"
                                                android:textSize="10sp"
                                                android:textStyle="bold"
                                                android:paddingStart="2dp"
                                                android:paddingEnd="15dp"
                                                android:paddingTop="2dp"
                                                android:paddingBottom="2dp"
                                                android:text="Đang bay" />
                                        </LinearLayout>
                                    </LinearLayout>


                                </LinearLayout>

                            </LinearLayout>
                        </LinearLayout>
                    </LinearLayout>

                    <androidx.appcompat.widget.AppCompatButton
                        android:visibility="gone"
                        android:layout_marginBottom="5dp"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="10dp"
                        android:padding="5dp"
                        android:id="@+id/btnViewOnMap"
                        android:background="@drawable/button_gradient"
                        android:textColor="@color/white"
                        android:layout_width="match_parent"
                        android:textAlignment="gravity"
                        android:textSize="16sp"
                        android:layout_height="wrap_content"
                        android:gravity="center_horizontal|center_vertical"
                        android:text="Xem trên bản đồ" />

                </LinearLayout>
            </ScrollView>


        </LinearLayout>
    </LinearLayout>
</layout>