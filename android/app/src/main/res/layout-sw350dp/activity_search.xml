<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.hqt.viewmodel.BookingViewModel" />

        <import type="com.hqt.datvemaybay.Common" />

        <import type="android.view.View" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/coordinatorLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true"
        tools:context="com.hqt.view.ui.SearchActivity"
        tools:ignore="MissingPrefix">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/primary"
            android:fitsSystemWindows="true"
            android:orientation="vertical">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
                app:elevation="0dp"
                app:layout_collapseMode="pin"
                app:layout_scrollFlags="scroll|exitUntilCollapsed"
                app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

            <ScrollView
                android:id="@+id/scrollView1"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_alignParentTop="true"
                android:background="@color/gbgray"
                android:scrollbars="none">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/bg_gradient"
                        android:contentDescription="@string/app_name"
                        android:scaleType="centerCrop"
                        android:weightSum="3">

                        <RelativeLayout
                            android:id="@+id/header"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:adjustViewBounds="true">

                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignTop="@+id/select"
                                android:layout_alignBottom="@+id/select"
                                android:scaleType="center"
                                app:srcCompat="@drawable/world" />

                            <LinearLayout

                                android:id="@+id/change"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignTop="@+id/select"
                                android:layout_alignBottom="@+id/select"
                                android:gravity="center_vertical|center_horizontal"
                                android:orientation="vertical"
                                android:paddingLeft="10dp"
                                android:paddingTop="?attr/actionBarSize"
                                android:paddingRight="10dp"
                                android:paddingBottom="30dp">

                                <View
                                    android:layout_width="1dp"
                                    android:layout_height="60dp"
                                    android:background="@color/diver_color" />

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    android:rotation="180"
                                    app:iiv_color="@color/white"
                                    app:iiv_icon="gmd_airplanemode_active" />

                                <View
                                    android:layout_width="1dp"
                                    android:layout_height="fill_parent"
                                    android:background="@color/diver_color" />
                            </LinearLayout>

                            <LinearLayout

                                android:id="@+id/select"
                                android:layout_width="fill_parent"
                                android:layout_height="wrap_content"
                                android:layout_alignParentTop="true"
                                android:layout_marginLeft="20dp"
                                android:layout_toRightOf="@+id/change"
                                android:orientation="vertical"
                                android:paddingTop="?attr/actionBarSize"
                                android:paddingBottom="40dp">

                                <LinearLayout
                                    android:id="@+id/select_origin"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_toRightOf="@+id/change"
                                    android:orientation="vertical"
                                    android:visibility="visible">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="@string/txt_dep_from"
                                        android:textColor="@color/white" />

                                    <TextView
                                        android:id="@+id/originCode"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Chọn nơi đi"
                                        android:textColor="@color/white"
                                        android:textSize="20sp"
                                        android:textStyle="bold" />

                                    <TextView
                                        android:id="@+id/originName"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginRight="10dp"
                                        android:text=""
                                        android:textColor="@color/white" />
                                </LinearLayout>

                                <LinearLayout
                                    android:id="@+id/select_destination"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentTop="true"
                                    android:layout_marginTop="15dp"
                                    android:layout_toRightOf="@+id/change"
                                    android:orientation="vertical"
                                    android:visibility="visible">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="@string/txt_dep_to"
                                        android:textColor="@color/white" />

                                    <TextView
                                        android:id="@+id/destinationCode"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Chọn nơi đến"
                                        android:textColor="@color/white"
                                        android:textSize="20sp"
                                        android:textStyle="bold" />

                                    <TextView
                                        android:id="@+id/destinationName"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginRight="10dp"
                                        android:text=""
                                        android:textColor="@color/white" />
                                </LinearLayout>
                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/swapRouteLayout"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignTop="@+id/select"
                                android:layout_alignBottom="@+id/select"
                                android:layout_alignParentRight="true"
                                android:gravity="center_vertical|center_horizontal"
                                android:orientation="vertical"
                                android:paddingLeft="10dp"
                                android:paddingTop="?attr/actionBarSize"
                                android:paddingRight="10dp"
                                android:paddingBottom="30dp">

                                <View
                                    android:layout_width="1dp"
                                    android:layout_height="60dp"
                                    android:background="@color/diver_color" />

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:id="@+id/swapRoute"
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    app:iiv_color="@color/white"
                                    app:iiv_icon="gmd_import_export" />

                                <View
                                    android:layout_width="1dp"
                                    android:layout_height="fill_parent"
                                    android:background="@color/diver_color" />
                            </LinearLayout>

                        </RelativeLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="-25dp"
                        android:orientation="vertical"
                        android:paddingLeft="20dp"
                        android:paddingRight="20dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="right">

                            <RadioGroup
                                android:id="@+id/radio"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="5dp"
                                android:layout_weight="1"
                                android:background="@drawable/corner_full"
                                android:orientation="horizontal">

                                <Button
                                    android:id="@+id/checkOneWay"
                                    style="@style/MyApp.Button.Big"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="0.5"
                                    android:background="@drawable/button_one_way"
                                    android:capitalize="characters"
                                    android:checked="true"
                                    android:text="@string/txt_one_way_up"
                                    android:textAllCaps="true"
                                    android:textColor="#FFFFFF" />

                                <Button
                                    android:id="@+id/checkRoundtrip"
                                    style="@style/MyApp.Button.Big"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="0.5"
                                    android:background="@color/fui_transparent"
                                    android:capitalize="characters"
                                    android:text="@string/txt_round_trip_up"
                                    android:textAllCaps="true"
                                    android:textColor="#00a2e3" />
                            </RadioGroup>

                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:orientation="horizontal"
                        android:paddingLeft="20dp"
                        android:paddingRight="20dp"
                        android:weightSum="1">

                        <LinearLayout
                            android:id="@+id/selectDepDate"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginRight="5dp"
                            android:layout_weight="0.5"
                            android:background="@drawable/corner_full">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:padding="10dp">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/txt_dep_date"
                                    android:textSize="12sp" />

                                <LinearLayout
                                    android:layout_width="fill_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:id="@+id/txtDepDate"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center"
                                        android:text="14/09"
                                        android:textSize="24dp"
                                        android:textStyle="bold" />

                                    <LinearLayout

                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="left"
                                        android:orientation="vertical"
                                        android:paddingLeft="10dp">

                                        <TextView
                                            android:id="@+id/txtDepDayofweek"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="Thứ s" />

                                        <TextView
                                            android:id="@+id/txtDepYear"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="2018" />
                                    </LinearLayout>
                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="fill_parent"
                                    android:layout_height="wrap_content">

                                    <TextView
                                        android:id="@+id/txtDepLunarDate"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Âm lịch 20/11"
                                        android:textSize="12sp" />

                                    <LinearLayout
                                        android:layout_width="fill_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="right|end">

                                        <com.mikepenz.iconics.view.IconicsImageView

                                            android:layout_width="12dp"
                                            android:layout_height="12dp"
                                            android:paddingTop="6dp"
                                            app:iiv_color="@color/input_register_hint"
                                            app:iiv_icon="faw-caret_down" />
                                    </LinearLayout>
                                </LinearLayout>


                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout

                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="5dp"
                            android:layout_weight="0.5"
                            android:background="@drawable/corner_full">

                            <LinearLayout
                                android:id="@+id/selectRetDate"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"

                                android:padding="10dp">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/txt_ret_date"
                                    android:textSize="12sp" />

                                <LinearLayout
                                    android:id="@+id/llRetDate"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:orientation="vertical">
                                    <LinearLayout
                                        android:layout_width="fill_parent"
                                        android:layout_height="wrap_content"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:id="@+id/txtRetDate"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_gravity="center"
                                            android:text=""
                                            android:textSize="24sp"
                                            android:textStyle="bold" />

                                        <LinearLayout
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:gravity="left"
                                            android:orientation="vertical"
                                            android:paddingLeft="10dp">

                                            <TextView
                                                android:id="@+id/txtRetDayOfWeek"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content" />

                                            <TextView
                                                android:id="@+id/txtRetYear"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content" />
                                        </LinearLayout>
                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_width="fill_parent"
                                        android:layout_height="wrap_content">

                                        <TextView
                                            android:id="@+id/txtRetLunarDate"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:textSize="12sp" />

                                        <LinearLayout
                                            android:layout_width="fill_parent"
                                            android:layout_height="wrap_content"
                                            android:gravity="right|end">

                                            <com.mikepenz.iconics.view.IconicsImageView
                                                android:id="@+id/txtRetCaret"
                                                android:layout_width="12dp"
                                                android:layout_height="12dp"
                                                android:paddingTop="6dp"
                                                android:visibility="gone"
                                                app:iiv_color="@color/input_register_hint"
                                                app:iiv_icon="faw-caret_down" />
                                        </LinearLayout>
                                    </LinearLayout>
                                </LinearLayout>


                            </LinearLayout>

                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/select_pax_button"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="20dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginRight="20dp"
                        android:background="@drawable/corner_full">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:padding="10dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/txt_pax"
                                android:textSize="12sp" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center_horizontal"
                                android:orientation="horizontal"
                                android:padding="5dp"
                                android:weightSum="3">

                                <LinearLayout
                                    android:id="@+id/showPaxSelect"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:gravity="center">

                                    <TextView
                                        android:id="@+id/adultCount"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="1"
                                        android:textAlignment="center"
                                        android:textSize="14sp"
                                        android:textStyle="bold" />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:paddingLeft="5dp"
                                        android:text="@string/txt_adult"
                                        android:textAlignment="center"
                                        android:textSize="14sp"
                                        android:textStyle="bold" />
                                </LinearLayout>

                                <View
                                    android:layout_width="1dp"
                                    android:layout_height="fill_parent"
                                    android:layout_marginLeft="10dp"
                                    android:layout_marginRight="10dp"
                                    android:background="#dddddd" />

                                <LinearLayout
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:gravity="center">

                                    <TextView
                                        android:id="@+id/childCount"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="0"
                                        android:textAlignment="center"
                                        android:textSize="14sp"
                                        android:textStyle="bold" />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:paddingLeft="5dp"
                                        android:text="@string/txt_child"
                                        android:textAlignment="center"
                                        android:textSize="14sp"
                                        android:textStyle="bold" />
                                </LinearLayout>

                                <View
                                    android:layout_width="1dp"
                                    android:layout_height="fill_parent"
                                    android:layout_marginLeft="10dp"
                                    android:layout_marginRight="10dp"
                                    android:background="#dddddd" />

                                <LinearLayout
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:gravity="center">

                                    <TextView
                                        android:id="@+id/infantCount"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="0"
                                        android:textAlignment="center"
                                        android:textSize="14sp"
                                        android:textStyle="bold" />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:paddingLeft="5dp"
                                        android:text="@string/txt_infant"
                                        android:textAlignment="center"
                                        android:textSize="14sp"
                                        android:textStyle="bold" />
                                </LinearLayout>


                            </LinearLayout>

                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="20dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginRight="20dp"
                        android:background="@drawable/corner_full">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="fill_parent"
                                android:background="@drawable/border_left"
                                android:padding="10dp">

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    app:iiv_color="@color/white"
                                    app:iiv_icon="gmd_timeline" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="5dp"
                                android:background="@color/white"
                                android:orientation="vertical"
                                android:padding="5dp">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/txt_find_cheap"
                                    android:textColor="@color/textDark" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/txt_find_cheap_description"
                                    android:textSize="12sp" />

                            </LinearLayout>

                            <Switch
                                android:id="@+id/cheapSearch"
                                android:layout_width="fill_parent"
                                android:layout_height="fill_parent"
                                android:layout_gravity="center_horizontal|center_vertical" />

                        </LinearLayout>

                    </LinearLayout>


                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/search"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginLeft="25dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginRight="25dp"
                        android:layout_marginBottom="10dp"
                        android:background="@drawable/button_gradient"
                        android:gravity="center_horizontal|center_vertical"
                        android:paddingLeft="50dp"
                        android:paddingRight="50dp"
                        android:text="TÌM CHUYẾN BAY"
                        android:textAlignment="gravity"
                        android:textColor="#FFFFFF"
                        android:textSize="16sp" />

                </LinearLayout>
            </ScrollView>

        </RelativeLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>