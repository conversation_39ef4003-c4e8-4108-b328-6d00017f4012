<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:bind="http://schemas.android.com/apk/res-auto"
    tools:context="com.hqt.view.ui.booking.BookingActivity">

    <data>

        <variable
            name="vm"
            type="com.hqt.viewmodel.BookingViewModel" />

        <import type="com.hqt.datvemaybay.Common" />

        <import type="android.view.View" />
    </data>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        android:background="#F2F1F1"
        tools:showIn="@layout/activity_train_book">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <HorizontalScrollView
                android:scrollbars="none"
                android:background="@color/primary"
                android:id="@+id/horizalScroll"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingBottom="20dp">

                    <LinearLayout
                        android:id="@+id/tripContainer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical" />

                    <LinearLayout
                        android:id="@+id/tripContainerRt"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical" />


                </LinearLayout>
            </HorizontalScrollView>

            <androidx.cardview.widget.CardView
                android:layout_marginTop="-15dp"
                android:id="@+id/card_view1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="2dp"
                app:cardElevation="2dp"
                style="@style/CardViewStyle.Light"
                android:layout_marginBottom="5dp"
                android:layout_marginLeft="5dp"
                android:layout_marginRight="5dp"
                app:cardPreventCornerOverlap="false"
                app:contentPadding="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="10dp"
                    android:background="#FFFFFF"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_gravity="left"
                            android:textStyle="bold"
                            style="@style/Text"
                            android:text="@{vm.getInputPaxTitle()}" />

                    </LinearLayout>

                    <LinearLayout
                        android:orientation="vertical"
                        android:layout_width="match_parent"
                        android:id="@+id/paxInPut"
                        android:layout_height="wrap_content"
                        android:animateLayoutChanges="true"
                        android:layout_gravity="center_horizontal">

                        <ProgressBar
                            android:indeterminate="true"
                            android:id="@+id/load_bag"
                            android:layout_gravity="center_horizontal"
                            android:layout_width="20dp"
                            style="?android:attr/progressBarStyle"
                            android:layout_height="20dp" />

                    </LinearLayout>


                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:weightSum="10">

                <androidx.cardview.widget.CardView
                    android:visibility="@{vm.isDomesticBooking() ? View.VISIBLE : View.GONE}"
                    android:id="@+id/show_seat_select"
                    android:layout_width="0dp"
                    android:layout_weight="5"
                    android:layout_height="wrap_content"
                    app:cardCornerRadius="2dp"
                    app:cardElevation="2dp"
                    style="@style/CardViewStyle.Light"
                    android:layout_marginBottom="5dp"
                    android:layout_marginLeft="5dp"
                    android:layout_marginRight="5dp"
                    app:cardPreventCornerOverlap="false"
                    app:contentPadding="2dp">

                    <LinearLayout
                        android:padding="5dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:gravity="center_horizontal|center_vertical"
                            android:padding="5dp">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:background="@drawable/ic_seat" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@color/white"
                            android:padding="5dp"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/txt_seat_select"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textStyle="bold"
                                style="@style/Text"
                                android:text="@string/txt_seat_select" />

                            <TextView
                                android:lines="2"
                                android:id="@+id/txt_seat_select_detail"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="12sp"
                                android:text="@string/txt_seat_select_detail" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="fill_parent"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical|end"
                            android:padding="10dp">

                            <com.mikepenz.iconics.view.IconicsImageView
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                app:iiv_color="@color/primary"
                                app:iiv_icon="gmd_keyboard_arrow_right" />
                        </LinearLayout>

                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    android:visibility="@{vm.isDomesticBooking() ? View.VISIBLE : View.GONE}"
                    android:id="@+id/show_add_on_select"
                    android:layout_width="0dp"
                    android:layout_weight="5"
                    android:layout_height="wrap_content"
                    app:cardCornerRadius="2dp"
                    app:cardElevation="2dp"
                    style="@style/CardViewStyle.Light"
                    android:layout_marginBottom="5dp"
                    android:layout_marginRight="5dp"
                    app:cardPreventCornerOverlap="false"
                    app:contentPadding="2dp">

                    <LinearLayout
                        android:padding="5dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:gravity="center_horizontal|center_vertical"
                            android:padding="5dp">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:background="@drawable/ic_meal" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@color/white"
                            android:padding="5dp"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/txt_addon_select"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textStyle="bold"
                                style="@style/Text"
                                android:text="@string/txt_addon_select" />

                            <TextView
                                android:lines="2"
                                android:id="@+id/txt_addon_select_detail"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="12sp"
                                android:text="@string/txt_addon_select_detail" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="fill_parent"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical|end"
                            android:padding="10dp">

                            <com.mikepenz.iconics.view.IconicsImageView
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                app:iiv_color="@color/primary"
                                app:iiv_icon="gmd_keyboard_arrow_right" />
                        </LinearLayout>

                    </LinearLayout>
                </androidx.cardview.widget.CardView>
            </LinearLayout>

            <androidx.cardview.widget.CardView
                android:visibility="@{vm.isShowLogin() ? View.VISIBLE : View.GONE}"
                android:id="@+id/loginLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="2dp"
                app:cardElevation="2dp"
                style="@style/CardViewStyle.Light"
                android:layout_marginBottom="5dp"
                android:layout_marginLeft="5dp"
                android:layout_marginRight="5dp"
                app:cardPreventCornerOverlap="false"
                app:contentPadding="2dp">

                <LinearLayout
                    android:id="@+id/txt_reward"
                    android:padding="10dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_horizontal"
                        android:padding="10dp">

                        <com.mikepenz.iconics.view.IconicsImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            app:iiv_size="20dp"
                            app:iiv_icon="gmd_account_circle" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_marginLeft="5dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@color/white"
                        android:padding="5dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textStyle="bold"
                            style="@style/Text"
                            android:text="@string/txt_not_login" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="12sp"
                            android:text="@string/txt_txt_not_login" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical|end"
                        android:padding="10dp">

                        <com.mikepenz.iconics.view.IconicsImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            app:iiv_color="@color/primary"
                            app:iiv_icon="gmd_keyboard_arrow_right" />
                    </LinearLayout>

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/card_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="2dp"
                app:cardElevation="2dp"
                style="@style/CardViewStyle.Light"
                android:layout_marginBottom="5dp"
                android:layout_marginLeft="5dp"
                android:layout_marginRight="5dp"
                app:cardPreventCornerOverlap="false"
                app:contentPadding="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="10dp"
                    android:background="#FFFFFF"
                    android:orientation="vertical">


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/textView8"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_gravity="left"
                            android:textStyle="bold"
                            style="@style/Text"
                            android:text="@{vm.getInputContactTitle()}" />


                    </LinearLayout>

                    <LinearLayout
                        android:visibility="@{vm.isShowBookingView() ? View.VISIBLE : View.GONE}"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <LinearLayout

                            android:layout_width="match_parent"
                            android:weightSum="5"
                            android:paddingBottom="5dp"
                            android:gravity="center_vertical"
                            android:layout_height="wrap_content">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_weight="2"
                                android:textColor="@color/textDark"
                                android:layout_height="wrap_content"
                                android:text="Liên hệ" />

                            <LinearLayout
                                android:layout_weight="3"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="right"
                                android:orientation="vertical">

                                <TextView
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@{vm.booking.contact_name}" />

                                <TextView
                                    android:textSize="12sp"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@{vm.booking.contact_phone +` - `+vm.booking.contact_email}" />
                            </LinearLayout>


                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:visibility="@{vm.isShowBookingView() ? View.GONE : View.VISIBLE}"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <LinearLayout

                                android:layout_width="match_parent"
                                android:weightSum="5"
                                android:paddingBottom="5dp"
                                android:layout_height="wrap_content">

                                <TextView
                                    android:id="@+id/textView1"
                                    android:layout_width="0dp"
                                    android:layout_weight="2"
                                    android:textColor="@color/textDark"
                                    android:layout_height="wrap_content"
                                    android:text="@string/hoVaTen" />

                                <EditText
                                    android:focusable="@{!vm.isShowBookingView()}"
                                    android:enabled="@{!vm.isShowBookingView()}"
                                    android:id="@+id/txtContactName"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="3"
                                    android:singleLine="true"
                                    android:paddingTop="2dp"
                                    android:paddingBottom="2dp"
                                    android:paddingRight="10dp"
                                    android:paddingLeft="10dp"
                                    android:inputType="textPersonName"
                                    android:text="@={vm.booking.contact_name}"
                                    android:hint="@string/txtNamehint"
                                    android:textColor="#000"
                                    android:background="@drawable/edit_text"
                                    android:ems="10" />


                            </LinearLayout>
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:weightSum="5"
                            android:paddingBottom="5dp"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_weight="2"
                                    android:textColor="@color/textDark"
                                    android:layout_height="wrap_content"
                                    android:text="@string/soDienThoai" />

                                <EditText
                                    android:focusable="@{!vm.isShowBookingView()}"
                                    android:enabled="@{!vm.isShowBookingView()}"
                                    android:id="@+id/txtContactPhone"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="3"
                                    android:textColor="#000"
                                    android:paddingTop="2dp"
                                    android:paddingBottom="2dp"
                                    android:paddingRight="10dp"
                                    android:paddingLeft="10dp"
                                    android:inputType="phone"
                                    android:text="@={vm.booking.contact_phone}"
                                    android:background="@drawable/edit_text"
                                    android:ems="10" />

                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:weightSum="5"
                            android:paddingBottom="5dp"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_weight="2"
                                    android:textColor="@color/textDark"
                                    android:layout_height="wrap_content"
                                    android:text="@string/email" />

                                <EditText
                                    android:focusable="@{!vm.isShowBookingView()}"
                                    android:enabled="@{!vm.isShowBookingView()}"
                                    android:id="@+id/txtContactEmail"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="3"
                                    android:textColor="#000"
                                    android:paddingTop="2dp"
                                    android:paddingBottom="2dp"
                                    android:paddingRight="10dp"
                                    android:paddingLeft="10dp"
                                    android:inputType="textEmailAddress|textCapCharacters"
                                    android:text="@={vm.booking.contact_email}"
                                    android:hint="@string/txtEmailhint"
                                    android:background="@drawable/edit_text"
                                    android:ems="10" />

                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:paddingBottom="0dp"
                            android:weightSum="5"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <TextView
                                    android:id="@+id/textView2"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="2"
                                    android:text="@string/maGiamGia"
                                    android:textColor="@color/textDark" />

                                <EditText
                                    android:focusable="@{!vm.isShowBookingView()}"
                                    android:enabled="@{!vm.isShowBookingView()}"
                                    android:id="@+id/txtVoucherCode"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1.5"
                                    android:background="@drawable/edit_text"
                                    android:paddingTop="2dp"
                                    android:paddingBottom="2dp"
                                    android:inputType="textCapCharacters"
                                    android:paddingRight="10dp"
                                    android:paddingLeft="10dp"
                                    android:ems="10"
                                    android:textColor="#000"
                                    android:text="@={vm.booking.voucher}"
                                    android:layout_marginTop="2dp" />

                                <androidx.appcompat.widget.AppCompatButton
                                    android:layout_width="0dp"
                                    android:backgroundTint="#FF5959"
                                    android:textColor="#FFFFFF"
                                    android:layout_height="31dp"
                                    android:text="Áp dụng"
                                    android:padding="2dp"
                                    android:layout_weight="1.5"
                                    android:textSize="10sp"
                                    android:id="@+id/btnGetVoucher"
                                    style="@style/MyApp.Button.Big"
                                    android:layout_margin="2dp" />


                            </LinearLayout>

                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:paddingBottom="5dp">

                        <LinearLayout
                            android:visibility="@{vm.isShowBookingView() ? View.VISIBLE : View.GONE}"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:orientation="vertical">

                            <LinearLayout
                                android:id="@+id/showBookingStatus"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <LinearLayout
                                    android:id="@+id/waittingView"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:paddingTop="2dp"
                                    android:paddingBottom="5dp"
                                    android:orientation="vertical">

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:paddingBottom="2dp">

                                        <TextView

                                            android:textColor="@color/textDark"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="@string/maDonHang" />


                                        <TextView
                                            android:id="@+id/txtBookingId"
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:gravity="right"
                                            android:paddingRight="5dp"
                                            android:text="@{`#`+vm.booking.id}"
                                            android:textColor="#14a9e5"
                                            android:textStyle="bold" />

                                    </LinearLayout>

                                </LinearLayout>

                                <LinearLayout
                                    android:paddingTop="2dp"
                                    android:paddingBottom="2dp"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:orientation="vertical">

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:paddingBottom="5dp"
                                        android:layout_height="wrap_content">

                                        <TextView
                                            android:textColor="@color/textDark"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="Mã đặt chỗ (Code):" />


                                        <TextView
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:gravity="right"
                                            android:textColor="#ff0f0b"
                                            android:textSize="14sp"
                                            android:text="@{vm.booking.pnr}"
                                            android:textStyle="bold" />

                                    </LinearLayout>


                                </LinearLayout>

                                <LinearLayout
                                    android:paddingTop="2dp"
                                    android:paddingBottom="2dp"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:id="@+id/pnrReturnLayout"
                                    android:visibility="gone"
                                    android:orientation="vertical">

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:paddingBottom="2dp"
                                        android:layout_height="wrap_content">

                                        <TextView
                                            android:id="@+id/txtPnrReturn"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="Mã đặt chỗ lượt về:" />


                                        <TextView
                                            android:id="@+id/pnrReturn"
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:gravity="right"
                                            android:textColor="#ff0f0b"
                                            android:textSize="14sp"
                                            android:text="@{vm.booking.pnr_return}"
                                            android:textStyle="bold" />

                                    </LinearLayout>
                                </LinearLayout>

                                <LinearLayout

                                    android:layout_width="match_parent"
                                    android:paddingBottom="5dp"
                                    android:orientation="horizontal"
                                    android:layout_height="wrap_content">

                                    <TextView
                                        android:textColor="@color/textDark"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Trạng thái:" />

                                    <LinearLayout
                                        android:visibility="@{vm.isCreatedBooking() ? View.VISIBLE : View.GONE}"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="right"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:id="@+id/txtBookingIdd"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:paddingRight="5dp"
                                            android:text="Đang tiến hành đặt chỗ"
                                            android:textColor="#14a9e5" />

                                        <ProgressBar
                                            android:id="@+id/marker_progress"
                                            style="?android:attr/progressBarStyle"
                                            android:layout_width="20dp"
                                            android:layout_height="20dp"
                                            android:paddingRight="5dp"
                                            android:indeterminate="true" />
                                    </LinearLayout>

                                    <TextView
                                        android:visibility="@{vm.isCreatedBooking() ? View.GONE : View.VISIBLE}"
                                        android:text="@{vm.booking.status_text}"
                                        android:id="@+id/txtStatus"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="right"
                                        android:textColor="#FF0000"
                                        android:textStyle="bold" />

                                </LinearLayout>

                                <LinearLayout
                                    android:paddingTop="2dp"
                                    android:paddingBottom="5dp"
                                    android:visibility="@{vm.booking.payment.status ? View.VISIBLE : View.GONE}"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content">

                                    <TextView
                                        android:textColor="@color/textDark"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Hạn thanh toán:" />

                                    <TextView
                                        android:id="@+id/txtTimeLimit"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="right"
                                        android:textColor="#14a9e5"
                                        android:text="@{vm.getTimeLimitText()}"
                                        android:textStyle="bold" />

                                </LinearLayout>
                            </LinearLayout>
                        </LinearLayout>

                        <LinearLayout
                            android:paddingTop="2dp"
                            android:layout_width="match_parent"
                            android:paddingBottom="5dp"
                            android:layout_height="wrap_content">

                            <TextView
                                android:id="@+id/textViews3"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/textDark"
                                android:text="@string/giaVe" />

                            <TextView
                                android:textColor="#000000"
                                android:id="@+id/txtGiaVe"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="@{vm.formatTotal(vm.booking.grandTotal)}"
                                android:gravity="right" />

                        </LinearLayout>

                        <LinearLayout
                            android:visibility="@{vm.booking.addon_fee > 0 ? View.VISIBLE : View.GONE}"
                            android:paddingTop="2dp"
                            android:layout_width="match_parent"
                            android:paddingBottom="5dp"
                            android:layout_height="wrap_content">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/textDark"
                                android:text="@string/seat_fee" />

                            <TextView
                                android:textColor="#000000"
                                android:id="@+id/txtSeatFee"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="@{vm.formatTotal(vm.booking.totalAddOn)}"
                                android:gravity="right" />

                        </LinearLayout>

                        <LinearLayout
                            android:visibility="@{vm.booking.bag_fee > 0 ? View.VISIBLE : View.GONE}"
                            android:layout_width="match_parent"
                            android:paddingBottom="2dp"
                            android:id="@+id/bagLayout"
                            android:layout_height="wrap_content">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/textDark"
                                android:text="@string/hanhLy" />

                            <TextView
                                android:id="@+id/txtbagFee"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="#FF0000"
                                android:text="@{vm.formatTotal(vm.booking.totalBagFee)}"
                                android:gravity="right" />

                        </LinearLayout>

                        <LinearLayout
                            android:visibility="@{vm.booking.discount > 0 ? View.VISIBLE : View.GONE}"
                            android:paddingTop="2dp"
                            android:layout_width="match_parent"
                            android:paddingBottom="5dp"
                            android:id="@+id/voucherLayout"
                            android:layout_height="wrap_content">

                            <TextView
                                android:layout_width="wrap_content"
                                android:textColor="@color/textDark"
                                android:layout_height="wrap_content"
                                android:text="@string/giamGia" />

                            <TextView
                                android:id="@+id/txtdiscount"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="#FF0000"
                                android:text="@{Common.dinhDangTien(vm.booking.discount)}"
                                android:gravity="right" />

                        </LinearLayout>


                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_margin="2dp"
                            android:background="@color/diver_color" />

                        <LinearLayout
                            android:paddingTop="2dp"
                            android:layout_width="match_parent"
                            android:paddingBottom="2dp"
                            android:layout_height="wrap_content">

                            <TextView
                                android:id="@+id/textView3"
                                android:layout_width="wrap_content"
                                style="@style/Text"
                                android:layout_height="wrap_content"
                                android:textStyle="bold"
                                android:text="@string/tongCong" />


                            <TextView
                                android:id="@+id/txtGiaTongCong"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="right"
                                android:text="@{vm.formatTotal(vm.booking.calculatorFinalPrice)}"
                                android:textColor="#FF0000"
                                android:textStyle="bold" />

                        </LinearLayout>

                        <View
                            android:layout_width="0dp"
                            android:layout_height="0dp"
                            android:id="@+id/got_it" />

                        <LinearLayout
                            bind:visibility="@{vm.booking.getRewardPointTotal() > 0}"
                            android:id="@+id/layoutPointReward"
                            android:layout_width="match_parent"
                            android:paddingBottom="2dp"
                            android:gravity="end"
                            android:layout_height="wrap_content">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="end"
                                android:textColor="@color/primary"

                                android:text="Nhận" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="end"
                                android:textColor="@color/primary"
                                android:paddingEnd="5dp"
                                android:paddingStart="5dp"
                                android:text="@{vm.booking.getRewardPointTotal() +``}" />

                            <com.mikepenz.iconics.view.IconicsTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/primary"
                                android:text="điểm {gmd_stars} " />

                        </LinearLayout>

                    </LinearLayout>

                    <androidx.appcompat.widget.AppCompatButton
                        android:layout_marginTop="5dp"
                        android:layout_marginBottom="5dp"
                        android:layout_marginLeft="25dp"
                        android:layout_marginRight="25dp"
                        android:padding="10dp"
                        android:id="@+id/btnBookVe"
                        android:background="@drawable/button_gradient"
                        android:textColor="@color/white"
                        android:layout_width="match_parent"
                        android:textAlignment="gravity"
                        android:textSize="16sp"
                        android:layout_height="wrap_content"
                        android:gravity="center_horizontal|center_vertical"
                        android:text="@{vm.getBookButtonText()}" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>


        </LinearLayout>

    </androidx.core.widget.NestedScrollView>
</layout>