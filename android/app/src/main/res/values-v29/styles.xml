<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="MyMaterialTheme.Base" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="android:forceDarkAllowed">false</item>

        <!-- Customize your theme here. -->
        <item name="colorControlNormal">@color/gbgray</item>
        <!-- colorPrimary is used for the default action bar background -->
        <item name="colorPrimary">@color/primary</item>
        <!-- colorPrimaryDark is used for the status bar -->
        <item name="colorPrimaryDark">@color/primary_dark</item>
        <item name="android:typeface">serif</item>
        <!--<item name="fontFamily">sans-serif</item>-->
        <!-- colorAccent is used as the default value for colorControlActivated
             which is used to tint widgets -->
        <item name="colorAccent">@color/accent</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="actionMenuTextColor">@android:color/black</item>
        <item name="android:navigationBarColor" tools:targetApi="21">@color/navigationBarColor
        </item>
        <item name="windowActionModeOverlay">true</item>
        <item name="actionModeBackground">@color/primary</item>
        <item name="android:textColorSecondary">@android:color/black</item>
        <item name="colorSurface">#FFFFFF</item>

    </style>
</resources>