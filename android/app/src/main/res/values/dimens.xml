<resources>
    <!-- Default screen margins, per the Android Design guidelines. -->
    <dimen name="activity_horizontal_margin">5dp</dimen>
    <dimen name="activity_vertical_margin">5dp</dimen>
    <dimen name="nav_drawer_width">260dp</dimen>

    <dimen name="cardMarginHorizontal">4dp</dimen>
    <dimen name="cardMarginVertical">2dp</dimen>
    <dimen name="app_bar_height">180dp</dimen>
    <dimen name="text_margin">16dp</dimen>
    <dimen name="section_height">140dp</dimen>
    <dimen name="margins">16dp</dimen>
    <dimen name="snackbar_margin">-48dp</dimen>


    <!-- Default screen margins, per the Android Design guidelines. -->


    <dimen name="button_size">40dp</dimen>
    <dimen name="color_button_size">35dp</dimen>

    <dimen name="four_button_margin">7dp</dimen>
    <dimen name="three_button_margin">17dp</dimen>
    <dimen name="two_button_margin">17dp</dimen>

    <dimen name="enter_button_margin">6dp</dimen>

    <dimen name="line_point_s">13dp</dimen>
    <dimen name="line_point_m">20dp</dimen>
    <dimen name="line_point_h">27dp</dimen>
    <dimen name="line_point_x">35dp</dimen>

    <dimen name="chart_button_size">100dp</dimen>

    <dimen name="axis_label_size">50sp</dimen>

    <dimen name="play_chart_padding">15dp</dimen>

    <dimen name="card_toolbar_btn">40dp</dimen>

    <dimen name="card_view_padding">1dp</dimen>
    <dimen name="card_layout_padding">.5dp</dimen>
    <dimen name="card_view_roundcorner">0dp</dimen>


    <dimen name="fab_margin">16dp</dimen>
    <dimen name="item_offset">10dp</dimen>
    <dimen name="detail_backdrop_height">250dp</dimen>
    <dimen name="backdrop_title">30dp</dimen>
    <dimen name="backdrop_subtitle">18dp</dimen>
    <dimen name="card_margin">5dp</dimen>
    <dimen name="card_album_radius">0dp</dimen>
    <dimen name="album_cover_height">160dp</dimen>
    <dimen name="album_title_padding">10dp</dimen>
    <dimen name="album_title">15dp</dimen>
    <dimen name="songs_count_padding_bottom">5dp</dimen>
    <dimen name="songs_count">12dp</dimen>


    <dimen name="imageview_width">0dp</dimen>
    <dimen name="imageview_height">130dp</dimen>


    <!-- Default screen margins, per the Android Design guidelines. -->

    <dimen name="logo_w_h">150dp</dimen>

    <dimen name="cell_height">40dp</dimen>
    <dimen name="row_header_width">55dp</dimen>
    <dimen name="text_size">12sp</dimen>

    <!-- Overriding the default values of the tableView -->
    <dimen name="default_row_header_width">55dp</dimen>
    <dimen name="default_column_header_height">55dp</dimen>
</resources>
