<resources xmlns:android="http://schemas.android.com/apk/res/android" xmlns:tools="http://schemas.android.com/tools">

    <!--
        Base application theme, dependent on API level. This theme is replaced
        by AppBaseTheme from res/values-vXX/styles.xml on newer devices.
    -->
    <style name="MyMaterialTheme" parent="MyMaterialTheme.Base"></style>


    <style name="AppTheme.AppBarOverlay" parent="Theme.AppCompat.Light.DarkActionBar"></style>

    <style name="AppTheme.PopupOverlay" parent="Theme.AppCompat.Light.DarkActionBar" />
    <!-- Application theme. -->
    <style name="MyMaterialTheme.Base" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="android:forceDarkAllowed">false</item>

        <!-- Customize your theme here. -->
        <item name="colorControlNormal">@color/gbgray</item>
        <!-- colorPrimary is used for the default action bar background -->
        <item name="colorPrimary">@color/primary</item>
        <!-- colorPrimaryDark is used for the status bar -->
        <item name="colorPrimaryDark">@color/primary_dark</item>
        <item name="android:typeface">serif</item>
        <!--<item name="fontFamily">sans-serif</item>-->
        <!-- colorAccent is used as the default value for colorControlActivated
             which is used to tint widgets -->
        <item name="colorAccent">@color/accent</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="actionMenuTextColor">@android:color/black</item>
        <item name="android:navigationBarColor" tools:targetApi="21">@color/navigationBarColor
        </item>
        <item name="windowActionModeOverlay">true</item>
        <item name="actionModeBackground">@color/primary</item>
        <item name="android:textColorSecondary">@android:color/black</item>
        <item name="colorSurface">#FFFFFF</item>
        <item name="materialCalendarTheme">@style/YourCustomCalendarStyle</item>


    </style>

    <style name="disableShadow" parent="MyMaterialTheme.Base">
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <style name="splashTheme" parent="MyMaterialTheme.Base">


        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowDisablePreview">true</item>
        <item name="android:statusBarColor">@color/primary_dark</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.5</item>
        <item name="android:windowAnimationStyle">@android:style/Animation</item>
        <item name="android:windowBackground">@color/primary_dark</item>
        <item name="android:windowFullscreen">true</item>


        <!--        <item name="android:windowDisablePreview">true</item>-->
        <!--        <item name="android:statusBarColor">@color/white</item>-->
        <!--        <item name="android:windowContentOverlay">@null</item>-->
        <!--        <item name="android:windowFullscreen">true</item>-->
        <!--        <item name="android:windowIsTranslucent">true</item>-->
        <!--        <item name="android:windowNoTitle">true</item>-->
        <!--        <item name="android:windowAnimationStyle">@android:style/Animation</item>-->
        <!--        <item name="android:windowBackground">@drawable/bg_gradient</item>-->
    </style>

    <style name="CustomMaterialDatePicker" parent="ThemeOverlay.MaterialComponents.MaterialCalendar">
        <!-- Thay đổi màu chính của DatePicker -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryVariant">@color/black</item>
        <item name="android:textColorPrimary">@color/white</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Thay đổi màu nhấn -->
        <item name="colorAccent">@color/colorPrimary</item>
        <item name="colorOnSurface">@color/white</item>
        <!-- Thay đổi màu nền -->
        <item name="colorSurface">#1E293B</item>
        <item name="android:textColor">#E2E8F0</item>
        <!-- Thay đổi màu nền của calendar -->
        <item name="materialCalendarTheme">@style/YourCustomCalendarStyle</item>
    </style>

    <style name="YourCustomDayStyle" parent="Widget.MaterialComponents.MaterialCalendar.Day.Selected">
        <item name="itemFillColor">@color/white</item>
        <item name="itemTextColor">@color/white</item>
    </style>

    <style name="YourCustomYearStyle" parent="Widget.MaterialComponents.MaterialCalendar.Year.Selected">
        <item name="itemTextColor">@color/white</item>
    </style>

    <style name="YourCustomCalendarStyle" parent="Widget.MaterialComponents.MaterialCalendar">
        <item name="daySelectedStyle">@style/YourCustomDayStyle</item>
        <item name="yearSelectedStyle">@style/YourCustomYearStyle</item>
        <!-- Tùy chỉnh các thành phần khác nếu cần -->
    </style>

    <style name="CardViewStyle.Light" parent="CardView.Light">
        <item name="android:foreground">?attr/selectableItemBackground</item>
        <item name="android:state_pressed">true</item>
        <item name="android:drawable">@drawable/press</item>
    </style>

    <style name="MyApp.Button.Big" parent="">
        <item name="android:theme">@style/Theme.MyApp.Button</item>
    </style>

    <style name="MyApp.Button.Gradient" parent="">
        <item name="android:theme">@style/Theme.MyApp.Button</item>
        <item name="android:background">@drawable/button_gradient_no_round</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="MyApp.Button.Blue" parent="">
        <item name="android:theme">@style/Theme.MyApp.Button.Blue</item>
    </style>

    <style name="SheetDialogTransparent" parent="Theme.Design.Light.BottomSheetDialog">
        <!--<item name="android:windowCloseOnTouchOutside">false</item>-->
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:colorBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.3</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
    </style>

    <style name="Text">
        <!-- All customizations that are NOT specific to a particular API-level can go here. -->
        <item name="android:textColor">#003A6F</item>
        <item name="android:textSize">15sp</item>
    </style>

    <style name="Divider_ngang">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">1dp</item>
        <item name="background">?android:attr/listDivider</item>
        <item name="android:padding">15dp</item>
    </style>

    <style name="ToolbarSubtitleAppearance" parent="@style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle">
        <item name="android:textSize">12dp</item>
    </style>


    <style name="LoginUiTheme" parent="FirebaseUI">
        <item name="android:textColorTertiary">@color/white</item>
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryDark">@color/primary_dark</item>
        <item name="colorAccent">@color/white</item>
        <item name="colorControlNormal">#2B2A2A</item>
        <item name="colorControlActivated">#FC1E1E</item>
        <item name="colorControlHighlight">#2B2A2A</item>
        <item name="android:textColor">#FFF</item>
        <item name="android:textColorPrimary">#FFF</item>
        <item name="android:textColorSecondary">#2B2A2A</item>
        <item name="android:windowBackground">@color/primary</item>
        <item name="windowActionBar">true</item>
        <item name="windowNoTitle">false</item>
    </style>

    <style name="FirebaseUI.Button" parent="@style/Widget.AppCompat.Button.Colored">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_gravity">right</item>
        <item name="android:textColor">#14A2E3</item>
        <item name="android:textColorSecondary">#B85353</item>
        <item name="android:windowBackground">@color/btnColor</item>
        <item name="windowActionBar">true</item>
        <item name="windowNoTitle">false</item>

    </style>


    <style name="FirebaseUI.Text">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/white</item>
    </style>


    <style name="Divider_doc">
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_width">1dp</item>
        <item name="background">?android:attr/listDivider</item>
        <item name="android:padding">15dp</item>
    </style>

    <style name="text_bank">
        <item name="android:textSize">14.0dip</item>
        <item name="android:textColor">#ff666666</item>
        <item name="android:gravity">left</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">3.0dip</item>
        <item name="android:layout_marginStart">10dp</item>
    </style>

    <style name="text_header">
        <item name="android:textSize">16.0dip</item>
        <item name="android:textColor">#ffffffff</item>
        <item name="android:paddingLeft">5.0dip</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
    </style>

    <style name="text_name_user">
        <item name="android:textSize">16.0dip</item>
        <item name="android:textColor">#ff0088cd</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginRight">5.0dip</item>
    </style>

    <style name="text_title_bank">
        <item name="android:textSize">14.0dip</item>
        <item name="android:textColor">#ff333333</item>
        <item name="android:gravity">left</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">10.0dip</item>

    </style>

    <style name="block_header">
        <item name="android:background">#ff0088cd</item>
        <item name="android:paddingLeft">5.0dip</item>
        <item name="android:paddingTop">5.0dip</item>
        <item name="android:paddingBottom">5.0dip</item>
        <item name="android:layout_width">fill_parent</item>
        <item name="android:layout_height">wrap_content</item>
    </style>

    <style name="Layout_block">
        <item name="android:background">@android:color/white</item>
        <item name="android:paddingLeft">10.0dip</item>
        <item name="android:paddingTop">10.0dip</item>
        <item name="android:paddingRight">10.0dip</item>
        <item name="android:paddingBottom">10.0dip</item>
        <item name="android:layout_width">fill_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">10.0dip</item>
    </style>

    <style name="BarraProgreso" parent="@android:style/Widget.ProgressBar.Horizontal">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:padding">0dp</item>
        <item name="android:indeterminate">true</item>
    </style>

    <style name="TitleTextView">
        <item name="android:textColor">#ff0000</item>
        <item name="android:textSize">20sp</item>
        <item name="android:shadowColor">#ffffff</item>
        <item name="android:shadowDx">1</item>
        <item name="android:shadowDy">2</item>
        <item name="android:shadowRadius">3</item>
    </style>

    <style name="TitleDateView">
        <item name="android:textColor">#202020</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="TitleWeekView">
        <item name="android:textSize">18sp</item>
        <item name="android:textColor">#FF0E256E</item>
    </style>

    <style name="CustomDayTextAppearance" parent="TextAppearance.AppCompat.Medium">
        <item name="android:textColor">@color/mcv_text_date_light</item>

    </style>

    <style name="MenuLabelsStyle">
        <item name="background">@drawable/fab_label_background</item>
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textSize">14sp</item>
        <item name="android:maxLines">2</item>
        <item name="android:ellipsize">end</item>
    </style>

    <style name="MenuButtonsStyle">
        <item name="fab_size">mini</item>
        <item name="fab_showAnimation">@anim/jump_from_down</item>
        <item name="fab_hideAnimation">@anim/jump_to_down</item>
        <item name="fab_shadowColor">#444</item>
        <item name="fab_colorNormal">#FFB805</item>
        <item name="fab_colorPressed">#F2AB00</item>
        <item name="fab_colorRipple">#D99200</item>
    </style>

    <style name="MenuButtonsSmall">
        <item name="fab_size">mini</item>
        <item name="fab_colorNormal">#1565C0</item>
        <item name="fab_colorPressed">#2272CD</item>
        <item name="fab_colorRipple">#62B2FF</item>
    </style>

    <style name="MenuButtonsSmall.Green">
        <item name="fab_colorNormal">#43A047</item>
        <item name="fab_colorPressed">#2E7D32</item>
        <item name="fab_colorRipple">#1B5E20</item>
    </style>

    <style name="Theme.MyApp.Button" parent="Widget.AppCompat.Button">
        <item name="colorButtonNormal">@color/btnColor</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textColorPrimary">@android:color/white</item>
    </style>

    <style name="Theme.MyApp.Button.Blue" parent="Widget.AppCompat.Button.Borderless">
        <item name="colorButtonNormal">@color/primary</item>
        <item name="android:textColorPrimary">@android:color/white</item>
    </style>

    <declare-styleable name="ArcProgress">
        <attr name="arc_progress" format="float" />
        <attr name="arc_angle" format="float" />
        <attr name="arc_stroke_width" format="dimension" />
        <attr name="arc_max" format="integer" />
        <attr name="arc_unfinished_color" format="color" />
        <attr name="arc_finished_color" format="color" />
        <attr name="arc_text_size" format="dimension" />
        <attr name="arc_text_color" format="color" />
        <attr name="arc_suffix_text" format="string" />
        <attr name="arc_suffix_text_size" format="dimension" />
        <attr name="arc_suffix_text_padding" format="dimension" />
        <attr name="arc_bottom_text" format="string" />
        <attr name="arc_bottom_text_size" format="dimension" />
    </declare-styleable>

    <style name="PayooSdkTheme.Blue" parent="MyMaterialTheme.Base">
        <item name="colorPrimary">@android:color/holo_blue_light</item>
        <item name="colorPrimaryDark">@android:color/holo_blue_dark</item>
        <item name="colorAccent">@android:color/holo_blue_light</item>
    </style>
</resources>
