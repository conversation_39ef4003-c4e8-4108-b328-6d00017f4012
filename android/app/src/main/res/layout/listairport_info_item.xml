<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="itemViewState"
            type="com.hqt.view.ui.search.ui.state.AirportInfoItemState" />
    </data>

    <LinearLayout
        android:id="@+id/container"
        android:layout_width="fill_parent"
        android:layout_margin="2dp"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/image"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:scaleType="centerCrop"
                android:padding="10dp"
                android:src="@drawable/ic_marker_aiport" />

            <LinearLayout
                android:paddingLeft="5dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"

                android:orientation="vertical">

                <TextView
                    android:id="@+id/airPortCity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="18sp"
                    tools:text="Ho Chi MInh City"
                    android:text="@{itemViewState.item.city}"
                    android:textColor="#000000" />

                <TextView
                    android:id="@+id/airPortName"
                    android:layout_width="wrap_content"
                    tools:text="Tan Son Nhat Airport"
                    android:text="@{itemViewState.item.name}"
                    android:layout_height="wrap_content"
                    android:layout_toRightOf="@+id/airPortName" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="fill_parent"
                android:gravity="end|center_vertical"
                android:layout_height="match_parent">

                <TextView
                    android:background="@drawable/round_airport_code"
                    android:padding="2dp"
                    android:id="@+id/airPortCode"
                    android:layout_width="70dp"
                    android:gravity="center"
                    android:layout_gravity="center_horizontal|center_vertical"
                    android:layout_height="wrap_content"
                    android:textSize="20sp"
                    android:text="@{itemViewState.item.code}"
                    tools:text="SGN" />

            </LinearLayout>

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@drawable/gradientdiv_vertical" />

    </LinearLayout>
</layout>