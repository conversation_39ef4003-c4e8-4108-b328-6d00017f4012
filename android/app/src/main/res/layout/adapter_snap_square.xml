<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="120dp"
    android:layout_height="120dp"
    android:layout_margin="4dp"
    android:focusable="true"
    app:cardCornerRadius="5dp"
    android:foreground="?attr/selectableItemBackground"
    app:cardElevation="0dp"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="0dp">

        <ImageView
            android:id="@+id/imageView"
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:scaleType="centerCrop"
            app:srcCompat="@drawable/cash" />

        <TextView
            android:id="@+id/nameTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignBottom="@+id/imageView"
            android:layout_alignParentStart="true"
            android:maxLines="1"
            android:padding="5dp"
            android:text="Item"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/subtitleTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:background="@drawable/top_subtitle"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLength="5"
            android:maxLines="1"
            android:paddingStart="10dp"
            android:paddingTop="5dp"
            android:paddingEnd="5dp"
            android:paddingBottom="10dp"
            android:text="300K"
            android:textAlignment="center"
            android:textAppearance="@style/TextAppearance.AppCompat.Body1"

            android:textColor="@color/white"
            android:textSize="12sp"
            android:textStyle="bold|italic" />

    </RelativeLayout>

</androidx.cardview.widget.CardView>