<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="meal"
            type="com.hqt.data.model.AddOnInfo" />
        <variable
            name="total"
            type="Integer" />
        <import type="com.hqt.datvemaybay.Common" />
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:padding="5dp"
        android:clickable="true"
        android:focusable="true"
        android:layout_height="wrap_content">

        <androidx.cardview.widget.CardView
            android:background="@drawable/corner_full"
            android:layout_width="match_parent"
            app:cardCornerRadius="5dp"
            app:cardElevation="5dp"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <com.makeramen.roundedimageview.RoundedImageView
                    android:layout_gravity="center_horizontal|center_vertical"
                    android:scaleType="centerInside"
                    android:layout_width="100dp"
                    bind:imageUrl="@{meal.image}"
                    android:layout_height="match_parent" />

                <LinearLayout
                    android:padding="10dp"
                    android:layoutDirection="rtl"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:orientation="vertical">


                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="fill_parent"
                            android:gravity="center_vertical|end">


                            <LinearLayout
                                android:id="@+id/btn_in_add_on"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@drawable/border_full"
                                android:padding="5dp">

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:layout_width="18dp"
                                    android:layout_height="18dp"
                                    app:iiv_color="@color/white"
                                    app:iiv_icon="gmd_add" />
                            </LinearLayout>

                            <TextView
                                android:textColor="@color/textDark"
                                android:id="@+id/sheet_infant_number"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{total.toString()}"
                                android:textSize="20sp"
                                android:paddingRight="20dp"
                                android:paddingLeft="20dp" />

                            <LinearLayout
                                android:id="@+id/btn_de_add_on"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@drawable/border_full"
                                android:padding="5dp">

                                <com.mikepenz.iconics.view.IconicsImageView

                                    android:layout_width="18dp"
                                    android:layout_height="18dp"
                                    app:iiv_color="@color/white"
                                    app:iiv_icon="gmd_remove" />
                            </LinearLayout>
                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/headerBackground"
                        android:background="@drawable/background_selector"
                        android:padding="2dp"
                        android:layout_width="fill_parent"
                        android:layout_gravity="start"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/txtBagValue"
                            android:layout_width="fill_parent"
                            android:textStyle="bold"
                            android:layout_height="wrap_content"
                            android:text="@{meal.text}"
                            tools:text="Tên món ăn dsf "
                            android:textColor="@color/textDark" />

                        <TextView
                            tools:text="Mô tả"
                            android:paddingTop="2dp"
                            android:paddingBottom="2dp"
                            android:textSize="12sp"
                            android:id="@+id/txtBagPrice"
                            android:layout_width="fill_parent"
                            android:maxLines="2"
                            android:layout_height="wrap_content"
                            android:text="@{meal.description}" />

                        <TextView
                            tools:text="Giá"
                            android:layout_width="fill_parent"
                            android:gravity="start"
                            android:layout_height="wrap_content"
                            android:textStyle="bold"
                            android:textColor="#da281c"
                            android:text="@{Common.dinhDangTien(meal.price)}" />

                        <TextView
                            android:visibility="gone"
                            android:id="@+id/key"
                            android:layout_width="fill_parent"
                            android:gravity="start"
                            android:layout_height="wrap_content"
                            android:textColor="@color/textDark"
                            android:text="@{meal.value}" />

                        <TextView
                            android:id="@+id/pos"
                            android:layout_width="wrap_content"
                            android:visibility="gone"
                            android:layout_height="wrap_content" />
                    </LinearLayout>
                </LinearLayout>


            </LinearLayout>
        </androidx.cardview.widget.CardView>
    </FrameLayout>
</layout>