<?xml version="1.0" encoding="UTF-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="isShowSeatLineTop"
            type="Boolean" />

        <variable
            name="isShowSeatLineBottom"
            type="Boolean" />

        <variable
            name="isRoomSeat"
            type="Boolean" />

        <import type="com.hqt.datvemaybay.Common" />

        <import type="android.view.View" />

    </data>

    <LinearLayout
        android:id="@+id/seat_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical|center_horizontal"
        android:orientation="vertical"
        android:visibility="visible">


        <androidx.cardview.widget.CardView
            android:id="@+id/seat_group_view"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_margin="2dp"
            android:gravity="center_horizontal|center_vertical"
            android:visibility="visible"
            app:cardBackgroundColor="@color/primary"
            app:cardCornerRadius="5dp"
            app:cardElevation="0dp">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="40dp">

                <TextView
                    android:layout_centerInParent="true"
                    android:id="@+id/textTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical|center_horizontal"
                    android:text="AB"
                    android:textColor="@color/white" />

                <View
                    android:visibility="@{isShowSeatLineTop ? View.VISIBLE : View.GONE}"
                    android:layout_alignParentTop="true"
                    android:layout_width="match_parent"
                    android:background="@drawable/seat_border_full"
                    android:layout_height="5dp" />

                <View
                    android:visibility="@{isShowSeatLineTop ? View.VISIBLE : View.GONE}"
                    android:layout_alignParentStart="true"
                    android:layout_alignParentTop="true"
                    android:layout_width="5dp"
                    android:background="@drawable/seat_border_full"
                    android:layout_height="20dp" />

                <View

                    android:visibility="@{isShowSeatLineTop ? View.VISIBLE : View.GONE}"
                    android:layout_alignParentEnd="true"
                    android:layout_alignParentTop="true"
                    android:layout_width="5dp"
                    android:background="@drawable/seat_border_full"
                    android:layout_height="20dp" />

                <View
                    android:visibility="@{isShowSeatLineBottom ? View.VISIBLE : View.GONE}"
                    android:layout_alignParentBottom="true"
                    android:layout_width="match_parent"
                    android:background="@drawable/seat_border_full"
                    android:layout_height="5dp" />

                <View
                    android:visibility="@{isShowSeatLineBottom ? View.VISIBLE : View.GONE}"
                    android:layout_alignParentStart="true"
                    android:layout_alignParentBottom="true"
                    android:layout_width="5dp"
                    android:background="@drawable/seat_border_full"
                    android:layout_height="20dp" />

                <View
                    android:visibility="@{isShowSeatLineBottom ? View.VISIBLE : View.GONE}"
                    android:layout_alignParentEnd="true"
                    android:layout_alignParentBottom="true"
                    android:layout_width="5dp"
                    android:background="@drawable/seat_border_full"
                    android:layout_height="20dp" />

                <View
                    android:visibility="@{isRoomSeat ? View.VISIBLE : View.GONE}"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_width="10dp"
                    android:layout_marginEnd="10dp"
                    android:background="@drawable/seat_border_full"
                    android:layout_height="30dp" />
            </RelativeLayout>

        </androidx.cardview.widget.CardView>


        <View
            android:id="@+id/space"
            android:layout_width="match_parent"
            android:layout_height="20dp"
            android:background="@color/fui_transparent"
            android:visibility="gone" />

        <TextView
            android:id="@+id/seat_title"
            android:layout_width="match_parent"
            android:layout_height="20dp"
            android:background="@color/red"
            android:text="xxxx"
            android:textAlignment="center"
            android:visibility="gone" />


    </LinearLayout>
</layout>