<?xml version="1.0" encoding="UTF-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.hqt.datvemaybay.Common" />

    </data>

    <LinearLayout
        android:orientation="vertical"
        android:id="@+id/seat_view"
        android:visibility="visible"
        android:layout_width="match_parent"
        android:gravity="center_vertical|center_horizontal"
        android:layout_height="wrap_content">

        <androidx.cardview.widget.CardView
            android:id="@+id/seat_group_view"
            android:visibility="gone"
            android:layout_margin="5dp"
            app:cardCornerRadius="5dp"
            app:cardElevation="0dp"
            app:cardBackgroundColor="@color/primary"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:gravity="center_horizontal|center_vertical">

            <TextView
                android:layout_gravity="center_vertical|center_horizontal"
                android:id="@+id/textTitle"
                android:textColor="@color/white"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="AB" />

        </androidx.cardview.widget.CardView>

        <LinearLayout
            android:id="@+id/seat_title_view"
            android:visibility="visible"
            android:layout_margin="2dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/row_number"
                android:textStyle="bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="ABx" />

        </LinearLayout>

    </LinearLayout>
</layout>