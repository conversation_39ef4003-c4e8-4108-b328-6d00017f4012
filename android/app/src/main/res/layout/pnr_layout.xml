<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/gbgray"
    android:id="@+id/coordinatorLayout"
    android:animateLayoutChanges="true"
    tools:context="com.hqt.datvemaybay.PnrActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar"
        android:visibility="visible"
        android:layout_width="match_parent"
        android:background="@color/primary_dark"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true"
        app:layout_constraintTop_toTopOf="parent"
        app:elevation="5dp"
        android:theme="@style/AppTheme.AppBarOverlay">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/toolbar_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:title="12bay.vn"
            android:background="@color/primary"
            android:fitsSystemWindows="true"
            app:titleEnabled="false"
            app:layout_scrollFlags="enterAlwaysCollapsed">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                app:elevation="0dp"
                app:layout_collapseMode="pin"
                app:layout_scrollFlags="scroll|enterAlways"
                app:popupTheme="@style/AppTheme.PopupOverlay" />

        </com.google.android.material.appbar.CollapsingToolbarLayout>


    </com.google.android.material.appbar.AppBarLayout>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="visible"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <com.facebook.shimmer.ShimmerFrameLayout
                android:id="@+id/shimmer_view_container"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:orientation="vertical"
                android:visibility="gone"
                app:shimmer_duration="800">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <include layout="@layout/placeholder_item_pnr_view" />

                    <include layout="@layout/placeholder_item_pnr_view" />

                    <include layout="@layout/placeholder_item_pnr_view" />

                    <include layout="@layout/placeholder_item_pnr_view" />

                    <include layout="@layout/placeholder_item_pnr_view" />

                </LinearLayout>
            </com.facebook.shimmer.ShimmerFrameLayout>

            <include layout="@layout/head_status_layout" />

            <include layout="@layout/pnr_layout_content_scrolling" />
        </LinearLayout>
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <View
        android:id="@+id/behavior_dependency"
        android:layout_width="1dp"
        android:layout_height="1dp"
        android:layout_marginBottom="30dp"
        android:layout_gravity="bottom"
        app:layout_anchor="@id/got_it"
        app:layout_anchorGravity="bottom" />

    <FrameLayout
        android:visibility="visible"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:behavior_hideable="true"
        app:layout_behavior="com.hqt.util.helper.OutOfScreenBottomSheetBehavior">

        <LinearLayout
            android:id="@+id/bottom_sheet"
            android:orientation="vertical"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_gravity="center_horizontal|bottom"
            android:background="#FFFFFF">

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnBookVe"
                android:background="@drawable/button_gradient_noborder"
                android:textColor="#FFFFFF"
                android:layout_width="match_parent"
                android:textAlignment="center"
                android:textSize="16sp"
                android:visibility="gone"
                android:layout_height="wrap_content"
                app:textAllCaps="true"
                android:textAllCaps="true"
                android:text="LIÊN LẠC HỖ TRỢ" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnPaynow"
                android:background="@drawable/button_gradient_noborder"
                android:textColor="#FFFFFF"
                android:layout_width="match_parent"
                android:textAlignment="center"
                android:textSize="16sp"
                android:visibility="gone"
                android:layout_height="wrap_content"
                app:textAllCaps="true"
                android:textAllCaps="true"
                android:text="NHẤN ĐỂ THANH TOÁN" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:weightSum="2"
                android:visibility="gone"
                android:id="@+id/addonlayout"
                android:orientation="horizontal">

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btnCheckve"
                    android:backgroundTint="@color/primary"
                    android:textColor="#FFFFFF"
                    style="@style/MyApp.Button.Big"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:textAlignment="center"
                    android:textSize="16sp"
                    android:layout_height="wrap_content"
                    android:text="{faw_check_square_o} Kiểm Tra Vé" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btnCheckin"
                    android:backgroundTint="@color/primary"
                    android:textColor="#FFFFFF"
                    style="@style/MyApp.Button.Big"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:textAlignment="center"
                    android:textSize="16sp"
                    android:visibility="visible"
                    android:layout_height="wrap_content"
                    android:text="{faw_sign_in} Checkin Online" />

            </LinearLayout>
        </LinearLayout>
    </FrameLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>


