<?xml version="1.0" encoding="UTF-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="train"
            type="com.hqt.data.model.Train" />

        <variable
            name="viewHolder"
            type="com.hqt.view.adapter.TrainAdapter.ViewHolder" />

        <import type="com.hqt.datvemaybay.Common" />

        <variable
            name="handler"
            type="com.hqt.view.ui.train.TrainSelectHanlder" />
    </data>

    <androidx.cardview.widget.CardView
        android:id="@+id/card_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="5dp"
        app:cardElevation="0dp"
        android:layout_marginBottom="@dimen/cardMarginVertical"
        android:layout_marginLeft="@dimen/cardMarginHorizontal"
        android:layout_marginRight="@dimen/cardMarginHorizontal"
        android:layout_marginTop="@dimen/cardMarginVertical">

        <LinearLayout
            android:background="@drawable/corner_full"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:visibility="visible"
                android:layout_margin="5dp"
                android:id="@+id/layoutTrainView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="5dp"
                android:layout_gravity="center_horizontal"
                android:orientation="horizontal">

                <ImageView
                    android:layout_gravity="center_horizontal"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:background="@drawable/ic_transportation"
                    android:contentDescription="@string/Description" />

                <TextView
                    android:layout_gravity="center_vertical"
                    android:paddingStart="5dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/textDark"
                    android:text="@{train.getTripName()}"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:gravity="end"
                    android:paddingEnd="5dp"
                    android:text="@{Common.getDayOfWeek(train.departureDateTime) + `, `+Common.dateToString(train.departureDateTime ,`dd/MM` )}" />
            </LinearLayout>

            <LinearLayout
                android:padding="5dp"
                android:onClick="@{() -> handler.onSelectTrain(train)}"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal">

                <View
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:layout_marginRight="5dp"
                    android:layout_marginLeft="5dp"
                    android:background="@drawable/gradientdiv" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.mikepenz.iconics.view.IconicsImageView
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:padding="5dp"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            app:iiv_color="@color/stt_gray"
                            app:iiv_icon="faw-clock" />

                        <TextView
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:padding="5dp"
                            android:id="@+id/txtGioDi"
                            style="@style/Text"
                            android:textStyle="bold"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{Common.dateToString(train.departureDateTime ,`HH:mm` )  }" />

                        <LinearLayout
                            android:gravity="right"
                            android:layout_width="fill_parent"
                            android:layout_height="match_parent">

                            <ImageView
                                android:layout_gravity="center_horizontal|center_vertical"
                                android:padding="5dp"
                                android:layout_width="50dp"
                                android:layout_height="25dp"
                                android:scaleType="centerCrop"
                                android:background="@drawable/logo_vr" />

                            <TextView
                                android:padding="5dp"
                                android:textColor="@color/textDark"
                                android:textStyle="bold"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Tàu" />

                            <TextView
                                android:textStyle="bold"
                                android:paddingRight="5dp"
                                android:paddingTop="5dp"
                                android:paddingBottom="5dp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/textDark"
                                android:text="@{train.trainNumber}" />
                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.mikepenz.iconics.view.IconicsImageView
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:padding="5dp"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            app:iiv_color="@color/stt_gray"
                            app:iiv_icon="faw-clock" />

                        <TextView
                            style="@style/Text"
                            android:textStyle="bold"
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:padding="5dp"
                            android:id="@+id/txtGioDen"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{Common.dateToString(train.arrivalDateTime,`HH:mm` ) }" />

                        <TextView
                            android:padding="5dp"
                            android:id="@+id/txtNgayDen"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/primary_dark"
                            android:text="@{Common.dateToString(train.arrivalDateTime ,`dd/MM` ) }" />

                        <LinearLayout
                            android:gravity="right"
                            android:layout_width="fill_parent"
                            android:layout_height="match_parent">

                            <TextView
                                android:visibility="gone"
                                android:layout_gravity="center_horizontal|center_vertical"
                                android:id="@+id/txtKm"
                                android:padding="5dp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{train.getTripLength()}" />

                            <TextView
                                android:layout_gravity="center_horizontal|center_vertical"
                                android:id="@+id/txtThoiGianDi"
                                android:padding="5dp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/stt_gray"

                                android:text="@{train.getDuration()}" />

                            <TextView
                                bind:isBold="@{train.getRemainSeat() > 0 ? true : false}"
                                android:layout_gravity="center_horizontal|center_vertical"
                                android:padding="5dp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/stt_gray"
                                android:text="@{train.getRemainSeat() + ``}" />

                            <TextView
                                bind:isBold="@{train.getRemainSeat() > 0 ? true : false}"
                                android:layout_gravity="center_horizontal|center_vertical"
                                android:paddingRight="5dp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/stt_gray"
                                android:text="Chỗ trống" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>
</layout>