<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:bind="http://schemas.android.com/apk/res-auto"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/title_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:padding="10dp"
                android:id="@+id/title"
                android:textSize="16sp"
                android:textStyle="bold"
                android:singleLine="true"
                android:ellipsize="marquee"
                android:textColor="@color/black"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="Title" />

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="match_parent"
                android:gravity="center_vertical|end"
                android:padding="10dp">

                <com.mikepenz.iconics.view.IconicsImageView
                    android:layout_width="16dp"
                    android:layout_height="10dp"
                    app:iiv_color="@color/primary"
                    app:iiv_icon="gmd_keyboard_arrow_right" />
            </LinearLayout>
        </LinearLayout>


        <HorizontalScrollView
            android:paddingStart="10dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:id="@+id/sub_category">


            </LinearLayout>

        </HorizontalScrollView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">


            <com.github.rubensousa.gravitysnaphelper.OrientationAwareRecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:clipToPadding="false"
                android:padding="5dp" />


        </LinearLayout>


    </LinearLayout>
</layout>