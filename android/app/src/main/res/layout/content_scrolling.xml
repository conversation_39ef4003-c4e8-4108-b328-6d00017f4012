<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        tools:context="com.hqt.view.ui.HomeActivity"
        app:behavior_overlapTop="60dp"
        tools:ignore="MissingPrefix"
        tools:showIn="@layout/activity_scrolling">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="0dp"
                android:padding="4dp"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingTop="5dp"
                    android:paddingBottom="0dp"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:gravity="center_horizontal"
                        android:layout_height="wrap_content">

                        <androidx.cardview.widget.CardView
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="5dp"
                            android:layout_marginLeft="5dp"
                            android:layout_marginRight="5dp"
                            android:background="@color/white"
                            android:focusable="true"
                            app:cardMaxElevation="0dp"
                            android:foreground="?attr/selectableItemBackground"
                            app:cardCornerRadius="10dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:weightSum="12"
                                    android:padding="5dp"
                                    android:gravity="center_horizontal">

                                    <LinearLayout
                                        android:id="@+id/btnSearch"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_margin="5dp"
                                        android:layout_weight="3"
                                        android:gravity="center"
                                        android:orientation="vertical">

                                        <LinearLayout
                                            android:clickable="false"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:gravity="center">

                                            <ImageButton
                                                android:clickable="false"
                                                android:id="@+id/Search"
                                                android:layout_width="50dp"
                                                android:layout_height="50dp"
                                                android:background="@drawable/round_button"
                                                android:gravity="center_vertical|center_horizontal"
                                                android:textColor="#fff"
                                                android:textSize="24sp"
                                                app:srcCompat="@drawable/ic_logo_no_free"
                                                android:contentDescription="@string/app_name" />
                                        </LinearLayout>

                                        <LinearLayout
                                            android:clickable="false"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:gravity="center"
                                            android:paddingTop="5dp">

                                            <TextView
                                                android:lines="1"
                                                android:clickable="false"
                                                android:textSize="12sp"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:text="Vé M/bay"
                                                android:textColor="#126aa8" />
                                        </LinearLayout>
                                    </LinearLayout>

                                    <View
                                        android:layout_width="1dp"
                                        android:layout_height="match_parent"
                                        android:layout_marginTop="5dp"
                                        android:layout_marginBottom="5dp"
                                        android:background="@drawable/gradientdiv" />

                                    <LinearLayout
                                        android:id="@+id/btnPromo"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_margin="5dp"
                                        android:layout_weight="3"
                                        android:gravity="center"
                                        android:orientation="vertical">

                                        <LinearLayout
                                            android:clickable="false"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:gravity="center">

                                            <ImageButton
                                                android:clickable="false"
                                                android:id="@+id/Promo"
                                                android:layout_width="50dp"
                                                android:layout_height="50dp"
                                                android:background="@drawable/round_button"
                                                android:gravity="center_vertical|center_horizontal"
                                                app:srcCompat="@drawable/ic_action_cheap_outline"
                                                android:textColor="#fff"
                                                android:textSize="24sp" />
                                        </LinearLayout>

                                        <LinearLayout
                                            android:clickable="false"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:gravity="center"
                                            android:paddingTop="5dp">

                                            <TextView
                                                android:clickable="false"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:textSize="12sp"
                                                android:text="Săn vé"
                                                android:textColor="#126aa8" />
                                        </LinearLayout>
                                    </LinearLayout>

                                    <View
                                        android:layout_width="1dp"
                                        android:layout_height="match_parent"
                                        android:layout_marginTop="5dp"
                                        android:layout_marginBottom="5dp"
                                        android:background="@drawable/gradientdiv" />

                                    <LinearLayout
                                        android:id="@+id/btnPaymendt"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_margin="5dp"
                                        android:layout_weight="3"
                                        android:gravity="center"
                                        android:orientation="vertical">

                                        <LinearLayout
                                            android:id="@+id/btnTrainLN"
                                            android:layout_width="wrap_content"
                                            android:clickable="false"
                                            android:layout_height="wrap_content"
                                            android:gravity="center">

                                            <ImageButton
                                                android:clickable="false"
                                                android:id="@+id/btnTrain"
                                                android:layout_width="50dp"
                                                android:layout_height="50dp"
                                                android:background="@drawable/round_button"
                                                android:gravity="center_vertical|center_horizontal"
                                                app:srcCompat="@drawable/icon_train"
                                                android:textColor="#fff"
                                                android:textSize="24sp" />
                                        </LinearLayout>

                                        <LinearLayout
                                            android:clickable="false"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:gravity="center"
                                            android:paddingTop="5dp">

                                            <TextView
                                                android:layout_width="wrap_content"
                                                android:layout_height="21dp"
                                                android:clickable="false"
                                                android:text="Vé tàu"
                                                android:textColor="#126aa8"
                                                android:textSize="12sp" />
                                        </LinearLayout>
                                    </LinearLayout>

                                    <View
                                        android:layout_width="1dp"
                                        android:layout_height="match_parent"
                                        android:layout_marginTop="5dp"
                                        android:layout_marginBottom="5dp"
                                        android:background="@drawable/gradientdiv" />

                                    <LinearLayout
                                        android:id="@+id/btnBus"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_margin="5dp"
                                        android:layout_weight="3"
                                        android:gravity="center"
                                        android:orientation="vertical">

                                        <LinearLayout
                                            android:layout_width="wrap_content"
                                            android:clickable="false"
                                            android:layout_height="wrap_content"
                                            android:gravity="center">

                                            <ImageButton
                                                android:clickable="false"
                                                android:layout_width="50dp"
                                                android:layout_height="50dp"
                                                android:background="@drawable/round_button"
                                                android:gravity="center_vertical|center_horizontal"
                                                app:srcCompat="@drawable/ic_bus_white"
                                                android:textColor="#fff"
                                                android:textSize="24sp" />
                                        </LinearLayout>

                                        <LinearLayout
                                            android:clickable="false"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:gravity="center"
                                            android:paddingTop="5dp">

                                            <TextView
                                                android:clickable="false"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:textSize="12sp"
                                                android:text="Vé xe"
                                                android:textColor="#126aa8" />
                                        </LinearLayout>
                                    </LinearLayout>

                                    <LinearLayout
                                        android:visibility="gone"
                                        android:id="@+id/btnRewardLayout"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_margin="5dp"
                                        android:layout_weight="3"
                                        android:gravity="center"
                                        android:orientation="vertical">

                                        <LinearLayout
                                            android:layout_width="wrap_content"
                                            android:clickable="false"
                                            android:layout_height="wrap_content"
                                            android:gravity="center">

                                            <com.mikepenz.iconics.view.IconicsButton
                                                android:clickable="false"
                                                android:layout_width="50dp"
                                                android:layout_height="50dp"
                                                android:background="@drawable/round_button"
                                                android:gravity="center_vertical|center_horizontal"
                                                android:text="{gmd_loyalty}"
                                                android:textColor="#fff"
                                                android:textSize="24sp" />
                                        </LinearLayout>

                                        <LinearLayout
                                            android:clickable="false"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:gravity="center"
                                            android:paddingTop="5dp">

                                            <TextView
                                                android:clickable="false"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:textSize="12sp"
                                                android:text="Ưu đãi"
                                                android:textColor="#126aa8" />
                                        </LinearLayout>
                                    </LinearLayout>

                                </LinearLayout>

                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="1dp"
                                    android:background="@drawable/gradientdiv_vertical" />

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:weightSum="8"
                                    android:padding="5dp"
                                    android:gravity="center_horizontal">

                                    <LinearLayout
                                        android:id="@+id/btnTour"

                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_margin="5dp"
                                        android:layout_weight="2"
                                        android:gravity="center"
                                        android:orientation="vertical">

                                        <LinearLayout
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:gravity="center">

                                            <ImageView
                                                android:layout_width="30dp"
                                                android:layout_height="30dp"
                                                android:background="@drawable/ic_tour" />
                                        </LinearLayout>

                                        <LinearLayout

                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:gravity="center"
                                            android:paddingTop="5dp">

                                            <TextView
                                                android:textSize="12sp"
                                                android:layout_width="match_parent"
                                                android:layout_height="wrap_content"
                                                android:lines="2"
                                                android:textAlignment="center"
                                                android:textColor="#126aa8"
                                                android:text="Tour \ndu lịch" />
                                        </LinearLayout>
                                    </LinearLayout>

                                    <View
                                        android:layout_width="1dp"
                                        android:layout_height="match_parent"
                                        android:layout_marginTop="5dp"
                                        android:layout_marginBottom="5dp"
                                        android:background="@drawable/gradientdiv" />

                                    <LinearLayout
                                        android:id="@+id/btnFlightWatches"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_margin="5dp"
                                        android:layout_weight="2"
                                        android:gravity="center"
                                        android:orientation="vertical">

                                        <LinearLayout
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:gravity="center">

                                            <ImageView
                                                android:layout_width="30dp"
                                                android:layout_height="30dp"
                                                android:background="@drawable/ic_flight_watched_btn" />
                                        </LinearLayout>

                                        <LinearLayout

                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:gravity="center"
                                            android:paddingTop="5dp">

                                            <TextView
                                                android:textAlignment="center"
                                                android:textSize="12sp"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:lines="2"
                                                android:textColor="#126aa8"
                                                android:text="Săn vé \ntự động" />
                                        </LinearLayout>
                                    </LinearLayout>

                                    <View
                                        android:layout_width="1dp"
                                        android:layout_height="match_parent"
                                        android:layout_marginTop="5dp"
                                        android:layout_marginBottom="5dp"
                                        android:background="@drawable/gradientdiv" />

                                    <LinearLayout
                                        android:focusable="true"
                                        android:id="@+id/btnCheckin"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_margin="5dp"
                                        android:layout_weight="2"
                                        android:gravity="center"
                                        android:orientation="vertical">

                                        <LinearLayout
                                            android:layout_width="wrap_content"
                                            android:clickable="false"
                                            android:layout_height="wrap_content"
                                            android:gravity="center">

                                            <ImageView
                                                android:layout_width="30dp"
                                                android:layout_height="30dp"
                                                android:background="@drawable/ic_checkin_online" />
                                        </LinearLayout>

                                        <LinearLayout
                                            android:clickable="false"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:gravity="center"
                                            android:paddingTop="5dp">

                                            <TextView
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:clickable="false"
                                                android:textAlignment="center"
                                                android:text="Checkin\n online"
                                                android:textColor="#126aa8"
                                                android:textSize="12sp" />
                                        </LinearLayout>
                                    </LinearLayout>

                                    <View
                                        android:visibility="visible"
                                        android:layout_width="1dp"
                                        android:layout_height="match_parent"
                                        android:layout_marginTop="5dp"
                                        android:layout_marginBottom="5dp"
                                        android:background="@drawable/gradientdiv" />

                                    <LinearLayout
                                        android:id="@+id/btnStatus"
                                        android:visibility="visible"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_margin="5dp"
                                        android:layout_weight="2"
                                        android:gravity="center"
                                        android:orientation="vertical">

                                        <LinearLayout
                                            android:clickable="false"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:gravity="center">

                                            <ImageView
                                                android:layout_width="30dp"
                                                android:layout_height="30dp"
                                                android:background="@drawable/ic_flight_status" />
                                        </LinearLayout>

                                        <LinearLayout
                                            android:clickable="false"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:gravity="center"
                                            android:paddingTop="5dp">

                                            <TextView
                                                android:textAlignment="center"
                                                android:clickable="false"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:textSize="12sp"
                                                android:lines="2"
                                                android:textColor="#126aa8"
                                                android:text="Theo dõi chuyến bay" />
                                        </LinearLayout>
                                    </LinearLayout>

                                    <View
                                        android:visibility="visible"
                                        android:layout_width="1dp"
                                        android:layout_height="match_parent"
                                        android:layout_marginTop="5dp"
                                        android:layout_marginBottom="5dp"
                                        android:background="@drawable/gradientdiv" />


                                    <View
                                        android:visibility="gone"
                                        android:layout_width="1dp"
                                        android:layout_height="match_parent"
                                        android:layout_marginTop="5dp"
                                        android:layout_marginBottom="5dp"
                                        android:background="@drawable/gradientdiv" />

                                    <LinearLayout
                                        android:visibility="gone"
                                        android:id="@+id/btnPayment"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_margin="5dp"
                                        android:layout_weight="3"
                                        android:gravity="center"
                                        android:orientation="vertical">

                                        <LinearLayout
                                            android:layout_width="wrap_content"
                                            android:clickable="false"
                                            android:layout_height="wrap_content"
                                            android:gravity="center">

                                            <ImageView
                                                android:layout_width="30dp"
                                                android:layout_height="30dp"
                                                android:background="@drawable/ic_money" />
                                        </LinearLayout>

                                        <LinearLayout
                                            android:clickable="false"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:gravity="center"
                                            android:paddingTop="5dp">

                                            <TextView
                                                android:clickable="false"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:textSize="12sp"
                                                android:lines="1"
                                                android:textColor="@color/gradient_start"
                                                android:text="Thanh toán" />
                                        </LinearLayout>
                                    </LinearLayout>


                                </LinearLayout>
                            </LinearLayout>

                        </androidx.cardview.widget.CardView>


                    </LinearLayout>


                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp"
                    android:textAlignment="center">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="@color/white"
                        android:text="TIỆN ÍCH"
                        android:gravity="center_horizontal" />
                </LinearLayout>

                <LinearLayout
                    android:layout_marginRight="5dp"
                    android:layout_marginLeft="5dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:weightSum="10"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_weight="5"
                        android:gravity="center_horizontal"
                        android:layout_height="wrap_content">

                        <androidx.cardview.widget.CardView
                            android:id="@+id/blog"
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginRight="5dp"
                            android:layout_marginBottom="5dp"
                            android:layout_marginTop="5dp"
                            android:layout_marginLeft="10dp"
                            android:background="@color/white"
                            app:cardCornerRadius="2dp"
                            app:cardElevation="2dp"
                            android:clickable="true"
                            android:foreground="?attr/selectableItemBackground"
                            android:layout_margin="5dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:gravity="center">

                                    <com.mikepenz.iconics.view.IconicsImageView
                                        android:layout_width="48dp"
                                        android:layout_height="48dp"
                                        android:padding="5dp"
                                        android:layout_margin="5dp"
                                        app:iiv_color="#1279c1"
                                        app:iiv_icon="gmd_explore" />
                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="fill_parent"
                                    android:gravity="center">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textColor="#126aa8"
                                        android:text="Khám phá" />
                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="fill_parent"
                                    android:layout_height="fill_parent"
                                    android:gravity="top|right">

                                    <com.mikepenz.iconics.view.IconicsTextView
                                        android:textColor="#ff0206"
                                        android:padding="2dp"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="{gmd-favorite}" />
                                </LinearLayout>
                            </LinearLayout>

                        </androidx.cardview.widget.CardView>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_weight="5"
                        android:gravity="center_horizontal"
                        android:layout_height="wrap_content">

                        <androidx.cardview.widget.CardView
                            android:id="@+id/checkVe"
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="5dp"
                            android:layout_marginBottom="5dp"
                            android:layout_marginTop="5dp"
                            android:layout_marginRight="10dp"
                            app:cardCornerRadius="2dp"
                            app:cardElevation="2dp"
                            android:clickable="true"
                            android:foreground="?attr/selectableItemBackground"
                            android:background="@color/white"
                            android:layout_margin="5dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:gravity="center">

                                    <com.mikepenz.iconics.view.IconicsImageView
                                        android:layout_width="48dp"
                                        android:layout_height="48dp"
                                        android:padding="5dp"
                                        android:layout_margin="5dp"
                                        app:iiv_color="#1279c1"
                                        app:iiv_icon="gmd_contact_mail" />
                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="fill_parent"
                                    android:gravity="center">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textColor="#126aa8"
                                        android:text="Kiểm tra vé" />
                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="fill_parent"
                                    android:layout_height="fill_parent"
                                    android:gravity="top|right">

                                    <com.mikepenz.iconics.view.IconicsTextView
                                        android:textColor="#ff0206"
                                        android:padding="2dp"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="{gmd-star}" />
                                </LinearLayout>
                            </LinearLayout>
                        </androidx.cardview.widget.CardView>
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:weightSum="10"
                    android:layout_marginRight="5dp"
                    android:layout_marginLeft="5dp"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_weight="5"
                        android:gravity="center_horizontal"
                        android:layout_height="wrap_content">

                        <androidx.cardview.widget.CardView
                            android:id="@+id/history"
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginRight="5dp"
                            android:layout_marginBottom="5dp"
                            android:layout_marginTop="5dp"
                            android:layout_marginLeft="10dp"
                            android:background="@color/white"
                            app:cardCornerRadius="2dp"
                            app:cardElevation="3dp"
                            android:clickable="true"
                            android:foreground="?attr/selectableItemBackground"
                            android:layout_margin="5dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:gravity="center">

                                    <com.mikepenz.iconics.view.IconicsImageView
                                        android:layout_width="48dp"
                                        android:layout_height="48dp"
                                        android:padding="5dp"
                                        android:layout_margin="5dp"
                                        app:iiv_color="#e44242"
                                        app:iiv_icon="gmd_history" />
                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="fill_parent"
                                    android:gravity="center">

                                    <TextView
                                        android:textColor="#126aa8"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Lịch sử" />
                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="fill_parent"
                                    android:layout_height="fill_parent"
                                    android:gravity="top|right">

                                    <com.mikepenz.iconics.view.IconicsTextView
                                        android:textColor="#ff0206"
                                        android:padding="2dp"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="{gmd-star}" />
                                </LinearLayout>
                            </LinearLayout>

                        </androidx.cardview.widget.CardView>


                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_weight="5"
                        android:gravity="center_horizontal"
                        android:layout_height="wrap_content">

                        <androidx.cardview.widget.CardView
                            android:id="@+id/checkin"
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="5dp"
                            android:layout_marginBottom="5dp"
                            android:layout_marginTop="5dp"
                            android:layout_marginRight="10dp"
                            app:cardCornerRadius="2dp"
                            app:cardElevation="3dp"
                            android:clickable="true"
                            android:foreground="?attr/selectableItemBackground"
                            android:background="@color/white"
                            android:layout_margin="5dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:gravity="center">

                                    <com.mikepenz.iconics.view.IconicsImageView
                                        android:layout_width="48dp"
                                        android:layout_height="48dp"
                                        android:padding="5dp"
                                        android:layout_margin="5dp"
                                        app:iiv_color="#e44242"
                                        app:iiv_icon="gmd_spellcheck" />
                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="fill_parent"
                                    android:gravity="center">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textColor="#126aa8"
                                        android:text="Checkin Online"
                                        android:id="@+id/textView" />
                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="fill_parent"
                                    android:layout_height="fill_parent"
                                    android:gravity="top|right">

                                    <com.mikepenz.iconics.view.IconicsTextView
                                        android:textColor="#ff0206"
                                        android:padding="2dp"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="{gmd-star}" />
                                </LinearLayout>
                            </LinearLayout>
                        </androidx.cardview.widget.CardView>
                    </LinearLayout>
                </LinearLayout>

            </LinearLayout>

            <com.facebook.shimmer.ShimmerFrameLayout
                android:id="@+id/shimmer_view_container"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:orientation="vertical"
                android:visibility="visible"
                app:shimmer_duration="800">

                <!-- Adding 3 rows of placeholders -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <include layout="@layout/placeholder_item_post" />

                    <include layout="@layout/placeholder_item_post" />

                    <include layout="@layout/placeholder_item_post" />

                    <include layout="@layout/placeholder_item_post" />

                </LinearLayout>

            </com.facebook.shimmer.ShimmerFrameLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_behavior="@string/appbar_scrolling_view_behavior" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingBottom="10dp"
                android:gravity="center_horizontal|center_vertical"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/txtHotline"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Tổng đài CSKH " />

                    <com.mikepenz.iconics.view.IconicsTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="12sp"
                        android:text="{faw_headset}" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textStyle="bold"
                        android:text=" 19002642" />

                </LinearLayout>
            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>
</layout>