<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <FrameLayout
        android:layout_width="wrap_content"
        android:padding="5dp"
        android:clickable="true"
        android:focusable="true"
        android:layout_height="wrap_content">

        <androidx.cardview.widget.CardView
            android:background="@drawable/corner_full"
            android:layout_width="wrap_content"
            app:cardCornerRadius="5dp"
            app:cardElevation="5dp"
            android:layout_height="wrap_content">

            <LinearLayout
                android:id="@+id/bagViewBg"
                android:background="@drawable/background_selector"
                android:padding="10dp"
                android:layout_width="100dp"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/txtBagValue"
                    android:layout_width="fill_parent"
                    android:gravity="center_horizontal"
                    android:layout_height="wrap_content"
                    android:text="20KG"
                    android:textColor="@color/textDark" />

                <TextView
                    android:id="@+id/txtBagPrice"
                    android:layout_width="fill_parent"
                    android:gravity="center_horizontal"
                    android:layout_height="wrap_content"
                    android:textColor="@color/textDark"
                    android:text="200.000đ" />

                <TextView
                    android:id="@+id/pos"
                    android:layout_width="wrap_content"
                    android:visibility="gone"
                    android:layout_height="wrap_content" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>
    </FrameLayout>
</layout>