<?xml version="1.0" encoding="UTF-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data></data>

    <LinearLayout
        android:orientation="vertical"
        android:id="@+id/seat_view"
        android:visibility="visible"
        android:layout_width="match_parent"
        android:layout_margin="5dp"
        android:gravity="center_vertical|center_horizontal"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_margin="5dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            tools:ignore="UselessParent">

            <TextView
                android:id="@+id/txt_title"
                android:layout_gravity="center"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="5dp"

                android:text="               " />


            <com.google.android.flexbox.FlexboxLayout
                android:layout_margin="5dp"
                android:id="@+id/flex_box_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:alignContent="flex_start"
                app:alignItems="flex_start"
                app:justifyContent="flex_start"
                app:showDivider="beginning|middle"
                app:flexWrap="wrap">

                <TextView
                    android:textAlignment="center"
                    app:layout_flexBasisPercent="49%"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Tầng 1" />

                <TextView
                    android:textAlignment="center"
                    app:layout_flexBasisPercent="49%"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Tầng 2" />


            </com.google.android.flexbox.FlexboxLayout>

        </LinearLayout>

        <View
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/stt_gray" />


    </LinearLayout>
</layout>