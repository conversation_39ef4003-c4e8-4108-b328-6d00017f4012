<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/select_bag_sheet"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="2dp"
    android:visibility="visible"
    app:cardElevation="20dp"
    android:orientation="vertical"
    android:clickable="true"
    android:focusable="true"
    app:behavior_hideable="true"
    app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">
    <LinearLayout
        android:background="@drawable/border_top"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">
        <LinearLayout
            android:padding="10dp"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Mua thêm hành lý"/>
            <TextView
                android:id="@+id/depPaxName"
                android:gravity="end"
                android:textColor="@color/textDark"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"/>
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/diver_color"/>
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/diver_color"/>
        <LinearLayout
            android:padding="10dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/gbgray"
            android:orientation="horizontal">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Chuyến bay đi"/>
            <LinearLayout
                android:layout_width="fill_parent"
                android:gravity="right"
                android:layout_height="match_parent">
                <ImageView
                    android:contentDescription="@string/txt_share_app"
                    android:layout_width="wrap_content"
                    android:layout_height="fill_parent"
                    android:id="@+id/depLogo"/>
                <TextView
                    android:layout_marginRight="10dp"
                    android:textColor="@color/textDark"
                    android:id="@+id/depRoute"
                    android:text="HAN -> SGN"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
            </LinearLayout>
            <TextView
                android:visibility="gone"
                android:id="@+id/depBag"
                android:layout_width="fill_parent"
                android:gravity="right"
                android:textColor="@color/textDark"
                android:layout_height="match_parent"/>
        </LinearLayout>
        <HorizontalScrollView
            android:paddingBottom="5dp"
            android:scrollbars="none"
            android:id="@+id/horizalScrollx"
            android:layout_width="fill_parent"
            android:background="@color/gbgray"
            android:layout_height="wrap_content" >
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:id="@+id/bagSelectContainer">


            </LinearLayout>
        </HorizontalScrollView>

        <LinearLayout
            android:id="@+id/selectBagReturnLayout"
            android:visibility="visible"
            android:background="@color/gbgray"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_margin="5dp"
                android:background="@color/diver_color"/>
            <LinearLayout
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                android:paddingRight="10dp"
                android:paddingLeft="10dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/gbgray"
                android:orientation="horizontal">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Chuyến bay về"/>
                <LinearLayout
                    android:layout_width="fill_parent"
                    android:gravity="right"
                    android:layout_height="match_parent">
                    <ImageView
                        android:contentDescription="@string/txt_share_app"
                        android:layout_width="wrap_content"
                        android:layout_height="fill_parent"
                        android:id="@+id/retLogo"/>
                    <TextView
                        android:layout_marginRight="10dp"
                        android:textColor="@color/textDark"
                        android:id="@+id/retRoute"
                        android:text="HAN -> SGN"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>
                </LinearLayout>
                <TextView
                    android:visibility="gone"
                    android:id="@+id/retBag"
                    android:layout_width="fill_parent"
                    android:gravity="right"
                    android:textColor="@color/textDark"
                    android:layout_height="match_parent"/>
            </LinearLayout>
            <HorizontalScrollView
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                android:scrollbars="none"
                android:id="@+id/horizalScrollRt"
                android:layout_width="fill_parent"
                android:background="@color/gbgray"
                android:layout_height="wrap_content" >
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:id="@+id/bagSelectContainerRt">

                </LinearLayout>
            </HorizontalScrollView>

        </LinearLayout>
        <LinearLayout
            android:background="@color/gbgray"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center|center_vertical"
            android:paddingBottom="10dp"
            android:orientation="vertical" >
            <Button
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:id="@+id/btnSelectBag"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:textSize="15sp"
                android:backgroundTint="@color/primary_dark"
                android:layout_gravity="center_horizontal"
                style="@style/Theme.MyApp.Button"
                android:text="Chọn"
                android:textColor="#FFFFFF" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>