<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:id="@+id/coordinatorLayout"
        android:fitsSystemWindows="true"
        android:animateLayoutChanges="true"
        android:background="@color/gbgray"
        tools:context="com.hqt.view.ui.HomeActivity">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true"
            app:elevation="0dp"
            android:background="@drawable/bg_gradient"
            android:theme="@style/AppTheme.AppBarOverlay">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/toolbar_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fitsSystemWindows="true"
                app:contentScrim="?attr/colorPrimary"
                app:title="12bay.vn"
                app:titleEnabled="false"
                app:layout_scrollFlags="noScroll">

                <ImageView
                    android:id="@+id/headerBG"
                    android:layout_width="match_parent"
                    android:layout_height="150dp"
                    android:scaleType="centerCrop"
                    android:contentDescription="@string/app_name"
                    android:fitsSystemWindows="true"
                    android:adjustViewBounds="true"
                    app:srcCompat="@drawable/world"
                    android:alpha="1" />

                <androidx.appcompat.widget.Toolbar
                    android:id="@+id/toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize"
                    app:elevation="0dp"
                    app:layout_collapseMode="pin"
                    app:layout_scrollFlags="scroll|exitUntilCollapsed"
                    app:popupTheme="@style/AppTheme.PopupOverlay" />

            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <LinearLayout
            android:layout_marginTop="?attr/actionBarSize"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/corner_full"
                app:cardCornerRadius="5dp"
                app:cardElevation="0dp"
                android:layout_marginLeft="30dp"
                android:layout_marginRight="30dp"
                android:layout_marginBottom="10dp"
                android:clickable="true"
                android:visibility="visible"
                app:cardPreventCornerOverlap="false"
                android:animateLayoutChanges="true"
                android:focusable="true">

                <LinearLayout
                    android:id="@+id/searchButton"
                    android:background="@color/white"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:padding="5dp"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:orientation="horizontal">

                        <com.mikepenz.iconics.view.IconicsImageView
                            android:layout_marginStart="5dp"
                            android:layout_gravity="center_vertical"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            app:iiv_color="@color/stt_gray"
                            app:iiv_icon="gmd_search" />

                        <TextView
                            android:paddingStart="5dp"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:paddingTop="10dp"
                            android:paddingBottom="10dp"
                            android:text="Tìm chuyến bay (VD: VN110, VJ123)" />

                    </LinearLayout>

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="fill_parent">


                <fragment

                    android:id="@+id/map"
                    android:name="com.google.android.gms.maps.SupportMapFragment"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    tools:context="com.hqt.view.ui.flighthistory.ui.activity.MapViewActivity" />

                <ProgressBar
                    android:visibility="gone"
                    android:padding="10dp"
                    android:layout_alignParentBottom="true"
                    android:layout_centerInParent="true"
                    android:id="@+id/progress_loader"
                    style="?android:attr/progressBarStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />

                <LinearLayout
                    android:animateLayoutChanges="true"
                    android:layout_alignParentBottom="true"
                    android:id="@+id/tripContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical" />


                <include
                    android:id="@+id/mapType"
                    layout="@layout/widget_map_type" />
            </RelativeLayout>
        </LinearLayout>


    </androidx.coordinatorlayout.widget.CoordinatorLayout>


</layout>