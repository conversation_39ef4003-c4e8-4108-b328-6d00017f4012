<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/close"
            android:background="@color/primary"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:padding="10dp"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                style="@style/TextAppearance.AppCompat.Widget.ActionBar.Title"
                android:text="Chi tiết mặt vé" />

            <LinearLayout
                android:background="@color/primary"
                android:layout_width="fill_parent"
                android:layout_height="match_parent"
                android:padding="10dp"
                android:gravity="end|center_vertical">

                <com.mikepenz.iconics.view.IconicsImageView
                    android:padding="2dp"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    app:iiv_color="@color/white"
                    app:iiv_icon="gmd_close" />

            </LinearLayout>

        </LinearLayout>

        <RelativeLayout
            android:background="@color/black"
            android:layout_width="match_parent"
            android:layout_height="fill_parent">

            <ProgressBar
                android:id="@+id/loadding"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <com.ortiz.touchview.TouchImageView
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:id="@+id/iv_image_activity"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerInside"

                android:scrollbars="horizontal|vertical" />
        </RelativeLayout>
    </LinearLayout>
</layout>