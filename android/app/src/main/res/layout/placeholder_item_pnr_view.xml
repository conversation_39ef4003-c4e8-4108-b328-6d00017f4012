<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_view_chieuDi"
    style="@style/CardViewStyle.Light"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_margin="5dp"
    android:animateLayoutChanges="true"
    app:cardCornerRadius="2dp"
    app:cardElevation="2dp"
    app:cardPreventCornerOverlap="false">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="5dp">

            <View
                android:layout_width="fill_parent"
                android:layout_height="30dp"
                android:background="#dddddd" />



        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_margin="5dp"
            android:orientation="horizontal"
            android:weightSum="9">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="3"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="30dp"
                    android:layout_below="@id/thumbnail"
                    android:background="#dddddd" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="3"
                android:orientation="vertical">
                <View
                    android:layout_marginLeft="5dp"
                    android:layout_marginRight="5dp"
                    android:layout_width="match_parent"
                    android:layout_height="30dp"
                    android:layout_below="@id/thumbnail"
                    android:background="#dddddd" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="3"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="30dp"
                    android:layout_below="@id/thumbnail"
                    android:background="#dddddd" />


            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="5dp"
            android:weightSum="9">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="3"
                android:gravity="center"
                android:orientation="vertical">

                <View
                    android:id="@+id/subtitle"
                    android:layout_width="match_parent"
                    android:layout_height="30dp"
                    android:background="#dddddd" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="3"
                android:orientation="vertical">

                <View
                    android:layout_marginLeft="5dp"
                    android:layout_marginRight="5dp"
                    android:id="@+id/subtitled"
                    android:layout_width="match_parent"
                    android:layout_height="30dp"
                    android:background="#dddddd" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="3"
                android:gravity="center"
                android:orientation="vertical">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="30dp"
                    android:background="#dddddd" />


            </LinearLayout>

        </LinearLayout>
        <LinearLayout
            android:id="@+id/viewShowTotalDi"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">


            <LinearLayout
                android:id="@+id/viewTotalDi"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="3"
                android:animateLayoutChanges="true"
                android:orientation="vertical">


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="5dp">

                    <View
                        android:id="@+id/content"
                        android:layout_width="fill_parent"
                        android:layout_height="30dp"
                        android:layout_toEndOf="@+id/thumbnail"
                        android:background="#dddddd" />


                </LinearLayout>

                <LinearLayout
                    android:id="@+id/viewTotalDiDetail"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:animateLayoutChanges="true"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingLeft="2dp"
                        android:paddingRight="2dp">

                        <TextView

                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Giá vé người lớn" />

                        <TextView
                            android:id="@+id/txtDiGiaNguoiLon"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="right"
                            android:text="15000k"
                            android:textSize="12sp" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/diTreEm"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingLeft="2dp"
                        android:paddingRight="2dp">

                        <TextView

                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Giá vé trẻ em" />

                        <TextView
                            android:id="@+id/txtDiGiaTreem"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="right"
                            android:text="90k"
                            android:textSize="12sp" />


                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/diEmbe"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingLeft="2dp"
                        android:paddingRight="2dp">

                        <TextView

                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Giá vé em bé" />

                        <TextView
                            android:id="@+id/txtDiGiaEmBe"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="right"
                            android:text="90k"
                            android:textSize="12sp" />
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>
