<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:bind="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="paymentType"
            type="com.hqt.data.model.PaymentType" />

        <import type="com.hqt.datvemaybay.Common" />

        <import type="android.view.View" />
    </data>

    <androidx.cardview.widget.CardView
        bind:visibility="@{paymentType.on}"
        android:layout_margin="5dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="5dp"
        app:cardElevation="0dp">

        <LinearLayout
            android:id="@+id/payment_type_view_hide_all"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="0dp">

            <LinearLayout
                android:id="@+id/payment_type_view"
                android:padding="5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_horizontal"
                    android:padding="5dp">

                    <ImageView
                        android:layout_gravity="center_horizontal|center_vertical"
                        android:layout_width="35dp"
                        android:layout_height="35dp"
                        bind:imageUrl="@{paymentType.img}" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:padding="5dp"
                    android:orientation="vertical">

                    <TextView
                        android:textColor="@color/black"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textStyle="bold"
                        tools:text="Thẻ nội địa "
                        android:text="@{paymentType.title}" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Phí tiện ích: " />

                        <TextView
                            bind:visibility="@{paymentType.fee > 0}"
                            android:textStyle="bold"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{ Common.dinhDangTien(paymentType.fee) + `` }" />

                        <TextView
                            bind:visibility="@{paymentType.fee == 0}"
                            android:textStyle="bold"
                            android:textColor="@color/red"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Miễn phí" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="fill_parent"
                    android:gravity="center_vertical|end">


                    <com.mikepenz.iconics.view.IconicsImageView
                        android:id="@+id/showIcon"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_marginEnd="10dp"
                        app:iiv_color="@color/black"
                        app:iiv_size="10dp"
                        app:iiv_icon="gmd_expand_more" />

                </LinearLayout>
            </LinearLayout>


            <LinearLayout
                android:visibility="gone"
                android:background="@color/white"
                android:id="@+id/method_item"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:orientation="vertical">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginBottom="1dp"
                    android:background="@color/diver_color" />

                <com.google.android.flexbox.FlexboxLayout
                    android:id="@+id/flex_box"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    app:alignContent="center"
                    app:alignItems="center"
                    app:dividerDrawable="@color/diver_color"
                    app:flexWrap="wrap"
                    app:justifyContent="flex_start"
                    app:showDivider="middle|beginning">


                </com.google.android.flexbox.FlexboxLayout>
            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>
</layout>