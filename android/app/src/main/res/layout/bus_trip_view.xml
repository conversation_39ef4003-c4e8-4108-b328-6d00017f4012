<?xml version="1.0" encoding="UTF-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="trip"
            type="com.hqt.data.model.BusSeatFare" />

        <import type="com.hqt.datvemaybay.Common" />

        <import type="android.view.View" />
    </data>

    <androidx.cardview.widget.CardView
        android:id="@+id/layoutTrainDetail"
        style="@style/CardViewStyle.Light"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="5dp"
        android:animateLayoutChanges="true"
        app:cardCornerRadius="2dp"
        app:cardElevation="2dp"
        app:cardPreventCornerOverlap="false">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:visibility="visible"
                android:layout_margin="5dp"
                android:id="@+id/layoutTrainView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingTop="5dp"
                android:paddingStart="5dp"
                android:layout_gravity="center_horizontal"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="24dp"
                    android:layout_gravity="center_horizontal"
                    android:background="@drawable/ic_bus"
                    android:contentDescription="@string/Description" />

                <TextView
                    style="@style/Text"
                    android:layout_gravity="center_vertical"
                    android:paddingStart="5dp"
                    android:paddingEnd="10dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@{trip.originName + ` - `+trip.destinationName}"
                    tools:text="TPHCM DLat"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:gravity="end"
                    android:paddingEnd="5dp"
                    tools:text=""
                    android:text="@{Common.getDayOfWeek(trip.departureDateTime) + `, `+Common.dateToString(trip.departureDateTime ,`dd/MM/yyyy` )}" />
            </LinearLayout>

            <LinearLayout
                android:padding="5dp"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal">

                <View
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:layout_marginRight="5dp"
                    android:layout_marginLeft="5dp"
                    android:background="@drawable/gradientdiv" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.mikepenz.iconics.view.IconicsImageView
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:padding="5dp"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            app:iiv_color="@color/stt_gray"
                            app:iiv_icon="faw-clock" />

                        <TextView
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:padding="5dp"
                            style="@style/Text"
                            android:textStyle="bold"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:text="08:00"
                            android:text="@{Common.dateToString(trip.pickupDate ,`HH:mm` ) }" />


                        <TextView
                            android:textColor="@color/textDark"

                            android:layout_gravity="center_horizontal|center_vertical"
                            android:padding="5dp"
                            android:textStyle="bold"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:text="TP HCM"
                            android:text="@{trip.pickUpName }" />

                        <LinearLayout
                            android:visibility="gone"
                            android:gravity="right"
                            android:layout_width="fill_parent"
                            android:layout_height="match_parent">

                            <TextView
                                android:padding="5dp"
                                android:textColor="@color/textDark"
                                android:textStyle="bold"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                tools:text="20/12.2020"
                                android:text="@{Common.dateToString(trip.departureDateTime ,`dd/MM/yyyy` )}" />

                            <TextView
                                android:textStyle="bold"
                                android:paddingRight="5dp"
                                android:paddingTop="5dp"
                                android:paddingBottom="5dp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/textDark"
                                android:text="@{trip.trainNumber}" />
                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.mikepenz.iconics.view.IconicsImageView
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:padding="5dp"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            app:iiv_color="@color/stt_gray"
                            app:iiv_icon="faw-clock" />

                        <TextView
                            style="@style/Text"
                            android:textStyle="bold"
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:padding="5dp"
                            android:id="@+id/txtGioDen"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:text="18:00"
                            android:text="@{Common.dateToString(trip.arrivalDateTime,`HH:mm` ) }" />

                        <TextView
                            android:textColor="@color/textDark"
                            android:padding="5dp"
                            android:id="@+id/txtNgayDen"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textStyle="bold"
                            style="@style/Text"
                            tools:text="Đà Lạt"
                            android:text="@{trip.dropOffName }" />

                        <LinearLayout
                            android:gravity="right"
                            android:layout_width="fill_parent"
                            android:layout_height="match_parent">


                        </LinearLayout>

                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:paddingStart="5dp"
                android:layout_margin="5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:orientation="horizontal">

                <ImageView
                    android:layout_gravity="center_horizontal|center_vertical"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:background="@drawable/ic_bus_line"
                    android:contentDescription="@string/Description" />

                <TextView
                    style="@style/Text"
                    android:textStyle="bold"
                    android:layout_gravity="center_horizontal|center_vertical"
                    android:padding="5dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:text="Phương trang  "
                    android:text="@{trip.provider + ` -`}" />

                <TextView
                    style="@style/Text"
                    android:layout_gravity="center_horizontal|center_vertical"
                    android:padding="5dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:text=" Giường năm 48 chỗ"
                    android:text="@{ trip.seatClass }" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/viewTotalDi"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="5dp"
                android:layout_weight="3"
                android:animateLayoutChanges="true"
                android:orientation="vertical">


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="2dp"
                    android:paddingRight="2dp">

                    <TextView
                        android:id="@+id/txtTotalTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Tổng giá vé lượt đi +"
                        android:textColor="#000000"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/txtDiTongGia"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:text="@{Common.dinhDangTien( trip.totalFare )}"
                        android:textColor="#FF0000"
                        android:textStyle="bold" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/viewTotalDetail"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:animateLayoutChanges="true"
                    android:visibility="gone"
                    android:paddingStart="2dp"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:visibility="@{trip.adultCount > 0 ? View.VISIBLE:View.GONE}"
                        android:paddingLeft="2dp"
                        android:paddingRight="2dp">

                        <TextView

                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Giá vé người lớn" />

                        <TextView
                            android:id="@+id/txtAdultFare"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="right"
                            android:text="@{Common.dinhDangTien(trip.adult)}"
                            android:textSize="12sp" />

                    </LinearLayout>

                    <LinearLayout
                        android:visibility="@{trip.childCount > 0 ?  View.VISIBLE:View.GONE}"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingLeft="2dp"
                        android:paddingRight="2dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Giá vé trẻ em" />

                        <TextView
                            android:id="@+id/txtChildFare"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="right"
                            android:text="@{Common.dinhDangTien(trip.child)}"
                            android:textSize="12sp" />

                    </LinearLayout>

                    <LinearLayout
                        android:visibility="@{trip.studentCount > 0 ? View.VISIBLE:View.GONE }"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingLeft="2dp"
                        android:paddingRight="2dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Giá vé sinh viên" />

                        <TextView
                            android:id="@+id/txtStudentFare"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="right"
                            android:text="@{Common.dinhDangTien(trip.student)}"
                            android:textSize="12sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:visibility="@{trip.olderCount > 0 ?  View.VISIBLE:View.GONE}"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingLeft="2dp"
                        android:paddingRight="2dp">

                        <TextView

                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Giá vé người cao tuổi" />

                        <TextView
                            android:id="@+id/txtStudenFare"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="right"
                            android:text="@{Common.dinhDangTien(trip.older)}"
                            android:textSize="12sp" />
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>
</layout>