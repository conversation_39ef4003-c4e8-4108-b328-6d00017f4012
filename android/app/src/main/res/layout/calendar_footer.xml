<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:background="@color/white"
        android:id="@+id/footer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#eeeeee" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:paddingTop="5dp"
            android:orientation="horizontal">

            <com.mikepenz.iconics.view.IconicsImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center"
                android:padding="5dp"
                app:iiv_color="@color/primary_dark"
                app:iiv_icon="faw-calendar-alt" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="5dp"
                android:textColor="@color/black"
                android:textSize="14sp"
                android:text="Giá vé tốt nhất của ngày" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:paddingBottom="10dp"
            android:orientation="horizontal">

            <com.mikepenz.iconics.view.IconicsImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center"
                android:padding="5dp"
                app:iiv_color="@color/primary_dark"
                app:iiv_icon="faw-tag" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="5dp"
                android:textColor="@color/black"
                android:textSize="14sp"
                android:maxLines="1"
                android:text="Nhấn vào 1 ngày để xem chi tiết giá vé" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#eeeeee" />

    </LinearLayout>


</layout>