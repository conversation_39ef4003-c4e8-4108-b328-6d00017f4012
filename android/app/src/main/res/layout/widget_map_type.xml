<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <LinearLayout
        android:layout_margin="5dp"
        android:id="@+id/widgetMapType"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity=""
        android:orientation="horizontal">

        <androidx.cardview.widget.CardView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/CardViewStyle.Light"
            app:cardCornerRadius="2dp"
            app:cardElevation="2dp"
            android:layout_margin="2dp"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/btnNormal"
                    android:gravity="center_horizontal|center_vertical"
                    android:drawablePadding="2dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Bản đồ"
                    android:padding="4dp"
                    android:textSize="14sp" />

                <View
                    android:layout_marginStart="5dp"
                    android:layout_marginEnd="5dp"
                    android:layout_width="1dp"
                    android:layout_height="fill_parent"
                    android:background="@drawable/gradientdiv" />

                <TextView
                    android:id="@+id/btnStatelLte"
                    android:gravity="center_horizontal|center_vertical"
                    android:drawablePadding="2dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Vệ tinh"
                    android:padding="4dp"
                    android:textSize="14sp" />

                <View
                    android:layout_marginStart="5dp"
                    android:layout_marginEnd="5dp"
                    android:layout_width="1dp"
                    android:layout_height="fill_parent"
                    android:background="@drawable/gradientdiv" />

                <TextView
                    android:id="@+id/btnTerrain"
                    android:gravity="center_horizontal|center_vertical"
                    android:drawablePadding="2dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Địa hình"
                    android:padding="4dp"
                    android:textSize="14sp" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>
    </LinearLayout>

</layout>