<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:background="@color/primary"
    tools:ignore="MissingPrefix"
    android:layout_height="match_parent">
    <include layout="@layout/toolbar"/>
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">
    <androidx.cardview.widget.CardView
        android:id="@+id/card_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="2dp"
        app:cardElevation="2dp"
        style="@style/CardViewStyle.Light"
        android:layout_marginBottom="0dp"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:layout_marginTop="10dp"
        app:mcv_showOtherDates="out_of_range"
        app:cardPreventCornerOverlap="true"
        >
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">


    <com.prolificinteractive.materialcalendarview.MaterialCalendarView
        android:id="@+id/calendarView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:mcv_showOtherDates="defaults"
        app:mcv_headerTextAppearance="@style/TitleTextView"
        app:mcv_dateTextAppearance="@style/TitleDateView"
        app:mcv_weekDayTextAppearance="@style/TitleWeekView"
        app:mcv_selectionColor="@color/primary"
        android:padding="10dp"
        app:mcv_firstDayOfWeek="monday"
        />
        <LinearLayout
            android:layout_width="match_parent"
            android:background="#EDEDED"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:orientation="vertical">
        <LinearLayout
            android:id="@+id/txtShowCheap"
            android:layout_width="match_parent"
            android:background="@drawable/corner_full"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="fill_parent"
                    android:background="@drawable/border_left"
                    android:padding="10dp">
                    <com.mikepenz.iconics.view.IconicsImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        app:iiv_color="@color/white"
                        app:iiv_icon="gmd_timeline"
                        />
                </LinearLayout>

                <LinearLayout
                    android:layout_marginLeft="5dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:padding="5dp"
                    android:orientation="vertical">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/txt_find_cheap"/>
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="12sp"
                        android:text="@string/txt_find_cheap_month"/>

                </LinearLayout>
                <Switch
                    android:id="@+id/cheapSearch"
                    android:layout_gravity="center_horizontal|center_vertical"
                    android:layout_width="fill_parent"
                    android:layout_height="fill_parent"/>

            </LinearLayout>
        </LinearLayout>
            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnOk"
                style="@style/MyApp.Button.Big"
                android:layout_width="match_parent"
                android:textAlignment="center"
                android:textSize="16sp"
                android:layout_height="wrap_content"
                android:padding="10dp"
                android:text="Chọn" />
        </LinearLayout>

        </LinearLayout>
    </androidx.cardview.widget.CardView>



        </LinearLayout>
    </ScrollView>
</LinearLayout>