<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:custom="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="flightDetail"
            type="com.hqt.view.ui.search.data.model.FlightV2" />


        <variable
            name="flightHistory"
            type="com.hqt.view.ui.flighthistory.ui.state.FlightHistoryItemState" />

        <import type="com.hqt.datvemaybay.Common" />

        <variable
            name="itemViewState"
            type="com.hqt.view.ui.search.ui.state.FlightItemState" />

    </data>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/activity_vertical_margin"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:padding="5dp">

            <com.mikepenz.iconics.view.IconicsTextView
                style="@style/Text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="{faw_paper_plane}  "
                android:textSize="10dp" />

            <TextView
                android:id="@+id/txtLuotdiTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Thông tin chuyến bay"
                android:textColor="#000000"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="end"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/txtDiNgayBay"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="right"
                    android:text="@{Common.getDayOfWeek(flightDetail.getDepartureDateTime().toString().substring(6, 16))}"
                    android:textColor="#000"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/txtDiNgayBayAmLich"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="right"
                    android:paddingLeft="5dp"
                    android:text="@{Common.getLunarDate(Common.getDateFromString(flightDetail.getDepartureDateTime().toString().substring(6, 16)).getTime())}"
                    android:textColor="#0084ff"
                    android:textSize="10sp"
                    android:textStyle="italic" />
            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:paddingTop="5dp"
            android:layout_margin="5dp"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:background="@drawable/corner_full"
            android:layout_height="wrap_content">

            <LinearLayout
                android:padding="5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:orientation="horizontal"
                android:weightSum="9"
                android:baselineAligned="false">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">


                    <TextView
                        android:id="@+id/txtDiThoiGianBay"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@{flightDetail.getDepartureDateTime().substring(0, 5)}"
                        android:textColor="#000000"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/txtDiFrom"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/code_rounded"
                        android:paddingLeft="3dp"
                        android:paddingTop="2dp"
                        android:paddingRight="3dp"
                        android:paddingBottom="2dp"
                        android:singleLine="true"
                        android:text="@{flightDetail.originCode}"
                        android:textColor="#7C7C7C"
                        android:textSize="12sp"
                        android:textStyle="bold" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:orientation="vertical">

                    <ImageView
                        android:contentDescription="@string/app_name"
                        android:layout_width="50dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        app:srcCompat="@drawable/flight_history_icon" />

                    <TextView
                        android:id="@+id/txtDiDuration"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:text="@{flightDetail.duration}"
                        android:maxLines="1"
                        android:textColor="#000000"
                        android:textSize="12sp"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">


                    <TextView
                        android:id="@+id/txtDiThoiGianDen"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@{flightDetail.arriverDateTime.substring(0, 5)}"
                        android:textColor="#000000"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/txtDiTo"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/code_rounded"
                        android:gravity="center"
                        android:paddingLeft="3dp"
                        android:paddingTop="2dp"
                        android:paddingRight="3dp"
                        android:paddingBottom="2dp"
                        android:singleLine="true"
                        android:text="@{flightDetail.destinationCode}"
                        android:textColor="#7C7C7C"
                        android:textSize="12sp"
                        android:textStyle="bold" />


                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="5dp"
                android:weightSum="9">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="3"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/airlineLogo"
                        android:layout_width="wrap_content"
                        android:layout_height="fill_parent"
                        android:layout_gravity="center"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="10dp"
                        android:contentDescription="Logo"
                        android:paddingLeft="10dp"
                        android:paddingRight="10dp"
                        android:scaleType="centerInside" />

                    <TextView
                        android:id="@+id/txtDiHangBay"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="VietNam Airline"
                        android:textColor="#000000"
                        android:textSize="11sp"
                        android:visibility="gone" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/txtDiDiemDung"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:text="@{flightDetail.stopsText}"
                        android:textColor="#000000"
                        android:textSize="11sp" />

                    <TextView
                        android:id="@+id/txtDiChuyenBay"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:text="@{flightDetail.flightNumber}"
                        android:textColor="#000000"
                        android:maxLines="1"
                        android:textSize="11sp"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Loại vé"
                        android:textColor="#000000"
                        android:textSize="11sp" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/txtClassIconDi"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="#000000"
                            android:textSize="11sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/txtDiLoaiVe"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{flightDetail.seatClass}"
                            android:singleLine="true"
                            android:ellipsize="end"
                            android:textColor="#000000"
                            android:textSize="11sp"
                            android:textStyle="bold" />
                    </LinearLayout>


                </LinearLayout>

            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingBottom="10dp"
                android:weightSum="9">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="3"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/chum"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:text="Giá vé"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/gia"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:singleLine="true"
                        android:textColor="#da281c"
                        android:textStyle="bold"
                        android:text="@{Common.dinhDangTien(flightDetail.netPrice)}"
                        android:textSize="12sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="3"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Thuế phí"
                        android:textSize="12sp" />

                    <TextView

                        android:id="@+id/thuePhi"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:singleLine="true"
                        android:text="@{Common.dinhDangTien(flightDetail.tax + flightDetail.airPortFee )}"
                        android:textColor="@color/textDark"
                        android:textSize="12sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0"
                    android:visibility="gone"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/textV"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Phí sân bay"
                        android:textColor="#000000"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/thueSanBay"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:singleLine="true"
                        android:text="100,100"
                        android:textColor="#000000"
                        android:textSize="12sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="3"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/giaP"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Tổng cộng:"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/giaPhi"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@{Common.dinhDangTien(flightDetail.adult)}"
                        android:textColor="#000FFF"
                        android:textSize="12sp"
                        android:textStyle="bold" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:id="@+id/meta_click">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:padding="5dp">

                <com.mikepenz.iconics.view.IconicsTextView
                    style="@style/Text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="{faw_history}  "
                    android:textSize="10dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Lịch sử chuyến bay"
                    android:textColor="#000000"
                    android:textStyle="bold" />

                <com.mikepenz.iconics.view.IconicsTextView
                    android:layout_width="fill_parent"
                    android:gravity="end"
                    android:layout_height="wrap_content"
                    android:text="{faw_angle_right}"
                    android:textColor="#000000"
                    android:textStyle="bold" />


            </LinearLayout>

            <com.facebook.shimmer.ShimmerFrameLayout
                android:id="@+id/meta_shimmer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:orientation="vertical"
                android:visibility="visible"
                app:shimmer_duration="800">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <include layout="@layout/placeholder_item_flight_history_meta" />


                </LinearLayout>
            </com.facebook.shimmer.ShimmerFrameLayout>

            <LinearLayout
                android:layout_margin="5dp"
                android:background="@drawable/corner_full"
                android:visibility="gone"
                android:id="@+id/meta_layout"
                android:padding="10dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">


                <LinearLayout
                    android:layoutDirection="ltr"
                    android:layout_gravity="center_horizontal|center_vertical"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <com.hqt.util.helper.ArcProgress
                        android:id="@+id/score_progress"
                        android:background="@color/white"
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        custom:arc_progress="0"
                        custom:arc_text_size="16sp"
                        custom:arc_suffix_text_size="12sp"
                        custom:arc_suffix_text=""
                        custom:arc_suffix_text_padding="-2dp"
                        custom:arc_finished_color="@color/primary"
                        custom:arc_unfinished_color="@color/light_primary"
                        custom:arc_bottom_text="Đúng giờ" />

                    <View
                        android:layout_marginStart="5dp"
                        android:layout_marginEnd="5dp"
                        android:layout_width="2dp"
                        android:layout_height="match_parent"
                        android:background="@drawable/gradientdiv" />

                    <LinearLayout
                        android:layout_gravity="center_horizontal|center_vertical"
                        android:layoutDirection="ltr"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:paddingEnd="10dp"
                        android:paddingStart="10dp"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:weightSum="9">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Số hiệu: " />

                                <TextView
                                    android:id="@+id/txt_meta_flightNumber"
                                    style="@style/Text"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textStyle="bold"
                                    android:text="@{flightHistory.txtMetaFlightNumberText}"
                                    tools:text="..." />
                            </LinearLayout>

                            <LinearLayout
                                android:gravity="end"
                                android:layout_width="fill_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">


                                <TextView
                                    android:id="@+id/txt_meta_total_flight"
                                    style="@style/Text"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@{flightHistory.metaTotalFlightText}"
                                    tools:text="" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:singleLine="true"
                                    android:text=" chuyến" />
                            </LinearLayout>


                        </LinearLayout>

                        <RelativeLayout

                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:weightSum="9">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Đúng giờ: " />

                                <TextView
                                    android:id="@+id/txt_meta_green"
                                    android:textColor="#4CAF50"
                                    android:textStyle="bold"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@{flightHistory.metaGreenText}"
                                    tools:text="" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_centerInParent="true"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_marginStart="30dp"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Chậm: " />

                                <TextView
                                    android:id="@+id/txt_meta_yellow"
                                    android:textColor="#FB953B"
                                    android:textStyle="bold"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@{flightHistory.metaYellowText}"
                                    tools:text="" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_alignParentEnd="true"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Trễ: " />

                                <TextView
                                    android:id="@+id/txt_meta_red"
                                    android:textStyle="bold"
                                    android:textColor="#d62d20"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@{flightHistory.metaRedText}"
                                    tools:text="" />
                            </LinearLayout>

                        </RelativeLayout>
                    </LinearLayout>


                </LinearLayout>


            </LinearLayout>

            <LinearLayout
                bind:visibility="@{flightDetail.description.length()>0}"
                android:layout_margin="5dp"
                android:background="@drawable/corner_full"
                android:visibility="visible"
                android:id="@+id/info_layout"
                android:padding="10dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="40dp"
                    android:layout_height="50dp"
                    app:srcCompat="@drawable/ic_checkin_online" />


                <LinearLayout
                    android:layout_gravity="center_horizontal|center_vertical"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/txtDescription"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@{itemViewState.txtDescriptionText}"
                        tools:text="Miễn phí hành lý 15kg" />


                </LinearLayout>


            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</layout>
