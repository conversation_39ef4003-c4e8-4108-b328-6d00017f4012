<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:id="@+id/coordinatorLayout"
        android:fitsSystemWindows="true"
        android:background="@drawable/bg_gradient"
        tools:ignore="MissingPrefix"
        tools:context="com.hqt.util.amlich.AmLich">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true"
            app:elevation="0dp"
            android:background="@color/fui_transparent"
            android:theme="@style/AppTheme.AppBarOverlay">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/toolbar_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fitsSystemWindows="true"
                app:contentScrim="?attr/colorPrimary"
                app:title="12bay.vn"
                app:titleEnabled="false"
                app:layout_scrollFlags="scroll|exitUntilCollapsed">

                <ImageView
                    android:id="@+id/headerBG"
                    android:layout_width="match_parent"
                    android:layout_height="120dp"
                    android:scaleType="centerCrop"
                    android:contentDescription="@string/app_name"
                    android:fitsSystemWindows="true"
                    android:adjustViewBounds="true"
                    app:srcCompat="@drawable/world"
                    android:alpha="1" />

                <androidx.appcompat.widget.Toolbar
                    android:id="@+id/toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize"
                    app:elevation="0dp"
                    app:layout_collapseMode="pin"
                    app:layout_scrollFlags="scroll|exitUntilCollapsed"
                    app:popupTheme="@style/AppTheme.PopupOverlay" />


            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>


        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            tools:showIn="@layout/activity_scrolling"
            app:layout_behavior="@string/appbar_scrolling_view_behavior"
            app:behavior_overlapTop="60dp"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <androidx.cardview.widget.CardView
                    android:id="@+id/card_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardCornerRadius="2dp"
                    app:cardElevation="2dp"
                    style="@style/CardViewStyle.Light"
                    android:layout_marginBottom="0dp"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:layout_marginTop="10dp"
                    app:mcv_showOtherDates="out_of_range"
                    app:cardPreventCornerOverlap="true">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <com.prolificinteractive.materialcalendarview.MaterialCalendarView android:id="@+id/calendarView"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            xmlns:app="http://schemas.android.com/apk/res-auto"
                            app:mcv_showOtherDates="defaults"
                            app:mcv_headerTextAppearance="@style/TitleTextView"
                            app:mcv_dateTextAppearance="@style/TitleDateView"
                            app:mcv_weekDayTextAppearance="@style/TitleWeekView"
                            app:mcv_selectionColor="@color/primary"
                            android:padding="10dp"
                            app:mcv_firstDayOfWeek="monday" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:background="#EDEDED"
                            android:layout_height="wrap_content"
                            android:padding="10dp"
                            android:orientation="vertical">

                            <LinearLayout
                                android:id="@+id/txtShowCheap"
                                android:layout_width="match_parent"
                                android:background="@drawable/corner_full"
                                android:layout_height="wrap_content">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal">

                                    <LinearLayout
                                        android:layout_width="wrap_content"
                                        android:layout_height="fill_parent"
                                        android:background="@drawable/border_left"
                                        android:padding="10dp">

                                        <com.mikepenz.iconics.view.IconicsImageView
                                            android:layout_width="24dp"
                                            android:layout_height="24dp"
                                            app:iiv_color="@color/white"
                                            app:iiv_icon="gmd_timeline" />
                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_marginLeft="5dp"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:background="@color/white"
                                        android:padding="5dp"
                                        android:orientation="vertical">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="@string/txt_find_cheap" />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:textSize="12sp"
                                            android:text="@string/txt_find_cheap_month" />

                                    </LinearLayout>

                                    <Switch
                                        android:id="@+id/cheapSearch"
                                        android:layout_gravity="center_horizontal|center_vertical"
                                        android:layout_width="fill_parent"
                                        android:layout_height="fill_parent" />

                                </LinearLayout>
                            </LinearLayout>

                            <androidx.appcompat.widget.AppCompatButton
                                android:id="@+id/btnOk"
                                android:background="@drawable/button_gradient"
                                android:layout_marginTop="15dp"
                                android:layout_marginBottom="5dp"
                                android:textColor="@color/white"
                                android:layout_width="match_parent"
                                android:textAlignment="center"
                                android:textSize="16sp"
                                android:layout_height="wrap_content"
                                android:padding="10dp"
                                android:text="Chọn" />
                        </LinearLayout>

                    </LinearLayout>
                </androidx.cardview.widget.CardView>


            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

        <!--<com.airbnb.lottie.LottieAnimationView-->
        <!--android:id="@+id/animation_view"-->
        <!--android:layout_width="wrap_content"-->
        <!--android:layout_height="wrap_content"-->
        <!--android:scaleType="centerCrop"-->
        <!--app:lottie_fileName="data.json"-->
        <!--app:lottie_loop="false"-->
        <!--app:lottie_autoPlay="true" />-->


    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>
