<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="itemViewState"
            type="com.hqt.view.ui.booking.ui.state.BookingItemState" />

        <import type="android.view.View" />

    </data>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="fill_parent">

        <LinearLayout
            android:id="@+id/select_flight_sheet"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:animateLayoutChanges="true"
            android:background="@color/fui_transparent"
            android:clickable="true"
            android:elevation="6dp"
            android:focusable="true"
            android:orientation="vertical"
            app:behavior_hideable="true"
            app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">

            <View
                android:layout_width="50dp"
                android:layout_height="4dp"
                android:layout_gravity="center_vertical|center_horizontal"
                android:layout_margin="2dp"
                android:background="@drawable/top_line" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/gbgray"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/flightInfo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <include
                        android:id="@+id/info"
                        layout="@layout/widget_flight_info"/>

                </LinearLayout>

                <androidx.cardview.widget.CardView
                    android:id="@+id/next_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:paddingTop="5dp">

                        <LinearLayout
                            android:id="@+id/quickView"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="left|center_vertical"
                                android:orientation="vertical">

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:paddingLeft="5dp"
                                    android:paddingTop="2dp"
                                    android:paddingRight="5dp"
                                    android:paddingBottom="2dp">

                                    <TextView
                                        android:id="@+id/textView2"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Tổng số tiền "
                                        android:textColor="@color/black"
                                        android:textStyle="bold" />

                                    <TextView
                                        android:id="@+id/txtTotalPax"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="top|left"
                                        android:text="@{itemViewState.txtTotalPaxText}"
                                        android:paddingRight="5dp" />


                                </LinearLayout>

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:paddingLeft="5dp"
                                    android:paddingTop="2dp"
                                    android:paddingRight="5dp"
                                    android:paddingBottom="2dp"
                                    android:singleLine="true"
                                    android:text="Giá vé đã bao gồm thuế phí"
                                    android:textSize="13sp" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="fill_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/txtGrandTotalPrice"
                                    android:layout_width="fill_parent"
                                    android:layout_height="wrap_content"
                                    android:gravity="end"
                                    android:paddingLeft="15dp"
                                    android:paddingRight="5dp"
                                    android:singleLine="true"
                                    android:text="@{itemViewState.txtGrandTotalPriceText}"
                                    android:textColor="#da281c"
                                    android:textSize="20sp"
                                    android:textStyle="bold"
                                    tools:text="900k" />

                                <LinearLayout
                                    android:id="@+id/layoutPointReward"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:visibility="@{itemViewState.pointReward > 0 ? View.VISIBLE : View.GONE}"
                                    android:gravity="end"
                                    android:paddingBottom="2dp">

                                    <TextView
                                        android:id="@+id/txtPoint"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:gravity="end"
                                        tools:text="Nhận 10 điểm "
                                        android:text="@{itemViewState.txtPointText}"
                                        android:textColor="@color/primary"
                                        android:textSize="13sp"
                                        android:textStyle="italic" />

                                    <com.mikepenz.iconics.view.IconicsTextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="{gmd_stars} "
                                        android:textColor="@color/primary" />

                                </LinearLayout>
                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:padding="10dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="5dp">

                                <TextView
                                    android:id="@+id/quickViewDepTitle"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@{itemViewState.quickViewDepTitleText()}"
                                    android:textColor="@color/textDark"
                                    tools:text="HAN-SGN Vietjet Air 3x1 2x1" />

                                <TextView
                                    android:id="@+id/quickViewDepPrice"
                                    android:layout_width="fill_parent"
                                    android:layout_height="wrap_content"
                                    android:gravity="right"
                                    android:text="@{itemViewState.quickViewDepPriceText()}"
                                    android:textColor="@color/textDark" />
                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/quickViewRetLayout"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="5dp"
                                android:visibility="@{itemViewState.showQuickViewRetLayout ? View.VISIBLE : View.GONE}">

                                <TextView
                                    android:id="@+id/quickViewRetTitle"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@{itemViewState.quickViewRetTitleText()}"
                                    android:textColor="@color/textDark" />

                                <TextView
                                    android:id="@+id/quickViewRetPrice"
                                    android:layout_width="fill_parent"
                                    android:layout_height="wrap_content"
                                    android:gravity="right"
                                    android:text="@{itemViewState.quickViewRetPriceText()}"
                                    android:textColor="@color/textDark" />
                            </LinearLayout>


                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:paddingBottom="10dp"
                            android:weightSum="10">

                            <Button
                                android:id="@+id/btnBack"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_gravity="right"
                                android:layout_weight="4"
                                android:text="Trở về"
                                android:textColor="#FFFFFF"
                                android:textSize="15sp"
                                app:backgroundTint="@color/stt_gray" />

                            <Button
                                android:id="@+id/btnBookVe"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_gravity="right"
                                android:layout_weight="6"
                                android:text="Chọn"
                                android:textColor="#FFFFFF"
                                app:backgroundTint="@color/primary" />

                        </LinearLayout>
                    </LinearLayout>
                </androidx.cardview.widget.CardView>
            </LinearLayout>
        </LinearLayout>
    </ScrollView>
</layout>