<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:id="@+id/emptyStateLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/animation_view"
        android:layout_width="match_parent"
        android:layout_height="300dp"
        android:visibility="gone"
        />

        <ImageView
            android:id="@+id/emptyStateBackground"
            android:contentDescription="@string/txt_general_rules"
            android:layout_width="wrap_content"
            android:layout_height="300dp"
            android:padding="20dp"
            android:visibility="gone"
            android:adjustViewBounds="true"
            android:scaleType="centerCrop"
            app:layout_behavior="@string/appbar_scrolling_view_behavior" />
        <TextView
            android:id="@+id/emptyStateTitle"
            android:layout_width="match_parent"
            android:gravity="center"
            android:layout_height="wrap_content"
            android:text=""/>
        <LinearLayout
            android:layout_margin="20dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="right">

            <RadioGroup
                android:id="@+id/radio"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:layout_weight="1"
                android:background="@drawable/corner_full"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btnNegative"
                    style="@style/MyApp.Button.Big"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.5"
                    android:background="@drawable/button_one_way"
                    android:checked="true"
                    android:text="Tìm ngày khác"
                    android:textAllCaps="true"
                    android:textColor="#FFFFFF" />

                <Button
                    android:id="@+id/btnPositive"
                    style="@style/MyApp.Button.Big"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.5"
                    android:background="@color/fui_transparent"
                    android:text="Săn vé"
                    android:textAllCaps="true"
                    android:textColor="#00a2e3" />
            </RadioGroup>

        </LinearLayout>

</LinearLayout>