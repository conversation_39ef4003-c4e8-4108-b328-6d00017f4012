<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:fab="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/coordinatorLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".SearchResult">


        <com.github.florent37.materialviewpager.MaterialViewPager
            android:id="@+id/materialViewPager"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:viewpager_pagerTitleStrip="@layout/my_tabs"
            app:viewpager_color="@color/colorPrimary"
            app:viewpager_headerHeight="150dp"
            app:viewpager_headerAlpha="1.0"
            app:viewpager_imageHeaderDarkLayerAlpha="0.2"
            app:viewpager_hideLogoWithFade="true"
            app:viewpager_hideToolbarAndTitle="false"
            app:viewpager_enableToolbarElevation="false"
            app:viewpager_parallaxHeaderFactor="1.5"
            app:viewpager_headerAdditionalHeight="15dp"
            app:viewpager_displayToolbarWhenSwipe="true"
            app:viewpager_transparentToolbar="false"
            app:viewpager_animatedHeaderImage="true"
            android:background="#EDEDED" />


        <include
            android:layout_marginTop="200dp"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            layout="@layout/empty_state_layout" />

        <com.facebook.shimmer.ShimmerFrameLayout
            android:id="@+id/shimmer_view_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_marginTop="200dp"
            android:background="#EDEDED"
            android:orientation="vertical"
            android:visibility="gone"
            app:shimmer_duration="800">

            <LinearLayout
                android:layout_width="match_parent"
                android:background="#EDEDED"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <include layout="@layout/placeholder_item_flight" />

                <include layout="@layout/placeholder_item_flight" />

                <include layout="@layout/placeholder_item_flight" />

                <include layout="@layout/placeholder_item_flight" />

                <include layout="@layout/placeholder_item_flight" />

                <include layout="@layout/placeholder_item_flight" />

                <include layout="@layout/placeholder_item_flight" />

                <include layout="@layout/placeholder_item_flight" />

                <include layout="@layout/placeholder_item_flight" />

                <include layout="@layout/placeholder_item_flight" />

            </LinearLayout>
        </com.facebook.shimmer.ShimmerFrameLayout>

        <ProgressBar
            android:layout_gravity="bottom"
            style="@style/Widget.AppCompat.ProgressBar.Horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:indeterminate="true"
            android:id="@+id/progressBar"
            android:visibility="visible"
            android:layout_marginBottom="-5dp" />


        <com.github.clans.fab.FloatingActionButton
            android:id="@+id/fab_alert_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_price_alert"
            fab:fab_colorNormal="#FFAC0D"
            fab:fab_label="Tạo thông báo giá tự động"
            android:layout_alignParentEnd="true"
            android:layout_gravity="bottom|end"
            android:layout_marginBottom="61dp"
            android:layout_marginEnd="9dp"
            fab:fab_size="mini" />

        <com.github.clans.fab.FloatingActionMenu
            android:id="@+id/fb_menu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="61dp"
            android:layout_marginEnd="9dp"
            fab:menu_showShadow="true"
            fab:menu_shadowColor="#66000000"
            fab:menu_shadowRadius="4dp"
            fab:menu_shadowXOffset="1dp"
            fab:menu_shadowYOffset="3dp"
            fab:menu_animationDelayPerItem="55"
            fab:menu_buttonSpacing="0dp"
            fab:menu_colorNormal="#fd5959"
            fab:menu_colorPressed="#038dc4"
            fab:menu_colorRipple="#99d4d4d4"
            fab:menu_fab_size="mini"
            fab:menu_icon="@drawable/ic_sort_white_24dp"
            fab:menu_labels_colorNormal="#333"
            fab:menu_labels_colorPressed="#444"
            fab:menu_labels_colorRipple="#66efecec"
            fab:menu_labels_cornerRadius="3dp"
            fab:menu_labels_ellipsize="none"
            fab:menu_labels_margin="0dp"
            fab:menu_labels_maxLines="-1"
            fab:menu_labels_padding="8dp"
            fab:menu_labels_position="left"
            fab:menu_labels_showShadow="true"
            fab:menu_labels_singleLine="true"
            fab:menu_labels_textColor="@color/white"
            fab:menu_labels_textSize="15sp"
            fab:menu_openDirection="down"
            fab:menu_labels_showAnimation="@anim/jump_from_down"
            fab:menu_labels_hideAnimation="@anim/jump_to_down"
            app:fab_progress_color="#64d3ff"
            app:fab_progress_indeterminate="true"
            app:fab_progress_max="100"
            app:fab_progress_showBackground="false">

            <com.github.clans.fab.FloatingActionButton
                android:id="@+id/fab_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_functions_white_24dp"
                fab:fab_colorNormal="@color/primary"
                fab:fab_label="Hiển thị giá thuế phí"
                fab:fab_size="mini" />

            <com.github.clans.fab.FloatingActionButton
                android:id="@+id/fab_sortTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_access_alarms_white_24dp"
                fab:fab_label="Sắp xếp theo thời gian"
                fab:fab_colorNormal="@color/primary"
                fab:fab_size="mini" />

            <com.github.clans.fab.FloatingActionButton
                android:id="@+id/fab_sortPrice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_attach_money_white_24dp"
                fab:fab_colorNormal="@color/primary"
                fab:fab_label="Sắp xếp theo giá"
                fab:fab_size="mini" />

            <com.github.clans.fab.FloatingActionButton
                android:id="@+id/fab_sortRefesh"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_refresh_white_24dp"
                fab:fab_colorNormal="@color/primary"
                fab:fab_label="Tải lại"
                fab:fab_size="mini" />


        </com.github.clans.fab.FloatingActionMenu>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>