<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:fab="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewModel"
            type="com.hqt.viewmodel.BookingViewModel" />

        <import type="com.hqt.datvemaybay.Common" />

        <import type="android.view.View" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true"
        android:background="@color/gbgray"
        android:id="@+id/coordinatorLayout"
        android:animateLayoutChanges="true">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar"
            android:layout_width="match_parent"
            android:background="@color/primary_dark"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true"
            app:layout_constraintTop_toTopOf="parent"
            app:elevation="5dp"
            android:theme="@style/AppTheme.AppBarOverlay">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/toolbar_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:title="12bay.vn"
                android:background="@color/primary"
                android:fitsSystemWindows="true"
                app:titleEnabled="false"
                app:layout_scrollFlags="enterAlwaysCollapsed">

                <androidx.appcompat.widget.Toolbar
                    android:id="@+id/toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize"
                    app:elevation="0dp"
                    app:layout_collapseMode="pin"
                    app:layout_scrollFlags="scroll|enterAlways"
                    app:popupTheme="@style/AppTheme.PopupOverlay" />


            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:background="@color/gbgray"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <com.facebook.shimmer.ShimmerFrameLayout
                android:id="@+id/shimmer_view_container"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:orientation="vertical"
                android:visibility="visible"
                app:shimmer_duration="800">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <include layout="@layout/placeholder_item_flight_inter" />

                    <include layout="@layout/placeholder_item_flight_inter" />

                    <include layout="@layout/placeholder_item_flight_inter" />

                    <include layout="@layout/placeholder_item_flight_inter" />

                    <include layout="@layout/placeholder_item_flight_inter" />

                    <include layout="@layout/placeholder_item_flight_inter" />

                    <include layout="@layout/placeholder_item_flight_inter" />

                    <include layout="@layout/placeholder_item_flight_inter" />

                    <include layout="@layout/placeholder_item_flight_inter" />

                    <include layout="@layout/placeholder_item_flight_inter" />


                </LinearLayout>
            </com.facebook.shimmer.ShimmerFrameLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <include layout="@layout/empty_state_layout" />


        </LinearLayout>

        <View
            android:id="@+id/behavior_dependency"
            android:layout_width="1dp"
            android:layout_height="1dp"
            android:layout_marginBottom="30dp"
            android:layout_gravity="bottom"
            app:layout_anchorGravity="bottom" />

        <FrameLayout
            android:visibility="@{viewModel.isShowBookingView() ? View.VISIBLE : View.GONE}"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:behavior_hideable="true"
            app:layout_behavior="com.hqt.util.helper.OutOfScreenBottomSheetBehavior">

            <LinearLayout
                android:id="@+id/bottom_sheet"
                android:outlineAmbientShadowColor="@color/textDark"
                android:orientation="vertical"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_gravity="center_horizontal|bottom"
                android:background="#FFFFFF">

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btnFloatBookVe"
                    android:background="@drawable/button_gradient_noborder"
                    android:textColor="#FFFFFF"
                    android:layout_width="match_parent"
                    android:textAlignment="center"
                    android:textSize="16sp"
                    android:visibility="visible"
                    android:layout_height="wrap_content"
                    app:textAllCaps="true"
                    android:textAllCaps="true"
                    android:text="@{viewModel.getBookButtonText()}" />

            </LinearLayout>
        </FrameLayout>

        <com.github.clans.fab.FloatingActionMenu
            android:visibility="visible"
            android:id="@+id/fb_menu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="161dp"
            android:layout_marginEnd="10dp"
            fab:menu_showShadow="true"
            fab:menu_shadowColor="#66000000"
            fab:menu_shadowRadius="4dp"
            fab:menu_shadowXOffset="1dp"
            fab:menu_shadowYOffset="3dp"
            fab:menu_animationDelayPerItem="55"
            fab:menu_buttonSpacing="0dp"
            fab:menu_colorNormal="#fd5959"
            fab:menu_colorPressed="#038dc4"
            fab:menu_colorRipple="#99d4d4d4"
            fab:menu_fab_size="mini"
            fab:menu_icon="@drawable/ic_sort_white_24dp"
            fab:menu_labels_colorNormal="#333"
            fab:menu_labels_colorPressed="#444"
            fab:menu_labels_colorRipple="#66efecec"
            fab:menu_labels_cornerRadius="3dp"
            fab:menu_labels_ellipsize="none"
            fab:menu_labels_margin="0dp"
            fab:menu_labels_maxLines="-1"
            fab:menu_labels_padding="8dp"
            fab:menu_labels_position="left"
            fab:menu_labels_showShadow="true"
            fab:menu_labels_singleLine="true"
            fab:menu_labels_textColor="@color/white"
            fab:menu_labels_textSize="15sp"
            fab:menu_openDirection="down"
            fab:menu_labels_showAnimation="@anim/jump_from_down"
            fab:menu_labels_hideAnimation="@anim/jump_to_down"
            app:fab_progress_color="#64d3ff"
            app:fab_progress_indeterminate="true"
            app:fab_progress_max="100"
            app:fab_progress_showBackground="false">

            <com.github.clans.fab.FloatingActionButton
                android:id="@+id/fab_sortPrice"
                android:tag="sortPrice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_functions_white_24dp"
                fab:fab_colorNormal="@color/primary"
                fab:fab_label="Giá thấp nhất"
                fab:fab_size="mini" />

            <com.github.clans.fab.FloatingActionButton
                android:tag="sortDepTime"
                android:id="@+id/fab_sortDepTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_flight_takeoff_white_24dp"
                fab:fab_label="Bay sớm nhất"
                fab:fab_colorNormal="@color/primary"
                fab:fab_size="mini" />

            <com.github.clans.fab.FloatingActionButton
                android:tag="sortDepTimeDesc"
                android:id="@+id/fab_sortDepTimeDesc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_flight_takeoff_white_24dp"
                fab:fab_colorNormal="@color/primary"
                fab:fab_label="Bay muộn nhất"
                fab:fab_size="mini" />

            <com.github.clans.fab.FloatingActionButton
                android:tag="sortArrTime"
                android:id="@+id/fab_sortArrTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_flight_land"
                fab:fab_colorNormal="@color/primary"
                fab:fab_label="Hạ cánh sớm nhất"
                fab:fab_size="mini" />

            <com.github.clans.fab.FloatingActionButton
                android:tag="sortArrTimeDesc"
                android:id="@+id/fab_sortArrTimeDesc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_flight_land"
                fab:fab_colorNormal="@color/primary"
                fab:fab_label="Hạ cánh muộn nhất"
                fab:fab_size="mini" />

            <com.github.clans.fab.FloatingActionButton
                android:tag="sortDuration"
                android:id="@+id/fab_sortDuration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_refresh_white_24dp"
                fab:fab_colorNormal="@color/primary"
                fab:fab_label="Bay nhanh nhất"
                fab:fab_size="mini" />


        </com.github.clans.fab.FloatingActionMenu>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>