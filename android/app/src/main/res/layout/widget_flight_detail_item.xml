<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="flightInfo"
            type="com.hqt.view.ui.flightSearch.model.Flight" />

        <import type="com.hqt.datvemaybay.Common" />


    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:background="@drawable/corner_full_primary_boder"
            android:id="@+id/title"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:paddingEnd="10dp"
            android:paddingStart="10dp"
            android:paddingTop="5dp"
            android:paddingBottom="5dp"
            android:layout_marginBottom="10dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <com.mikepenz.iconics.view.IconicsImageView
                        android:layout_width="15dp"
                        android:layout_height="15dp"
                        android:layout_gravity="center_vertical"
                        app:iiv_size="12dp"
                        app:iiv_color="@color/black"
                        app:iiv_icon="faw_check_circle" />

                    <TextView
                        android:textStyle="bold"
                        android:textColor="#003A6F"
                        android:textSize="16sp"
                        android:layout_gravity="center_horizontal|center_vertical"
                        android:padding="5dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="Lượt đi"
                        android:text="@{flightInfo.title +flightInfo.startPoint+`-`+flightInfo.endPoint}" />


                </LinearLayout>

                <TextView
                    android:textSize="15sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@{flightInfo.stopInfoString}"
                    tools:text="Ho Chi Minh (SGN) - Lon don (LHR)" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="match_parent"
                android:gravity="end|center_vertical"
                android:layout_gravity="center_horizontal"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/txtDiNgayBay"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:text="@{flightInfo.startDate.substring(0, 10)}"
                    android:textColor="#000"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/txtDiNgayBayAmLich"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:paddingLeft="5dp"
                    android:text="@{flightInfo.getLunarDate(true)}"
                    android:textColor="#0084ff"
                    android:textSize="10sp"
                    android:textStyle="italic" />
            </LinearLayout>
        </LinearLayout>

        <androidx.cardview.widget.CardView
            android:id="@+id/departure_trip"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            app:contentPadding="5dp"
            app:cardCornerRadius="5dp"
            app:cardElevation="0dp">

            <LinearLayout
                android:paddingEnd="10dp"
                android:paddingTop="5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:weightSum="10"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:gravity="center_horizontal"
                        android:layout_weight="2"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:textColor="#003A6F"
                            android:textSize="16sp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:text="10:20"
                            android:text="@{flightInfo.listSegment[0].startTimeShort}"
                            android:textStyle="bold" />

                        <TextView
                            android:textSize="12sp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{flightInfo.listSegment[0].startDateShort}"
                            tools:text="S" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="match_parent"
                        android:gravity="center_horizontal|center_vertical">

                        <com.mikepenz.iconics.view.IconicsImageView
                            android:rotation="90"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="center_vertical"
                            app:iiv_size="20dp"
                            app:iiv_color="@color/black"
                            app:iiv_icon="faw_plane" />


                    </LinearLayout>

                    <LinearLayout
                        android:layout_weight="7"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:text="@{flightInfo.listSegment[0].startPointInfo.name}"
                            android:textColor="#003A6F"
                            android:textSize="16sp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:text="Ho Chi Minh (SGN)"
                            android:textStyle="bold" />

                        <TextView
                            android:text="@{flightInfo.listSegment[0].airlineInfo +` | `+ flightInfo.listSegment[0].flightNumber }"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:text="Carthay Pacific | VN110" />
                    </LinearLayout>


                </LinearLayout>

                <net.cachapa.expandablelayout.ExpandableLayout
                    android:id="@+id/expandable_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:el_duration="500"
                    app:el_expanded="true"
                    app:el_parallax="0.5">

                    <LinearLayout
                        android:id="@+id/segment_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                    </LinearLayout>
                </net.cachapa.expandablelayout.ExpandableLayout>

                <LinearLayout
                    android:weightSum="10"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:gravity="center_horizontal"
                        android:layout_weight="2"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:text="@{flightInfo.endTimeShort}"
                            android:textColor="#003A6F"
                            android:textSize="16sp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:text="10:20" />

                        <TextView
                            android:textSize="12sp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{flightInfo.endDateShort}"
                            tools:text="10h20m" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="match_parent"
                        android:gravity="center_horizontal|center_vertical">


                        <com.mikepenz.iconics.view.IconicsImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="center_vertical"
                            app:iiv_size="20dp"
                            app:iiv_color="@color/red"
                            app:iiv_icon="faw_map-marker-alt" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_gravity="center_vertical"
                        android:layout_weight="7"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:textStyle="bold"
                            android:text="@{flightInfo.endPointInfo.name}"
                            android:textColor="#003A6F"
                            android:textSize="16sp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:text="Hong Kong (HKG)" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Lưu ý: Giờ đến là giờ địa phương tại nơi đến" />

                    </LinearLayout>


                </LinearLayout>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

    </LinearLayout>
</layout>