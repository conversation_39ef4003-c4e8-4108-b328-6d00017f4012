<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <variable
            name="schedule"
            type="com.hqt.view.ui.tour.DepartureDates" />

        <import type="com.hqt.datvemaybay.Common" />

        <import type="android.text.Html" />
    </data>


    <TableRow
        android:layout_height="wrap_content"
        android:background="@drawable/table_border">


        <TextView
            android:layout_height="match_parent"
            android:background="@drawable/table_border"
            android:text="@{schedule.departureDate}"
            android:paddingStart="2dp"
            android:paddingEnd="2dp"
            android:gravity="center" />

        <TextView
            android:layout_height="match_parent"
            android:background="@drawable/table_border"
            android:text="Tiêu chuẩn"
            android:paddingStart="2dp"
            android:paddingEnd="2dp"
            android:gravity="center" />

        <TextView
            android:layout_height="match_parent"
            android:background="@drawable/table_border"
            android:text="@{Common.dinhDangTien(schedule.estimatePrice)}"
            android:textColor="@color/red"
            android:textStyle="bold"
            android:paddingStart="5dp"
            android:paddingEnd="5dp"
            android:gravity="center" />

        <TextView
            android:layout_height="match_parent"
            android:background="@drawable/table_border"
            android:text="@{schedule.numberGuest.toString()}"
            android:paddingStart="5dp"
            android:paddingEnd="5dp"
            android:gravity="center" />

        <androidx.appcompat.widget.AppCompatButton
            android:layout_width="wrap_content"
            android:backgroundTint="#FF5959"
            android:textColor="#FFFFFF"
            android:layout_height="31dp"
            android:text="Đặt ngay"
            android:padding="2dp"
            android:textSize="10sp"
            android:id="@+id/btnSelectTour"
            style="@style/MyApp.Button.Big"
            android:layout_margin="2dp" />
    </TableRow>


</layout>