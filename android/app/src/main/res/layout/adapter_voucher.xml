<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="itemViewState"
            type="com.hqt.view.ui.reward.ui.state.VoucherItemState" />

    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <com.vipulasri.ticketview.TicketView
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:layout_marginTop="10dp"
            android:layout_marginStart="15dp"
            android:layout_marginEnd="15dp"
            android:id="@+id/ticketView"
            app:ticketOrientation="vertical"
            app:ticketElevation="0dp"
            app:ticketCornerRadius="5dp"
            app:ticketCornerType="rounded"
            app:ticketScallopRadius="10dp"
            app:ticketDividerPadding="0dp"
            app:ticketShowBorder="false"
            app:ticketScallopPositionPercent="30"
            app:ticketShowDivider="false"
            app:ticketDividerType="dash" />

        <LinearLayout
            android:padding="5dp"
            android:layout_margin="5dp"
            android:layout_width="match_parent"
            android:layout_height="100dp"
            android:orientation="horizontal"
            android:weightSum="10">

            <LinearLayout
                android:layout_width="0dp"
                android:gravity="center_vertical|center_horizontal"
                android:layout_weight="3"
                android:layout_height="match_parent">

                <androidx.cardview.widget.CardView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:cardCornerRadius="5dp"
                    app:cardElevation="0dp"
                    android:background="@drawable/corner_full">

                    <com.hqt.util.AspectRatioImageView
                        android:id="@+id/imgLogo"
                        android:layout_width="70dp"
                        android:layout_height="70dp"
                        android:scaleType="centerInside" />
                </androidx.cardview.widget.CardView>

            </LinearLayout>

            <LinearLayout
                android:padding="10dp"
                android:layout_marginLeft="10dp"
                android:layout_width="0dp"
                android:layout_weight="7"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <TextView
                    android:textStyle="bold"
                    android:id="@+id/txtTitle"
                    android:textColor="@color/textDark"
                    android:layout_marginTop="10dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    tools:text="Title"
                    android:text="@{itemViewState.item.promotionDetail.name}"
                    />

                <TextView
                    android:id="@+id/txtExpiredTime"
                    android:layout_marginTop="10dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    tools:text="Hạn dùng"
                    android:text="@{itemViewState.expiredTime}"

                    />

            </LinearLayout>

        </LinearLayout>


    </RelativeLayout>
</layout>