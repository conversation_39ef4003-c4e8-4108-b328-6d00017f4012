<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:fab="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewModel"
            type="com.hqt.viewmodel.BookingViewModel" />

        <import type="com.hqt.datvemaybay.Common" />

        <import type="android.view.View" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout

        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/gbgray"
        android:id="@+id/coordinatorLayout"
        android:animateLayoutChanges="true">


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:background="@color/gbgray"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <com.facebook.shimmer.ShimmerFrameLayout
                android:id="@+id/shimmer_view_container"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:orientation="vertical"
                android:visibility="visible"
                app:shimmer_duration="800">

                <LinearLayout
                    android:visibility="visible"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:weightSum="10"
                    android:orientation="horizontal">


                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_weight="10"
                        android:layout_height="match_parent"
                        android:orientation="vertical">

                        <include layout="@layout/placeholder_item_flight_history_meta" />

                        <include layout="@layout/placeholder_item_flight_history_meta" />

                        <include layout="@layout/placeholder_item_flight_history_meta" />

                        <include layout="@layout/placeholder_item_flight_history_meta" />

                        <include layout="@layout/placeholder_item_flight_history_meta" />

                        <include layout="@layout/placeholder_item_flight_history_meta" />

                        <include layout="@layout/placeholder_item_flight_history_meta" />

                        <include layout="@layout/placeholder_item_flight_history_meta" />

                        <include layout="@layout/placeholder_item_flight_history_meta" />


                    </LinearLayout>


                </LinearLayout>
            </com.facebook.shimmer.ShimmerFrameLayout>

            <LinearLayout
                android:visibility="visible"
                android:id="@+id/emptyState"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center_vertical|center_horizontal"
                android:orientation="vertical">

                <com.hqt.util.AspectRatioImageView
                    android:layout_margin="20dp"
                    android:layout_width="match_parent"
                    android:layout_height="200dp"
                    android:scaleType="centerInside"
                    android:src="@drawable/plane_meal_empty"
                    android:contentDescription="@string/no_seat" />

                <TextView
                    android:layout_width="match_parent"
                    android:gravity="center"
                    android:layout_height="wrap_content"
                    android:text="Chưa hỗ trợ đặt món ăn với chuyến bay này!" />

                <TextView
                    android:layout_width="match_parent"
                    android:gravity="center"
                    android:layout_height="wrap_content"
                    android:text="Vui lòng liên hệ tổng đài để hỗ trợ" />

                <Button

                    android:layout_marginTop="10dp"
                    android:paddingStart="40dp"
                    android:paddingEnd="40dp"
                    android:id="@+id/btnBack"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/btn_back"
                    app:backgroundTint="@color/btnColor"
                    android:textColor="#FFFFFF" />


            </LinearLayout>


            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rcvMeal"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_behavior="@string/hide_bottom_view_on_scroll_behavior"
                android:layout_marginBottom="50dp"

                />


            <androidx.core.widget.NestedScrollView

                android:layout_width="match_parent"
                android:id="@+id/data_scrollView"
                app:layout_behavior="@string/hide_bottom_view_on_scroll_behavior"
                android:layout_height="match_parent">

                <LinearLayout
                    android:id="@+id/container"
                    android:layout_marginBottom="50dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">



                </LinearLayout>


            </androidx.core.widget.NestedScrollView>


        </LinearLayout>


        <FrameLayout
            android:id="@+id/bottomLayout"
            android:layout_width="match_parent"
            app:layout_behavior="@string/hide_bottom_view_on_scroll_behavior"
            android:layout_height="match_parent">

            <LinearLayout
                android:id="@+id/bottom_sheet"
                android:orientation="vertical"
                android:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal|bottom"
                android:background="#FFFFFF">

                <include
                    android:id="@+id/selectBottomLayout"
                    layout="@layout/select_addon_bottom_layout" />

            </LinearLayout>
        </FrameLayout>


    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>