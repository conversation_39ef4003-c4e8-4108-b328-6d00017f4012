<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:id="@+id/coordinatorLayout"
        android:fitsSystemWindows="true"
        app:statusBarBackground="@color/primary_dark"
        tools:context="com.hqt.view.ui.priceboard.MonthViewActivity">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true"
            app:elevation="0dp"
            android:theme="@style/AppTheme.AppBarOverlay">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/toolbar_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fitsSystemWindows="true"
                app:contentScrim="?attr/colorPrimary"
                app:title="12bay.vn"
                app:titleEnabled="false"
                app:layout_scrollFlags="noScroll">


                <androidx.appcompat.widget.Toolbar
                    android:id="@+id/toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize"
                    app:elevation="0dp"
                    app:layout_collapseMode="pin"
                    app:layout_scrollFlags="scroll|exitUntilCollapsed"
                    app:popupTheme="@style/AppTheme.PopupOverlay" />

            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <LinearLayout
            android:layout_marginTop="?attr/actionBarSize"
            android:layout_width="match_parent"
            android:background="@color/gbgray"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:background="@color/white"
                android:padding="5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <com.mikepenz.iconics.view.IconicsImageView
                    android:padding="5dp"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    app:iiv_color="@color/primary"
                    app:iiv_icon="faw-map-marker-alt" />

                <TextView
                    android:layout_marginEnd="5dp"
                    android:id="@+id/txtOrigin"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text=""
                    tools:text="Nha trang" />

                <com.mikepenz.iconics.view.IconicsImageView
                    android:padding="5dp"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    app:iiv_color="@color/primary"
                    app:iiv_icon="faw-plane" />

                <TextView
                    android:id="@+id/txtDestination"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxLines="1"
                    android:ellipsize="end"
                    tools:text="Nha trang" />

                <LinearLayout
                    android:layout_width="fill_parent"
                    android:gravity="end"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:paddingStart="10dp"
                        android:paddingEnd="10dp"
                        android:id="@+id/cell_price_background"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical|end"
                        android:background="@drawable/price_board_cell_red"
                        android:gravity="end">

                        <TextView
                            android:id="@+id/liveText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:padding="2dp"
                            android:textColor="@color/white"
                            android:textSize="@dimen/text_size"
                            tools:text="Live X" />
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>

            <com.kizitonwose.calendarview.CalendarView
                android:id="@+id/calendarView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:animateLayoutChanges="false"
                app:cv_dayViewResource="@layout/calendar_day_layout"
                app:cv_monthHeaderResource="@layout/calendar_header"
                app:cv_monthFooterResource="@layout/calendar_footer"
                app:cv_orientation="horizontal"
                app:cv_outDateStyle="endOfGrid"
                app:cv_inDateStyle="allMonths"
                app:cv_hasBoundaries="true"
                app:cv_wrappedPageHeightAnimationDuration="0"
                app:cv_scrollMode="paged" />
        </LinearLayout>


    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>