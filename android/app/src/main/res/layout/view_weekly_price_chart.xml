<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- Chart Title -->
    <TextView
        android:id="@+id/chartTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Giá vé rẻ nhất theo tuần"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/colorPrimaryDark"
        android:gravity="center"
        android:layout_marginBottom="4dp" />

    <!-- Chart Subtitle -->
    <TextView
        android:id="@+id/chartSubtitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="SGN → HAN"
        android:textSize="14sp"
        android:textColor="@color/colorAccent"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <!-- Chart Container -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp"
        app:cardUseCompatPadding="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Bar Chart -->
            <com.db.williamchart.view.BarChartView
                android:id="@+id/weeklyPriceChart"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:layout_marginBottom="8dp"
                android:visibility="gone" />

            <!-- Loading State -->
            <TextView
                android:id="@+id/loadingText"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:text="Đang tải dữ liệu giá vé..."
                android:textSize="16sp"
                android:textColor="@color/colorAccent"
                android:gravity="center"
                android:visibility="gone" />

            <!-- Error State -->
            <TextView
                android:id="@+id/errorText"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:text="Không thể tải dữ liệu giá vé"
                android:textSize="16sp"
                android:textColor="@android:color/holo_red_dark"
                android:gravity="center"
                android:visibility="gone" />

            <!-- Chart Legend -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center"
                android:layout_marginTop="8dp">

                <View
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:background="@color/colorPrimary"
                    android:layout_marginEnd="4dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Giá thường"
                    android:textSize="12sp"
                    android:textColor="@color/colorPrimaryDark"
                    android:layout_marginEnd="16dp" />

                <View
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:background="@color/chart_cheapest_color"
                    android:layout_marginEnd="4dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Giá rẻ nhất"
                    android:textSize="12sp"
                    android:textColor="@color/colorPrimaryDark" />

            </LinearLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- Chart Instructions -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Nhấn vào cột để xem chi tiết giá vé"
        android:textSize="12sp"
        android:textColor="@color/colorAccent"
        android:gravity="center"
        android:layout_marginTop="8dp"
        android:fontFamily="sans-serif-light" />

</LinearLayout>
