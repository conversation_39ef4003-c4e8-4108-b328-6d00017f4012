<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true"
        android:background="@color/gbgray"
        android:id="@+id/coordinatorLayout"
        android:animateLayoutChanges="true">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:background="@color/primary_dark"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true"
            app:layout_constraintTop_toTopOf="parent"
            app:elevation="5dp"
            android:theme="@style/AppTheme.AppBarOverlay">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/toolbar_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:title="12bay.vn"
                android:background="@color/primary"
                android:fitsSystemWindows="true"
                app:titleEnabled="false"
                app:layout_scrollFlags="enterAlwaysCollapsed">

                <androidx.appcompat.widget.Toolbar
                    android:id="@+id/toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize"
                    app:elevation="0dp"
                    app:layout_collapseMode="pin"
                    app:layout_scrollFlags="scroll|enterAlways"
                    app:popupTheme="@style/AppTheme.PopupOverlay" />

            </com.google.android.material.appbar.CollapsingToolbarLayout>


        </com.google.android.material.appbar.AppBarLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Chart Title -->
            <TextView
                android:id="@+id/chartTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Giá vé rẻ nhất theo tuần"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/colorPrimaryDark"
                android:gravity="center"
                android:layout_marginBottom="4dp" />

            <!-- Chart Subtitle -->
            <TextView
                android:id="@+id/chartSubtitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="SGN → HAN"
                android:textSize="14sp"
                android:textColor="@color/colorAccent"
                android:gravity="center"
                android:layout_marginBottom="16dp" />

            <!-- Chart Container -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- Bar Chart -->
                    <com.github.mikephil.charting.charts.BarChart
                        android:id="@+id/weeklyPriceChart"
                        android:layout_width="match_parent"
                        android:layout_height="250dp"
                        android:layout_marginBottom="8dp"
                        android:visibility="gone"
                        tools:ignore="MissingClass" />

                    <!-- Loading State -->
                    <TextView
                        android:id="@+id/loadingText"
                        android:layout_width="match_parent"
                        android:layout_height="200dp"
                        android:text="Đang tải dữ liệu giá vé..."
                        android:textSize="16sp"
                        android:textColor="@color/colorAccent"
                        android:gravity="center"
                        android:visibility="gone" />

                    <!-- Error State -->
                    <TextView
                        android:id="@+id/errorText"
                        android:layout_width="match_parent"
                        android:layout_height="200dp"
                        android:text="Không thể tải dữ liệu giá vé"
                        android:textSize="16sp"
                        android:textColor="@android:color/holo_red_dark"
                        android:gravity="center"
                        android:visibility="gone" />

                    <!-- Chart Legend -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center"
                        android:layout_marginTop="8dp">

                        <View
                            android:layout_width="12dp"
                            android:layout_height="12dp"
                            android:background="@color/colorPrimary"
                            android:layout_marginEnd="4dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Giá thường"
                            android:textSize="12sp"
                            android:textColor="@color/colorPrimaryDark"
                            android:layout_marginEnd="16dp" />

                        <View
                            android:layout_width="12dp"
                            android:layout_height="12dp"
                            android:background="@color/chart_cheapest_color"
                            android:layout_marginEnd="4dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Giá rẻ nhất"
                            android:textSize="12sp"
                            android:textColor="@color/colorPrimaryDark" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Chart Instructions -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Nhấn vào cột để xem chi tiết giá vé"
                android:textSize="12sp"
                android:textColor="@color/colorAccent"
                android:gravity="center"
                android:layout_marginTop="8dp"
                android:fontFamily="sans-serif-light" />

        </LinearLayout>


    </androidx.coordinatorlayout.widget.CoordinatorLayout>


</layout>
