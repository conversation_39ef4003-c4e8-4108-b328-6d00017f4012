<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gbgray"
    app:statusBarBackground="@android:color/transparent"
    tools:context="com.hqt.view.ui.HomeActivity">

    <RelativeLayout
        app:layout_constraintBottom_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.hqt.util.helper.NonSwipeableViewPager
            android:id="@+id/viewpager"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@id/bottomNav"
            android:text="@string/title_home" />

        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:id="@+id/viewSnack"
            android:layout_above="@id/bottomNav" />

        <com.google.android.material.bottomnavigation.BottomNavigationView
            android:id="@+id/bottomNav"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:background="@color/white"
            app:itemIconTint="@color/selector_item_gray_color"
            app:itemTextColor="@color/selector_item_gray_color"
            app:labelVisibilityMode="labeled"
            app:menu="@menu/navigation" />
    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
