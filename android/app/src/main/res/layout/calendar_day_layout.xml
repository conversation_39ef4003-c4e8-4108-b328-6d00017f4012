<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:id="@+id/day_background"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/cell_background_on"
        android:orientation="vertical"
        android:paddingTop="5dp"
        android:paddingBottom="5dp"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        tools:ignore="MissingConstraints">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="20dp">

            <TextView
                android:id="@+id/calendarDayText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_alignParentTop="true"
                android:textAlignment="textStart"
                android:textSize="12sp"
                tools:text="21" />

            <TextView
                android:visibility="gone"
                android:id="@+id/calendarDayLunarText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_alignParentBottom="true"
                android:textAlignment="textEnd"
                android:textSize="12sp"
                tools:text="22" />
        </RelativeLayout>

        <LinearLayout
            android:id="@+id/cell_price_background"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical|center_horizontal"
            android:gravity="center">

            <TextView
                android:id="@+id/cell_data"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical|center_horizontal"
                android:gravity="center"
                android:maxLines="1"
                android:textColor="@color/white"
                android:textSize="10sp"
                android:visibility="visible"
                tools:text="100" />
        </LinearLayout>
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>