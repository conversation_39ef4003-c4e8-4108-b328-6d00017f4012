<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:background="@drawable/cell_background_hight_light"
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal|center_vertical"
            android:orientation="horizontal">

            <com.mikepenz.iconics.view.IconicsImageView
                android:id="@+id/previous_week"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center"
                android:layout_marginEnd="20dp"
                android:padding="5dp"
                app:iiv_color="@color/stt_gray"
                app:iiv_icon="faw-chevron-left" />

            <TextView
                android:id="@+id/exSixMonthText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:padding="10dp"
                android:textColor="@color/black"
                android:textSize="16sp"
                tools:text="June 2019" />

            <com.mikepenz.iconics.view.IconicsImageView
                android:id="@+id/next_week"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center"
                android:layout_marginStart="20dp"
                android:padding="5dp"
                app:iiv_color="@color/stt_gray"
                app:iiv_icon="faw-chevron-right" />
        </LinearLayout>

        <LinearLayout
            android:background="@color/light_primary"
            android:id="@+id/legendLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            tools:ignore="HardcodedText">

            <TextView
                android:id="@+id/legendText2"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text="T2"
                android:textColor="@color/black"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/legendText3"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text="T3"
                android:textColor="@color/black"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/legendText4"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text="T4"
                android:textColor="@color/black"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/legendText5"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text="T5"
                android:textColor="@color/black"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/legendText6"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text="T6"
                android:textColor="@color/black"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/legendText7"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text="T7"
                android:textColor="@color/black"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/legendText1"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text="CN"
                android:textColor="@color/black"
                android:textSize="14sp" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#eeeeee" />

    </LinearLayout>
</layout>