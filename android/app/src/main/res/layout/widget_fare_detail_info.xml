<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:custom="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewmodel"
            type="com.hqt.view.ui.flightSearch.model.FareData" />

        <import type="com.hqt.datvemaybay.Common" />

        <import type="android.view.View" />

    </data>

    <LinearLayout
        android:animateLayoutChanges="true"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/activity_vertical_margin"
        android:orientation="vertical">

        <LinearLayout

            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/showDepartureInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:padding="5dp">

                <com.mikepenz.iconics.view.IconicsTextView
                    style="@style/Text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="{faw_paper_plane}  "
                    android:textSize="10dp" />

                <TextView
                    android:id="@+id/txtLuotdiTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Thông tin chuyến bay"
                    android:textColor="#000000"
                    android:textStyle="bold" />


            </LinearLayout>

            <include

                android:id="@+id/departure_container"
                bind:flightInfo="@{viewmodel.departureFlight}"
                layout="@layout/widget_flight_detail_item" />
        </LinearLayout>

        <LinearLayout
            android:visibility="@{viewmodel.roundTrip ? View.VISIBLE : View.GONE}"
            android:layout_marginTop="10dp"
            android:id="@+id/return_flight_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <include

                android:id="@+id/return_container"
                bind:flightInfo="@{viewmodel.returnFlight}"
                layout="@layout/widget_flight_detail_item" />
        </LinearLayout>

    </LinearLayout>

</layout>
