<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:id="@+id/row_root"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/default_row_header_width"
    android:layout_height="@dimen/cell_height">

    <LinearLayout
        android:id="@+id/row_header_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:orientation="vertical">

        <LinearLayout
            android:padding="4dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:paddingBottom="0dp"
                android:id="@+id/row_header_textview"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="start"
                android:maxLines="1"
                android:textSize="12sp"
                android:textStyle="bold"
                tools:text="Row line1" />

            <TextView
                android:paddingTop="0dp"
                android:id="@+id/row_header_textview_line_2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="start"
                android:maxLines="1"
                android:textSize="12sp"
                android:textStyle="bold"
                tools:text="Row Line2" />
        </LinearLayout>

    </LinearLayout>

    <View
        android:layout_width="2dp"
        android:layout_height="match_parent"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="0dp"
        android:background="@color/fui_transparent" />
</RelativeLayout>