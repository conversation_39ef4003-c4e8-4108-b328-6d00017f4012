<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="user"
            type="com.hqt.data.model.User" />

        <import type="android.view.View" />

        <import type="com.hqt.util.Helper.ValidatorType" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:id="@+id/coordinatorLayout"
        android:fitsSystemWindows="true"
        android:addStatesFromChildren="true"
        android:background="@color/gbgray">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true"
            app:elevation="0dp"
            android:background="@color/gbgray"
            android:theme="@style/AppTheme.AppBarOverlay">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/toolbar_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fitsSystemWindows="true"
                app:contentScrim="?attr/colorPrimary"
                app:title="12bay.vn"
                app:titleEnabled="false"
                app:layout_scrollFlags="noScroll">

                <RelativeLayout
                    android:fitsSystemWindows="true"
                    android:adjustViewBounds="true"
                    android:layout_width="match_parent"
                    android:layout_height="150dp">

                    <ImageView
                        android:layout_alignParentTop="true"
                        android:id="@+id/headerBG"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scaleType="centerCrop"
                        android:contentDescription="@string/app_name"
                        app:srcCompat="@drawable/world_background"
                        android:alpha="1" />

                </RelativeLayout>

                <androidx.appcompat.widget.Toolbar

                    android:id="@+id/toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize"
                    app:elevation="0dp"
                    app:layout_collapseMode="pin"
                    app:layout_scrollFlags="scroll|exitUntilCollapsed"
                    app:popupTheme="@style/AppTheme.PopupOverlay" />


            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <LinearLayout
            tools:ignore="MissingPrefix"
            android:layout_marginTop="?attr/actionBarSize"
            android:layout_width="match_parent"
            android:background="@drawable/transparent"
            android:layout_height="match_parent">

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:paddingTop="10dp"
                        android:paddingStart="15dp"
                        android:paddingEnd="15dp"
                        android:paddingBottom="15dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <RelativeLayout

                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal|center_vertical">


                            <de.hdodenhof.circleimageview.CircleImageView
                                android:id="@+id/profile_image"
                                android:layout_width="80dp"
                                android:layout_height="80dp"
                                android:src="@drawable/logo_mini"
                                app:civ_border_width="2dp"
                                app:civ_border_color="#ffffff" />


                        </RelativeLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical|center_horizontal">

                            <com.mikepenz.iconics.view.IconicsImageView
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                android:paddingRight="5dp"
                                app:iiv_color="@color/google_yellow"
                                app:iiv_icon="gmd_stars" />

                            <TextView
                                android:id="@+id/text_point"
                                android:padding="5dp"
                                android:textStyle="bold"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="12sp"
                                android:text="@{user.point.text}"
                                tools:text="100 Điểm" />

                        </LinearLayout>

                        <androidx.cardview.widget.CardView
                            android:layout_width="match_parent"
                            android:elevation="0dp"
                            app:cardElevation="0dp"
                            android:layout_height="wrap_content">

                            <LinearLayout
                                android:padding="15dp"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content">


                                    <com.google.android.material.textfield.TextInputLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content">

                                        <AutoCompleteTextView
                                            android:id="@+id/inputPaxLastName"
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:completionThreshold="1"
                                            android:maxLines="1"
                                            android:inputType="textPersonName|textCapWords"
                                            android:hint="Họ và tên "
                                            android:autofillHints="Họ: (vd Nguyễn Văn A)"
                                            bind:textChange="@{`fullName`}"
                                            android:text="@={user.userName}" />
                                    </com.google.android.material.textfield.TextInputLayout>
                                    <!--                                bind:textChange="@{`fullName`}"-->
                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content">

                                    <com.google.android.material.textfield.TextInputLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content">

                                        <com.google.android.material.textfield.TextInputEditText
                                            android:enabled="false"
                                            android:id="@+id/inputPaxFirstName"
                                            android:layout_width="match_parent"
                                            android:maxLines="1"
                                            android:layout_height="wrap_content"
                                            android:inputType="textPhonetic|textCapWords"
                                            android:hint="Số điện thoại"
                                            bind:textChange="@{`phone`}"
                                            android:text="@={user.phoneNumber}" />

                                    </com.google.android.material.textfield.TextInputLayout>
                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content">

                                    <com.google.android.material.textfield.TextInputLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content">

                                        <com.google.android.material.textfield.TextInputEditText
                                            android:id="@+id/inputPaxBirthday"
                                            android:inputType="date"
                                            android:focusable="false"
                                            android:maxLines="1"
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:hint="Ngày sinh:"
                                            bind:textChange="@{`birthDate`}"
                                            android:text="@={user.birthDay}" />


                                    </com.google.android.material.textfield.TextInputLayout>
                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content">

                                    <com.google.android.material.textfield.TextInputLayout

                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content">

                                        <com.google.android.material.textfield.TextInputEditText
                                            android:id="@+id/inputPaxIdNumber"
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:maxLines="1"
                                            android:lines="1"
                                            android:hint="Email: "
                                            android:inputType="textEmailAddress"
                                            bind:textChange="@{`required|email`}"
                                            android:text="@={user.userEmail}" />

                                    </com.google.android.material.textfield.TextInputLayout>
                                </LinearLayout>

                                <EditText
                                    android:id="@+id/none_edit"
                                    android:visibility="visible"
                                    android:layout_width="wrap_content"
                                    android:layout_height="1dp" />
                            </LinearLayout>

                        </androidx.cardview.widget.CardView>


                    </LinearLayout>


                    <LinearLayout
                        android:paddingTop="10dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center|center_vertical"
                        android:paddingBottom="10dp"
                        android:orientation="vertical">

                        <Button
                            android:layout_marginLeft="20dp"
                            android:layout_marginRight="20dp"
                            android:id="@+id/btnSaved"
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:textSize="15sp"
                            android:background="@drawable/button_gradient"
                            android:layout_gravity="center_horizontal"
                            style="@style/Theme.MyApp.Button"
                            android:text="Lưu"
                            android:textColor="#FFFFFF" />

                    </LinearLayout>


                </LinearLayout>
            </ScrollView>

        </LinearLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>