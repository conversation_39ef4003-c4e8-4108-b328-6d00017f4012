<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true"
        tools:ignore="MissingPrefix"
        android:id="@+id/coordinatorLayout"
        tools:context="com.hqt.view.ui.train.BusSearchActivity">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/primary"
            android:fitsSystemWindows="true"
            android:orientation="vertical">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                app:elevation="0dp"
                app:layout_collapseMode="pin"
                app:layout_scrollFlags="scroll|exitUntilCollapsed"
                android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
                app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

            <ScrollView
                android:id="@+id/scrollView1"
                android:layout_alignParentTop="true"
                android:scrollbars="none"
                android:background="@color/gbgray"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/bg_gradient"
                        android:contentDescription="@string/app_name"
                        android:weightSum="3"
                        android:scaleType="centerCrop">

                        <RelativeLayout
                            android:paddingBottom="25dp"
                            android:id="@+id/header"
                            android:layout_width="match_parent"
                            android:adjustViewBounds="true"
                            android:layout_height="wrap_content">

                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:scaleType="center"
                                android:layout_alignTop="@+id/select"
                                android:layout_alignBottom="@+id/select"
                                app:srcCompat="@drawable/world" />

                            <LinearLayout
                                android:id="@+id/change"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center_vertical|center_horizontal"
                                android:layout_alignTop="@+id/select"
                                android:layout_alignBottom="@+id/select"
                                android:orientation="vertical"
                                android:paddingTop="?attr/actionBarSize"
                                android:paddingRight="10dp"
                                android:paddingLeft="10dp"
                                android:paddingBottom="30dp">

                                <View
                                    android:layout_width="1dp"
                                    android:layout_height="50dp"
                                    android:background="@color/diver_color" />

                                <ImageButton
                                    android:id="@+id/btnTrain"
                                    android:layout_width="50dp"
                                    android:layout_height="50dp"
                                    android:background="@drawable/round_button"
                                    android:gravity="center_vertical|center_horizontal"
                                    app:srcCompat="@drawable/ic_bus_white"
                                    android:textColor="#fff" />

                                <View
                                    android:layout_width="1dp"
                                    android:layout_height="fill_parent"
                                    android:background="@color/diver_color" />
                            </LinearLayout>

                            <LinearLayout

                                android:id="@+id/select"
                                android:layout_width="fill_parent"
                                android:layout_height="wrap_content"
                                android:layout_alignParentTop="true"
                                android:layout_toRightOf="@+id/change"
                                android:orientation="vertical"
                                android:layout_marginLeft="20dp"
                                android:paddingBottom="40dp"
                                android:paddingTop="?attr/actionBarSize">

                                <LinearLayout
                                    android:id="@+id/select_origin"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_toRightOf="@+id/change"
                                    android:orientation="vertical"
                                    android:visibility="visible">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textColor="@color/white"
                                        android:text="@string/txt_dep_from" />

                                    <TextView
                                        android:id="@+id/originCode"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textColor="@color/white"
                                        android:textStyle="bold"
                                        android:textSize="20sp"
                                        android:text="Chọn nơi đi" />

                                    <TextView
                                        android:id="@+id/originName"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginRight="10dp"
                                        android:textColor="@color/white"
                                        android:text="" />
                                </LinearLayout>

                                <LinearLayout
                                    android:id="@+id/select_destination"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentTop="true"
                                    android:layout_toRightOf="@+id/change"
                                    android:layout_marginTop="15dp"
                                    android:orientation="vertical"
                                    android:visibility="visible">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textColor="@color/white"
                                        android:text="@string/txt_dep_to" />

                                    <TextView
                                        android:id="@+id/destinationCode"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textColor="@color/white"
                                        android:textStyle="bold"
                                        android:textSize="20sp"
                                        android:text="Chọn nơi đến" />

                                    <TextView
                                        android:id="@+id/destinationName"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginRight="10dp"
                                        android:textColor="@color/white"
                                        android:text="" />
                                </LinearLayout>
                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/swapRouteLayout"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentRight="true"
                                android:layout_alignTop="@+id/select"
                                android:layout_alignBottom="@+id/select"
                                android:gravity="center_vertical|center_horizontal"
                                android:orientation="vertical"
                                android:paddingTop="?attr/actionBarSize"
                                android:paddingRight="10dp"
                                android:paddingLeft="10dp"
                                android:paddingBottom="30dp">

                                <View
                                    android:layout_width="1dp"
                                    android:layout_height="60dp"
                                    android:background="@color/diver_color" />

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:id="@+id/swapRoute"
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    app:iiv_color="@color/white"
                                    app:iiv_icon="gmd_import_export" />

                                <View
                                    android:layout_width="1dp"
                                    android:layout_height="fill_parent"
                                    android:background="@color/diver_color" />
                            </LinearLayout>

                        </RelativeLayout>
                    </LinearLayout>


                    <LinearLayout
                        android:layout_marginTop="-50dp"
                        android:layout_width="match_parent"
                        android:paddingLeft="20dp"
                        android:paddingRight="20dp"
                        android:layout_height="match_parent">

                        <LinearLayout
                            android:id="@+id/selectDepDate"
                            android:layout_marginTop="10dp"
                            android:layout_width="match_parent"
                            android:background="@drawable/corner_full"
                            android:layout_height="wrap_content">

                            <LinearLayout
                                android:animateLayoutChanges="true"
                                android:padding="10dp"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <TextView
                                    android:textSize="12sp"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Chọn ngày khởi hành:" />

                                <LinearLayout
                                    android:padding="10dp"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:id="@+id/txtDepDate"
                                        android:layout_gravity="center"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textStyle="bold"
                                        tools:text="xxxxxxx" />

                                    <LinearLayout
                                        android:layout_width="fill_parent"
                                        android:layout_height="fill_parent"
                                        android:gravity="end|center_vertical">

                                        <com.mikepenz.iconics.view.IconicsImageView
                                            android:layout_marginEnd="0dp"
                                            android:layout_width="20dp"
                                            android:layout_height="20dp"
                                            app:iiv_size="16dp"
                                            app:iiv_color="@color/stt_gray"
                                            app:iiv_icon="faw-calendar_alt" />
                                    </LinearLayout>


                                </LinearLayout>

                            </LinearLayout>

                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <LinearLayout
                                android:animateLayoutChanges="true"
                                android:paddingTop="10dp"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <TextView
                                    android:paddingStart="20dp"
                                    android:textSize="12sp"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Hành trình đề xuất:" />

                                <HorizontalScrollView
                                    android:scrollbars="none"
                                    android:paddingTop="10dp"
                                    android:paddingBottom="10dp"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal">

                                    <LinearLayout
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:orientation="horizontal"
                                        android:id="@+id/suggestTripContainer" />

                                </HorizontalScrollView>

                            </LinearLayout>

                        </LinearLayout>
                    </LinearLayout>

                    <androidx.appcompat.widget.AppCompatButton
                        android:layout_marginTop="10dp"
                        android:layout_marginLeft="25dp"
                        android:layout_marginRight="25dp"
                        android:id="@+id/search"
                        android:background="@drawable/button_gradient"
                        android:textColor="#FFFFFF"
                        android:layout_width="wrap_content"
                        android:layout_gravity="center"
                        android:paddingLeft="50dp"
                        android:paddingRight="50dp"
                        android:textAlignment="gravity"
                        android:textSize="16sp"
                        android:layout_height="wrap_content"
                        android:gravity="center_horizontal|center_vertical"
                        android:text="TÌM KIẾM" />

                </LinearLayout>
            </ScrollView>

        </RelativeLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</layout>