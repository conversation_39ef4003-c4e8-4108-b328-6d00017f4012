<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:id="@+id/coordinatorLayout"
        android:fitsSystemWindows="true"
        android:animateLayoutChanges="true"
        android:background="@color/gbgray"
        tools:context="com.hqt.view.ui.HomeActivity">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true"
            app:elevation="0dp"
            android:theme="@style/AppTheme.AppBarOverlay">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/toolbar_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fitsSystemWindows="true"
                app:contentScrim="?attr/colorPrimary"
                app:title="12bay.vn"
                app:titleEnabled="false"
                app:layout_scrollFlags="noScroll">

                <androidx.appcompat.widget.Toolbar
                    android:id="@+id/toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize"
                    app:elevation="0dp"
                    app:layout_collapseMode="pin"
                    app:layout_scrollFlags="scroll|exitUntilCollapsed"
                    app:popupTheme="@style/AppTheme.PopupOverlay" />

            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="?attr/actionBarSize"
            android:orientation="horizontal">

            <com.facebook.shimmer.ShimmerFrameLayout
                android:id="@+id/shimmer_view_container"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:orientation="vertical"
                android:visibility="visible"
                app:shimmer_duration="800">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:weightSum="10"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical">

                        <include layout="@layout/placeholder_item_post" />

                        <include layout="@layout/placeholder_item_post" />

                        <include layout="@layout/placeholder_item_post" />

                        <include layout="@layout/placeholder_item_post" />

                        <include layout="@layout/placeholder_item_post" />

                        <include layout="@layout/placeholder_item_post" />

                        <include layout="@layout/placeholder_item_post" />


                    </LinearLayout>


                </LinearLayout>
            </com.facebook.shimmer.ShimmerFrameLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:visibility="gone"
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:clipToPadding="false"
                android:background="@color/gbgray"
                android:layout_height="match_parent" />
        </LinearLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>