<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:id="@+id/cell_container"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/cell_height"
    android:gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/cell_background"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:id="@+id/cell_price_background"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:layout_width="match_parent"
            android:gravity="center"
            android:layout_gravity="center_vertical|center_horizontal"
            android:layout_height="wrap_content">

            <TextView
                android:padding="2dp"
                android:id="@+id/cell_data"
                android:layout_width="40dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical|center_horizontal"
                android:gravity="center"
                android:maxLines="1"
                android:textColor="@color/white"
                android:textSize="@dimen/text_size"
                android:visibility="visible"
                tools:text="100" />
        </LinearLayout>
    </LinearLayout>

</LinearLayout>
