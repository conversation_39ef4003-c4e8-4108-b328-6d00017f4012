<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="200dp"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardElevation="0dp"
    app:cardCornerRadius="5dp"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_gravity="center"
        android:padding="0dp">

        <ImageView
            android:contentDescription="@string/txt_chat"
            android:id="@+id/imageView"
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:scaleType="centerCrop"
            app:srcCompat="@drawable/top_banner" />

        <TextView
            android:id="@+id/nameTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:paddingStart="5dp"
            android:text="Item"
            android:textAppearance="@style/TextAppearance.AppCompat.Body1"
            android:textStyle="bold" />

        <TextView
            android:paddingBottom="5dp"
            android:paddingStart="5dp"
            android:id="@+id/subtitleTextView"
            android:visibility="visible"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxLines="2"
            android:gravity="center"
            android:text="subtitle"
            android:ellipsize="end"
            android:textAppearance="@style/TextAppearance.AppCompat.Body1"
            android:textSize="12sp" />

    </LinearLayout>

</androidx.cardview.widget.CardView>