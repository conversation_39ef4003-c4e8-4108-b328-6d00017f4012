<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="flightHistory"
            type="com.hqt.view.ui.flighthistory.data.model.FlightHistoryItem" />

        <import type="com.hqt.datvemaybay.Common" />

    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:background="@color/fui_transparent"
        android:animateLayoutChanges="true"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_behavior="android.support.design.widget.BottomSheetBehavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@color/fui_transparent"
            app:cardBackgroundColor="@color/fui_transparent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:weightSum="12"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_weight="7"
                        android:gravity="bottom"
                        android:layout_width="0dp"
                        android:layout_height="fill_parent"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/closeText"
                            android:paddingTop="2dp"
                            android:paddingBottom="2dp"
                            android:paddingStart="5dp"
                            android:paddingEnd="10dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Đóng" />

                        <LinearLayout
                            android:layoutDirection="ltr"
                            android:background="#9A403D3D"
                            android:padding="5dp"
                            android:layout_width="fill_parent"
                            android:elevation="2dp"
                            android:orientation="vertical"
                            android:layout_gravity="bottom"
                            android:layout_height="wrap_content">

                            <TextView
                                android:textColor="@color/white"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                tools:text="VJ110"
                                android:textSize="20sp"
                                android:text="@{flightHistory.number}" />

                            <TextView
                                android:textColor="@color/white"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="14sp"
                                tools:text="VIETJET AIR"
                                android:text="@{flightHistory.airline.name}" />


                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_weight="5"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:background="#9A403D3D"
                        android:orientation="vertical">

                        <com.smarteist.autoimageslider.SliderView
                            android:id="@+id/imageSlider"
                            android:layout_width="match_parent"
                            android:layout_height="80dp"
                            app:sliderAnimationDuration="1500"
                            app:sliderAutoCycleDirection="back_and_forth"
                            app:sliderAutoCycleEnabled="false"
                            app:sliderIndicatorAnimationDuration="600"
                            app:sliderIndicatorGravity="center_horizontal|bottom"
                            app:sliderIndicatorMargin="15dp"
                            app:sliderIndicatorOrientation="horizontal"
                            app:sliderIndicatorPadding="3dp"
                            app:sliderIndicatorRadius="1dp"
                            app:sliderIndicatorSelectedColor="#FFF"
                            app:sliderIndicatorUnselectedColor="#5A5A5A"
                            app:sliderScrollTimeInSec="2"
                            app:sliderStartAutoCycle="false" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/mainContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:weightSum="12"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_weight="7"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <LinearLayout
                                android:padding="5dp"
                                android:background="@color/white"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_horizontal"
                                    android:orientation="horizontal"
                                    android:weightSum="8">

                                    <LinearLayout
                                        android:padding="5dp"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="3"
                                        android:gravity="center_horizontal|center_vertical"
                                        android:orientation="vertical">

                                        <TextView
                                            android:layout_gravity="center_horizontal|center_vertical"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:paddingLeft="3dp"
                                            android:paddingTop="2dp"
                                            android:paddingRight="3dp"
                                            android:paddingBottom="2dp"
                                            android:singleLine="true"
                                            android:text="@{flightHistory.airport.origin.code}"
                                            android:textColor="#7C7C7C"
                                            android:textSize="16sp"
                                            android:textStyle="bold" />

                                        <TextView
                                            android:layout_gravity="center_horizontal|center_vertical"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="@{flightHistory.airport.origin.city}"
                                            android:textColor="#000000"
                                            android:maxLines="1"
                                            android:textSize="14sp"
                                            android:textStyle="bold" />


                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_width="0dp"
                                        android:layout_height="match_parent"
                                        android:layout_weight="2"
                                        android:gravity="center_horizontal|center_vertical"
                                        android:layout_gravity="center_vertical"
                                        android:orientation="vertical">

                                        <ImageView
                                            android:contentDescription="@string/app_name"
                                            android:layout_width="50dp"
                                            android:layout_height="wrap_content"
                                            android:layout_gravity="center_horizontal|center_vertical"
                                            app:srcCompat="@drawable/flight_history_icon" />


                                    </LinearLayout>

                                    <LinearLayout
                                        android:padding="5dp"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="3"
                                        android:gravity="center_horizontal|center_vertical"
                                        android:orientation="vertical">

                                        <TextView
                                            android:layout_gravity="center_horizontal|center_vertical"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:paddingLeft="3dp"
                                            android:paddingTop="2dp"
                                            android:paddingRight="3dp"
                                            android:paddingBottom="2dp"
                                            android:singleLine="true"
                                            android:text="@{flightHistory.airport.destination.code}"
                                            android:textColor="#7C7C7C"
                                            android:textSize="16sp"
                                            android:textStyle="bold" />

                                        <TextView
                                            android:layout_gravity="center_horizontal|center_vertical"
                                            android:id="@+id/txtDiThoiGianDen"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="@{flightHistory.airport.destination.city}"
                                            android:textColor="#000000"
                                            android:maxLines="1"
                                            android:textSize="14sp"
                                            android:textStyle="bold" />


                                    </LinearLayout>

                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:animateLayoutChanges="true"
                                    android:orientation="vertical">

                                    <SeekBar
                                        android:paddingStart="0dp"
                                        android:paddingEnd="0dp"
                                        android:splitTrack="false"
                                        android:visibility="visible"
                                        android:id="@+id/flightProgress"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:max="101"
                                        android:progress="@{flightHistory.getProgress()}"
                                        android:progressDrawable="@drawable/custom_seekbar"
                                        android:thumb="@drawable/flight_history_icon_mini" />

                                    <LinearLayout
                                        bind:visibility="@{flightHistory.status.live}"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="@{flightHistory.live.getFlightTextShort()}" />

                                        <LinearLayout
                                            android:layout_width="fill_parent"
                                            android:layout_height="match_parent"
                                            android:layoutDirection="rtl"
                                            android:layout_alignParentRight="true">

                                            <TextView
                                                android:gravity="end"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:text="@{flightHistory.live.getFlightRemainTextShort()}" />


                                        </LinearLayout>

                                    </LinearLayout>
                                </LinearLayout>

                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="1dp"
                                    android:layout_marginTop="5dp"
                                    android:layout_marginBottom="5dp"
                                    android:background="@drawable/gradientdiv" />

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:gravity="center"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Máy bay: "
                                        android:textSize="14sp" />

                                    <LinearLayout
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="@{flightHistory.aircraft.model.text}"
                                            android:singleLine="true"
                                            android:textColor="#000000"
                                            android:maxLines="1"
                                            android:textSize="14sp"
                                            android:textStyle="bold" />
                                    </LinearLayout>


                                </LinearLayout>
                            </LinearLayout>
                        </LinearLayout>

                        <View
                            android:layout_width="2sp"
                            android:layout_height="match_parent"
                            android:background="@drawable/gradientdiv_vertical" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="5"
                            android:orientation="vertical">

                            <LinearLayout
                                android:background="@color/white"
                                android:padding="5dp"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical">

                                <LinearLayout

                                    android:visibility="visible"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center"
                                    android:orientation="vertical">

                                    <com.hqt.util.AspectRatioImageView
                                        android:id="@+id/diLogo"
                                        android:layout_width="match_parent"
                                        android:layout_height="40dp"
                                        android:layout_gravity="center"
                                        android:scaleType="fitCenter"
                                        bind:imageUrl="@{flightHistory.airline.logo}" />


                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="fill_parent"
                                    android:gravity="bottom"
                                    android:orientation="vertical">

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content">


                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="Độ cao: "
                                            android:layout_gravity="center"
                                            android:maxLines="1"
                                            android:textSize="14sp" />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="@{ flightHistory.getCurrentPosition().altitude +` m`}"
                                            android:layout_gravity="center"
                                            android:textColor="#000000"
                                            android:maxLines="1"
                                            android:textStyle="bold"
                                            android:textSize="14sp" />
                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content">


                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="Vận tốc: "
                                            android:layout_gravity="center"
                                            android:maxLines="1"
                                            android:textSize="14sp" />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="@{ flightHistory.getCurrentPosition().speed +` km/h`}"
                                            android:layout_gravity="center"
                                            android:textColor="#000000"
                                            android:textStyle="bold"
                                            android:maxLines="1"
                                            android:textSize="14sp" />
                                    </LinearLayout>

                                    <View
                                        android:layout_width="match_parent"
                                        android:layout_height="1dp"
                                        android:layout_marginTop="5dp"
                                        android:layout_marginBottom="5dp"
                                        android:background="@drawable/gradientdiv" />

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content">


                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="Mã hiệu: "
                                            android:layout_gravity="center"
                                            android:maxLines="1"
                                            android:textSize="14sp" />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="@{flightHistory.aircraft.registration}"
                                            android:layout_gravity="center"
                                            android:textColor="#000000"
                                            android:maxLines="1"
                                            tools:text="VN110 "
                                            android:textSize="14sp"
                                            android:textStyle="bold" />
                                    </LinearLayout>
                                </LinearLayout>
                            </LinearLayout>

                        </LinearLayout>
                    </LinearLayout>


                    <LinearLayout
                        android:visibility="gone"
                        android:layout_marginStart="2dp"
                        android:layout_marginEnd="2dp"
                        android:layout_marginBottom="5dp"
                        android:layout_width="match_parent"
                        android:weightSum="10"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:baselineAligned="false">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_weight="5"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_marginTop="10dp"
                                android:background="@drawable/corner_full"
                                android:padding="10dp"
                                android:layout_marginEnd="5dp"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:gravity="center_horizontal"
                                    android:text="Giờ khởi hành" />


                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal">

                                    <TextView

                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Lịch trình: " />

                                    <TextView
                                        android:gravity="end"
                                        android:layout_width="fill_parent"
                                        android:layout_height="wrap_content"
                                        style="@style/Text"
                                        android:textSize="14sp"
                                        android:textStyle="bold"
                                        android:text="@{Common.dateToString(flightHistory.time.scheduled.departure,`HH:mm`)}" />


                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal">

                                    <TextView

                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Thực tế: " />

                                    <TextView
                                        style="@style/Text"
                                        android:gravity="end"
                                        android:textSize="14sp"
                                        android:textStyle="bold"
                                        android:layout_width="fill_parent"
                                        android:layout_height="wrap_content"
                                        android:text="@{Common.dateToString(flightHistory.time.real.departure,`HH:mm`)}" />


                                </LinearLayout>
                            </LinearLayout>
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_weight="5"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_marginTop="10dp"
                                android:background="@drawable/corner_full"
                                android:padding="10dp"
                                android:layout_marginStart="5dp"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:gravity="center_horizontal"
                                    android:text="Giờ hạ cánh" />

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal">

                                    <TextView

                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Lịch trình: " />

                                    <TextView
                                        style="@style/Text"
                                        android:gravity="end"
                                        android:textSize="14sp"
                                        android:textStyle="bold"
                                        android:layout_width="fill_parent"
                                        android:layout_height="wrap_content"
                                        android:text="@{Common.dateToString(flightHistory.time.scheduled.arrival,`HH:mm`)}" />


                                </LinearLayout>

                                <LinearLayout
                                    bind:visibility="@{!flightHistory.status.live}"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal">

                                    <TextView

                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Thực tế: " />

                                    <TextView
                                        style="@style/Text"
                                        android:layout_width="fill_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="end"
                                        android:text="@{Common.dateToString(flightHistory.time.real.arrival,`HH:mm`)}"
                                        android:textSize="14sp"
                                        android:textStyle="bold" />


                                </LinearLayout>

                                <LinearLayout
                                    android:visibility="gone"
                                    bind:visibility="@{flightHistory.status.live}"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Dự kiến: " />

                                    <View
                                        android:layout_width="10dp"
                                        android:layout_height="10dp"
                                        bind:viewColor="@{flightHistory.status.getIconColor()}" />

                                    <TextView
                                        style="@style/Text"
                                        android:gravity="end"
                                        android:textSize="14sp"
                                        android:textStyle="bold"
                                        android:layout_width="fill_parent"
                                        android:layout_height="wrap_content"
                                        android:text="@{Common.dateToString(flightHistory.time.estimated.arrival,`HH:mm`)}" />


                                </LinearLayout>
                            </LinearLayout>
                        </LinearLayout>

                    </LinearLayout>


                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>