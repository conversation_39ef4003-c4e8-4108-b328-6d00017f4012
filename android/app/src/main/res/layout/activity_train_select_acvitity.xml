<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true"
        android:background="@color/primary_dark"
        app:statusBarBackground="@color/primary_dark"
        tools:context="com.hqt.view.ui.train.TrainSelectActivity">

        <RelativeLayout
            app:layout_constraintBottom_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:visibility="gone"
                android:background="@color/primary"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:elevation="0dp"
                app:layout_collapseMode="pin"
                app:layout_scrollFlags="scroll|enterAlways"
                app:popupTheme="@style/AppTheme.PopupOverlay" />

            <com.hqt.util.helper.NonSwipeableViewPager
                android:id="@+id/viewpager"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_alignParentStart="true"
                android:text="@string/title_home" />

        </RelativeLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
