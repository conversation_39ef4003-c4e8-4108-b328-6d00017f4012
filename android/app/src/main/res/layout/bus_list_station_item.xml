<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="station"
            type="com.hqt.data.model.response.StationPoints" />
    </data>

    <androidx.cardview.widget.CardView
        android:id="@+id/card_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="5dp"
        app:cardElevation="0dp"
        android:layout_marginBottom="@dimen/cardMarginVertical"
        android:layout_marginLeft="@dimen/cardMarginHorizontal"
        android:layout_marginRight="@dimen/cardMarginHorizontal"
        android:layout_marginTop="@dimen/cardMarginVertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:padding="10dp"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <RadioButton

                    android:padding="5dp"
                    android:layout_gravity="center_horizontal|center_vertical"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:id="@+id/checkBox" />


                <LinearLayout
                    android:paddingLeft="5dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_gravity="center_vertical|center_horizontal">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content">

                        <TextView
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="18sp"
                            android:paddingEnd="5dp"
                            android:textStyle="bold"
                            tools:text="80:10"
                            android:text="@{station.time }"
                            android:textColor="#000000" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="16sp"
                            tools:text="Ho Chi MInh City"
                            android:text="@{station.name }"
                            android:textColor="#000000" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        tools:text="Tan Son Nhat Airport"
                        android:layout_height="wrap_content"
                        android:textSize="14sp"
                        android:maxLines="1"
                        android:text="@{station.address }"
                        android:layout_toRightOf="@+id/airPortName" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="fill_parent"
                    android:gravity="right|center_vertical"
                    android:layout_height="fill_parent">

                    <com.mikepenz.iconics.view.IconicsImageView
                        android:id="@+id/btn_open_map"
                        android:layout_gravity="center_horizontal|center_vertical"
                        android:padding="5dp"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        app:iiv_color="@color/primary_dark"
                        app:iiv_icon="faw-map-marker-alt" />
                </LinearLayout>
            </LinearLayout>

        </LinearLayout>
    </androidx.cardview.widget.CardView>
</layout>