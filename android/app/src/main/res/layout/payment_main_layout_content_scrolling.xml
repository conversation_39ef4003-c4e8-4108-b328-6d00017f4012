<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.hqt.view.ui.payment.ui.main.MainViewModel" />

        <import type="com.hqt.datvemaybay.Common" />

        <import type="android.view.View" />
    </data>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        tools:context="com.hqt.datvemaybay.PnrActivity"
        android:background="#F2F1F1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="30dp"
            android:orientation="vertical">


            <androidx.cardview.widget.CardView
                android:layout_marginTop="5dp"
                android:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="2dp"
                app:cardElevation="0dp"
                style="@style/CardViewStyle.Light"
                android:layout_marginBottom="5dp"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                app:cardPreventCornerOverlap="false">

                <LinearLayout
                    android:padding="10dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_horizontal|center_vertical">

                        <com.hqt.util.AspectRatioImageView
                            android:gravity="center_horizontal|center_vertical"
                            android:layout_width="45dp"
                            android:layout_height="45dp"
                            android:scaleType="centerCrop"
                            android:background="@mipmap/ic_launcher" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/viewBookingInfo"
                        android:layoutDirection="rtl"
                        android:layout_marginLeft="5dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/white"
                        android:padding="5dp"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical|end"
                            android:padding="10dp">

                            <com.mikepenz.iconics.view.IconicsImageView
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                app:iiv_color="@color/primary"
                                app:iiv_icon="gmd_expand_more" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_marginLeft="5dp"
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:background="@color/white"
                            android:padding="5dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_gravity="center_horizontal"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textStyle="bold"
                                style="@style/Text"
                                tools:text="Title"
                                android:text="@{viewModel.orderInfo.title}" />

                            <TextView
                                android:layout_gravity="center_horizontal"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="12sp"
                                tools:text="sub Title"
                                android:text="@{viewModel.orderInfo.subtitle}" />

                        </LinearLayout>


                    </LinearLayout>

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="10dp"
                android:text="Chọn phương thức thanh toán" />


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">


                <androidx.recyclerview.widget.RecyclerView

                    android:id="@+id/recyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="fill_parent"
                    android:paddingLeft="5dp"
                    android:paddingRight="5dp" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>
</layout>