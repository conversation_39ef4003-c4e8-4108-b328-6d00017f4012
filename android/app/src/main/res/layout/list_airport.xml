<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/coordinatorLayout"
    android:fitsSystemWindows="true"
    tools:context="com.hqt.view.ui.HomeActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar"
        android:visibility="visible"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true"
        app:elevation="0dp"
        android:background="@drawable/bg_gradient"
        android:theme="@style/AppTheme.AppBarOverlay">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/toolbar_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fitsSystemWindows="true"
            app:contentScrim="?attr/colorPrimary"
            app:title="12bay.vn"
            app:titleEnabled="false"
            app:layout_scrollFlags="noScroll">

            <ImageView
                android:id="@+id/headerBG"
                android:layout_width="match_parent"
                android:layout_height="150dp"
                android:scaleType="centerCrop"
                android:contentDescription="@string/app_name"
                android:fitsSystemWindows="true"
                android:adjustViewBounds="true"
                app:srcCompat="@drawable/world"
                android:alpha="1" />

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                app:elevation="0dp"
                app:layout_collapseMode="pin"
                app:layout_scrollFlags="scroll|exitUntilCollapsed"
                app:popupTheme="@style/AppTheme.PopupOverlay" />

        </com.google.android.material.appbar.CollapsingToolbarLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <LinearLayout
        android:layout_marginTop="?attr/actionBarSize"
        android:layout_width="match_parent"
        android:layout_height="fill_parent"
        android:orientation="vertical">

        <LinearLayout
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:layout_width="0px"
            android:layout_height="0px" />

        <LinearLayout
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:padding="5dp"
            android:layout_width="match_parent"
            android:background="@drawable/corner_full"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <com.mikepenz.iconics.view.IconicsTextView
                android:clickable="false"
                android:id="@+id/Payment"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:gravity="center_vertical|center_horizontal"
                android:text="{faw_search}"
                android:textColor="@color/primary"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/search"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:hint="Tìm thành phố hoặc sân bay"
                android:textSize="16sp" />

        </LinearLayout>

        <com.facebook.shimmer.ShimmerFrameLayout
            android:paddingLeft="5dp"
            android:paddingRight="5dp"
            android:layout_marginTop="?attr/actionBarSize"
            android:id="@+id/shimmer_view_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:orientation="vertical"
            android:visibility="gone"
            app:shimmer_duration="800">

            <LinearLayout
                android:layout_below="@+id/search"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <include layout="@layout/placeholder_item_train" />

                <include layout="@layout/placeholder_item_train" />

                <include layout="@layout/placeholder_item_train" />

                <include layout="@layout/placeholder_item_train" />

                <include layout="@layout/placeholder_item_train" />

                <include layout="@layout/placeholder_item_train" />

                <include layout="@layout/placeholder_item_train" />

                <include layout="@layout/placeholder_item_train" />

                <include layout="@layout/placeholder_item_train" />

                <include layout="@layout/placeholder_item_train" />
            </LinearLayout>
        </com.facebook.shimmer.ShimmerFrameLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:layout_alignParentTop="true"
                android:layout_above="@+id/layout_choice"
                android:id="@+id/my_recycler_view"
                android:layout_marginTop="20dp"
                android:background="@color/white"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:paddingLeft="5dp"
                android:paddingRight="5dp" />

            <LinearLayout

                android:id="@+id/layout_choice"
                android:layout_alignParentBottom="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="right"
                android:orientation="vertical">

                <Button
                    android:visibility="gone"
                    android:id="@+id/btn_choice"
                    android:clickable="true"
                    android:layout_margin="5dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="15sp"
                    android:layout_gravity="center_vertical|center_horizontal"
                    android:background="@drawable/button_gradient"
                    android:text="CHỌN"
                    android:textColor="#FFFFFF" />

            </LinearLayout>
        </RelativeLayout>


    </LinearLayout>


    <!--<com.airbnb.lottie.LottieAnimationView-->
    <!--android:id="@+id/animation_view"-->
    <!--android:layout_width="wrap_content"-->
    <!--android:layout_height="wrap_content"-->
    <!--android:scaleType="centerCrop"-->
    <!--app:lottie_fileName="data.json"-->
    <!--app:lottie_loop="false"-->
    <!--app:lottie_autoPlay="true" />-->


</androidx.coordinatorlayout.widget.CoordinatorLayout>
