<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layout_behavior="@string/appbar_scrolling_view_behavior"
    tools:context="com.hqt.datvemaybay.Checkin"
    tools:showIn="@layout/check_in">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.smarteist.autoimageslider.SliderView
            android:visibility="visible"
            android:background="@drawable/top_banner"
            android:id="@+id/sliderCheckin"
            android:layout_width="match_parent"
            android:layout_height="300dp"
            app:sliderAnimationDuration="600"
            app:sliderAutoCycleDirection="back_and_forth"
            app:sliderAutoCycleEnabled="true"
            app:sliderIndicatorAnimationDuration="600"
            app:sliderIndicatorGravity="center_horizontal|bottom"
            app:sliderIndicatorMargin="15dp"
            app:sliderIndicatorOrientation="horizontal"
            app:sliderIndicatorPadding="3dp"
            app:sliderIndicatorRadius="2dp"
            app:sliderIndicatorSelectedColor="#5A5A5A"
            app:sliderIndicatorUnselectedColor="#FFF"
            app:sliderScrollTimeInSec="1"
            app:sliderStartAutoCycle="true" />

        <androidx.cardview.widget.CardView
            android:id="@+id/card_view"
            android:layout_below="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="2dp"
            app:cardElevation="2dp"
            style="@style/CardViewStyle.Light"
            android:layout_marginBottom="5dp"
            android:layout_marginLeft="5dp"
            android:layout_marginRight="5dp"
            android:layout_marginTop="5dp"
            app:cardPreventCornerOverlap="false"
            app:contentPadding="2dp">


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="10dp"
                android:background="#FFFFFF"
                android:orientation="vertical">


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="5dp"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_horizontal|center_vertical"
                        android:layout_gravity="center_vertical|center_horizontal"
                        tools:ignore="UselessParent">

                        <RadioGroup
                            android:gravity="center_vertical"
                            android:id="@+id/radio"
                            android:layout_width="match_parent"
                            android:orientation="horizontal"
                            android:layout_height="wrap_content">

                            <RadioButton
                                android:id="@+id/checkVn"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:checked="true"
                                android:textSize="12sp"
                                android:lines="4"
                                style="@style/Text"
                                android:text="VietNam\nPacific" />


                            <RadioButton
                                android:id="@+id/checkVietJet"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Vietjet"
                                style="@style/Text"
                                android:textSize="12sp" />

                            <RadioButton
                                android:id="@+id/checkQH"
                                android:textSize="12sp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                style="@style/Text"
                                android:text="Bamboo\nAirway" />

                            <RadioButton
                                android:id="@+id/checkVU"
                                android:textSize="12sp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                style="@style/Text"
                                android:text="Vietravel" />
                        </RadioGroup>

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingBottom="5dp"
                    android:orientation="vertical">

                    <LinearLayout

                        android:layout_width="match_parent"
                        android:weightSum="5"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/textView1"
                            android:layout_width="0dp"
                            android:layout_weight="1.5"
                            style="@style/Text"
                            android:layout_height="wrap_content"
                            android:text="@string/hoTen" />

                        <EditText
                            android:id="@+id/lName"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1.5"
                            android:singleLine="true"
                            android:inputType="textCapCharacters"
                            android:background="@drawable/edit_text"
                            android:ems="10"
                            android:hint="Nguyen">

                            <requestFocus />
                        </EditText>

                        <EditText
                            android:background="@drawable/edit_text"
                            android:id="@+id/fName"
                            android:layout_marginLeft="2dp"
                            android:layout_weight="2"
                            android:layout_width="0dp"
                            android:inputType="textCapCharacters"
                            android:layout_height="wrap_content"
                            android:hint="Van A"
                            android:textAllCaps="true"
                            android:singleLine="true"
                            android:ems="10" />


                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingBottom="5dp"
                    android:weightSum="5"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/textView2"
                            android:layout_width="0dp"
                            style="@style/Text"
                            android:layout_weight="1.5"
                            android:layout_height="wrap_content"
                            android:text="@string/diemDi" />

                        <TextView
                            android:id="@+id/diemDi"
                            android:layout_width="0dp"
                            android:padding="5dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="3.5"
                            android:textColor="#000000"
                            android:background="@drawable/edit_text"
                            android:ems="10"
                            android:hint="@string/diemDiHint" />


                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingBottom="5dp"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:weightSum="5"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/textView5"
                            style="@style/Text"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1.5"
                            android:text="@string/maDatCho" />

                        <EditText
                            android:background="@drawable/edit_text"
                            android:id="@+id/pnr"
                            android:layout_weight="3.5"
                            android:layout_width="0dp"
                            android:inputType="textCapCharacters"
                            android:layout_height="wrap_content"
                            android:ems="10" />

                    </LinearLayout>

                </LinearLayout>

                <androidx.appcompat.widget.AppCompatButton
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="5dp"
                    android:layout_marginLeft="25dp"
                    android:layout_marginRight="25dp"
                    android:padding="10dp"
                    android:id="@+id/btnCheckin"
                    android:background="@drawable/button_gradient"
                    android:textColor="@color/white"
                    android:layout_width="match_parent"
                    android:textAlignment="gravity"
                    android:textSize="16sp"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal|center_vertical"
                    android:text="LÀM THỦ TỤC" />


            </LinearLayout>
        </androidx.cardview.widget.CardView>
    </LinearLayout>
</androidx.core.widget.NestedScrollView>