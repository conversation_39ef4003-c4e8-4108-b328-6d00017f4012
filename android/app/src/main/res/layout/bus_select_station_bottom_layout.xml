<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.hqt.viewmodel.BookingViewModel" />

        <import type="com.hqt.datvemaybay.Common" />

        <import type="android.view.View" />
    </data>

    <LinearLayout
        android:id="@+id/select_station_sheet"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/fui_transparent"
        android:orientation="vertical"
        android:clickable="true"
        android:focusable="true"
        android:elevation="6dp">

        <View
            android:layout_margin="2dp"
            android:layout_gravity="center_vertical|center_horizontal"
            android:layout_width="50dp"
            android:layout_height="4dp"
            android:background="@drawable/top_line" />

        <LinearLayout
            android:background="@color/gbgray"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:orientation="vertical"
                android:id="@+id/flightInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">


            </LinearLayout>

            <androidx.cardview.widget.CardView

                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:id="@+id/quickView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <ImageView
                            android:padding="10dp"
                            android:layout_width="40dp"
                            android:src="@drawable/ic_marker_aiport"
                            android:layout_height="50dp"
                            android:contentDescription="Seat" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="left|center_vertical"
                            android:orientation="horizontal">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:paddingRight="2dp"
                                android:paddingLeft="2dp"
                                android:paddingTop="2dp"
                                android:paddingBottom="2dp"
                                android:orientation="vertical"
                                android:layout_height="wrap_content">

                                <TextView
                                    android:paddingRight="5dp"
                                    android:paddingLeft="5dp"
                                    android:paddingTop="2dp"
                                    android:paddingBottom="2dp"
                                    android:singleLine="true"
                                    android:textSize="13sp"
                                    android:text="Điểm đón: "
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content" />

                                <TextView
                                    android:paddingRight="5dp"
                                    android:paddingLeft="5dp"
                                    android:paddingTop="2dp"
                                    android:paddingBottom="2dp"
                                    android:singleLine="true"
                                    android:textSize="13sp"
                                    android:text="Điểm trả: "
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content" />


                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:paddingRight="2dp"
                                android:paddingLeft="2dp"
                                android:paddingTop="2dp"
                                android:paddingBottom="2dp"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <TextView
                                    android:lines="1"
                                    android:id="@+id/txt_pick_up"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textStyle="bold"
                                    android:textColor="#003A6F"
                                    android:textSize="16sp"
                                    android:text="Vui lòng chọn" />

                                <TextView
                                    android:lines="1"
                                    android:id="@+id/txt_dropoff_select"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textStyle="bold"
                                    android:textColor="#003A6F"
                                    android:textSize="16sp"
                                    android:text="Vui lòng chọn" />


                            </LinearLayout>


                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical" />

                    </LinearLayout>


                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:weightSum="10"
                        android:paddingBottom="5dp"
                        android:orientation="horizontal">

                        <Button
                            android:id="@+id/btnBack"
                            android:layout_width="0dp"
                            android:layout_weight="4"
                            android:layout_height="wrap_content"
                            android:textSize="15sp"
                            android:layout_gravity="right"
                            android:text="Trở về"
                            app:backgroundTint="@color/stt_gray"
                            android:textColor="#FFFFFF" />

                        <Button
                            android:id="@+id/btnNext"
                            android:layout_width="0dp"
                            android:layout_weight="6"
                            android:layout_height="wrap_content"
                            android:layout_gravity="right"
                            android:text="Tiếp tục"
                            app:backgroundTint="@color/primary"
                            android:textColor="#FFFFFF" />

                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>
    </LinearLayout>
</layout>