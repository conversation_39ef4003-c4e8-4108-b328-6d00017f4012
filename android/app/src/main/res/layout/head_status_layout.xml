<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <variable
            name="bindBgcorlor"
            type="java.lang.Integer" />

        <variable
            name="statusText"
            type="java.lang.String" />

        <import type="android.view.View" />
    </data>

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:id="@+id/headStatusLayout"
            android:layout_width="fill_parent"
            android:layout_gravity="center_horizontal"
            android:gravity="center_horizontal"
            android:visibility="visible"
            android:layout_height="wrap_content">

            <TextView

                android:background="@{bindBgcorlor}"
                android:id="@+id/headStatus"
                android:textColor="@color/white"
                android:layout_width="match_parent"
                android:textAlignment="center"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:padding="4dp"
                android:layout_gravity="center_horizontal|center_vertical"
                android:text="@{statusText}" />

        </LinearLayout>
    </LinearLayout>
</layout>