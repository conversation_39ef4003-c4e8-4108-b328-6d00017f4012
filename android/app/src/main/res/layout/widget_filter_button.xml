<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewModel"
            type="com.hqt.viewmodel.WidgetFilterButtonViewModel" />

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical|center_horizontal"
        android:orientation="horizontal">

        <androidx.cardview.widget.CardView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/CardViewStyle.Light"
            app:cardCornerRadius="10dp"
            app:cardElevation="2dp"
            android:layout_margin="5dp"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="10dp"
                android:paddingTop="2dp"
                android:paddingEnd="10dp"
                android:paddingBottom="2dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/btnOne"
                    android:gravity="center_horizontal|center_vertical"
                    android:drawablePadding="2dp"
                    android:layout_width="100dp"
                    android:textColor="@{viewModel.textDefaultColor}"
                    android:layout_height="wrap_content"
                    android:text="@{viewModel.btnOne.btnText}"
                    android:padding="4dp"
                    android:textSize="14sp"
                    android:onClick="@{(view) -> viewModel.onSortClick(view, viewModel.btnOne) }" />


                <TextView
                    android:id="@+id/btnTrue"
                    android:gravity="center_horizontal|center_vertical"
                    android:drawablePadding="2dp"
                    android:layout_width="100dp"
                    android:textColor="@{viewModel.textDefaultColor}"
                    android:layout_height="wrap_content"
                    android:text="@{viewModel.btnTwo.btnText}"
                    android:padding="4dp"
                    android:textSize="14sp"
                    android:onClick="@{(view) -> viewModel.onSortClick(view, viewModel.btnTwo) }" />


                <TextView
                    android:id="@+id/btnThree"
                    android:gravity="center_horizontal|center_vertical"
                    android:drawablePadding="2dp"
                    android:layout_width="90dp"
                    android:textColor="@{viewModel.textDefaultColor}"
                    android:layout_height="wrap_content"
                    android:text="@{viewModel.btnThree.btnText}"
                    android:padding="4dp"
                    android:textSize="14sp"
                    android:onClick="@{(view) -> viewModel.onSortClick(view, viewModel.btnThree) }" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>
    </LinearLayout>

</layout>