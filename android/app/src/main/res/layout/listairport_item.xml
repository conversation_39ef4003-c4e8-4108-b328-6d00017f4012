<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="fill_parent"
    android:paddingTop="10dp"
    android:paddingBottom="5dp"
    android:paddingLeft="5dp"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/image"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_marginEnd="5dp"
            android:scaleType="centerCrop"
            app:srcCompat="@drawable/top_banner" />

        <LinearLayout
            android:paddingLeft="5dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"

            android:orientation="vertical">

            <TextView
                android:id="@+id/airPortName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="18sp"
                android:text="Ho Chi MInh City"
                android:textColor="#000000" />

            <TextView
                android:id="@+id/airPortCode"
                android:layout_width="wrap_content"
                android:text="Tan Son Nhat Airport"
                android:layout_height="wrap_content"
                android:textSize="22sp"
                android:layout_toRightOf="@+id/airPortName" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="fill_parent"
            android:gravity="right|center_vertical"
            android:layout_height="fill_parent">

            <CheckBox
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:id="@+id/checkBox"
                android:layout_alignParentTop="true"
                android:layout_alignParentRight="true"
                android:layout_alignParentEnd="true" />
        </LinearLayout>
    </LinearLayout>

</LinearLayout>