<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:bind="http://schemas.android.com/tools">

    <data>

        <variable
            name="plans"
            type="com.hqt.view.ui.tour.Plans" />

        <import type="android.view.View" />

        <import type="android.text.Html" />
    </data>


    <LinearLayout
        android:layout_margin="5dp"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:padding="5dp"
            android:weightSum="10"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">


            <LinearLayout
                android:layout_marginBottom="5dp"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <com.mikepenz.iconics.view.IconicsImageView
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_gravity="center_vertical"
                    app:iiv_color="#003A6F"
                    app:iiv_icon="faw_map-marker-alt"
                    android:layout_marginEnd="10dp"
                    app:iiv_size="20dp" />

                <TextView

                    android:text="@{plans.title}"
                    android:textColor="#003A6F"
                    android:textSize="16sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:text="Ho Chi Minh (SGN)"
                    android:textStyle="bold" />


            </LinearLayout>


        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <TextView
                    android:maxLines="1"
                    android:rotation="-90"
                    android:gravity="center_vertical|center_horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@{plans.timeTitle}"
                    tools:text="xxxx fdsfsdfsd" />

            </LinearLayout>

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:layout_gravity="center_horizontal">

                <com.mikepenz.iconics.view.IconicsImageView
                    android:layout_alignParentTop="true"
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_gravity="center_vertical|center_horizontal"
                    app:iiv_size="20dp"
                    app:iiv_color="@color/primary"
                    app:iiv_icon="faw_dot-circle" />

                <View
                    android:layout_marginTop="15dp"
                    android:layout_marginBottom="15dp"
                    android:gravity="center_horizontal"
                    android:layout_centerInParent="true"
                    android:layout_height="match_parent"
                    android:layout_width="2dp"
                    android:background="@color/primary" />

                <com.mikepenz.iconics.view.IconicsImageView
                    android:layout_centerVertical="true"
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_gravity="center_vertical"
                    app:iiv_size="20dp"
                    app:iiv_color="@color/primary"
                    android:layout_alignParentBottom="true"
                    app:iiv_icon="faw_dot-circle" />


            </RelativeLayout>

            <LinearLayout
                android:layout_margin="5dp"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <ImageView
                    android:visibility="@{plans.hasThumbnail() ? View.VISIBLE : View.GONE}"
                    android:scaleType="centerInside"
                    android:layout_width="match_parent"
                    android:layout_height="150dp"
                    bind:imageUrl="@{plans.thumbnail}" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@{Html.fromHtml(plans.description)}" />
            </LinearLayout>


        </LinearLayout>


    </LinearLayout>


</layout>