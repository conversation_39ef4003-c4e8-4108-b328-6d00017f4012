<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:id="@+id/coordinatorLayout"
    tools:context="com.hqt.datvemaybay.CheckVe">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true"
        android:theme="@style/AppTheme.AppBarOverlay">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/toolbar_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fitsSystemWindows="true"
            app:contentScrim="?attr/colorPrimary"
            app:title="12bay.vn"
            app:titleEnabled="false"
            app:layout_scrollFlags="snap|enterAlways">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                app:layout_collapseMode="pin"
                app:layout_scrollFlags="scroll|exitUntilCollapsed"
                app:popupTheme="@style/AppTheme.PopupOverlay" />

        </com.google.android.material.appbar.CollapsingToolbarLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <androidx.cardview.widget.CardView
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        android:id="@+id/card_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="2dp"
        app:cardElevation="2dp"
        style="@style/CardViewStyle.Light"
        android:layout_marginBottom="5dp"
        android:layout_marginLeft="5dp"
        android:layout_marginRight="5dp"
        android:layout_marginTop="5dp"
        app:cardPreventCornerOverlap="false"
        app:contentPadding="2dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:background="#FFFFFF"
            android:orientation="vertical">


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="right">

                    <RadioGroup
                        android:id="@+id/radio"
                        android:layout_width="match_parent"
                        android:background="#fdfdfd"
                        android:orientation="horizontal"
                        android:layout_height="wrap_content">

                        <RadioButton
                            android:id="@+id/checkVn"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_weight="0.5"
                            android:checked="true"
                            android:textSize="12sp"
                            style="@style/Text"
                            android:text="VNAirline" />

                        <View
                            android:layout_width="2dp"
                            android:layout_height="match_parent"

                            android:background="#FFFFFF" />

                        <RadioButton
                            android:id="@+id/checkVietJet"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_weight="0.5"
                            android:text="VietJet"
                            style="@style/Text"
                            android:textSize="12sp" />

                        <View
                            android:layout_width="2dp"
                            android:layout_height="match_parent"

                            android:background="#FFFFFF" />

                        <RadioButton
                            android:id="@+id/checkJetStar"
                            android:textSize="12sp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_weight="0.5"
                            style="@style/Text"
                            android:text="Pacific Airlines" />

                        <View
                            android:layout_width="2dp"
                            android:layout_height="match_parent"
                            android:background="#FFFFFF" />

                        <RadioButton
                            android:id="@+id/checkVu"
                            android:textSize="12sp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_weight="0.5"
                            style="@style/Text"
                            android:text="Vietravel " />
                    </RadioGroup>

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingBottom="5dp"
                android:orientation="vertical">

                <LinearLayout

                    android:layout_width="match_parent"
                    android:weightSum="5"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/textView1"
                        android:layout_width="0dp"
                        android:layout_weight="1.5"
                        style="@style/Text"
                        android:layout_height="wrap_content"
                        android:text="@string/hoTen" />

                    <EditText
                        android:id="@+id/lName"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1.5"
                        android:singleLine="true"
                        android:inputType="textCapCharacters"
                        android:background="@drawable/edit_text"
                        android:ems="10"
                        android:hint="Nguyen">

                        <requestFocus />
                    </EditText>

                    <EditText
                        android:background="@drawable/edit_text"
                        android:id="@+id/fName"
                        android:layout_marginLeft="2dp"
                        android:layout_weight="2"
                        android:layout_width="0dp"
                        android:inputType="textCapCharacters"
                        android:layout_height="wrap_content"
                        android:hint="Van A"
                        android:textAllCaps="true"
                        android:singleLine="true"
                        android:ems="10" />


                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingBottom="5dp"
                android:weightSum="5"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/textView2"
                        android:layout_width="0dp"
                        style="@style/Text"
                        android:layout_weight="1.5"
                        android:layout_height="wrap_content"
                        android:text="@string/diemDi" />

                    <TextView
                        android:id="@+id/diemDi"
                        android:layout_width="0dp"
                        android:padding="5dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="3.5"
                        android:textColor="#000000"
                        android:background="@drawable/edit_text"
                        android:ems="10"
                        android:hint="@string/diemDiHint" />


                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingBottom="5dp"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:weightSum="5"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/textView5"
                        style="@style/Text"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1.5"
                        android:text="@string/maDatCho" />

                    <EditText
                        android:background="@drawable/edit_text"
                        android:id="@+id/pnr"
                        android:layout_weight="3.5"
                        android:layout_width="0dp"
                        android:inputType="textCapCharacters"
                        android:layout_height="wrap_content"
                        android:ems="10" />

                </LinearLayout>

            </LinearLayout>

            <androidx.appcompat.widget.AppCompatButton
                android:layout_marginTop="10dp"
                android:id="@+id/button1"
                android:backgroundTint="#FF5959"
                android:layout_width="match_parent"
                android:textAlignment="center"
                android:textColor="#FFFFFF"
                android:textSize="16sp"
                style="@style/MyApp.Button.Big"
                android:layout_height="wrap_content"
                android:text="KIỂM TRA" />

        </LinearLayout>
    </androidx.cardview.widget.CardView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>