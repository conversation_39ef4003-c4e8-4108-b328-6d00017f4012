<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.cardview.widget.CardView
        android:id="@+id/card_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginLeft="4dp"
        android:layout_marginRight="4dp"
        android:layout_marginTop="2dp"
        android:layout_marginBottom="0dp"
        card_view:cardElevation="0dp"
        card_view:cardCornerRadius="4dp">

        <LinearLayout
            android:layout_margin="5dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="fill_parent"
                android:gravity="center_horizontal|center_vertical">

                <com.mikepenz.iconics.view.IconicsTextView
                    android:textColor="@color/primary_dark"
                    android:id="@+id/notificationType"
                    android:contentDescription="@string/txt_checkin_online"
                    android:layout_margin="10dp"
                    android:layout_width="25dp"
                    android:layout_height="wrap_content" />
            </LinearLayout>

            <LinearLayout
                android:baselineAligned="true"
                android:weightSum="10"
                android:layout_width="match_parent"
                android:layout_height="fill_parent">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="fill_parent"
                    android:layout_weight="9"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/txtTitle"
                        android:paddingBottom="2dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:maxLines="1"
                        android:textColor="@color/textDark"
                        android:textStyle="bold"
                        android:text="@string/txt_pax" />

                    <TextView
                        android:id="@+id/txtBody"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:maxLines="3"
                        android:paddingBottom="2dp"
                        android:text="string/txt_terms_andconditions" />

                    <TextView
                        android:textSize="12sp"
                        android:id="@+id/txtCreatedDate"
                        android:layout_width="fill_parent"
                        android:layout_height="fill_parent"
                        android:maxLines="1"
                        android:text="@string/txt_dep_date" />

                    <TextView
                        android:id="@+id/pos"
                        android:layout_width="0dp"
                        android:layout_height="0dp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:gravity="center_horizontal"
                    android:layout_gravity="center_vertical"
                    android:layout_height="fill_parent">

                    <com.mikepenz.iconics.view.IconicsTextView
                        android:id="@+id/seen"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical|center_horizontal"
                        android:text="faw-dot"
                        android:contentDescription="@string/txt_general_rules" />
                </LinearLayout>
            </LinearLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</LinearLayout>