<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="15dp"
        android:orientation="vertical">


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout

                android:layout_width="match_parent"
                android:weightSum="5"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_width="0dp"
                    android:layout_weight="2"
                    style="@style/Text"
                    android:layout_height="wrap_content"
                    android:text="Mã giảm giá: " />

                <TextView
                    android:editable="false"
                    android:id="@+id/txtCode"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:singleLine="true"
                    android:textStyle="bold"
                    android:padding="4dp"
                    android:textColor="#ff1a1a"
                    android:inputType="textCapCharacters"
                    android:background="@drawable/edit_text"
                    android:ems="12">

                    <requestFocus />
                </TextView>


            </LinearLayout>
        </LinearLayout>

        <View
            style="@style/Divider_ngang"
            android:layout_margin="5dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="5dp"
            android:weightSum="5"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_width="0dp"
                    style="@style/Text"
                    android:layout_weight="2"
                    android:layout_height="wrap_content"
                    android:text="Giảm giá:" />

                <TextView
                    android:editable="false"
                    android:id="@+id/txtDiscount"
                    android:layout_width="0dp"
                    android:padding="4dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:textColor="#ff1a1a"
                    android:background="@drawable/edit_text"
                    android:ems="10" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="5dp"
            android:weightSum="5"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_width="0dp"
                    style="@style/Text"
                    android:layout_weight="2"
                    android:layout_height="wrap_content"
                    android:text="Trạng thái: " />

                <TextView
                    android:id="@+id/txtStatus"
                    android:padding="4dp"
                    android:editable="false"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:textColor="#000000"
                    android:ems="10" />

            </LinearLayout>

        </LinearLayout>

        <View
            style="@style/Divider_ngang"
            android:layout_margin="5dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingTop="5dp"
            android:textAppearance="?android:attr/textAppearanceMedium"
            android:text="Mẹo: Bạn hãy chụp màn hình để lưu lại mã giảm giá để sau sử dụng khi đặt vé!"
            android:textColor="#ff1a1a"
            android:id="@+id/voucherHelp" />


    </LinearLayout>

    <com.facebook.login.widget.LoginButton
        android:id="@+id/button_sign_in"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />
</LinearLayout>