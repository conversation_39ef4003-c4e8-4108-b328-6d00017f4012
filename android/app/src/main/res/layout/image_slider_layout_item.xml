<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/iv_auto_image_slider"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitXY" />


    <ImageView
        android:id="@+id/iv_gif_container"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_gravity="bottom|start"
        android:layout_margin="50dp" />


    <FrameLayout
        android:id="@+id/fl_shadow_container"
        android:background="@drawable/bg_overlay"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom">

        <TextView
            android:id="@+id/tv_auto_image_slider"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="4dp"
            android:textColor="#FFF"
            android:textSize="18sp" />

    </FrameLayout>

</FrameLayout>