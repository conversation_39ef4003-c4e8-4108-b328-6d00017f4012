<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/coordinatorLayout"
    android:fitsSystemWindows="true"
    android:background="@color/gbgray"
    tools:context="com.hqt.view.ui.HomeActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar"
        android:visibility="visible"
        android:layout_width="match_parent"
        android:background="@color/primary_dark"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true"
        app:elevation="0dp"
        android:theme="@style/AppTheme.AppBarOverlay">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/toolbar_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:contentScrim="@color/primary_dark"
            app:statusBarScrim="@android:color/holo_red_dark"
            app:title="12bay.vn"
            app:titleEnabled="false"
            app:layout_scrollFlags="enterAlwaysCollapsed">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:background="@color/primary"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                app:elevation="0dp"
                app:layout_collapseMode="pin"
                app:layout_scrollFlags="scroll|enterAlways"
                app:popupTheme="@style/AppTheme.PopupOverlay" />


        </com.google.android.material.appbar.CollapsingToolbarLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <com.facebook.shimmer.ShimmerFrameLayout
        android:id="@+id/shimmer_view_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:layout_marginTop="?attr/actionBarSize"
        android:orientation="vertical"
        android:visibility="visible"
        app:shimmer_duration="800">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <include layout="@layout/placeholder_item_notification" />

            <include layout="@layout/placeholder_item_notification" />

            <include layout="@layout/placeholder_item_notification" />

            <include layout="@layout/placeholder_item_notification" />

            <include layout="@layout/placeholder_item_notification" />

            <include layout="@layout/placeholder_item_notification" />

            <include layout="@layout/placeholder_item_notification" />

            <include layout="@layout/placeholder_item_notification" />

            <include layout="@layout/placeholder_item_notification" />

            <include layout="@layout/placeholder_item_notification" />

        </LinearLayout>
    </com.facebook.shimmer.ShimmerFrameLayout>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_behavior="@string/appbar_scrolling_view_behavior" />


    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <LinearLayout
        android:layout_marginBottom="100dp"
        android:layout_gravity="bottom"
        android:id="@+id/notfound"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/animation_view"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:layout_marginBottom="50dp"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/no_notifications" />

        <TextView
            android:layout_width="match_parent"
            android:gravity="center"
            android:layout_height="wrap_content"
            android:text="Ooh Chưa có thông báo dành cho bạn !" />

        <LinearLayout
            android:layout_margin="20dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="right">

            <RadioGroup
                android:id="@+id/radio"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:layout_weight="1"
                android:background="@drawable/corner_full"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btnSearch"
                    style="@style/MyApp.Button.Big"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.5"
                    android:background="@drawable/button_one_way"
                    android:checked="true"
                    android:text="Đặt chuyến bay"
                    android:textAllCaps="true"
                    android:textColor="#FFFFFF" />

                <Button
                    android:id="@+id/btnCheap"
                    style="@style/MyApp.Button.Big"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.5"
                    android:background="@color/fui_transparent"
                    android:text="@string/sanVe"
                    android:textAllCaps="true"
                    android:textColor="#00a2e3" />
            </RadioGroup>

        </LinearLayout>
    </LinearLayout>


</androidx.coordinatorlayout.widget.CoordinatorLayout>


