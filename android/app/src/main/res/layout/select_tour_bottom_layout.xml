<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="schedule"
            type="com.hqt.view.ui.tour.DepartureDates" />

        <import type="com.hqt.datvemaybay.Common" />

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/fui_transparent"
        android:orientation="vertical"
        android:clickable="true"
        android:focusable="true"
        android:elevation="6dp">

        <View
            android:layout_margin="2dp"
            android:layout_gravity="center_vertical|center_horizontal"
            android:layout_width="50dp"
            android:layout_height="4dp"
            android:background="@drawable/top_line" />


        <androidx.cardview.widget.CardView
            app:contentPadding="5dp"
            android:elevation="10dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout

                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/quickView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:padding="10dp"
                        android:layout_width="50dp"
                        android:src="@drawable/ic_seat"
                        android:layout_height="50dp" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="left|center_vertical"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:ellipsize="end"
                                android:gravity="end"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="@color/primary"
                                android:text="Giá tạm tính: " />


                            <TextView
                                android:text="@{Common.dinhDangTien(schedule.estimatePrice)}"
                                android:gravity="start"
                                android:singleLine="true"
                                android:textColor="#da281c"
                                android:textStyle="bold"
                                android:textSize="16sp"
                                android:paddingEnd="5dp"
                                tools:text="900k"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content" />
                        </LinearLayout>


                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:ellipsize="end"
                                android:gravity="end"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="@color/primary"
                                android:text="Ngày khởi hành: " />

                            <TextView
                                android:ellipsize="end"
                                android:gravity="end"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="@color/primary"
                                android:textStyle="bold"
                                android:text="@{schedule.departureDate}" />
                        </LinearLayout>


                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <Button
                            android:id="@+id/btnNext"
                            android:layout_width="wrap_content"
                            android:layout_weight="6"
                            android:layout_height="wrap_content"
                            android:layout_gravity="right"
                            android:text=" Đặt tour >>"
                            app:backgroundTint="@color/primary"
                            android:textColor="#FFFFFF" />


                    </LinearLayout>

                </LinearLayout>


            </LinearLayout>
        </androidx.cardview.widget.CardView>

    </LinearLayout>
</layout>