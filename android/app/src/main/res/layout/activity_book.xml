<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/primary"
    android:id="@+id/coordinatorLayout"
    android:animateLayoutChanges="true"
    android:fitsSystemWindows="true"
    android:focusableInTouchMode="true"
    tools:context="com.hqt.datvemaybay.BookView">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#EDEDED"
        android:animateLayoutChanges="true"
        android:orientation="vertical">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:elevation="0dp"
            android:background="@color/primary"
            app:layout_collapseMode="pin"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
            app:popupTheme="@style/ThemeOverlay.AppCompat" />

        <ScrollView
            android:id="@+id/scrollView1"
            android:scrollbars="none"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <HorizontalScrollView
                    android:scrollbars="none"
                    android:background="@color/primary"
                    android:id="@+id/horizalScroll"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:paddingBottom="20dp"
                        android:paddingTop="?attr/actionBarSize">

                        <LinearLayout
                            android:id="@+id/container"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"></LinearLayout>

                        <LinearLayout
                            android:id="@+id/containerRt"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                        </LinearLayout>

                    </LinearLayout>
                </HorizontalScrollView>

                <androidx.cardview.widget.CardView
                    android:layout_marginTop="-15dp"
                    android:id="@+id/card_view1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardCornerRadius="2dp"
                    app:cardElevation="2dp"
                    style="@style/CardViewStyle.Light"
                    android:layout_marginBottom="5dp"
                    android:layout_marginLeft="5dp"
                    android:layout_marginRight="5dp"
                    app:cardPreventCornerOverlap="false"
                    app:contentPadding="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="10dp"
                        android:background="#FFFFFF"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="5dp"
                            android:orientation="vertical">

                            <TextView

                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_gravity="left"
                                android:textStyle="bold"
                                style="@style/Text"
                                android:text="@string/nhapthongTinHanhKhach" />

                            <View
                                android:layout_width="match_parent"
                                android:layout_height="1dp"
                                android:layout_margin="4dp"
                                android:background="@color/diver_color" />

                            <ProgressBar
                                android:indeterminate="true"
                                android:id="@+id/load_bag"
                                android:layout_gravity="center_horizontal"
                                android:layout_width="20dp"
                                style="?android:attr/progressBarStyle"
                                android:layout_height="20dp" />


                        </LinearLayout>

                        <LinearLayout
                            android:orientation="vertical"
                            android:layout_width="match_parent"
                            android:id="@+id/paxInPut"
                            android:layout_height="wrap_content"
                            android:animateLayoutChanges="true"
                            android:layout_gravity="center_horizontal">


                        </LinearLayout>


                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    android:id="@+id/show_seat_select"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardCornerRadius="2dp"
                    app:cardElevation="2dp"
                    style="@style/CardViewStyle.Light"
                    android:layout_marginBottom="5dp"
                    android:layout_marginLeft="5dp"
                    android:layout_marginRight="5dp"
                    app:cardPreventCornerOverlap="false"
                    app:contentPadding="2dp">

                    <LinearLayout
                        android:padding="5dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_gravity="center_horizontal"
                            android:padding="10dp">

                            <ImageView
                                android:layout_width="20dp"
                                android:layout_height="20dp"
                                android:background="@drawable/ic_seat" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_marginLeft="5dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@color/white"
                            android:padding="5dp"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/txt_seat_select"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textStyle="bold"
                                style="@style/Text"
                                android:text="@string/txt_seat_select" />

                            <TextView
                                android:id="@+id/txt_seat_select_detail"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="12sp"
                                android:text="@string/txt_seat_select_detail" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="fill_parent"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical|end"
                            android:padding="10dp">

                            <com.mikepenz.iconics.view.IconicsImageView
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                app:iiv_color="@color/primary"
                                app:iiv_icon="gmd_keyboard_arrow_right" />
                        </LinearLayout>

                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    android:id="@+id/loginLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardCornerRadius="2dp"
                    app:cardElevation="2dp"
                    style="@style/CardViewStyle.Light"
                    android:layout_marginBottom="5dp"
                    android:layout_marginLeft="5dp"
                    android:layout_marginRight="5dp"
                    app:cardPreventCornerOverlap="false"
                    app:contentPadding="2dp">

                    <LinearLayout
                        android:id="@+id/txt_reward"
                        android:padding="5dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_gravity="center_horizontal"
                            android:padding="10dp">

                            <com.mikepenz.iconics.view.IconicsImageView
                                android:layout_width="20dp"
                                android:layout_height="20dp"
                                app:iiv_size="20dp"
                                app:iiv_icon="gmd_account_circle" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_marginLeft="5dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@color/white"
                            android:padding="5dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textStyle="bold"
                                style="@style/Text"
                                android:text="@string/txt_not_login" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="12sp"
                                android:text="@string/txt_txt_not_login" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="fill_parent"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical|end"
                            android:padding="10dp">

                            <com.mikepenz.iconics.view.IconicsImageView
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                app:iiv_color="@color/primary"
                                app:iiv_icon="gmd_keyboard_arrow_right" />
                        </LinearLayout>

                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    android:id="@+id/card_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardCornerRadius="2dp"
                    app:cardElevation="2dp"
                    style="@style/CardViewStyle.Light"
                    android:layout_marginBottom="5dp"
                    android:layout_marginLeft="5dp"
                    android:layout_marginRight="5dp"
                    app:cardPreventCornerOverlap="false"
                    app:contentPadding="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="10dp"
                        android:background="#FFFFFF"
                        android:orientation="vertical">


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="5dp"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/textView8"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_gravity="left"
                                android:textStyle="bold"
                                style="@style/Text"
                                android:text="Nhập thông tin liên hệ"
                                android:textAlignment="center" />

                            <View
                                android:layout_width="match_parent"
                                android:layout_height="1dp"
                                android:layout_margin="4dp"
                                android:background="@color/diver_color" />


                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <LinearLayout

                                android:layout_width="match_parent"
                                android:weightSum="5"
                                android:paddingBottom="5dp"
                                android:layout_height="wrap_content">

                                <TextView
                                    android:id="@+id/textView1"
                                    android:layout_width="0dp"
                                    android:layout_weight="2"
                                    android:textColor="@color/textDark"
                                    android:layout_height="wrap_content"
                                    android:text="@string/hoVaTen" />

                                <EditText
                                    android:id="@+id/txtName"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="3"
                                    android:singleLine="true"
                                    android:paddingTop="2dp"
                                    android:paddingBottom="2dp"
                                    android:paddingRight="10dp"
                                    android:paddingLeft="10dp"
                                    android:hint="@string/txtNamehint"
                                    android:textColor="#000"
                                    android:background="@drawable/edit_text"
                                    android:ems="10">


                                </EditText>


                            </LinearLayout>
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:weightSum="5"
                            android:paddingBottom="5dp"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_weight="2"
                                    android:textColor="@color/textDark"
                                    android:layout_height="wrap_content"
                                    android:text="@string/soDienThoai" />

                                <EditText
                                    android:id="@+id/txtPhone"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="3"
                                    android:textColor="#000"
                                    android:paddingTop="2dp"
                                    android:paddingBottom="2dp"
                                    android:paddingRight="10dp"
                                    android:paddingLeft="10dp"
                                    android:inputType="phone"
                                    android:background="@drawable/edit_text"
                                    android:ems="10" />

                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:weightSum="5"
                            android:paddingBottom="5dp"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_weight="2"
                                    android:textColor="@color/textDark"
                                    android:layout_height="wrap_content"
                                    android:text="@string/email" />

                                <EditText
                                    android:id="@+id/txtEmail"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="3"
                                    android:textColor="#000"
                                    android:paddingTop="2dp"
                                    android:paddingBottom="2dp"
                                    android:paddingRight="10dp"
                                    android:paddingLeft="10dp"
                                    android:inputType="textEmailAddress"
                                    android:hint="@string/txtEmailhint"
                                    android:background="@drawable/edit_text"
                                    android:ems="10" />

                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:paddingBottom="0dp"
                            android:weightSum="5"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <TextView
                                    android:id="@+id/textView2"
                                    android:layout_width="0dp"
                                    android:layout_weight="2"
                                    android:textColor="@color/textDark"
                                    android:layout_height="wrap_content"
                                    android:text="@string/maGiamGia" />

                                <EditText
                                    android:id="@+id/txtVoucherCode"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1.5"
                                    android:background="@drawable/edit_text"
                                    android:paddingTop="2dp"
                                    android:paddingBottom="2dp"
                                    android:inputType="textCapCharacters"
                                    android:paddingRight="10dp"
                                    android:paddingLeft="10dp"
                                    android:ems="10"
                                    android:textColor="#000"
                                    android:layout_marginTop="2dp" />

                                <androidx.appcompat.widget.AppCompatButton
                                    android:layout_width="0dp"
                                    android:backgroundTint="#FF5959"
                                    android:textColor="#FFFFFF"
                                    android:layout_height="31dp"
                                    android:text="Áp dụng"
                                    android:padding="2dp"
                                    android:layout_weight="1.5"
                                    android:textSize="10sp"
                                    android:id="@+id/btnGetVoucher"
                                    style="@style/MyApp.Button.Big"
                                    android:layout_margin="2dp" />


                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:paddingBottom="5dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:orientation="vertical">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:paddingBottom="2dp"
                                    android:layout_height="wrap_content">

                                    <TextView
                                        android:id="@+id/textViews3"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textColor="@color/textDark"
                                        android:text="@string/giaVe" />

                                    <TextView
                                        android:textColor="#000000"
                                        android:id="@+id/txtGiaVe"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="right" />

                                </LinearLayout>


                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:paddingBottom="2dp"
                                    android:visibility="gone"
                                    android:id="@+id/bagLayout"
                                    android:layout_height="wrap_content">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textColor="@color/textDark"
                                        android:text="@string/hanhLy" />

                                    <TextView
                                        android:id="@+id/txtbagFee"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:textColor="#FF0000"
                                        android:gravity="right" />

                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:paddingBottom="2dp"
                                    android:visibility="gone"
                                    android:id="@+id/voucherLayout"
                                    android:layout_height="wrap_content">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:textColor="@color/textDark"
                                        android:layout_height="wrap_content"
                                        android:text="@string/giamGia" />

                                    <TextView
                                        android:id="@+id/txtdiscount"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:textColor="#FF0000"
                                        android:gravity="right" />

                                </LinearLayout>

                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="1dp"
                                    android:layout_margin="2dp"
                                    android:background="@color/diver_color" />

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:paddingBottom="2dp"
                                    android:layout_height="wrap_content">

                                    <TextView
                                        android:id="@+id/textView3"
                                        android:layout_width="wrap_content"
                                        style="@style/Text"
                                        android:layout_height="wrap_content"
                                        android:textStyle="bold"
                                        android:text="@string/tongCong" />


                                    <TextView
                                        android:id="@+id/txtGiaTongCong"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="right"
                                        android:textColor="#FF0000"
                                        android:textStyle="bold" />

                                </LinearLayout>

                                <LinearLayout
                                    android:id="@+id/layoutPointReward"
                                    android:layout_width="match_parent"
                                    android:paddingBottom="2dp"
                                    android:gravity="end"
                                    android:layout_height="wrap_content">

                                    <TextView
                                        android:id="@+id/txtPoint"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:gravity="end"
                                        android:textColor="@color/primary"
                                        android:text="Nhận 10 điểm" />

                                    <com.mikepenz.iconics.view.IconicsTextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textColor="@color/primary"
                                        android:text="{gmd_stars} " />

                                </LinearLayout>
                            </LinearLayout>
                        </LinearLayout>

                        <androidx.appcompat.widget.AppCompatButton
                            android:layout_marginTop="5dp"
                            android:layout_marginBottom="5dp"
                            android:layout_marginLeft="25dp"
                            android:layout_marginRight="25dp"
                            android:padding="10dp"
                            android:id="@+id/btnBookVe"
                            android:background="@drawable/button_gradient"
                            android:textColor="@color/white"
                            android:layout_width="match_parent"
                            android:textAlignment="gravity"
                            android:textSize="16sp"
                            android:layout_height="wrap_content"
                            android:gravity="center_horizontal|center_vertical"
                            android:text="ĐẶT VÉ" />
                    </LinearLayout>

                </androidx.cardview.widget.CardView>


            </LinearLayout>
        </ScrollView>

    </RelativeLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>