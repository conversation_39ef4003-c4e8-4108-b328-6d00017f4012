<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="pax"
            type="com.hqt.data.model.Passenger" />

        <variable
            name="isAllowEdit"
            type="Boolean" />


        <import type="com.hqt.datvemaybay.Common" />

        <import type="android.view.View" />
    </data>

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:animateLayoutChanges="true"
        android:layout_marginBottom="5dp">

        <androidx.cardview.widget.CardView
            android:id="@+id/pax_input_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="2dp"
            app:cardElevation="0dp"
            style="@style/CardViewStyle.Light"
            app:cardPreventCornerOverlap="false"
            app:contentPadding="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/input_bg"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal|center_vertical"
                        android:padding="10dp">

                        <com.mikepenz.iconics.view.IconicsImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            app:iiv_size="20dp"
                            bind:iconis="@{pax.paxIcon}" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:padding="5dp"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/paxFullName"
                            android:gravity="center_vertical|center_horizontal"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textStyle="bold"
                            android:textColor="@color/black"
                            tools:text="Gh3Aế 3A"
                            android:text="@{pax.fullNameDetail}" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical|end"
                        android:padding="10dp">

                        <TextView
                            android:textAlignment="textEnd"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:maxLines="2"
                            android:text="@{pax.seatInfo}"
                            tools:text="Ghế 3A Ghế 3AGhế \nGhế 3AGhế 3A" />

                        <com.mikepenz.iconics.view.IconicsImageView
                            android:visibility="@{pax.isValidated ? View.VISIBLE : View.GONE}"
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            app:iiv_color="@color/primary"
                            app:iiv_icon="gmd_done" />

                        <TextView
                            android:visibility="@{pax.isValidated ? View.GONE : View.VISIBLE}"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:gravity="center_horizontal|center_vertical"
                            android:textColor="@color/red"
                            android:textSize="24sp"
                            android:text="*" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/paxDetailLayout"
                    android:background="#E8F1FB"
                    android:visibility="@{pax.isShowAddon  ? View.VISIBLE : View.GONE}"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:padding="10dp"
                        android:orientation="vertical">


                        <TextView
                            android:gravity="center_vertical"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="14sp"
                            android:text="@{pax.getAddonDetail}"
                            app:iiv_size="12dp" />


                    </LinearLayout>
                </LinearLayout>


            </LinearLayout>
        </androidx.cardview.widget.CardView>

    </LinearLayout>


</layout>