<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:bind="http://schemas.android.com/tools">

    <data>

        <variable
            name="info"
            type="com.hqt.view.ui.tour.TourSubInfo" />

        <import type="com.hqt.datvemaybay.Common" />

        <import type="android.text.Html" />
    </data>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <com.mikepenz.iconics.view.IconicsImageView
            android:layout_width="10dp"
            android:layout_height="10dp"
            android:layout_gravity="center_vertical"
            bind:iconis="@{info.subIcon}"
            android:layout_marginEnd="5dp"
            app:iiv_color="@color/textDark"
            app:iiv_size="15dp" />

        <TextView
            android:textColor="@color/textDark"
            android:lines="1"
            android:text="@{info.title}"
            android:textSize="14sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="2-3 sao" />

    </LinearLayout>

</layout>