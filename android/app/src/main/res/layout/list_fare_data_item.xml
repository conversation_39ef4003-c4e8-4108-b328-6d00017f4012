<?xml version="1.0" encoding="UTF-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewmodel"
            type="com.hqt.view.ui.flightSearch.model.FareData" />

        <import type="com.hqt.datvemaybay.Common" />

        <import type="android.view.View" />

    </data>

    <androidx.cardview.widget.CardView
        android:id="@+id/item"
        android:tag="@{viewmodel.fareDataId}"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/corner_full"
        app:cardCornerRadius="4dp"
        app:cardElevation="0dp"
        android:layout_marginBottom="@dimen/cardMarginVertical"
        android:layout_marginLeft="@dimen/cardMarginHorizontal"
        android:layout_marginRight="@dimen/cardMarginHorizontal"
        android:layout_marginTop="@dimen/cardMarginVertical"
        android:clickable="true"
        app:contentPadding="5dp"
        android:focusable="true">


        <LinearLayout
            android:background="?android:attr/selectableItemBackground"
            android:paddingStart="5dp"
            android:paddingEnd="5dp"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal">

            <LinearLayout

                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:gravity="center"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:visibility="@{(viewmodel.isShowLabel()) ? View.VISIBLE : View.GONE}"
                        android:background="@drawable/button_gradient_no_round"
                        android:layout_width="wrap_content"
                        android:paddingStart="15dp"
                        android:paddingEnd="15dp"
                        android:textColor="@color/white"
                        android:layout_height="wrap_content"
                        android:text="@{viewmodel.description}" />

                    <LinearLayout
                        android:gravity="right"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content">

                        <TextView
                            android:textColor="#da281c"
                            style="@style/Text"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:gravity="center_vertical"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            tools:text="1,000,000đ"
                            android:text="@{Common.dinhDangTien(viewmodel.totalPrice)}" />

                    </LinearLayout>

                </LinearLayout>

                <include
                    android:id="@+id/departureFlight"
                    bind:flightInfo="@{viewmodel.departureFlight}"
                    layout="@layout/flight_data_item" />

                <View
                    android:visibility="@{(viewmodel.isRoundTrip == true) ? View.VISIBLE : View.GONE}"
                    android:background="@drawable/gradientdiv_vertical"
                    android:layout_width="match_parent"
                    android:layout_height="1dp" />

                <include
                    android:visibility="@{(viewmodel.isRoundTrip == true) ? View.VISIBLE : View.GONE}"
                    android:id="@+id/returnFlight"
                    bind:flightInfo="@{viewmodel.returnFlight}"
                    layout="@layout/flight_data_item" />

            </LinearLayout>

        </LinearLayout>
    </androidx.cardview.widget.CardView>
</layout>