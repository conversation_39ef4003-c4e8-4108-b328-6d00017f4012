<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/select_passenger_sheet"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    app:behavior_hideable="true"
    app:layout_behavior="android.support.design.widget.BottomSheetBehavior">

    <LinearLayout
        android:paddingTop="10dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">
        <TextView
            android:id="@+id/txtTitle"
            android:textSize="18sp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/textDark"
            android:text="Mã giảm giá 20 k"/>

        <TextView
            android:layout_marginTop="5dp"
            android:id="@+id/txtExpiredTime"
            android:textSize="14sp"
            android:padding="5dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:background="@drawable/corner_full_green"
            android:text="Dùng đến: "/>
        <LinearLayout
            android:layout_marginTop="10dp"
            android:layout_width="match_parent"
            android:background="@drawable/corner_full"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/txtDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </LinearLayout>

        <LinearLayout
            android:layout_marginTop="10dp"
            android:layout_width="match_parent"
            android:layout_gravity="center_vertical|center_horizontal"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <TextView
                android:id="@+id/txtVoucherCode"
                android:padding="10dp"
                android:textStyle="bold"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal|center_vertical"
                android:background="@drawable/corner_full_voucher_code"
                android:textColor="@color/primary_dark"/>

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/copyVoucher"
                android:padding="5dp"
                android:layout_width="match_parent"
                android:elevation="0dp"
                android:layout_height="wrap_content"
                style="@style/MyApp.Button.Blue"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="10dp"
                android:text="Sao chép mã"
                android:textColor="#fff" />
        </LinearLayout>

    </LinearLayout>


</LinearLayout>