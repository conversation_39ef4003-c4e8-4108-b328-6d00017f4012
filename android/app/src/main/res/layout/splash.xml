<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_gradient"
    android:id="@+id/lin_lay"
    android:orientation="vertical">

    <TextView
        android:id="@+id/view2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="10dp"
        android:textColor="#FFFFFF"
        android:text="@string/copyRight"
        android:textAppearance="?android:attr/textAppearanceSmall" />

    <TextView
        android:id="@+id/view1"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textAlignment="center"
        android:textColor="#FFFFFF"
        android:text="@string/splash"
        android:textSize="18sp"
        android:layout_centerVertical="true"
        android:layout_centerHorizontal="true" />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/animation_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="50dp" />

</RelativeLayout>
