<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#1BA2DC"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="@dimen/activity_horizontal_margin">


        <ImageView
            android:layout_width="@dimen/logo_w_h"
            android:layout_height="@dimen/logo_w_h"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="10dp"
            app:srcCompat="@drawable/logo_trans" />

        <androidx.cardview.widget.CardView
            android:id="@+id/email_login"
            style="@style/CardViewStyle.Light"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="5dp"
            android:layout_marginLeft="5dp"
            android:layout_marginRight="5dp"
            android:layout_marginTop="5dp"
            app:cardCornerRadius="2dp"
            app:cardElevation="2dp"
            app:cardPreventCornerOverlap="false"
            app:contentPadding="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="10dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:padding="10dp"
                    android:text="@string/btn_forgot_password"
                    android:textColor="@color/primary"
                    android:textSize="20dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp"
                    android:gravity="center_horizontal"
                    android:padding="@dimen/activity_horizontal_margin"
                    android:text="@string/forgot_password_msg"
                    android:textColor="@android:color/background_dark"
                    android:textSize="14dp" />

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <EditText
                        android:id="@+id/email"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="10dp"
                        android:layout_marginTop="20dp"
                        android:hint="@string/hint_email"
                        android:inputType="textEmailAddress"
                        android:textColor="@android:color/white"
                        android:textColorHint="@android:color/white" />
                </com.google.android.material.textfield.TextInputLayout>

                <!-- Login Button -->

                <Button
                    android:id="@+id/btn_reset_password"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dip"
                    android:background="@color/btnColor"
                    android:text="@string/btn_reset_password"
                    android:textColor="#ffffff" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <Button
            android:id="@+id/btn_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:background="@null"
            android:text="@string/btn_back"
            android:textColor="#ffffff" />

    </LinearLayout>

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_gravity="center|bottom"
        android:layout_marginBottom="20dp"
        android:visibility="gone" />
</LinearLayout>
