<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingLeft="18dp"
    android:paddingRight="18dp"
    android:orientation="vertical">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="5dp"
            android:textColor="@color/textDark"
            android:text="Chuyến bay: "/>
        <TextView
            android:id="@+id/chuyenBay"
            android:layout_margin="5dp"
            android:textColor="@color/textDark"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="From: Ho Chi Minh To: <PERSON> Nang" />
    </LinearLayout>



    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:weightSum="10"
        android:layout_margin="5dp" >

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="4.5"
            android:orientation="vertical" >

            <TextView
                android:id="@+id/gioBay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:textColor="@color/textDark"
                android:textSize="11sp"
                android:maxLines="2"
                android:text="Time" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:orientation="vertical" >

            <ImageView
                android:id="@+id/imageView1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                app:srcCompat="@drawable/outbound" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="4.5"
            android:orientation="vertical" >

            <TextView
                android:id="@+id/gioDen"
                android:textSize="12sp"
                android:layout_width="wrap_content"
                android:layout_gravity="center"
                android:layout_height="wrap_content"
                android:maxLines="2"
                android:textColor="@color/textDark"
                android:text="Time" />
        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="5dp" >

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:orientation="vertical" >

            <TextView
                android:id="@+id/TextView02"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Ngày bay:"
                android:textColor="@color/textDark"
                android:textSize="11sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" style=""
            android:layout_weight="1"
            android:orientation="vertical" >

            <TextView
                android:id="@+id/ngayBay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15dp"
                android:text=""
                android:textColor="#000000"
                android:textSize="12sp" />
        </LinearLayout>
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginLeft="7dp"
        android:layout_marginRight="7dp"
        android:background="#D7D7D7" />

<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="5dp"
    android:weightSum="5" >

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            android:orientation="vertical" >

            <TextView
                android:id="@+id/hangBay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Hành khách:"
                android:textColor="@color/textDark"
                android:textSize="11sp" />

            <TextView

                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Trạng thái:"
                android:textColor="@color/textDark"
                android:textSize="11sp" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="3"
            android:orientation="vertical" >

            <TextView
                android:id="@+id/tenHanhKhach"
                android:textSize="11sp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#000FFF"
                android:textStyle="bold"
                android:layout_gravity="center"
                android:text="Chuyến bay" />

            <TextView
                android:id="@+id/status"
                android:textSize="11sp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textStyle="bold"
                android:textColor="#EA0000"
                android:layout_gravity="center"
                android:text="(On Hold)" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical" >
        </LinearLayout>
    </LinearLayout>
</LinearLayout>