<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/payment_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    app:layout_behavior="@string/appbar_scrolling_view_behavior">
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                style="@style/block_header"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    style="@style/text_header"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/payments" />

            </LinearLayout>

            <LinearLayout
                style="@style/Layout_block"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="0.0dip"
                android:gravity="center"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp"
                    android:layout_marginTop="5.0dp">

                    <ImageView
                        android:layout_width="50.0dip"
                        android:layout_height="50.0dip"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="10.0dip"
                        app:srcCompat="@drawable/payoo_logo" />

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="5.0dip"
                            android:text="Thanh toán trực tuyến PAYOO"
                            android:textColor="#ff333333"
                            android:textSize="14.0sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Thanh toán trực tuyến qua cổng thanh toán Payoo"
                            android:textColor="#ff333333"
                            android:textSize="14.0sp" />
                    </LinearLayout>
                </LinearLayout>
                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp"
                    android:layout_marginTop="5.0dp">

                    <ImageView
                        android:layout_width="50.0dip"
                        android:layout_height="50.0dip"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="10.0dip"
                        app:srcCompat="@drawable/visa" />

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="5.0dip"
                            android:text="Trả bằng Thẻ tín dụng"
                            android:textColor="#ff333333"
                            android:textSize="14.0sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Thanh toán trực tuyến bằng thẻ tín dụng quốc tế Visa/Master/JCB"
                            android:textColor="#ff333333"
                            android:textSize="14.0sp" />
                    </LinearLayout>
                </LinearLayout>
                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp"
                    android:layout_marginTop="5.0dp">

                    <ImageView
                        android:layout_width="50.0dip"
                        android:layout_height="50.0dip"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="10.0dip"
                        app:srcCompat="@drawable/credit" />

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="5.0dip"
                            android:text="Thẻ ATM nội địa"
                            android:textColor="#ff333333"
                            android:textSize="14.0sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Thanh toán trực tuyến bằng ATM nội địa của các ngân hàng trong nước"
                            android:textColor="#ff333333"
                            android:textSize="14.0sp" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp"
                    android:layout_marginTop="5.0dp">

                    <ImageView
                        android:layout_width="50.0dip"
                        android:layout_height="50.0dip"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="10.0dip"
                        app:srcCompat="@drawable/store" />

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="5.0dip"
                            android:text="Thanh toán tại cửa hàng"
                            android:textColor="#ff333333"
                            android:textSize="14.0sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Thanh toán sau tại cửa hàng tiện ích. Vin Mart, B'Mart, Mini Stop, FPT Shop "
                            android:textColor="#ff333333"
                            android:textSize="14.0sp" />
                    </LinearLayout>
                </LinearLayout>
                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20.0dip">

                    <ImageView
                        android:layout_width="50.0dip"
                        android:layout_height="50.0dip"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="10.0dip"
                        app:srcCompat="@drawable/tk_bank" />

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="5.0dip"
                            android:text="@string/tt4"
                            android:textColor="#ff333333"
                            android:textSize="14.0sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/ct4"
                            android:textColor="#ff333333"
                            android:textSize="14.0sp" />

                        <LinearLayout
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                style="@style/text_title_bank"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/payment_detail" />

                            <TextView
                                style="@style/text_bank"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/payment_receiver" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="fill_parent"
                                android:text="@string/payment_content" />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:id="@+id/bank_vcb"
                        android:gravity="center">

                        <ImageView
                            android:layout_width="50.0dip"
                            android:layout_height="50.0dip"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:layout_marginRight="10.0dip"
                            app:srcCompat="@drawable/tk_vcb" />

                        <LinearLayout
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                style="@style/text_title_bank"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Ngân hàng VietComBank"
                                android:textStyle="bold" />

                            <TextView
                                style="@style/text_bank"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Chi Nhánh Tân Thuận, Q7" />

                            <TextView
                                style="@style/text_bank"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Chủ tài khoản : NGUYỄN VĂN TRÌNH" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="fill_parent"
                                android:text="Số TK: *************" />
                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:id="@+id/bank_vietin"
                        android:gravity="center">

                        <ImageView
                            android:layout_width="50.0dip"
                            android:layout_height="50.0dip"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:layout_marginRight="10.0dip"
                            app:srcCompat="@drawable/tk_vietin" />

                        <LinearLayout
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                style="@style/text_title_bank"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Ngân hàng công thương Việt Nam - VietinBank"
                                android:textStyle="bold" />

                            <TextView
                                style="@style/text_bank"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Vietinbank chi nhánh 4" />

                            <TextView
                                style="@style/text_bank"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Chủ tài khoản: CTY TNHH MTV TM NGUYỄN DƯƠNG" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="fill_parent"
                                android:text="Số TK: ************" />
                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:id="@+id/bank_acb"
                        android:gravity="center">

                        <ImageView
                            android:layout_width="50.0dip"
                            android:layout_height="50.0dip"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:layout_marginRight="10.0dip"
                            app:srcCompat="@drawable/tk_acb" />

                        <LinearLayout
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                style="@style/text_title_bank"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Ngân hàng Á Châu - ACB"
                                android:textStyle="bold" />

                            <TextView
                                style="@style/text_bank"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Chi nhánh Lê Văn Khương" />

                            <TextView
                                style="@style/text_bank"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Chủ tài khoản: NGUYỄN VĂN TRÌNH" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="fill_parent"
                                android:text="Số TK: *********" />
                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:id="@+id/bank_bidv"
                        android:gravity="center">

                        <ImageView
                            android:layout_width="50.0dip"
                            android:layout_height="50.0dip"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:layout_marginRight="10.0dip"
                            app:srcCompat="@drawable/tk_bidv" />

                        <LinearLayout
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                style="@style/text_title_bank"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Ngân hàng đầu tư và phát triển Việt Nam BIDV"
                                android:textStyle="bold" />

                            <TextView
                                style="@style/text_bank"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Chi nhánh Gia Định" />

                            <TextView
                                style="@style/text_bank"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Chủ tài khoản: DƯƠNG THỊ ĐÀO" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="fill_parent"
                                android:text="Số TK: **************" />
                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:id="@+id/bank_donga"
                        android:gravity="center">

                        <ImageView
                            android:layout_width="50.0dip"
                            android:layout_height="50.0dip"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:layout_marginRight="10.0dip"
                            app:srcCompat="@drawable/tk_donga" />

                        <LinearLayout
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                style="@style/text_title_bank"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Ngân hàng Đông Á - EAB"
                                android:textStyle="bold" />

                            <TextView
                                style="@style/text_bank"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="PGD Nguyễn Ảnh Thủ" />

                            <TextView
                                style="@style/text_bank"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Chủ tài khoản: NGUYỄN VĂN TRÌNH" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="fill_parent"
                                android:text="Số TK: **********" />
                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:id="@+id/bank_agri"
                        android:gravity="center">

                        <ImageView
                            android:layout_width="50.0dip"
                            android:layout_height="50.0dip"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:layout_marginRight="10.0dip"
                            app:srcCompat="@drawable/tk_agri" />

                        <LinearLayout
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                style="@style/text_title_bank"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Ngân hàng Agribank"
                                android:textStyle="bold" />

                            <TextView
                                style="@style/text_bank"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Chi nhánh Đông Sài Gòn" />

                            <TextView
                                style="@style/text_bank"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Chủ tài khoản: DƯƠNG THỊ ĐÀO" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="fill_parent"
                                android:text="Số TK: *************" />
                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:id="@+id/bank_tech"
                        android:gravity="center">

                        <ImageView
                            android:layout_width="50.0dip"
                            android:layout_height="50.0dip"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:layout_marginRight="10.0dip"
                            app:srcCompat="@drawable/tk_tech" />

                        <LinearLayout
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                style="@style/text_title_bank"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Ngân hàng TechComBank"
                                android:textStyle="bold" />

                            <TextView
                                style="@style/text_bank"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Chi nhánh Tô Ký" />

                            <TextView
                                style="@style/text_bank"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Chủ tài khoản: NGUYỄN VĂN TRÌNH"/>

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="fill_parent"
                                android:text="Số TK: **************" />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_marginTop="5dp"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:layout_width="50.0dip"
                        android:layout_height="50.0dip"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="10.0dip"
                        app:srcCompat="@drawable/cash" />

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="5.0dip"
                            android:text="@string/tt1"
                            android:textColor="#ff333333"
                            android:textSize="14.0sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/ct1"
                            android:textColor="#ff333333"
                            android:textSize="14.0sp" />
                    </LinearLayout>
                </LinearLayout>
                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="5dp"
                    android:paddingBottom="48dp"
                    android:layout_marginTop="5.0dp">

                    <ImageView
                        android:layout_width="50.0dip"
                        android:layout_height="50.0dip"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="10.0dip"
                        app:srcCompat="@drawable/tk_home" />

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="5.0dip"
                            android:text="@string/tt3"
                            android:textColor="#ff333333"
                            android:textSize="14.0sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/ct3"
                            android:textColor="#ff333333"
                            android:textSize="14.0sp" />
                    </LinearLayout>
                </LinearLayout>

            </LinearLayout>

        </LinearLayout>
    </ScrollView>
</LinearLayout>