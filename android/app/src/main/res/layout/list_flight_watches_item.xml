<?xml version="1.0" encoding="UTF-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="flightWatches"
            type="com.hqt.data.model.FlightWatches" />

        <variable
            name="viewHolder"
            type="com.hqt.view.adapter.FlightWatchesAdapter.ViewHolder" />

        <import type="com.hqt.datvemaybay.Common" />

    </data>

    <androidx.cardview.widget.CardView
        android:id="@+id/item"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/corner_full"
        app:cardCornerRadius="2dp"
        app:cardElevation="1dp"
        android:layout_marginBottom="@dimen/cardMarginVertical"
        android:layout_marginLeft="@dimen/cardMarginHorizontal"
        android:layout_marginRight="@dimen/cardMarginHorizontal"
        android:layout_marginTop="@dimen/cardMarginVertical"
        android:clickable="true"
        app:cardPreventCornerOverlap="false"
        android:focusable="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:padding="5dp"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal">

                <com.hqt.util.AspectRatioImageView
                    bind:imageStyle="@{`ROUND`}"
                    bind:imageUrl="@{flightWatches.getDestinationImages()}"
                    android:layout_gravity="center_horizontal|center_vertical"
                    android:layout_width="50dp"
                    android:layout_height="50dp"
                    android:layout_marginRight="5dp"
                    android:layout_marginLeft="5dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.mikepenz.iconics.view.IconicsImageView
                            android:visibility="gone"
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            app:iiv_color="@color/stt_gray"
                            app:iiv_icon="faw-clock" />

                        <TextView
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:padding="5dp"
                            android:id="@+id/txtGioDi"
                            style="@style/Text"
                            android:textStyle="bold"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{flightWatches.tripDetail }" />

                        <LinearLayout
                            android:gravity="right"
                            android:layout_width="fill_parent"
                            android:layout_height="match_parent">

                            <ProgressBar
                                bind:visibility="@{flightWatches.new}"
                                android:paddingTop="5dp"
                                android:layout_width="25dp"
                                android:layout_height="25dp" />

                            <TextView
                                bind:visibility="@{!flightWatches.new}"
                                android:textStyle="bold"
                                android:paddingEnd="5dp"
                                android:paddingTop="5dp"
                                android:paddingBottom="5dp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/textDark"
                                android:text="@{Common.dinhDangTien(flightWatches.current_price)}" />

                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:textColor="@color/stt_gray"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:padding="5dp"
                            android:text="@{` `+Common.dateToString(flightWatches.departure_date.value,`DOW dd/MM`)}" />

                        <TextView
                            bind:visibility="@{flightWatches.type.equals(`RANGE`)}"
                            android:textColor="@color/stt_gray"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:padding="5dp"
                            android:paddingStart="0dp"
                            android:text="@{`- `+Common.dateToString(flightWatches.departure_date_end.value,`DOW dd/MM`)}" />


                        <com.mikepenz.iconics.view.IconicsImageView
                            bind:visibility="@{flightWatches._round_trip}"
                            android:layout_marginStart="5dp"
                            android:layout_marginEnd="5dp"
                            android:layout_width="15dp"
                            android:layout_height="15dp"
                            android:layout_gravity="center_vertical"
                            app:iiv_size="20dp"
                            app:iiv_color="@color/stt_gray"
                            app:iiv_icon="faw-exchange_alt" />

                        <TextView
                            bind:visibility="@{flightWatches._round_trip}"
                            android:textColor="@color/stt_gray"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:padding="5dp"
                            android:paddingStart="0dp"
                            android:text="@{Common.dateToString(flightWatches.return_date.value,`DOW dd/MM`)}" />

                        <LinearLayout
                            android:layout_gravity="center_vertical|center_horizontal"
                            android:gravity="right"
                            android:layout_width="fill_parent"
                            android:layout_height="match_parent">

                            <com.mikepenz.iconics.view.IconicsImageView
                                bind:visibility="@{!flightWatches.new}"
                                android:layout_width="15dp"
                                android:layout_height="15dp"
                                android:layout_gravity="center_vertical"
                                app:iiv_size="20dp"
                                bind:iconis="@{flightWatches.pricesDiffIcon}"
                                bind:iconisColor="@{flightWatches.priceDiffIconColor}" />

                            <TextView
                                bind:visibility="@{!flightWatches.new}"
                                android:layout_gravity="center_horizontal|center_vertical"
                                android:padding="5dp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{flightWatches.pricesDiff}" />


                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@drawable/gradientdiv_vertical" />

            <LinearLayout
                android:padding="5dp"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal">

                <com.mikepenz.iconics.view.IconicsImageView
                    android:layout_marginStart="5dp"
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_gravity="center_vertical"
                    app:iiv_size="20dp"
                    app:iiv_color="@color/stt_gray"
                    app:iiv_icon="faw-child" />

                <TextView
                    android:textColor="@color/stt_gray"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="5dp"
                    android:paddingEnd="5dp"
                    android:text="@{``+flightWatches.paxCount }" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text=" hành khách" />

                <LinearLayout
                    android:layout_gravity="center_vertical"
                    android:gravity="right"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:onClick="@{view -> viewHolder.deleteClick(view,flightWatches.id)}"
                        bind:visibility="@{flightWatches.type.equals(`RANGE`)}"
                        android:gravity="right"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Ngày đi linh hoạt" />

                    <TextView
                        android:onClick="@{view -> viewHolder.deleteClick(view,flightWatches.id)}"
                        bind:visibility="@{flightWatches.type.equals(`TREND`)}"
                        android:gravity="right"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Ngày đi chính xác" />

                    <com.mikepenz.iconics.view.IconicsImageView
                        android:onClick="@{view -> viewHolder.deleteClick(view,flightWatches.id)}"
                        android:paddingStart="5dp"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center_vertical"
                        app:iiv_size="20dp"
                        app:iiv_color="@color/stt_gray"
                        app:iiv_icon="faw-ellipsis_v" />
                </LinearLayout>
            </LinearLayout>

        </LinearLayout>
    </androidx.cardview.widget.CardView>
</layout>