<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="80dp"
    android:layout_margin="4dp"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="4dp">

        <ImageView
            android:id="@+id/imageView"
            android:layout_width="72dp"
            android:layout_height="72dp"
            android:scaleY="1.25"
            android:scaleX="1.25"
            android:scaleType="fitXY"
            app:srcCompat="@drawable/ic_access_time_white_36dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="top"
            android:layout_marginTop="2dp"
            android:layout_marginStart="4dp"
            android:layout_marginLeft="4dp"
            android:layout_toEndOf="@id/imageView"
            android:layout_toRightOf="@id/imageView"
            android:orientation="vertical">

            <TextView
                android:id="@+id/nameTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxLines="1"
                android:text="Item"
                android:textAppearance="@style/TextAppearance.AppCompat.Body1" />

            <TextView
                android:id="@+id/devTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxLines="1"
                android:text="Google Inc."
                android:textAppearance="@style/TextAppearance.AppCompat.Caption" />

            <TextView
                android:id="@+id/ratingTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableEnd="@drawable/ic_action_call"
                android:layout_marginTop="2dp"
                android:drawablePadding="2dp"
                android:drawableRight="@drawable/ic_access_alarms_white_24dp"
                android:gravity="center"
                android:text="4,5"
                android:textAppearance="@style/TextAppearance.AppCompat.Body1"
                android:textSize="12sp" />

        </LinearLayout>


    </RelativeLayout>

</androidx.cardview.widget.CardView>