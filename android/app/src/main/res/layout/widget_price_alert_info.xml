<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:custom="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto">

    <data>

<!--        <variable-->
<!--            name="booking"-->
<!--            type="com.hqt.data.model.BookingV2" /> -->
<!--        -->
        <variable
            name="booking"
            type="com.hqt.view.ui.booking.data.model.BookingV3" />

        <import type="com.hqt.datvemaybay.Common" />

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/activity_vertical_margin"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:padding="5dp">

            <com.mikepenz.iconics.view.IconicsTextView
                style="@style/Text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="{faw_paper_plane}  "
                android:textSize="10dp" />

            <TextView
                android:id="@+id/txtLuotdiTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Cài đặt thông báo giá"
                android:textColor="#000000"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="end"
                android:orientation="vertical">

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:paddingTop="5dp"
            android:layout_margin="5dp"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:background="@drawable/corner_full"
            android:padding="5dp"
            android:layout_height="wrap_content">

            <LinearLayout
                android:padding="5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:orientation="horizontal"
                android:weightSum="9"
                android:baselineAligned="false">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@{Common.getAirPortName(booking.origin_code,false)}" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text=" đi " />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@{Common.getAirPortName(booking.destination_code, false)}" />

            </LinearLayout>

            <LinearLayout
                android:padding="5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:orientation="horizontal"
                android:weightSum="9"
                android:baselineAligned="false">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingEnd="10dp"
                    android:text="@{Common.getDayOfWeek(Common.stringToDate(booking.departure_date,`yyyy-MM-dd`).time)}" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@{Common.dateToString(Common.stringToDate(booking.departure_date,`yyyy-MM-dd`).time,`dd-MM-yyyy`)}" />

            </LinearLayout>

            <LinearLayout
                android:padding="5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <com.mikepenz.iconics.view.IconicsImageView
                    android:layout_marginStart="5dp"
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_gravity="center_vertical"
                    app:iiv_size="20dp"
                    app:iiv_color="@color/stt_gray"
                    app:iiv_icon="faw-child" />

                <TextView
                    android:textColor="@color/stt_gray"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="5dp"
                    android:paddingEnd="5dp"
                    android:text="@{``+booking.totalPax }" />


                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text=" hành khách" />

                <LinearLayout
                    android:layout_gravity="center_vertical"
                    android:gravity="right"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">


                    <TextView
                        android:gravity="right"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Ngày đi chính xác" />


                </LinearLayout>

            </LinearLayout>

            <TextView
                android:padding="5dp"
                style="@style/Text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Khi giá vé thay đổi. Hệ thống sẽ cập nhật qua email và thông báo cho bạn" />

        </LinearLayout>


        <Button
            app:backgroundTint="@color/primary"
            android:textColor="#FFFFFF"
            android:id="@+id/btn_price_alert_add"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Đặt thông báo" />


    </LinearLayout>
</layout>
