<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true"
        android:addStatesFromChildren="true"
        android:background="@color/gbgray"
        tools:context="com.hqt.view.ui.HomeActivity">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true"
            app:elevation="0dp"
            android:background="@color/gbgray"
            android:theme="@style/AppTheme.AppBarOverlay">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/toolbar_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fitsSystemWindows="true"
                app:contentScrim="?attr/colorPrimary"
                app:title="12bay.vn"
                app:titleEnabled="false"
                app:layout_scrollFlags="scroll|exitUntilCollapsed">

                <RelativeLayout
                    android:fitsSystemWindows="true"
                    android:adjustViewBounds="true"
                    android:layout_width="match_parent"
                    android:layout_height="250dp">

                    <ImageView
                        android:id="@+id/headerBG"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_alignParentTop="true"
                        android:alpha="1"
                        android:contentDescription="@string/app_name"
                        android:scaleType="centerCrop"
                        app:srcCompat="@drawable/top_banner" />

                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_above="@+id/headerBG"
                        android:layout_alignParentBottom="true"
                        android:layout_gravity="bottom"
                        android:background="@drawable/top_curve" />
                </RelativeLayout>

                <androidx.appcompat.widget.Toolbar

                    android:id="@+id/toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize"
                    app:elevation="0dp"
                    app:layout_collapseMode="pin"
                    app:layout_scrollFlags="scroll|exitUntilCollapsed"
                    app:popupTheme="@style/AppTheme.PopupOverlay" />


            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>


        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/refresh"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior"
            app:behavior_overlapTop="120dp"
            tools:showIn="@layout/activity_scrolling">

            <include
                android:id="@+id/content_scrolling"
                layout="@layout/content_scrolling"
                app:layout_anchor="@+id/refresh"
                app:layout_anchorGravity="center" />

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>


    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>
