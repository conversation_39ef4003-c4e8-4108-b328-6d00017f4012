<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="itemViewState"
            type="com.hqt.view.ui.reward.ui.state.PromotionItemState" />
    </data>

    <LinearLayout
        android:id="@+id/select_passenger_sheet"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:behavior_hideable="true"
        app:layout_behavior="android.support.design.widget.BottomSheetBehavior">

        <LinearLayout
            android:paddingTop="10dp"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/txtName"
                android:textSize="18sp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/textDark"
                android:text="@{itemViewState.item.name}"
                />

            <LinearLayout
                android:layout_marginTop="5dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView

                    android:id="@+id/txtExpiredTime"
                    android:textSize="14sp"
                    android:padding="5dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/white"
                    android:background="@drawable/corner_full_green"
                    android:text="@{itemViewState.expiredTimeText}"
                    tools:text="Dùng đến: " />

                <com.mikepenz.iconics.view.IconicsImageView
                    android:layout_marginLeft="10dp"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_gravity="center_horizontal|center_vertical"
                    app:iiv_size="16dp"
                    app:iiv_color="@color/google_yellow"
                    app:iiv_icon="gmd_stars" />

                <TextView
                    android:id="@+id/txtPoint"
                    android:padding="5dp"
                    style="@style/Text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:text="5 Điểm"
                    android:text="@{itemViewState.point}"
                    />

            </LinearLayout>

            <LinearLayout
                android:layout_marginTop="10dp"
                android:layout_width="match_parent"
                android:background="@drawable/corner_full"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/txtDescription"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@{itemViewState.descriptionText}"
                    />

            </LinearLayout>

            <LinearLayout
                android:layout_marginTop="10dp"
                android:layout_width="match_parent"
                android:layout_gravity="center_vertical|center_horizontal"
                android:layout_height="wrap_content"
                android:gravity="center_vertical|center_horizontal"
                android:orientation="vertical">

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btnRedeemVoucher"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="5dp"
                    android:gravity="center_vertical|center_horizontal"
                    android:layout_marginBottom="10dp"
                    android:background="@drawable/button_gradient_noborder"
                    android:text="NHẬN MÃ GIẢM GIÁ"
                    android:textColor="#fff" />

            </LinearLayout>

            <LinearLayout

                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal">

                <Button
                    android:visibility="gone"
                    android:id="@+id/btnLogin"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:capitalize="characters"
                    android:checked="true"
                    android:layout_marginBottom="10dp"
                    android:background="@drawable/button_gradient"
                    android:text="ĐĂNG NHẬP để nhận ưu đãi"
                    android:textAllCaps="true"
                    android:textColor="#FFFFFF" />

            </LinearLayout>


        </LinearLayout>


    </LinearLayout>
</layout>