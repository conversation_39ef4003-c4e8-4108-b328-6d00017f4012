<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:id="@+id/column_header_container"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/cell_height"
    android:orientation="vertical">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <LinearLayout
            android:background="#acd9f4"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:orientation="horizontal">

            <TextView
                android:paddingEnd="10dp"
                android:paddingStart="10dp"
                android:id="@+id/column_header_textView"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:layout_weight="4"
                android:gravity="center"
                android:textColor="@color/table_view_default_text_color"
                android:textSize="@dimen/text_size"
                tools:text="Header Data" />

            <ImageButton

                android:id="@+id/column_header_sortButton"
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:layout_gravity="end|center"
                android:layout_marginLeft="4dp"
                android:background="@android:color/transparent"
                android:paddingRight="-5dp"
                android:scaleType="fitXY"
                android:visibility="gone"
                app:srcCompat="@drawable/ic_arrow_drop_up" />

            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="#E1F3FD" />
        </LinearLayout>

    </FrameLayout>
</LinearLayout>
