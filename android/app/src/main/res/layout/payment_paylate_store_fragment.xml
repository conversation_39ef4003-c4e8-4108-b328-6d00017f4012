<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:bind="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="com.hqt.datvemaybay.Common" />

        <import type="android.view.View" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true"
        android:background="@color/gbgray"
        android:id="@+id/coordinatorLayout"
        android:animateLayoutChanges="true">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar"
            android:layout_width="match_parent"
            android:background="@color/primary_dark"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true"
            app:layout_constraintTop_toTopOf="parent"
            app:elevation="5dp"
            android:theme="@style/AppTheme.AppBarOverlay">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/toolbar_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:title="12bay.vn"
                android:background="@color/primary"
                android:fitsSystemWindows="true"
                app:titleEnabled="false"
                app:layout_scrollFlags="enterAlwaysCollapsed">

                <androidx.appcompat.widget.Toolbar
                    android:id="@+id/toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize"
                    app:elevation="0dp"
                    app:layout_collapseMode="pin"
                    app:layout_scrollFlags="scroll|enterAlways"
                    app:popupTheme="@style/AppTheme.PopupOverlay" />


            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/swipe_refresh_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.facebook.shimmer.ShimmerFrameLayout
                    android:id="@+id/shimmer_view_container"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:orientation="vertical"
                    android:visibility="gone"
                    app:shimmer_duration="800">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <include layout="@layout/placeholder_item_pax_input" />

                        <include layout="@layout/placeholder_item_pax_input" />

                        <include layout="@layout/placeholder_item_pax_input" />

                        <include layout="@layout/placeholder_item_pax_input" />

                        <include layout="@layout/placeholder_item_pax_input" />

                        <include layout="@layout/placeholder_item_pax_input" />

                    </LinearLayout>
                </com.facebook.shimmer.ShimmerFrameLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:orientation="vertical"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <LinearLayout
                            android:id="@+id/headStatusLayout"
                            android:layout_width="fill_parent"
                            android:layout_gravity="center_horizontal"
                            android:gravity="center_horizontal"
                            android:visibility="visible"
                            android:layout_height="wrap_content">

                            <TextView

                                android:background="@color/green"
                                android:id="@+id/headStatus"
                                android:textColor="@color/white"
                                android:layout_width="match_parent"
                                android:textAlignment="center"
                                android:layout_height="wrap_content"
                                android:textSize="12sp"
                                android:padding="4dp"
                                android:layout_gravity="center_horizontal|center_vertical"
                                android:text="hạn thanh toán trong 10h 20p" />

                        </LinearLayout>
                    </LinearLayout>

                    <include
                        android:id="@+id/content"
                        layout="@layout/payment_paylate_store_layout_content_scrolling" />
                </LinearLayout>

            </LinearLayout>
        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>