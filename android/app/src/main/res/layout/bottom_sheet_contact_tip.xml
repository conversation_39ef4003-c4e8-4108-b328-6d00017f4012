<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="booking"
            type="com.hqt.data.model.Booking" />

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#EDEDED"
        android:orientation="vertical"
        app:behavior_hideable="true"
        app:layout_behavior="android.support.design.widget.BottomSheetBehavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:gravity="center_vertical"
                android:layout_height="wrap_content">

                <com.mikepenz.iconics.view.IconicsImageView
                    android:layout_marginStart="10dp"
                    android:layout_gravity="center_vertical|center_horizontal"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    app:iiv_padding="2dp"
                    app:iiv_color="@color/primary_dark"
                    app:iiv_icon="gmd_perm_contact_calendar" />

                <TextView
                    android:layout_marginStart="5dp"
                    android:layout_width="wrap_content"
                    android:padding="10dp"
                    android:paddingStart="0dp"
                    android:layout_height="wrap_content"
                    android:text="Liên hệ hỗ trợ" />

                <LinearLayout
                    android:layout_marginRight="10dp"
                    android:gravity="end"
                    android:layout_width="fill_parent"
                    android:layout_height="match_parent">

                    <TextView
                        android:gravity="center_vertical"
                        android:paddingStart="40dp"
                        android:id="@+id/btnClose"
                        android:textSize="12sp"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:textColor="@color/primary_dark"
                        android:text="Đóng" />
                </LinearLayout>
            </LinearLayout>

            <androidx.cardview.widget.CardView
                android:id="@+id/card_view1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="0dp"
                app:cardElevation="4dp"
                style="@style/CardViewStyle.Light"
                app:cardPreventCornerOverlap="false"
                app:contentPadding="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="5dp"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Chào" />

                        <LinearLayout
                            android:gravity="center_vertical|center_horizontal"
                            android:paddingTop="2dp"
                            android:paddingBottom="2dp"
                            android:paddingStart="5dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_gravity="center_vertical|center_horizontal"
                                android:textStyle="bold"
                                android:textColor="@color/primary_dark"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{booking.contact_name}" />

                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Mã đơn hàng của bạn là: " />

                        <LinearLayout
                            android:id="@+id/btnCopy"
                            android:background="@drawable/edit_text"
                            android:gravity="center_vertical|center_horizontal"
                            android:paddingTop="2dp"
                            android:paddingBottom="2dp"
                            android:paddingStart="10dp"
                            android:paddingEnd="5dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_gravity="center_vertical|center_horizontal"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:textColor="@color/primary_dark"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{`#` +booking.id}" />

                            <com.mikepenz.iconics.view.IconicsImageView
                                android:layout_marginStart="5dp"
                                android:layout_gravity="center_vertical|center_horizontal"
                                android:layout_width="20dp"
                                android:layout_height="20dp"
                                app:iiv_padding="2dp"
                                app:iiv_color="@color/primary_dark"
                                app:iiv_icon="faw-copy" />
                        </LinearLayout>

                    </LinearLayout>

                    <TextView
                        android:paddingBottom="10dp"
                        android:paddingTop="10dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Để được hỗ trợ nhanh chóng, bạn vui lòng chọn một trong các phương thức liên hệ dưới đây" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingBottom="20dp">

                        <LinearLayout
                            android:padding="10dp"
                            android:gravity="center_horizontal|center_vertical"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <TextView
                                android:id="@+id/btnCallHotline"
                                android:textStyle="bold"
                                android:textColor="@color/primary_dark"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="16sp"
                                android:text="GỌI ĐIỆN THOẠI" />

                            <com.mikepenz.iconics.view.IconicsImageView
                                android:layout_marginStart="5dp"
                                android:gravity="center_horizontal|center_vertical"
                                android:layout_width="20dp"
                                android:layout_height="20dp"
                                app:iiv_color="@color/primary_dark"
                                app:iiv_icon="faw-phone"
                                app:iiv_padding="2dp" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="right"
                                android:orientation="horizontal">

                                <TextView
                                    android:id="@+id/btnSendSMS"
                                    android:textSize="16sp"
                                    android:textStyle="bold"
                                    android:textColor="@color/primary_dark"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="end"
                                    android:text="GỬI TIN NHẮN" />

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:layout_marginStart="5dp"
                                    android:layout_gravity="end"
                                    android:layout_width="20dp"
                                    android:layout_height="20dp"
                                    app:iiv_padding="2dp"
                                    app:iiv_color="@color/primary_dark"
                                    app:iiv_icon="gmd_message" />
                            </LinearLayout>
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>


        </LinearLayout>


    </LinearLayout>
</layout>