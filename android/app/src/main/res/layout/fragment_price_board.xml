<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:id="@+id/coordinatorLayout"
        android:fitsSystemWindows="true"
        android:animateLayoutChanges="true"
        android:background="@color/gbgray"
        tools:context="com.hqt.view.ui.HomeActivity">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true"
            app:elevation="0dp"
            android:theme="@style/AppTheme.AppBarOverlay">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/toolbar_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fitsSystemWindows="true"
                app:contentScrim="?attr/colorPrimary"
                app:title="12bay.vn"
                app:titleEnabled="false"
                app:layout_scrollFlags="noScroll">


                <androidx.appcompat.widget.Toolbar
                    android:id="@+id/toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize"
                    app:elevation="0dp"
                    app:layout_collapseMode="pin"
                    app:layout_scrollFlags="scroll|exitUntilCollapsed"
                    app:popupTheme="@style/AppTheme.PopupOverlay" />

            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <LinearLayout
            android:background="@color/gbgray"
            android:layout_marginTop="?attr/actionBarSize"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">


            <com.facebook.shimmer.ShimmerFrameLayout
                android:paddingLeft="5dp"
                android:paddingRight="5dp"
                android:id="@+id/shimmer_view_container"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:orientation="vertical"
                android:visibility="gone"
                app:shimmer_duration="800">

                <LinearLayout
                    android:layout_below="@+id/search"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <include layout="@layout/placeholder_item_train" />

                    <include layout="@layout/placeholder_item_train" />

                    <include layout="@layout/placeholder_item_train" />

                    <include layout="@layout/placeholder_item_train" />

                    <include layout="@layout/placeholder_item_train" />

                    <include layout="@layout/placeholder_item_train" />

                    <include layout="@layout/placeholder_item_train" />

                    <include layout="@layout/placeholder_item_train" />

                    <include layout="@layout/placeholder_item_train" />

                    <include layout="@layout/placeholder_item_train" />
                </LinearLayout>
            </com.facebook.shimmer.ShimmerFrameLayout>


            <LinearLayout
                android:background="@color/white"
                android:padding="5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <com.mikepenz.iconics.view.IconicsImageView
                    android:padding="5dp"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    app:iiv_color="@color/primary"
                    app:iiv_icon="faw-map-marker-alt" />

                <TextView
                    android:padding="7dp"
                    android:layout_marginEnd="5dp"
                    android:paddingEnd="25dp"
                    android:id="@+id/txtOrigin"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Chọn nơi đi" />

                <com.mikepenz.iconics.view.IconicsImageView
                    android:padding="5dp"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    app:iiv_color="@color/primary"
                    app:iiv_icon="faw-plane" />

                <TextView
                    android:padding="7dp"
                    android:paddingEnd="25dp"
                    android:id="@+id/txtDestination"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:text="Chọn nơi đến" />
            </LinearLayout>

            <com.evrencoskun.tableview.TableView
                android:id="@+id/tableview"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:column_header_height="40dp"
                app:separator_color="@color/fui_transparent"
                app:show_horizontal_separator="false"
                app:show_vertical_separator="false" />
        </LinearLayout>

        <LinearLayout
            android:layout_marginTop="?attr/actionBarSize"
            android:id="@+id/notfound"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="500dp"
            android:orientation="vertical">

            <ImageView
                android:layout_marginTop="200dp"
                android:layout_gravity="center_horizontal"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:adjustViewBounds="true"
                android:background="@drawable/icon_flight_status_state"
                android:scaleType="centerCrop"
                app:layout_behavior="@string/appbar_scrolling_view_behavior" />

            <TextView
                android:textSize="16sp"
                android:padding="5dp"
                android:textStyle="bold"
                android:layout_width="match_parent"
                android:gravity="center"
                android:layout_height="wrap_content"
                android:text="Hệ thống đang tiến hành cập nhật" />

            <TextView
                android:textSize="12sp"
                android:layout_width="match_parent"
                android:gravity="center"
                android:layout_height="wrap_content"
                android:text="Vui lòng đợi trong giây lát" />

            <LinearLayout
                android:layout_margin="20dp"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="right">

                <RadioGroup
                    android:id="@+id/radio"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="5dp"
                    android:layout_weight="1"
                    android:background="@drawable/corner_full"
                    android:orientation="horizontal"
                    tools:ignore="UselessParent">

                    <Button
                        android:id="@+id/btnAddNew"
                        style="@style/MyApp.Button.Big"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="0.5"
                        android:background="@drawable/button_one_way"
                        android:checked="true"
                        android:text="Về trang chủ"
                        android:textAllCaps="true"
                        android:textColor="#FFFFFF" />

                    <Button
                        android:id="@+id/btnSearchFlight"
                        style="@style/MyApp.Button.Big"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="0.5"
                        android:background="@color/fui_transparent"
                        android:text="@string/datCho"
                        android:textAllCaps="true"
                        android:textColor="#00a2e3" />
                </RadioGroup>

            </LinearLayout>
        </LinearLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</layout>