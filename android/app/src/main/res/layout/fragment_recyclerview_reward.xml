<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:context="com.hqt.view.ui.reward.ui.activity.RewardActivity">

        <com.facebook.shimmer.ShimmerFrameLayout
            android:layout_marginTop="155dp"
            android:id="@+id/shimmer_view_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:background="#EDEDED"
            android:orientation="vertical"
            android:visibility="visible"
            app:shimmer_duration="800">

            <LinearLayout
                android:layout_width="match_parent"
                android:background="#EDEDED"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <include layout="@layout/placeholder_item_voucher" />

                <include layout="@layout/placeholder_item_voucher" />

                <include layout="@layout/placeholder_item_voucher" />

                <include layout="@layout/placeholder_item_voucher" />

                <include layout="@layout/placeholder_item_voucher" />

                <include layout="@layout/placeholder_item_voucher" />

                <include layout="@layout/placeholder_item_voucher" />

            </LinearLayout>
        </com.facebook.shimmer.ShimmerFrameLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:clipToPadding="false"
            android:layout_height="match_parent">

        </androidx.recyclerview.widget.RecyclerView>

        <include
            android:visibility="gone"
            android:layout_marginTop="200dp"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            layout="@layout/empty_state_layout" />

    </RelativeLayout>
</layout>