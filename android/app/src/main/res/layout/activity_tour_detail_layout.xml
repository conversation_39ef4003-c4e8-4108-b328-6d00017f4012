<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:id="@+id/coordinatorLayout"
        android:addStatesFromChildren="true"
        android:fitsSystemWindows="true"
        android:animateLayoutChanges="true"
        android:background="@color/gbgray">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true"
            app:elevation="0dp"
            android:background="@color/gbgray"
            android:theme="@style/AppTheme.AppBarOverlay">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/toolbar_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fitsSystemWindows="true"
                app:contentScrim="?attr/colorPrimary"
                app:title="12bay.vn"
                app:titleEnabled="false"
                app:layout_scrollFlags="scroll|exitUntilCollapsed">

                <RelativeLayout
                    android:id="@+id/sliderLayout"
                    android:visibility="gone"
                    android:fitsSystemWindows="true"
                    android:adjustViewBounds="true"
                    android:layout_width="match_parent"
                    android:layout_height="300dp">

                    <com.smarteist.autoimageslider.SliderView
                        android:visibility="visible"
                        android:id="@+id/slider"
                        android:layout_alignParentTop="true"
                        android:layout_width="match_parent"
                        android:layout_height="300dp"
                        app:sliderAnimationDuration="600"
                        app:sliderAutoCycleDirection="back_and_forth"
                        app:sliderAutoCycleEnabled="true"
                        app:sliderIndicatorAnimationDuration="600"
                        app:sliderIndicatorGravity="center_horizontal|bottom"
                        app:sliderIndicatorMargin="15dp"
                        app:sliderIndicatorOrientation="horizontal"
                        app:sliderIndicatorPadding="3dp"
                        app:sliderIndicatorRadius="2dp"
                        app:sliderIndicatorSelectedColor="#5A5A5A"
                        app:sliderIndicatorUnselectedColor="#FFF"
                        app:sliderScrollTimeInSec="1"
                        app:sliderStartAutoCycle="true" />

                </RelativeLayout>

                <androidx.appcompat.widget.Toolbar

                    android:id="@+id/toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize"
                    app:elevation="0dp"
                    app:layout_collapseMode="pin"
                    app:layout_scrollFlags="scroll|exitUntilCollapsed"
                    app:popupTheme="@style/AppTheme.PopupOverlay" />


            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <LinearLayout
            app:behavior_overlapTop="20dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_behavior="@string/appbar_scrolling_view_behavior"
            android:orientation="vertical">

            <com.facebook.shimmer.ShimmerFrameLayout
                android:id="@+id/shimmer_view_container"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:orientation="vertical"
                android:visibility="visible"
                app:shimmer_duration="800">


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <include layout="@layout/placeholder_tour_detail_layout" />

                    <include layout="@layout/placeholder_tour_detail_layout" />

                    <include layout="@layout/placeholder_tour_detail_layout" />

                    <include layout="@layout/placeholder_tour_detail_layout" />

                    <include layout="@layout/placeholder_tour_detail_layout" />

                    <include layout="@layout/placeholder_tour_detail_layout" />


                </LinearLayout>

            </com.facebook.shimmer.ShimmerFrameLayout>

            <androidx.core.widget.NestedScrollView
                android:id="@+id/container"
                android:visibility="gone"
                android:layout_width="match_parent"
                app:layout_behavior="@string/hide_bottom_view_on_scroll_behavior"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:cardElevation="5dp">

                        <LinearLayout
                            android:orientation="vertical"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <TextView
                                android:id="@+id/tour_title"
                                android:textSize="16sp"
                                android:textColor="#003A6F"
                                android:textStyle="bold"
                                android:padding="10dp"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content" />

                            <include
                                android:id="@+id/tour_info"
                                layout="@layout/tour_short_info_layout" />


                        </LinearLayout>
                    </androidx.cardview.widget.CardView>

                    <androidx.cardview.widget.CardView
                        android:id="@+id/schedule_layout"
                        android:layout_marginTop="10dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:cardElevation="5dp">

                        <LinearLayout
                            android:orientation="vertical"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <TextView

                                android:text="Lịch khởi hành: "
                                android:textColor="@color/btnColor"
                                android:textStyle="bold"
                                android:textSize="16sp"
                                android:padding="10dp"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content" />

                            <include
                                android:id="@+id/schedule_view"
                                layout="@layout/widget_tour_schedule_item" />
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>

                    <androidx.cardview.widget.CardView
                        android:layout_marginTop="10dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:cardElevation="5dp">

                        <LinearLayout
                            android:orientation="vertical"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <TextView
                                android:textColor="@color/btnColor"
                                android:textStyle="bold"
                                android:text="Chương trình: "
                                android:textSize="16sp"
                                android:padding="10dp"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content" />

                            <LinearLayout
                                android:id="@+id/plans_container"
                                android:orientation="vertical"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content" />
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>

                    <androidx.cardview.widget.CardView
                        android:layout_marginTop="10dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:cardElevation="5dp">

                        <LinearLayout
                            android:orientation="vertical"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <TextView
                                android:textColor="@color/btnColor"
                                android:textStyle="bold"
                                android:text="Chi chú: "
                                android:textSize="16sp"
                                android:padding="10dp"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content" />

                            <LinearLayout
                                android:id="@+id/html_text"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical" />

                        </LinearLayout>
                    </androidx.cardview.widget.CardView>


                </LinearLayout>


            </androidx.core.widget.NestedScrollView>

        </LinearLayout>

        <FrameLayout
            android:id="@+id/bottomLayout"
            android:layout_width="match_parent"
            app:layout_behavior="@string/hide_bottom_view_on_scroll_behavior"
            android:layout_height="match_parent">

            <LinearLayout

                android:id="@+id/bottom_sheet"
                android:orientation="vertical"
                android:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal|bottom">

                <include
                    android:id="@+id/select_schedule"
                    layout="@layout/select_tour_bottom_layout" />

            </LinearLayout>
        </FrameLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>