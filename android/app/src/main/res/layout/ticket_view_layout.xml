<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">
<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">
    <LinearLayout

        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_margin="5dp"
        android:weightSum="8">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="3"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/from"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:singleLine="true"
                android:text="Hà Nội"
                android:textColor="#000000"
                android:textSize="12sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/thoiGianBay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:text="Time"
                android:textColor="@color/textDark"
                android:textSize="12sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="2"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/imageView1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                app:srcCompat="@drawable/outbound" />

            <TextView
                android:id="@+id/duration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="1h15p"
                android:textColor="#000000"
                android:textSize="12sp"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="3"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/to"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:text="Hồ Chí Minh"
                android:textColor="#000000"
                android:textSize="12sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/thoiGianDen"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:text="Time"
                android:textColor="@color/textDark"
                android:textSize="12sp" />
        </LinearLayout>

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        android:background="@color/diver_color" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="5dp"
        android:weightSum="8">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="3"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/logo"
                android:layout_width="wrap_content"
                android:layout_height="fill_parent"
                android:layout_gravity="center"
                android:layout_marginStart="20dp"
                android:layout_marginEnd="10dp"
                android:contentDescription="Logo"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:scaleType="centerInside"
                android:src="@drawable/logo_bl" />

            <TextView
                android:id="@+id/hangBay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:text=""
                android:textColor="#000000"
                android:visibility="gone" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/diemDung"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="Chuyến bay"
                android:textColor="@color/textDark"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/chuyenBay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="VN1P"
                android:textColor="@color/textDark"
                android:textSize="12sp"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="3"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/textView4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Hạng vé"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/loaiVe"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:text="Starter"
                android:textColor="#000000"
                android:textSize="12sp"
                android:textStyle="bold" />
        </LinearLayout>

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        android:background="@color/diver_color" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="5dp"
        android:weightSum="8">
        <LinearLayout
            android:gravity="center_horizontal"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="3"
            android:orientation="vertical">

            <TextView
                android:id="@+id/chum"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="Giá vé"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/gia"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:singleLine="true"
                android:textColor="#da281c"
                android:textStyle="bold"
                android:textSize="12sp" />
        </LinearLayout>

        <LinearLayout
            android:gravity="center_horizontal"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            android:orientation="vertical">

            <TextView
                android:id="@+id/text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Thuế phí"
                android:textSize="12sp" />

            <TextView

                android:id="@+id/thuePhi"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:text="Starter"
                android:textColor="@color/textDark"
                android:textSize="12sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0"
            android:visibility="gone"
            android:orientation="vertical">

            <TextView
                android:id="@+id/textV"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Phí sân bay"
                android:textColor="#000000"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/thueSanBay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:text="100,100"
                android:textColor="#000000"
                android:textSize="12sp" />
        </LinearLayout>

        <LinearLayout
            android:gravity="center_horizontal"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="3"
            android:orientation="vertical">

            <TextView
                android:id="@+id/giaP"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Tổng cộng:"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/giaPhi"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="100,000 Đ"
                android:textColor="#000FFF"
                android:textSize="12sp"
                android:textStyle="bold" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>
</RelativeLayout>