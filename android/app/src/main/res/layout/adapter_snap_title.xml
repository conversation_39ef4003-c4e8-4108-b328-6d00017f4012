<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:focusable="true"
    app:cardCornerRadius="5dp"
    android:foreground="?attr/selectableItemBackground"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="0dp">

        <ImageView
            android:contentDescription="@string/txt_chat"
            android:id="@+id/imageView"
            android:layout_width="200dp"
            android:layout_height="150dp"
            android:scaleType="centerCrop"
            android:background="@drawable/top_banner" />

        <TextView
            android:id="@+id/nameTextView"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_alignBottom="@+id/imageView"
            android:layout_alignParentEnd="true"
            android:maxLines="1"
            android:ellipsize="end"
            android:padding="5dp"
            android:textColor="@color/white"
            android:text="xxxxxxxxx"
            android:textStyle="bold"/>
        <TextView
            android:visibility="gone"
            android:id="@+id/subtitleTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
         />


    </RelativeLayout>

</androidx.cardview.widget.CardView>