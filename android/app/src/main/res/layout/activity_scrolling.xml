<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:id="@+id/coordinatorLayout"
    android:background="@color/gbgray"
    tools:context="com.hqt.view.ui.HomeActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar"
        android:visibility="visible"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true"
        app:elevation="0dp"
        android:theme="@style/AppTheme.AppBarOverlay">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/toolbar_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fitsSystemWindows="true"
            app:contentScrim="?attr/colorPrimary"
            app:title="12bay.vn"
            app:titleEnabled="false"
            app:layout_scrollFlags="scroll|exitUntilCollapsed">

            <ImageView
                android:id="@+id/headerBG"
                android:layout_width="match_parent"
                android:layout_height="220dp"
                android:scaleType="centerCrop"
                android:contentDescription="@string/app_name"
                android:fitsSystemWindows="true"
                android:adjustViewBounds="true"
                app:srcCompat="@drawable/top_banner"
                android:alpha="1" />

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:background="@drawable/top_curve" />

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                app:layout_collapseMode="pin"
                app:layout_scrollFlags="scroll|exitUntilCollapsed"
                app:popupTheme="@style/AppTheme.PopupOverlay"
                app:elevation="0dp" />


        </com.google.android.material.appbar.CollapsingToolbarLayout>
    </com.google.android.material.appbar.AppBarLayout>


    <include layout="@layout/content_scrolling" />

    <LinearLayout
        android:id="@+id/mini_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:paddingTop="60dp"
        android:paddingBottom="0dp"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="match_parent"
            android:gravity="center_horizontal"
            android:layout_height="wrap_content">

            <androidx.cardview.widget.CardView
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:layout_marginLeft="40dp"
                android:layout_marginRight="40dp"
                android:background="@color/white"
                android:focusable="true"
                app:cardMaxElevation="0dp"
                android:elevation="0dp"
                android:foreground="?attr/selectableItemBackground"
                app:cardCornerRadius="10dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:weightSum="9"
                    android:padding="1dp"
                    android:gravity="center_horizontal">

                    <LinearLayout
                        android:id="@+id/minibtnSearch"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_margin="5dp"
                        android:layout_weight="3"
                        android:gravity="center"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center">

                            <ImageButton
                                android:id="@+id/miniSearch"
                                android:layout_width="30dp"
                                android:layout_height="30dp"
                                android:background="@drawable/round_button"
                                android:gravity="center_vertical|center_horizontal"
                                android:textColor="#fff"
                                android:textSize="24sp"
                                app:srcCompat="@drawable/ic_logo_no_free" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:paddingTop="5dp">


                        </LinearLayout>
                    </LinearLayout>

                    <View
                        android:layout_width="1dp"
                        android:layout_height="match_parent"
                        android:layout_marginTop="5dp"
                        android:layout_marginBottom="5dp"
                        android:background="@drawable/gradientdiv" />

                    <LinearLayout
                        android:id="@+id/minibtnPromoss"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_margin="5dp"
                        android:layout_weight="3"
                        android:gravity="center"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center">

                            <com.mikepenz.iconics.view.IconicsButton
                                android:id="@+id/miniPromo"
                                android:layout_width="30dp"
                                android:layout_height="30dp"
                                android:background="@drawable/round_button"
                                android:gravity="center_vertical|center_horizontal"
                                android:text="{faw_rocket}"
                                android:textColor="#fff"
                                android:textSize="24sp" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:paddingTop="5dp">

                        </LinearLayout>
                    </LinearLayout>

                    <View
                        android:layout_width="1dp"
                        android:layout_height="match_parent"
                        android:layout_marginTop="5dp"
                        android:layout_marginBottom="5dp"
                        android:background="@drawable/gradientdiv" />

                    <LinearLayout
                        android:id="@+id/minibtnPromo"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_margin="5dp"
                        android:layout_weight="3"
                        android:gravity="center"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center">

                            <com.mikepenz.iconics.view.IconicsButton
                                android:id="@+id/miniPayment"
                                android:layout_width="30dp"
                                android:layout_height="30dp"
                                android:background="@drawable/round_button"
                                android:gravity="center_vertical|center_horizontal"
                                android:text="{faw_credit_card}"
                                android:textColor="#fff"
                                android:textSize="24sp" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:paddingTop="5dp">

                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>

            </androidx.cardview.widget.CardView>


        </LinearLayout>


    </LinearLayout>

    <!--<com.airbnb.lottie.LottieAnimationView-->
    <!--android:id="@+id/animation_view"-->
    <!--android:layout_width="wrap_content"-->
    <!--android:layout_height="wrap_content"-->
    <!--android:scaleType="centerCrop"-->
    <!--app:lottie_fileName="data.json"-->
    <!--app:lottie_loop="false"-->
    <!--app:lottie_autoPlay="true" />-->

    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bnve"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/white"
        android:layout_gravity="bottom"
        app:itemIconTint="@color/selector_item_gray_color"
        app:itemTextColor="@color/selector_item_gray_color"
        app:menu="@menu/navigation" />


</androidx.coordinatorlayout.widget.CoordinatorLayout>
