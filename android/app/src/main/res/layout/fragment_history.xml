<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/coordinatorLayout"
    android:fitsSystemWindows="true"
    android:background="@color/gbgray"
    tools:context="com.hqt.view.ui.HomeActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar"
        android:visibility="visible"
        android:layout_width="match_parent"
        android:background="@color/primary_dark"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true"
        app:elevation="0dp"
        android:theme="@style/AppTheme.AppBarOverlay">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/toolbar_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:contentScrim="@color/primary_dark"
            app:title="12bay.vn"
            app:titleEnabled="false"
            app:layout_scrollFlags="enterAlwaysCollapsed">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:background="@color/primary"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                app:elevation="0dp"
                app:layout_collapseMode="pin"
                app:layout_scrollFlags="scroll|enterAlways"
                app:popupTheme="@style/AppTheme.PopupOverlay" />


        </com.google.android.material.appbar.CollapsingToolbarLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <com.facebook.shimmer.ShimmerFrameLayout
        android:id="@+id/shimmer_view_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:layout_marginTop="?attr/actionBarSize"
        android:orientation="vertical"
        android:visibility="visible"
        app:shimmer_duration="800">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <include layout="@layout/placeholder_item_booking" />

            <include layout="@layout/placeholder_item_booking" />

            <include layout="@layout/placeholder_item_booking" />

            <include layout="@layout/placeholder_item_booking" />

            <include layout="@layout/placeholder_item_booking" />

        </LinearLayout>
    </com.facebook.shimmer.ShimmerFrameLayout>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_behavior="@string/appbar_scrolling_view_behavior" />

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <LinearLayout
        android:layout_marginTop="?attr/actionBarSize"
        android:id="@+id/notfound"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <ImageView
            android:contentDescription="@string/txt_general_rules"
            android:layout_width="wrap_content"
            android:layout_height="300dp"
            android:adjustViewBounds="true"
            android:background="@drawable/not_found_booking"
            android:scaleType="centerCrop"
            app:layout_behavior="@string/appbar_scrolling_view_behavior" />

        <TextView
            android:layout_width="match_parent"
            android:gravity="center"
            android:layout_height="wrap_content"
            android:text="Hình như bạn chưa có đặt chỗ trước đó !" />

        <LinearLayout
            android:layout_margin="20dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="right">

            <RadioGroup
                android:id="@+id/radio"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:layout_weight="1"
                android:background="@drawable/corner_full"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btnNewBook"
                    style="@style/MyApp.Button.Big"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.5"
                    android:background="@drawable/button_one_way"
                    android:checked="true"
                    android:text="Đặt chuyến mới"
                    android:textAllCaps="true"
                    android:textColor="#FFFFFF" />

                <Button
                    android:id="@+id/btnLogin"
                    style="@style/MyApp.Button.Big"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.5"
                    android:background="@color/fui_transparent"
                    android:text="@string/txt_login"
                    android:textAllCaps="true"
                    android:textColor="#00a2e3" />
            </RadioGroup>

        </LinearLayout>
    </LinearLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>


