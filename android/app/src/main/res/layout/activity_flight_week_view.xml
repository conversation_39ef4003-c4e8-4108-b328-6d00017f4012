<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true"
        android:background="@color/gbgray"
        android:id="@+id/coordinatorLayout"
        android:animateLayoutChanges="true">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:background="@color/primary_dark"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true"
            app:layout_constraintTop_toTopOf="parent"
            app:elevation="5dp"
            android:theme="@style/AppTheme.AppBarOverlay">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/toolbar_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:title="12bay.vn"
                android:background="@color/primary"
                android:fitsSystemWindows="true"
                app:titleEnabled="false"
                app:layout_scrollFlags="enterAlwaysCollapsed">

                <androidx.appcompat.widget.Toolbar
                    android:id="@+id/toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize"
                    app:elevation="0dp"
                    app:layout_collapseMode="pin"
                    app:layout_scrollFlags="scroll|enterAlways"
                    app:popupTheme="@style/AppTheme.PopupOverlay" />

            </com.google.android.material.appbar.CollapsingToolbarLayout>


        </com.google.android.material.appbar.AppBarLayout>


        <LinearLayout
            app:layout_behavior="@string/appbar_scrolling_view_behavior"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="5"
                        app:cardCornerRadius="2dp"
                        app:cardElevation="2dp"
                        style="@style/CardViewStyle.Light"
                        android:layout_marginBottom="5dp"
                        android:layout_marginLeft="5dp"
                        android:layout_marginRight="5dp"
                        android:layout_marginTop="5dp"
                        app:cardPreventCornerOverlap="false"
                        app:contentPadding="2dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:padding="2dp"
                                android:orientation="horizontal">

                                <ImageView
                                    android:layout_marginTop="3dp"
                                    android:layout_marginLeft="5dp"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:background="@drawable/outbound" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="match_parent"
                                    android:layout_gravity="left"
                                    android:padding="5dp"
                                    android:textStyle="bold"
                                    style="@style/Text"
                                    android:text="Khởi hành:" />

                                <LinearLayout
                                    android:layout_width="fill_parent"
                                    android:layout_height="wrap_content"
                                    android:gravity="right"
                                    android:orientation="horizontal">

                                    <com.mikepenz.iconics.view.IconicsTextView
                                        android:id="@+id/txtNgayDi"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:padding="5dp"
                                        android:textStyle="bold"
                                        style="@style/Text"
                                        android:text="Khởi hành{faw_calendar}" />
                                </LinearLayout>

                            </LinearLayout>

                            <View
                                android:layout_width="match_parent"
                                android:layout_height="1dp"
                                android:layout_marginLeft="7dp"
                                android:layout_marginRight="7dp"
                                android:background="@color/diver_color" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">


                                <com.prolificinteractive.materialcalendarview.MaterialCalendarView
                                    android:id="@+id/calendarView"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    app:mcv_showOtherDates="defaults"
                                    app:mcv_headerTextAppearance="@style/TitleTextView"
                                    app:mcv_dateTextAppearance="@style/TitleDateView"
                                    app:mcv_weekDayTextAppearance="@style/TitleWeekView"
                                    app:mcv_selectionColor="@color/primary"
                                    android:padding="10dp"
                                    app:mcv_firstDayOfWeek="monday" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:id="@+id/viewToolDi"
                                android:visibility="gone"
                                android:orientation="horizontal">

                                <androidx.appcompat.widget.AppCompatButton
                                    android:id="@+id/btnPreviousWeek"
                                    android:layout_width="wrap_content"
                                    android:textSize="10sp"
                                    android:layout_margin="2dp"
                                    android:layout_height="35dp"
                                    android:textColor="@color/white"
                                    style="@style/MyApp.Button.Big"
                                    android:text="Tuần trước" />

                                <LinearLayout
                                    android:layout_width="fill_parent"
                                    android:gravity="right"
                                    android:layout_height="wrap_content">

                                    <androidx.appcompat.widget.AppCompatButton
                                        android:id="@+id/btnNextWeek"
                                        android:layout_width="wrap_content"
                                        android:textColor="@color/white"
                                        style="@style/MyApp.Button.Big"
                                        android:textSize="10sp"
                                        android:layout_margin="2dp"
                                        android:layout_height="35dp"
                                        android:height="31dp"
                                        android:text="Tuần tiếp" />

                                </LinearLayout>
                            </LinearLayout>
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>

                    <androidx.cardview.widget.CardView
                        android:id="@+id/layout_chieuVe"
                        android:layout_width="match_parent"
                        android:layout_height="fill_parent"
                        android:layout_weight="5"
                        app:cardCornerRadius="2dp"
                        app:cardElevation="2dp"
                        android:visibility="visible"
                        style="@style/CardViewStyle.Light"
                        android:layout_marginBottom="5dp"
                        android:layout_marginLeft="5dp"
                        android:layout_marginRight="5dp"
                        android:layout_marginTop="5dp"
                        app:cardPreventCornerOverlap="false"
                        app:contentPadding="2dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal">

                                    <ImageView
                                        android:layout_marginTop="3dp"
                                        android:layout_marginLeft="5dp"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:background="@drawable/inbound" />

                                    <TextView

                                        android:layout_width="wrap_content"
                                        android:layout_height="match_parent"
                                        android:layout_gravity="left"
                                        android:padding="5dp"
                                        android:textStyle="bold"
                                        style="@style/Text"
                                        android:text="Trở về:" />

                                    <LinearLayout
                                        android:layout_width="fill_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="right"
                                        android:orientation="horizontal">

                                        <com.mikepenz.iconics.view.IconicsTextView
                                            android:id="@+id/txtNgayVe"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:padding="5dp"
                                            android:textStyle="bold"
                                            style="@style/Text"
                                            android:text="Khởi hành {faw_calendar}"
                                            tools:ignore="TooDeepLayout" />
                                    </LinearLayout>
                                </LinearLayout>

                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="1dp"
                                    android:layout_marginLeft="7dp"
                                    android:layout_marginRight="7dp"
                                    android:background="@color/diver_color" />

                            </LinearLayout>

                            <com.prolificinteractive.materialcalendarview.MaterialCalendarView
                                android:id="@+id/calendarViewReturn"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                app:mcv_showOtherDates="defaults"
                                app:mcv_headerTextAppearance="@style/TitleTextView"
                                app:mcv_dateTextAppearance="@style/TitleDateView"
                                app:mcv_weekDayTextAppearance="@style/TitleWeekView"
                                app:mcv_selectionColor="@color/primary"
                                app:mcv_firstDayOfWeek="monday" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:id="@+id/viewToolVe"
                                android:visibility="gone"
                                android:orientation="horizontal">

                                <androidx.appcompat.widget.AppCompatButton
                                    android:id="@+id/btnPreviousWeekVe"
                                    android:layout_width="wrap_content"
                                    android:textSize="10sp"
                                    android:layout_margin="2dp"
                                    android:layout_height="35dp"
                                    android:textColor="@color/white"
                                    style="@style/MyApp.Button.Big"
                                    android:text="Tuần trước" />

                                <LinearLayout
                                    android:layout_width="fill_parent"
                                    android:gravity="right"
                                    android:layout_height="wrap_content">

                                    <androidx.appcompat.widget.AppCompatButton
                                        android:id="@+id/btnNextWeekVe"
                                        android:layout_width="wrap_content"
                                        android:textSize="10sp"
                                        android:layout_margin="2dp"
                                        android:layout_height="35dp"
                                        android:textColor="@color/white"
                                        style="@style/MyApp.Button.Big"
                                        android:text="Tuần tiếp" />
                                </LinearLayout>
                            </LinearLayout>
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>

                </LinearLayout>

            </ScrollView>
        </LinearLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:behavior_hideable="true"
            app:layout_behavior="com.hqt.util.helper.OutOfScreenBottomSheetBehavior">

            <LinearLayout
                android:id="@+id/quickView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="visible"
                android:layout_alignParentBottom="true"
                android:layout_alignParentLeft="true"
                android:baselineAligned="true"
                android:orientation="vertical">

                <Button
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="14sp"
                    android:text="TIẾP TỤC »"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="5dp"
                    android:layout_marginLeft="25dp"
                    android:layout_marginRight="25dp"
                    android:padding="10dp"
                    android:id="@+id/btnBookVe"
                    android:background="@drawable/button_gradient"
                    style="@style/MyApp.Button.Big"
                    android:textColor="#FFFFFF" />

            </LinearLayout>
        </FrameLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>


</layout>