<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.cardview.widget.CardView
        android:id="@+id/card_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/corner_full"
        app:cardCornerRadius="5dp"
        android:layout_margin="5dp"
        app:cardElevation="0dp"
        style="@style/CardViewStyle.Light"
        app:cardPreventCornerOverlap="false">

        <LinearLayout
            android:padding="5dp"
            android:background="@drawable/background_selector"
            android:layout_width="match_parent"
            android:gravity="center_horizontal|center_vertical"
            android:layout_height="match_parent"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/logo"
                android:layout_marginLeft="5dp"
                android:layout_width="80dp"
                android:layout_height="50dp"
                android:background="#dddddd" />

            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:layout_marginRight="5dp"
                android:layout_marginLeft="5dp"
                android:background="@drawable/gradientdiv" />

            <LinearLayout
                android:layoutDirection="rtl"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_horizontal|center_vertical">

                <ImageView
                    android:layout_margin="5dp"
                    android:layout_width="50dp"
                    android:layout_height="50dp"
                    android:background="#dddddd" />

                <LinearLayout

                    android:id="@+id/main"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">


                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content">

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="50dp"
                            android:background="#dddddd" />

                    </LinearLayout>


                </LinearLayout>

            </LinearLayout>

        </LinearLayout>
    </androidx.cardview.widget.CardView>
</FrameLayout>