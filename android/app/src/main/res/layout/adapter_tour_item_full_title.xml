<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:bind="http://schemas.android.com/apk/res-auto"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="tour"
            type="com.hqt.view.ui.tour.TourItem" />

        <import type="com.hqt.datvemaybay.Common" />

        <import type="android.view.View" />
    </data>

    <androidx.cardview.widget.CardView
        android:id="@+id/card_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="5dp"
        app:cardElevation="0dp"
        app:cardCornerRadius="5dp"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_gravity="center"
            android:padding="0dp">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <ImageView
                    android:id="@+id/imageView"
                    android:layout_width="match_parent"
                    android:layout_height="120dp"
                    android:contentDescription="@string/txt_chat"
                    android:scaleType="centerCrop"
                    app:srcCompat="@drawable/top_banner"
                    bind:imageUrl="@{tour.image}" />

                <TextView
                    android:visibility="@{tour.isShowExtra() ? View.VISIBLE : View.GONE}"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:layout_alignParentEnd="true"
                    android:background="@drawable/top_subtitle"
                    android:singleLine="true"
                    android:paddingStart="20dp"
                    android:paddingTop="5dp"
                    android:paddingEnd="10dp"
                    android:paddingBottom="10dp"
                    android:text="@{tour.extra}"
                    android:textAlignment="center"
                    android:gravity="center"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    android:textStyle="bold|italic" />
            </RelativeLayout>


            <TextView
                android:marqueeRepeatLimit="1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:ellipsize="marquee"
                android:singleLine="true"
                android:paddingStart="5dp"
                android:text="@{tour.title}"
                android:textAppearance="@style/TextAppearance.AppCompat.Body1"
                android:textStyle="bold" />

            <LinearLayout
                android:padding="5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/tour_short_info"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">


                </LinearLayout>

                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:visibility="@{tour.oldPrice > 0 ? View.VISIBLE : View.GONE}"
                        android:gravity="end"
                        android:singleLine="true"
                        android:textColor="@color/diver_color"
                        android:textStyle="bold"
                        android:textSize="14sp"
                        tools:text="900k"
                        bind:strike="@{Common.dinhDangTien(tour.oldPrice)}"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                    <TextView
                        android:text="@{tour.getTourPriceString()}"
                        android:gravity="end"
                        android:singleLine="true"
                        android:textColor="#da281c"
                        android:textStyle="bold"
                        android:textSize="16sp"
                        tools:text="900k"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />
                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>
</layout>