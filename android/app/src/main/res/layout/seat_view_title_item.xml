<?xml version="1.0" encoding="UTF-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:orientation="horizontal"
        android:id="@+id/seat_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.cardview.widget.CardView
            android:id="@+id/seat_color"
            android:visibility="visible"
            android:layout_margin="5dp"
            app:cardCornerRadius="5dp"
            app:cardElevation="0dp"
            app:cardBackgroundColor="@color/primary"
            android:layout_width="20dp"
            android:layout_height="20dp" />

        <LinearLayout
            android:visibility="visible"
            android:layout_margin="2dp"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <TextView
                android:id="@+id/seat_title"
                android:textStyle="bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:ellipsize="end"
                tools:text="ABx" />

        </LinearLayout>

    </LinearLayout>
</layout>