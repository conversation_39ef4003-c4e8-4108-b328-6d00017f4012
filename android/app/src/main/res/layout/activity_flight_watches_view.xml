<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:bind="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewModel"
            type="com.hqt.viewmodel.FlightWatchesViewModel" />

        <import type="com.hqt.datvemaybay.Common" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:animateLayoutChanges="true"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:id="@+id/coordinatorLayout"
        android:fitsSystemWindows="true"
        app:statusBarBackground="@color/primary_dark"
        android:background="@color/primary_dark"
        tools:context="com.hqt.view.ui.train.TrainSelectActivity">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:background="@color/primary_dark"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true"
            app:elevation="0dp"
            android:theme="@style/AppTheme.AppBarOverlay">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/toolbar_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:contentScrim="@color/primary_dark"
                app:title="12bay.vn"
                app:titleEnabled="false"
                app:layout_scrollFlags="enterAlwaysCollapsed">

                <androidx.appcompat.widget.Toolbar
                    android:id="@+id/toolbar"
                    android:background="@color/primary"
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize"
                    app:elevation="0dp"
                    app:layout_collapseMode="pin"
                    app:layout_scrollFlags="scroll|enterAlways"
                    app:popupTheme="@style/AppTheme.PopupOverlay" />


            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <LinearLayout
            android:background="@color/gbgray"
            android:layout_marginTop="?attr/actionBarSize"
            android:layout_width="match_parent"
            android:layout_height="fill_parent"
            android:orientation="vertical">

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:background="@color/gbgray"
                    android:layout_width="match_parent"
                    android:layout_height="fill_parent"
                    android:orientation="vertical">

                    <LinearLayout
                        android:visibility="visible"
                        android:id="@+id/flightWatchesInfo"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingStart="5dp"
                        android:paddingEnd="5dp"
                        android:orientation="vertical" />

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <LinearLayout
                            android:padding="5dp"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <ImageView
                                android:layout_gravity="center_horizontal|center_vertical"
                                android:layout_width="30dp"
                                android:layout_height="30dp"
                                android:layout_marginRight="5dp"
                                android:layout_marginLeft="5dp"
                                android:background="@drawable/line_chart" />

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal">


                                    <TextView
                                        android:layout_gravity="center_horizontal|center_vertical"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Giá vé:" />

                                    <TextView
                                        android:layout_gravity="center_horizontal|center_vertical"
                                        android:padding="5dp"
                                        android:id="@+id/txtGioDi"
                                        style="@style/Text"
                                        android:textStyle="bold"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="@{Common.dinhDangTien(viewModel.flightWatches.current_price)}" />

                                    <TextView
                                        android:layout_gravity="center_horizontal|center_vertical"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textSize="12sp"
                                        android:text="/khách" />
                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal">

                                    <com.mikepenz.iconics.view.IconicsImageView
                                        android:layout_width="15dp"
                                        android:layout_height="15dp"
                                        android:layout_gravity="center_vertical"
                                        app:iiv_size="24dp"
                                        bind:iconis="@{viewModel.flightWatches.pricesDiffIcon}"
                                        bind:iconisColor="@{viewModel.flightWatches.priceDiffIconColor}" />

                                    <TextView
                                        android:layout_gravity="center_horizontal|center_vertical"
                                        android:padding="5dp"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textColor="@{viewModel.flightWatches.priceDiffIconColor}"
                                        android:text="@{viewModel.flightWatches.pricesDiff}" />

                                    <TextView
                                        android:layout_gravity="center_horizontal|center_vertical"
                                        android:paddingTop="5dp"
                                        android:paddingBottom="5dp"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="@{viewModel.flightWatches.dateDiffUpdate}" />


                                </LinearLayout>

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="fill_parent"
                                android:layout_height="match_parent"
                                android:gravity="end|center_vertical">

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical|center_horizontal"
                                    android:orientation="vertical">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Giá thuế phí" />

                                    <Switch
                                        android:checked="true"
                                        android:id="@+id/swShowBaseFare"
                                        android:layout_gravity="center_vertical|center_horizontal"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content" />
                                </LinearLayout>
                            </LinearLayout>
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <LinearLayout
                            android:onClick="@{(view) -> viewModel.onHelpClick(view) }"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Chiều hướng giá"
                                android:padding="10dp" />


                            <com.mikepenz.iconics.view.IconicsImageView
                                android:layout_width="15dp"
                                android:layout_height="15dp"
                                android:layout_gravity="center_vertical"
                                app:iiv_size="20dp"
                                app:iiv_color="@color/stt_gray"
                                app:iiv_icon="faw-info_circle" />

                            <LinearLayout
                                android:gravity="end"
                                android:layout_width="fill_parent"
                                android:layout_height="wrap_content">

                                <TextView
                                    android:textStyle="bold"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@{viewModel.maxPriceText}"
                                    android:paddingEnd="10dp"
                                    android:paddingTop="10dp"
                                    android:paddingBottom="10dp" />
                            </LinearLayout>
                        </LinearLayout>

                        <androidx.cardview.widget.CardView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <LinearLayout
                                    android:padding="5dp"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent">

                                    <TextView
                                        android:paddingStart="5dp"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="@{viewModel.flightWatches.getTripDetail(false)}" />

                                    <TextView
                                        android:textStyle="bold"
                                        android:layout_width="fill_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="end"
                                        android:text="@{viewModel.getDateString(viewModel.flightWatches.departure_date)}" />

                                </LinearLayout>

                                <RelativeLayout
                                    bind:visibility="@{viewModel.onLoading}"
                                    android:id="@+id/loadingPanel"
                                    android:layout_width="match_parent"
                                    android:layout_height="100dp"
                                    android:gravity="center"
                                    android:paddingBottom="10dp"
                                    android:layout_marginBottom="10dp">

                                    <com.airbnb.lottie.LottieAnimationView
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        app:lottie_rawRes="@raw/trend_chart"
                                        app:lottie_loop="true"
                                        android:scaleType="centerCrop"
                                        app:lottie_autoPlay="true" />
                                </RelativeLayout>

                                <HorizontalScrollView
                                    bind:visibility="@{viewModel.flightWatches.type.equals(`RANGE`)}"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:scrollbars="none">

                                    <com.bayvn.charts.ChartProgressBar
                                        bind:visibility="@{!viewModel.onLoading}"
                                        android:id="@+id/ChartProgressBar"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:gravity="center"
                                        app:hdBarCanBeClick="true"
                                        app:hdBarHeight="100dp"
                                        app:hdBarWidth="40dp"
                                        app:hdBarRadius="5dp"
                                        app:hdEmptyColor="@color/bar_empty_color"
                                        app:hdProgressColor="@color/primary"
                                        app:hdProgressClickColor="@color/primary_dark"
                                        app:hdPinBackgroundColor="@color/pin_background"
                                        app:hdPinTextColor="@color/white"
                                        app:hdPinPaddingBottom="5dp"
                                        app:hdBarTitleColor="@color/bar_title_color"
                                        app:hdBarTitleSelectedColor="@color/primary_dark"
                                        app:hdBarTitleTxtSize="12sp"
                                        app:hdPinTxtSize="12sp"
                                        app:hdPinMarginTop="10dp"
                                        app:hdPinMarginBottom="10dp"
                                        app:hdPinMarginEnd="10dp"
                                        app:hdPinMarginStart="10dp"
                                        app:hdBarTitleMarginTop="10dp"
                                        app:hdPinDrawable="@drawable/ic_pin" />
                                </HorizontalScrollView>

                                <HorizontalScrollView
                                    bind:visibility="@{viewModel.flightWatches.type.equals(`TREND`)}"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:scrollbars="none">

                                    <com.bayvn.charts.androidcharts.LineView
                                        bind:visibility="@{!viewModel.onLoading}"
                                        android:id="@+id/fareTrendChart"
                                        android:layout_width="wrap_content"
                                        android:layout_height="100dp"
                                        android:paddingBottom="10dp"
                                        android:layout_marginBottom="10dp" />
                                </HorizontalScrollView>
                            </LinearLayout>
                        </androidx.cardview.widget.CardView>

                        <androidx.cardview.widget.CardView
                            bind:visibility="@{viewModel.flightWatches._round_trip}"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <LinearLayout
                                    android:padding="5dp"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent">

                                    <TextView
                                        android:paddingStart="5dp"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="@{viewModel.flightWatches.getTripDetail(true)}" />

                                    <TextView
                                        android:layout_width="fill_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="right"
                                        android:text="@{viewModel.flightWatches._round_trip ?  viewModel.getDateString(viewModel.flightWatches.return_date) : ``}" />

                                </LinearLayout>

                                <RelativeLayout
                                    bind:visibility="@{viewModel.onLoading}"
                                    android:layout_width="match_parent"
                                    android:layout_height="100dp"
                                    android:gravity="center"
                                    android:paddingBottom="10dp"
                                    android:layout_marginBottom="10dp">

                                    <com.airbnb.lottie.LottieAnimationView
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        app:lottie_rawRes="@raw/trend_chart"
                                        app:lottie_loop="true"
                                        android:scaleType="centerCrop"
                                        app:lottie_autoPlay="true" />
                                </RelativeLayout>

                                <HorizontalScrollView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:scrollbars="none">

                                    <com.bayvn.charts.androidcharts.LineView
                                        bind:visibility="@{!viewModel.onLoading}"
                                        android:id="@+id/fareTrendChartRt"
                                        android:layout_width="wrap_content"
                                        android:layout_height="100dp"
                                        android:paddingBottom="10dp"
                                        android:layout_marginBottom="10dp" />
                                </HorizontalScrollView>
                            </LinearLayout>
                        </androidx.cardview.widget.CardView>

                    </LinearLayout>

                    <LinearLayout
                        android:onClick="@{(view) -> viewModel.onHelpClick(view) }"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:padding="10dp"
                            android:text="Giá tốt nhất hiện tại:" />

                        <TextView
                            android:textStyle="bold"
                            bind:visibility="@{viewModel.flightWatches.type.equals(`RANGE`)}"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{viewModel.getDateString(viewModel.flightWatches.departure_date)}" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="fill_parent"
                        android:orientation="vertical">

                        <com.google.android.material.tabs.TabLayout
                            bind:visibility="@{!viewModel.onLoading}"
                            android:id="@+id/tabs"
                            android:background="@color/white"
                            android:layout_width="match_parent"
                            app:tabTextColor="@color/primary"
                            app:tabIndicatorColor="@color/primary"
                            android:layout_height="wrap_content"
                            app:tabMode="fixed"
                            app:tabGravity="fill" />

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <com.facebook.shimmer.ShimmerFrameLayout
                                android:id="@+id/shimmer_view_container"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:background="#EDEDED"
                                android:orientation="vertical"
                                android:visibility="visible"
                                android:layout_alignParentTop="true"
                                app:shimmer_duration="800">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:background="#EDEDED"
                                    android:layout_height="wrap_content"
                                    android:orientation="vertical">

                                    <include layout="@layout/placeholder_item_flight" />

                                    <include layout="@layout/placeholder_item_flight" />

                                    <include layout="@layout/placeholder_item_flight" />

                                    <include layout="@layout/placeholder_item_flight" />

                                </LinearLayout>
                            </com.facebook.shimmer.ShimmerFrameLayout>

                            <com.hqt.util.HeightWrappingViewPager
                                android:layout_alignParentTop="true"
                                android:id="@+id/viewpager"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent" />
                        </RelativeLayout>


                    </LinearLayout>

                    <androidx.appcompat.widget.AppCompatButton
                        bind:visibility="@{viewModel.flightWatches.active &amp;&amp; !viewModel.onLoading ? true : false}"
                        android:layout_marginTop="10dp"
                        android:layout_marginLeft="25dp"
                        android:layout_marginRight="25dp"
                        android:layout_marginBottom="10dp"
                        android:id="@+id/search"
                        android:background="@drawable/button_gradient"
                        android:textColor="#FFFFFF"
                        android:layout_width="wrap_content"
                        android:layout_gravity="center"
                        android:paddingLeft="50dp"
                        android:paddingRight="50dp"
                        android:textAlignment="gravity"
                        android:textSize="16sp"
                        android:lines="1"
                        android:layout_height="wrap_content"
                        android:gravity="center_horizontal|center_vertical"
                        android:text="Đặt Vé Ngay" />

                </LinearLayout>
            </ScrollView>
        </LinearLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>