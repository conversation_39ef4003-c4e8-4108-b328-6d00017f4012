<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/row_header_width"
    android:layout_height="@dimen/cell_height"
    xmlns:tool="http://schemas.android.com/tools"
    android:background="#acd9f4">

    <TextView
        android:id="@+id/liveTitle"
        android:padding="2dp"
        android:paddingStart="5dp"
        android:paddingEnd="5dp"
        android:background="@drawable/price_board_cell_red"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        tool:text="Live"
        android:textColor="@color/white"
        android:textSize="@dimen/text_size" />

    <View
        android:layout_width="1dp"
        android:layout_height="match_parent"
        android:layout_alignParentRight="true"
        android:background="@color/white" />

</RelativeLayout>