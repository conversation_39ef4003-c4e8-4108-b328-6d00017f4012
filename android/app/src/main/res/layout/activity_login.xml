<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="user"
            type="com.hqt.data.model.User" />

        <import type="android.view.View" />

        <import type="com.hqt.util.Helper.ValidatorType" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#1BA2DC"
        android:fitsSystemWindows="true"
        android:visibility="visible"
        android:orientation="vertical">

        <com.google.android.material.appbar.AppBarLayout
            android:layout_alignParentTop="true"
            android:id="@+id/app_bar"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true"
            app:elevation="0dp"
            android:theme="@style/AppTheme.AppBarOverlay">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/toolbar_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fitsSystemWindows="true"
                app:contentScrim="?attr/colorPrimary"
                app:title="12bay.vn"
                app:titleEnabled="false"
                app:layout_scrollFlags="noScroll">


                <androidx.appcompat.widget.Toolbar
                    android:id="@+id/toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize"
                    app:elevation="0dp"
                    app:layout_collapseMode="pin"
                    app:layout_scrollFlags="scroll|exitUntilCollapsed"
                    app:popupTheme="@style/AppTheme.PopupOverlay" />

            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>


        <ScrollView
            android:layout_centerInParent="true"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_marginStart="50dp"
                android:layout_marginEnd="50dp"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="@dimen/activity_horizontal_margin">


                <ImageView
                    android:id="@+id/logo"
                    android:layout_width="@dimen/logo_w_h"
                    android:layout_height="@dimen/logo_w_h"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginBottom="10dp"
                    app:srcCompat="@drawable/logo_trans" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_below="@+id/logo"
                    android:text="Đăng nhập tài khoản "
                    android:textColor="#ffffff"
                    android:layout_marginBottom="40dp"
                    android:layout_height="wrap_content" />


                <com.mikepenz.iconics.view.IconicsButton
                    android:id="@+id/btn_phone_sign_in"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dip"
                    android:background="@color/white"
                    android:text="{faw_phone} Đăng nhập bằng số điện thoại"
                    android:textColor="@android:color/black" />

                <com.mikepenz.iconics.view.IconicsButton
                    android:id="@+id/btn_google_sign_in"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dip"
                    android:background="#DF4A32"
                    android:text="{faw_google_plus} Đăng nhập với tài khoản Google"
                    android:textColor="@android:color/white" />

                <com.mikepenz.iconics.view.IconicsButton
                    android:id="@+id/btn_facebook_sign_in"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dip"
                    android:background="#3B5999"
                    android:text="{faw_facebook} Đăng nhập với tài khoản Facebook"
                    android:textColor="@android:color/white" />


                <com.facebook.login.widget.LoginButton
                    android:visibility="gone"
                    android:id="@+id/btn_facebook"
                    android:layout_width="match_parent"
                    android:layout_height="20dp"
                    android:padding="10dp"
                    android:layout_marginTop="10dip"
                    android:background="#3B5999"
                    android:text="{faw_facebook} Đăng nhập với tài khoản Facebook"
                    android:textColor="@android:color/white" />


            </LinearLayout>
        </ScrollView>
    </RelativeLayout>
</layout>