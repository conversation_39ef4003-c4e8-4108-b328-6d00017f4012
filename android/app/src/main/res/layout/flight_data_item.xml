<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="flightInfo"
            type="com.hqt.view.ui.flightSearch.model.Flight" />

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">


            <TextView
                android:textStyle="bold"
                android:textColor="#003A6F"
                android:textSize="16sp"
                android:layout_gravity="center_horizontal|center_vertical"
                android:padding="5dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="Vietnam airlines"
                android:text="@{ flightInfo.airlineName }" />

            <LinearLayout
                android:gravity="right|center_vertical"
                android:layout_width="fill_parent"
                android:layout_height="match_parent">

                <TextView
                    android:background="@drawable/corner_full_gray"
                    android:textStyle="bold"
                    android:paddingStart="10dp"
                    android:paddingEnd="10dp"
                    android:gravity="center_vertical"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/white"
                    tools:text="+1d"
                    android:text="@{flightInfo.diffDate}" />

            </LinearLayout>

        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center">

            <LinearLayout
                android:layout_marginStart="5dp"
                android:layout_marginEnd="5dp"
                android:layout_width="wrap_content"
                android:layout_height="match_parent">

                <ImageView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_gravity="center_vertical"
                    app:riv_mutate_background="true"
                    bind:imageUrl="@{flightInfo.airlinesLogo}"
                    app:riv_oval="true" />


            </LinearLayout>

            <LinearLayout
                android:padding="5dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:layout_gravity="center_vertical"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:singleLine="true"
                    android:text="@{flightInfo.getStartTime()}"
                    tools:text="10:20"
                    android:textSize="14sp"
                    android:textColor="@color/textDark"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:singleLine="true"
                    android:textSize="12sp"
                    android:maxLength="20"
                    android:text="@{flightInfo.startPointInfo.name}"
                    android:textColor="@color/textDark"
                    tools:text="SGN" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:singleLine="true"
                    android:textSize="12sp"
                    android:text="@{`(`+flightInfo.startPoint+`)`}"
                    android:textColor="@color/textDark"
                    tools:text="SGN" />


            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layoutDirection="rtl"
                android:layout_margin="4dp">


                <LinearLayout
                    android:padding="5dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:layout_gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:textSize="14sp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:singleLine="true"
                        android:text="@{flightInfo.getEndTime()}"
                        tools:text="10:20"
                        android:textColor="@color/textDark"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:singleLine="true"
                        android:maxLength="20"
                        android:textSize="12sp"
                        android:text="@{flightInfo.endPointInfo.name}"
                        android:textColor="@color/textDark"
                        tools:text="SGN" />

                    <TextView
                        android:textSize="12sp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:singleLine="true"
                        android:text="@{`(`+flightInfo.endPoint+`)`}"
                        android:textColor="@color/textDark"
                        tools:text="SGN" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:paddingStart="10dp"
                    android:paddingEnd="10dp"
                    android:orientation="vertical">

                    <com.hqt.datvemaybay.RobotoTextView
                        android:id="@+id/txtDuration"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:text="@{``+flightInfo.flightTime}"
                        tools:text="1h15pd"
                        android:textColor="@color/textDark"
                        android:textStyle="bold" />


                    <com.hqt.util.DurationView
                        android:id="@+id/duration_view"
                        android:layout_width="match_parent"
                        android:layout_height="10dp" />


                    <com.hqt.datvemaybay.RobotoTextView
                        android:id="@+id/stop"
                        android:layout_width="wrap_content"
                        android:layout_height="26dp"
                        android:layout_gravity="center"
                        android:text="@{flightInfo.getStopString()}"
                        tools:text="Bay Thẳng"
                        android:textColor="@color/textDark"
                        android:textStyle="bold" />
                </LinearLayout>
            </LinearLayout>

        </LinearLayout>
    </LinearLayout>
</layout>