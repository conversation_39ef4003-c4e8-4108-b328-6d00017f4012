<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="itemViewState"
            type="com.hqt.view.ui.flighthistory.ui.state.FlightDetailItemState" />
    </data>

    <androidx.cardview.widget.CardView
        android:id="@+id/card_view_chieuDi"
        style="@style/CardViewStyle.Light"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="5dp"
        android:animateLayoutChanges="false"
        app:cardCornerRadius="2dp"
        app:cardElevation="2dp"
        app:cardPreventCornerOverlap="false">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/activity_vertical_margin"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="5dp">

                <com.mikepenz.iconics.view.IconicsTextView
                    style="@style/Text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="{faw_paper_plane}  "
                    android:textSize="10dp" />

                <TextView
                    android:id="@+id/txtLuotdiTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:text="@string/luotDi"
                    android:text="@{itemViewState.tileOfTurn}"
                    android:textColor="#000000"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_marginStart="60dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/txtDiNgayBay"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@{itemViewState.flightDate}"
                        android:gravity="right"
                        android:textColor="#000"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/txtDiNgayBayAmLich"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="right"
                        android:paddingLeft="5dp"
                        tools:text=""
                        android:text="@{itemViewState.flightDateLunar}"
                        android:textColor="#0084ff"
                        android:textSize="10sp"
                        android:textStyle="italic" />
                </LinearLayout>

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginLeft="7dp"
                android:layout_marginRight="7dp"
                android:background="@drawable/gradientdiv_vertical" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginLeft="4dp"
                android:layout_marginTop="4dp"
                android:layout_marginRight="4dp"
                android:layout_marginBottom="4dp"
                android:orientation="horizontal"
                android:weightSum="9">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">


                    <TextView
                        android:id="@+id/txtDiThoiGianBay"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="Time"
                        android:text="@{itemViewState.flightTime}"
                        android:textColor="#000000"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/txtDiFrom"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/code_rounded"
                        android:paddingLeft="3dp"
                        android:paddingTop="2dp"
                        android:paddingRight="3dp"
                        android:paddingBottom="2dp"
                        android:singleLine="true"
                        tools:text="HAN"
                        android:text="@{itemViewState.item.originCode}"
                        android:textColor="#7C7C7C"
                        android:textSize="12sp"
                        android:textStyle="bold" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:orientation="vertical">

                    <ImageView
                        android:contentDescription="@string/app_name"
                        android:layout_width="50dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        app:srcCompat="@drawable/outbound" />

                    <TextView
                        android:id="@+id/txtDiDuration"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        tools:text="22h25p"
                        android:maxLines="1"
                        android:textColor="#000000"
                        android:text="@{itemViewState.item.duration.toString()}"
                        android:textSize="12sp"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">


                    <TextView
                        android:id="@+id/txtDiThoiGianDen"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="Time"
                        android:text="@{itemViewState.arrival}"
                        android:textColor="#000000"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/txtDiTo"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/code_rounded"
                        android:gravity="center"
                        android:paddingLeft="3dp"
                        android:paddingTop="2dp"
                        android:paddingRight="3dp"
                        android:paddingBottom="2dp"
                        android:singleLine="true"
                        tools:text="SGN"
                        android:text="@{itemViewState.item.destinationCode}"
                        android:textColor="#7C7C7C"
                        android:textSize="12sp"
                        android:textStyle="bold" />


                </LinearLayout>

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginLeft="7dp"
                android:layout_marginRight="7dp"
                android:background="@drawable/gradientdiv_vertical" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:layout_marginRight="4dp"
                android:layout_marginLeft="4dp"
                android:weightSum="9">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="3"
                    android:gravity="center"
                    android:orientation="vertical">

                    <com.hqt.util.AspectRatioImageView
                        android:id="@+id/diLogo"
                        android:layout_width="wrap_content"
                        android:layout_height="fill_parent"
                        android:layout_gravity="center"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="10dp"
                        android:paddingLeft="10dp"
                        android:paddingRight="10dp"
                        android:src="@drawable/logo_bl" />

                    <TextView
                        android:id="@+id/txtDiHangBay"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="VietNam Airline"
                        android:text="@{itemViewState.item.provider.toString()}"
                        android:textColor="#000000"
                        android:textSize="11sp"
                        android:visibility="gone" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/txtDiDiemDung"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:text="Bay Thẳng"
                        android:textColor="#000000"
                        android:textSize="11sp" />

                    <TextView
                        android:id="@+id/txtDiChuyenBay"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:textColor="#000000"
                        android:maxLines="1"
                        android:text="@{itemViewState.item.flightNumber}"
                        android:textSize="11sp"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Loại vé"
                        android:textColor="#000000"
                        android:textSize="11sp" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/txtClassIconDi"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="#000000"
                            android:textSize="11sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/txtDiLoaiVe"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:singleLine="true"
                            android:maxLength="20"
                            android:ellipsize="end"
                            android:textColor="#000000"
                            android:text="@{itemViewState.item.seatClass.toString()}"
                            android:textSize="11sp"
                            android:textStyle="bold" />
                    </LinearLayout>


                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_marginTop="4dp"
                android:id="@+id/viewShowTotalDi"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginLeft="7dp"
                    android:layout_marginRight="7dp"
                    android:background="@drawable/gradientdiv_vertical" />

                <LinearLayout
                    android:id="@+id/viewTotalDi"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="5dp"
                    android:layout_weight="3"
                    android:animateLayoutChanges="true"
                    android:orientation="vertical">


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingLeft="2dp"
                        android:paddingRight="2dp">

                        <TextView
                            android:id="@+id/txtTotalTitle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Tổng giá vé lượt đi +"
                            android:textColor="#000000"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/txtDiTongGia"
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:gravity="end"
                            android:text="15000k"
                            android:textColor="#FF0000"
                            android:textStyle="bold" />


                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/viewTotalDiDetail"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:animateLayoutChanges="true"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:paddingLeft="2dp"
                            android:paddingRight="2dp">

                            <TextView

                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Giá vé người lớn" />

                            <TextView
                                android:id="@+id/txtDiGiaNguoiLon"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="right"
                                android:text="15000k"
                                android:textSize="12sp" />

                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/diTreEm"
                            android:visibility="gone"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:paddingLeft="2dp"
                            android:paddingRight="2dp">

                            <TextView

                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Giá vé trẻ em" />

                            <TextView
                                android:id="@+id/txtDiGiaTreem"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="right"
                                android:text="90k"
                                android:textSize="12sp" />


                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/diEmbe"
                            android:visibility="gone"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:paddingLeft="2dp"
                            android:paddingRight="2dp">

                            <TextView

                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Giá vé em bé" />

                            <TextView
                                android:id="@+id/txtDiGiaEmBe"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="right"
                                android:text="90k"
                                android:textSize="12sp" />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>
</layout>
