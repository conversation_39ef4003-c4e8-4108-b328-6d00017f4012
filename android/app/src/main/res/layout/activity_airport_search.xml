<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:fitsSystemWindows="true"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_gradient"
        android:addStatesFromChildren="true"
        tools:context="com.hqt.view.ui.airport.AirportSearchActivity">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true"
            android:theme="@style/AppTheme.AppBarOverlay"
            app:elevation="0dp">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/toolbar_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fitsSystemWindows="true"
                app:contentScrim="?attr/colorPrimary"
                app:title="12bay.vn"
                app:titleEnabled="false"
                app:layout_scrollFlags="noScroll">

                <ImageView
                    android:id="@+id/headerBG"
                    android:layout_width="match_parent"
                    android:layout_height="150dp"
                    android:scaleType="centerCrop"
                    android:contentDescription="@string/app_name"
                    android:fitsSystemWindows="true"
                    android:adjustViewBounds="true"
                    app:srcCompat="@drawable/world"
                    android:alpha="1" />

                <androidx.appcompat.widget.Toolbar
                    android:id="@+id/toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize"
                    app:elevation="0dp"
                    app:layout_collapseMode="pin"
                    app:layout_scrollFlags="scroll|exitUntilCollapsed"
                    app:popupTheme="@style/AppTheme.PopupOverlay" />

            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <LinearLayout
            android:layout_marginTop="?attr/actionBarSize"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">


            <LinearLayout
                android:layout_marginLeft="16dp"
                android:layout_marginRight="16dp"
                android:padding="5dp"
                android:layout_width="match_parent"
                android:background="@drawable/corner_full"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <com.mikepenz.iconics.view.IconicsTextView
                    android:clickable="false"
                    android:id="@+id/Payment"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:gravity="center_vertical|center_horizontal"
                    android:text="{faw_search}"
                    android:textColor="@color/primary"
                    android:textSize="16sp" />

                <EditText
                    android:id="@+id/edit_search"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:hint="Tìm thành phố hoặc sân bay"
                    android:textSize="16sp" />

            </LinearLayout>


            <RelativeLayout
                android:paddingTop="10dp"
                android:layout_marginTop="20dp"
                android:layout_width="match_parent"
                android:background="@color/white"
                android:layout_height="match_parent">

                <com.facebook.shimmer.ShimmerFrameLayout
                    android:paddingLeft="5dp"
                    android:paddingRight="5dp"
                    android:layout_alignParentTop="true"
                    android:id="@+id/shimmer_view_container"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:orientation="vertical"
                    android:visibility="visible"
                    app:shimmer_duration="800">

                    <LinearLayout

                        android:layout_below="@+id/search"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <include layout="@layout/placeholder_item_train" />

                        <include layout="@layout/placeholder_item_train" />

                        <include layout="@layout/placeholder_item_train" />

                        <include layout="@layout/placeholder_item_train" />

                        <include layout="@layout/placeholder_item_train" />

                        <include layout="@layout/placeholder_item_train" />

                        <include layout="@layout/placeholder_item_train" />

                        <include layout="@layout/placeholder_item_train" />

                        <include layout="@layout/placeholder_item_train" />

                        <include layout="@layout/placeholder_item_train" />
                    </LinearLayout>
                </com.facebook.shimmer.ShimmerFrameLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:visibility="gone"
                    android:id="@+id/my_recycler_view"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_alignParentTop="true"
                    android:background="@color/white"
                    android:paddingLeft="5dp"
                    android:paddingRight="5dp" />


            </RelativeLayout>


        </LinearLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>