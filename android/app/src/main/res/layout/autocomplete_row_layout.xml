<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        tools:ignore="UselessParent">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal|center_vertical"
            android:padding="10dp">

            <com.mikepenz.iconics.view.IconicsImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                app:iiv_color="@color/stt_gray"
                app:iiv_icon="gmd_account_circle" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:padding="5dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/txtFullName"
                android:gravity="center_vertical|center_horizontal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/black"
                android:text="pax.fullNameDetail" />
        </LinearLayout>


    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@drawable/gradientdiv_vertical" />
</LinearLayout>