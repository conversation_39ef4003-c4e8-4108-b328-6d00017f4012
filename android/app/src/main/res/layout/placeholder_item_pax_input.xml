<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_view_chieuDi"
    style="@style/CardViewStyle.Light"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_margin="5dp"
    android:animateLayoutChanges="true"
    app:cardCornerRadius="2dp"
    app:cardElevation="2dp"
    app:cardPreventCornerOverlap="false">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="5dp">

        <View
            android:id="@+id/title"
            android:layout_width="150dp"
            android:layout_height="10dp"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="10dp"
            android:background="#dddddd" />

        <View
            android:id="@+id/thumbnail"
            android:layout_width="240dp"
            android:layout_height="100dp"
            android:layout_marginRight="10dp"
            android:layout_below="@id/title"
            android:background="#dddddd" />

        <View
            android:layout_width="204dp"
            android:layout_height="100dp"
            android:layout_alignTop="@+id/thumbnail"
            android:layout_toRightOf="@+id/thumbnail"
            android:background="#dddddd" />

    </RelativeLayout>
</androidx.cardview.widget.CardView>