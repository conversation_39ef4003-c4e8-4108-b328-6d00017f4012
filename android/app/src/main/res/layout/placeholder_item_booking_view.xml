<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_view_chieuDi"
    style="@style/CardViewStyle.Light"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_margin="5dp"
    android:animateLayoutChanges="true"
    app:cardCornerRadius="2dp"
    app:cardElevation="2dp"
    app:cardPreventCornerOverlap="false">

    <RelativeLayout
        android:padding="5dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <View
            android:id="@+id/top"
            android:layout_width="240dp"
            android:layout_height="120dp"
            android:layout_marginRight="10dp"
            android:background="#dddddd" />

        <View
            android:layout_below="@id/top"
            android:layout_width="240dp"
            android:layout_height="25dp"
            android:layout_marginTop="5dp"
            android:layout_marginRight="10dp"
            android:background="#dddddd" />

        <View
            android:id="@+id/view2"
            android:layout_width="240dp"
            android:layout_height="120dp"
            android:layout_alignTop="@+id/top"
            android:layout_toRightOf="@+id/top"
            android:background="#dddddd" />

        <View
            android:layout_below="@+id/view2"
            android:layout_width="240dp"
            android:layout_height="25dp"
            android:layout_marginTop="5dp"
            android:layout_toRightOf="@+id/top"
            android:background="#dddddd" />


    </RelativeLayout>
</androidx.cardview.widget.CardView>
