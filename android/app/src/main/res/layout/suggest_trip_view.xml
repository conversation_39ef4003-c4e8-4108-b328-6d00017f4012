<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="trip"
            type="com.hqt.data.model.SuggestTrip" />

        <import type="com.hqt.datvemaybay.Common" />
    </data>

    <androidx.cardview.widget.CardView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:layout_marginStart="10dp"
        android:focusable="true"
        app:cardCornerRadius="5dp"
        android:foreground="?attr/selectableItemBackground"
        android:orientation="vertical">

        <LinearLayout
            android:padding="5dp"
            android:layout_width="250dp"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:padding="5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:textStyle="bold"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxLines="1"
                    tools:text="xxxx"
                    android:text="@{trip.origin.name}" />

                <TextView
                    android:textStyle="bold"
                    android:maxLines="1"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    tools:text="xxxx"
                    android:text="@{trip.destination.name}" />


            </LinearLayout>

            <LinearLayout
                android:padding="5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxLines="1"
                    android:text="@{trip.origin.base}"
                    tools:text="xxxx" />

                <TextView
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:maxLines="1"
                    android:gravity="end"
                    android:text="@{trip.destination.base}"
                    tools:text="xxxx" />


            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@drawable/gradientdiv_vertical" />

            <LinearLayout
                android:padding="5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <com.mikepenz.iconics.view.IconicsImageView
                    android:layout_marginEnd="0dp"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    app:iiv_size="16dp"
                    app:iiv_color="@color/stt_gray"
                    app:iiv_icon="faw-calendar_alt" />

                <TextView
                    android:paddingStart="2dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@{Common.dateToString(trip.depatureDate,`dd/MM/yyyy`)}"
                    tools:text="xxxx" />

            </LinearLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>
</layout>