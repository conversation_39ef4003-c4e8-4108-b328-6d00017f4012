<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context="com.hqt.view.ui.booking.BookingActivity">

    <data>

        <variable
            name="vm"
            type="com.hqt.view.ui.booking.ui.BookingViewModelV2" />

        <import type="com.hqt.datvemaybay.Common" />

        <import type="android.view.View" />
    </data>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#F2F1F1"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        tools:showIn="@layout/activity_train_book">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <HorizontalScrollView
                android:id="@+id/horizalScroll"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:background="@color/primary"
                android:scrollbars="none">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingBottom="20dp">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rcvFlightDetail"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"/>

                    <LinearLayout
                        android:id="@+id/tripContainer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical" />

                    <LinearLayout
                        android:id="@+id/tripContainerRt"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical" />


                </LinearLayout>
            </HorizontalScrollView>

            <androidx.cardview.widget.CardView
                android:id="@+id/card_view1"
                style="@style/CardViewStyle.Light"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:layout_marginTop="-15dp"
                android:layout_marginRight="5dp"
                android:layout_marginBottom="5dp"
                app:cardCornerRadius="2dp"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false"
                app:contentPadding="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="#FFFFFF"
                    android:orientation="vertical"
                    android:padding="10dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:orientation="vertical">

                        <TextView
                            style="@style/Text"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_gravity="left"
                            android:text="@{vm.getInputPaxTitle()}"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/paxInPut"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:animateLayoutChanges="true"
                        android:orientation="vertical">

                        <ProgressBar
                            android:id="@+id/load_bag"
                            style="?android:attr/progressBarStyle"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="center_horizontal"
                            android:indeterminate="true" />

                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rcvPassenger"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        />


                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/show_seat_select"
                style="@style/CardViewStyle.Light"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:layout_marginRight="5dp"
                android:layout_marginBottom="5dp"
                android:visibility="@{vm.isDomesticBooking() ? View.VISIBLE : View.GONE}"
                app:cardCornerRadius="2dp"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false"
                app:contentPadding="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="5dp">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_horizontal"
                        android:padding="5dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:background="@drawable/ic_seat" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="5dp"
                        android:background="@color/white"
                        android:orientation="vertical"
                        android:padding="5dp">

                        <TextView
                            android:id="@+id/txt_seat_select"
                            style="@style/Text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@={vm.seatCountText}"
                            android:textStyle="bold"
                            tools:text="@string/txt_seat_select" />

                        <TextView
                            android:id="@+id/txt_seat_select_detail"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:text="@string/txt_seat_select_detail"
                            android:text="@={vm.seatCountDetailText}"
                            android:textSize="12sp" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical|end"
                        android:padding="10dp">

                        <com.mikepenz.iconics.view.IconicsImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            app:iiv_color="@color/primary"
                            app:iiv_icon="gmd_keyboard_arrow_right" />
                    </LinearLayout>

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/show_add_on_select"
                style="@style/CardViewStyle.Light"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:layout_marginRight="5dp"
                android:layout_marginBottom="5dp"
                android:visibility="@{vm.isDomesticBooking() ? View.VISIBLE : View.GONE}"
                app:cardCornerRadius="2dp"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false"
                app:contentPadding="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="5dp">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_horizontal"
                        android:padding="5dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:background="@drawable/ic_meal" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="5dp"
                        android:background="@color/white"
                        android:orientation="vertical"
                        android:padding="5dp">

                        <TextView
                            android:id="@+id/txt_addon_select"
                            style="@style/Text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:text="@string/txt_addon_select"
                            android:text="@={vm.mealCountText}"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/txt_addon_select_detail"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:text="@string/txt_addon_select_detail"
                            android:text="@={vm.mealCountDetailText}"
                            android:textSize="12sp" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical|end"
                        android:padding="10dp">

                        <com.mikepenz.iconics.view.IconicsImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            app:iiv_color="@color/primary"
                            app:iiv_icon="gmd_keyboard_arrow_right" />
                    </LinearLayout>

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/loginLayout"
                style="@style/CardViewStyle.Light"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:layout_marginRight="5dp"
                android:layout_marginBottom="5dp"
                android:visibility="@{vm.isShowLogin() ? View.VISIBLE : View.GONE}"
                app:cardCornerRadius="2dp"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false"
                app:contentPadding="2dp">

                <LinearLayout
                    android:id="@+id/txt_reward"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="5dp">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_horizontal"
                        android:padding="5dp">

                        <com.mikepenz.iconics.view.IconicsImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            app:iiv_icon="gmd_account_circle"
                            app:iiv_size="20dp" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="5dp"
                        android:background="@color/white"
                        android:orientation="vertical"
                        android:padding="5dp">

                        <TextView
                            style="@style/Text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/txt_not_login"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/txt_txt_not_login"
                            android:textSize="12sp" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical|end"
                        android:padding="10dp">

                        <com.mikepenz.iconics.view.IconicsImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            app:iiv_color="@color/primary"
                            app:iiv_icon="gmd_keyboard_arrow_right" />
                    </LinearLayout>

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/card_view"
                style="@style/CardViewStyle.Light"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:layout_marginRight="5dp"
                android:layout_marginBottom="5dp"
                app:cardCornerRadius="2dp"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false"
                app:contentPadding="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="#FFFFFF"
                    android:orientation="vertical"
                    android:padding="5dp">


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/textView8"
                            style="@style/Text"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_gravity="left"
                            android:text="@{vm.getInputContactTitle()}"
                            android:textStyle="bold" />


                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="@{vm.isShowBookingView() ? View.VISIBLE : View.GONE}">

                        <LinearLayout

                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:paddingBottom="5dp"
                            android:weightSum="5">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="2"
                                android:text="Liên hệ"
                                android:textColor="@color/textDark" />

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_weight="3"
                                android:gravity="right"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@{vm.booking.contact_name}"
                                    android:textSize="14sp"
                                    android:textStyle="bold" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@{vm.booking.contact_phone +` - `+vm.booking.contact_email}"
                                    android:textSize="12sp" />
                            </LinearLayout>


                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="@{vm.isShowBookingView() ? View.GONE : View.VISIBLE}">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <LinearLayout

                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:paddingBottom="5dp"
                                android:weightSum="5">

                                <TextView
                                    android:id="@+id/textView1"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="2"
                                    android:text="@string/hoVaTen"
                                    android:textColor="@color/textDark" />

                                <EditText
                                    android:id="@+id/txtContactName"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="3"
                                    android:background="@drawable/edit_text"
                                    android:ems="10"
                                    android:enabled="@{!vm.isShowBookingView()}"
                                    android:focusable="@{!vm.isShowBookingView()}"
                                    android:hint="@string/txtNamehint"
                                    android:inputType="textPersonName"
                                    android:paddingLeft="10dp"
                                    android:paddingTop="2dp"
                                    android:paddingRight="10dp"
                                    android:paddingBottom="2dp"
                                    android:singleLine="true"
                                    android:text="@={vm.booking.contact_name}"
                                    android:textColor="#000" />


                            </LinearLayout>
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:paddingBottom="5dp"
                            android:weightSum="5">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="2"
                                    android:text="@string/soDienThoai"
                                    android:textColor="@color/textDark" />

                                <EditText
                                    android:id="@+id/txtContactPhone"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="3"
                                    android:background="@drawable/edit_text"
                                    android:ems="10"
                                    android:enabled="@{!vm.isShowBookingView()}"
                                    android:focusable="@{!vm.isShowBookingView()}"
                                    android:inputType="phone"
                                    android:paddingLeft="10dp"
                                    android:paddingTop="2dp"
                                    android:paddingRight="10dp"
                                    android:paddingBottom="2dp"
                                    android:text="@={vm.booking.contact_phone}"
                                    android:textColor="#000" />

                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:paddingBottom="5dp"
                            android:weightSum="5">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="2"
                                    android:text="@string/email"
                                    android:textColor="@color/textDark" />

                                <EditText
                                    android:id="@+id/txtContactEmail"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="3"
                                    android:background="@drawable/edit_text"
                                    android:ems="10"
                                    android:enabled="@{!vm.isShowBookingView()}"
                                    android:focusable="@{!vm.isShowBookingView()}"
                                    android:hint="@string/txtEmailhint"
                                    android:inputType="textEmailAddress|textCapCharacters"
                                    android:paddingLeft="10dp"
                                    android:paddingTop="2dp"
                                    android:paddingRight="10dp"
                                    android:paddingBottom="2dp"
                                    android:text="@={vm.booking.contact_email}"
                                    android:textColor="#000" />

                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:paddingBottom="0dp"
                            android:weightSum="5">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <TextView
                                    android:id="@+id/textView2"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="2"
                                    android:text="@string/maGiamGia"
                                    android:textColor="@color/textDark" />

                                <EditText
                                    android:id="@+id/txtVoucherCode"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="2dp"
                                    android:layout_weight="1.5"
                                    android:background="@drawable/edit_text"
                                    android:ems="10"
                                    android:enabled="@{!vm.isShowBookingView()}"
                                    android:focusable="@{!vm.isShowBookingView()}"
                                    android:inputType="textCapCharacters"
                                    android:paddingLeft="10dp"
                                    android:paddingTop="2dp"
                                    android:paddingRight="10dp"
                                    android:paddingBottom="2dp"
                                    android:text="@={vm.booking.voucher}"
                                    android:textColor="#000" />

                                <androidx.appcompat.widget.AppCompatButton
                                    android:id="@+id/btnGetVoucher"
                                    style="@style/MyApp.Button.Big"
                                    android:layout_width="0dp"
                                    android:layout_height="31dp"
                                    android:layout_margin="2dp"
                                    android:layout_weight="1.5"
                                    android:backgroundTint="#FF5959"
                                    android:padding="2dp"
                                    android:text="Áp dụng"
                                    android:textColor="#FFFFFF"
                                    android:textSize="10sp" />


                            </LinearLayout>

                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        tools:visibility="gone"
                        android:orientation="vertical"
                        android:paddingBottom="5dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:orientation="vertical"
                            android:visibility="@{vm.isShowBookingView() ? View.VISIBLE : View.GONE}">

                            <LinearLayout
                                android:id="@+id/showBookingStatus"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <LinearLayout
                                    android:id="@+id/waittingView"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="vertical"
                                    android:paddingTop="2dp"
                                    android:paddingBottom="5dp">

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:paddingBottom="2dp">

                                        <TextView

                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="@string/maDonHang"
                                            android:textColor="@color/textDark" />


                                        <TextView
                                            android:id="@+id/txtBookingId"
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:gravity="right"
                                            android:paddingRight="5dp"
                                            android:text="@{`#`+vm.booking.id}"
                                            android:textColor="#14a9e5"
                                            android:textStyle="bold" />

                                    </LinearLayout>

                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:orientation="vertical"
                                    android:paddingTop="2dp"
                                    android:paddingBottom="2dp">

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:paddingBottom="5dp">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="Mã đặt chỗ (Code):"
                                            android:textColor="@color/textDark" />


                                        <TextView
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:gravity="right"
                                            android:text="@{vm.booking.pnr}"
                                            android:textColor="#ff0f0b"
                                            android:textSize="14sp"
                                            android:textStyle="bold" />

                                    </LinearLayout>


                                </LinearLayout>

                                <LinearLayout
                                    android:id="@+id/pnrReturnLayout"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:orientation="vertical"
                                    android:paddingTop="2dp"
                                    android:paddingBottom="2dp"
                                    android:visibility="gone">

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:paddingBottom="2dp">

                                        <TextView
                                            android:id="@+id/txtPnrReturn"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="Mã đặt chỗ lượt về:" />


                                        <TextView
                                            android:id="@+id/pnrReturn"
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:gravity="right"
                                            android:text="@{vm.booking.pnr_return}"
                                            android:textColor="#ff0f0b"
                                            android:textSize="14sp"
                                            android:textStyle="bold" />

                                    </LinearLayout>
                                </LinearLayout>

                                <LinearLayout

                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal"
                                    android:paddingBottom="5dp">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Trạng thái:"
                                        android:textColor="@color/textDark" />

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="right"
                                        android:orientation="horizontal"
                                        android:visibility="@{vm.isCreatedBooking() ? View.VISIBLE : View.GONE}">

                                        <TextView
                                            android:id="@+id/txtBookingIdd"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:paddingRight="5dp"
                                            android:text="Đang tiến hành đặt chỗ"
                                            android:textColor="#14a9e5" />

                                        <ProgressBar
                                            android:id="@+id/marker_progress"
                                            style="?android:attr/progressBarStyle"
                                            android:layout_width="20dp"
                                            android:layout_height="20dp"
                                            android:indeterminate="true"
                                            android:paddingRight="5dp" />
                                    </LinearLayout>

                                    <TextView
                                        android:id="@+id/txtStatus"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="right"
                                        android:text="@{vm.booking.status_text}"
                                        android:textColor="#FF0000"
                                        android:textStyle="bold"
                                        android:visibility="@{vm.isCreatedBooking() ? View.GONE : View.VISIBLE}" />

                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:paddingTop="2dp"
                                    android:paddingBottom="5dp"
                                    android:visibility="@{vm.booking.payment.status ? View.VISIBLE : View.GONE}">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Hạn thanh toán:"
                                        android:textColor="@color/textDark" />

                                    <TextView
                                        android:id="@+id/txtTimeLimit"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="right"
                                        android:text="@{vm.getTimeLimitText()}"
                                        android:textColor="#14a9e5"
                                        android:textStyle="bold" />

                                </LinearLayout>
                            </LinearLayout>
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:paddingTop="2dp"
                            android:paddingBottom="5dp">

                            <TextView
                                android:id="@+id/textViews3"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/giaVe"
                                android:textColor="@color/textDark" />

                            <TextView
                                android:id="@+id/txtGiaVe"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="right"
                                android:text="@{vm.formatTotal(vm.booking.grandTotal)}"
                                android:textColor="#000000" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:paddingTop="2dp"
                            android:paddingBottom="5dp"
                            android:visibility="@{vm.booking.addon_fee > 0 ? View.VISIBLE : View.GONE}">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/seat_fee"
                                android:textColor="@color/textDark" />

                            <TextView
                                android:id="@+id/txtSeatFee"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="right"
                                android:text="@{vm.formatTotal(vm.booking.totalAddOn)}"
                                android:textColor="#000000" />

                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/bagLayout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:paddingBottom="2dp"
                            android:visibility="@{vm.booking.bag_fee > 0 ? View.VISIBLE : View.GONE}">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/hanhLy"
                                android:textColor="@color/textDark" />

                            <TextView
                                android:id="@+id/txtbagFee"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="right"
                                android:text="@{vm.formatTotal(vm.booking.totalBagFee)}"
                                android:textColor="#FF0000" />

                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/voucherLayout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:paddingTop="2dp"
                            android:paddingBottom="5dp"
                            android:visibility="@{vm.booking.discount > 0 ? View.VISIBLE : View.GONE}">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/giamGia"
                                android:textColor="@color/textDark" />

                            <TextView
                                android:id="@+id/txtdiscount"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="right"
                                android:text="@{Common.dinhDangTien(vm.booking.discount)}"
                                android:textColor="#FF0000" />

                        </LinearLayout>


                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_margin="2dp"
                            android:background="@color/diver_color" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:paddingTop="2dp"
                            android:paddingBottom="2dp">

                            <TextView
                                android:id="@+id/textView3"
                                style="@style/Text"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/tongCong"
                                android:textStyle="bold" />


                            <TextView
                                android:id="@+id/txtGiaTongCong"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="right"
                                android:text="@{vm.formatTotal(vm.booking.calculatorFinalPrice)}"
                                android:textColor="#FF0000"
                                android:textStyle="bold" />

                        </LinearLayout>

                        <View
                            android:id="@+id/got_it"
                            android:layout_width="0dp"
                            android:layout_height="0dp" />

                        <LinearLayout
                            android:id="@+id/layoutPointReward"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="end"
                            android:paddingBottom="2dp"
                            bind:visibility="@{vm.booking.getRewardPointTotal() > 0}">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="end"
                                android:text="Nhận"

                                android:textColor="@color/primary" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="end"
                                android:paddingStart="5dp"
                                android:paddingEnd="5dp"
                                android:text="@{vm.booking.getRewardPointTotal() +``}"
                                android:textColor="@color/primary" />

                            <com.mikepenz.iconics.view.IconicsTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="điểm {gmd_stars} "
                                android:textColor="@color/primary" />

                        </LinearLayout>

                    </LinearLayout>

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/btnBookVe"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="25dp"
                        android:layout_marginTop="5dp"
                        android:layout_marginRight="25dp"
                        android:layout_marginBottom="5dp"
                        android:background="@drawable/button_gradient"
                        android:gravity="center_horizontal|center_vertical"
                        android:padding="10dp"
                        android:text="@{vm.getBookButtonText()}"
                        android:textAlignment="gravity"
                        android:textColor="@color/white"
                        android:textSize="16sp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>


        </LinearLayout>

    </androidx.core.widget.NestedScrollView>
</layout>