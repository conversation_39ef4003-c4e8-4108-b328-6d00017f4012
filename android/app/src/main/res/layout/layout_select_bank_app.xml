<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ScrollView
        android:background="@color/gbgray"
        android:layout_height="fill_parent"
        android:layout_width="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/fui_transparent"
            android:orientation="vertical"
            android:clickable="true"
            android:focusable="true"
            android:elevation="6dp"
            app:behavior_hideable="true"
            app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">

            <View
                android:layout_margin="2dp"
                android:layout_gravity="center_vertical|center_horizontal"
                android:layout_width="50dp"
                android:layout_height="4dp"
                android:background="@drawable/top_line" />

            <LinearLayout
                android:background="@color/gbgray"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:orientation="vertical"
                    android:id="@+id/flightInfo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:paddingStart="10dp"
                        android:paddingEnd="10dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <LinearLayout
                            android:id="@+id/close"
                            android:padding="10dp"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical">

                            <com.mikepenz.iconics.view.IconicsTextView
                                style="@style/Text"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="{faw_paper_plane} "
                                android:textSize="10dp" />

                            <TextView
                                android:layout_marginStart="10dp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="#000000"
                                android:textStyle="bold"
                                android:text="Chọn ứng dụng ngân hàng của bạn" />

                            <LinearLayout
                                android:layout_width="fill_parent"
                                android:layout_height="match_parent"
                                android:gravity="end|center_vertical">

                                <ImageView
                                    android:layout_width="10dp"
                                    android:layout_height="10dp"
                                    android:src="@drawable/ic_close_darker" />

                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:background="@drawable/corner_full"
                            android:padding="5dp"
                            android:layout_marginTop="5dp"
                            android:visibility="visible"
                            android:id="@+id/method_item"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="5dp"
                            android:orientation="vertical">


                            <com.google.android.flexbox.FlexboxLayout
                                android:id="@+id/flex_box"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@color/white"
                                app:alignContent="center"
                                app:alignItems="center"
                                app:dividerDrawable="@color/diver_color"
                                app:flexWrap="wrap"
                                app:justifyContent="flex_start"
                                app:showDivider="middle|beginning">


                            </com.google.android.flexbox.FlexboxLayout>
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>


            </LinearLayout>
        </LinearLayout>
    </ScrollView>
</layout>