<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:fab="http://schemas.android.com/apk/res-auto"
    android:id="@+id/coordinatorLayout"
    android:fitsSystemWindows="true"
    android:background="#EDEDED"
    tools:context="com.hqt.view.ui.HomeActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar"
        android:visibility="visible"
        android:layout_width="match_parent"
        android:background="@color/primary_dark"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true"
        app:elevation="0dp"
        android:theme="@style/AppTheme.AppBarOverlay">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/toolbar_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:contentScrim="@color/primary_dark"
            app:title="12bay.vn"
            app:titleEnabled="false"
            app:layout_scrollFlags="enterAlwaysCollapsed">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:background="@color/primary"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                app:elevation="0dp"
                app:layout_collapseMode="pin"
                app:layout_scrollFlags="scroll|enterAlways"
                app:popupTheme="@style/AppTheme.PopupOverlay" />


        </com.google.android.material.appbar.CollapsingToolbarLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <RelativeLayout
        android:layout_marginTop="?attr/actionBarSize"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <include
            android:layout_marginTop="100dp"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            layout="@layout/empty_state_layout" />

        <im.delight.android.webview.AdvancedWebView
            android:id="@+id/webView1"
            android:layout_width="fill_parent"
            android:layout_height="fill_parent"
            android:layout_above="@+id/sanVe"
            android:layout_alignParentStart="true"
            android:visibility="visible" />

        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_centerInParent="true"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical|center_horizontal" />

        <com.github.clans.fab.FloatingActionMenu
            android:id="@+id/fb_menu"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_alignParentRight="true"
            android:layout_marginBottom="11dp"
            android:layout_marginLeft="11dp"
            android:layout_above="@+id/quickView"
            android:layout_marginRight="5dp"
            fab:menu_animationDelayPerItem="55"
            fab:menu_backgroundColor="@android:color/transparent"
            fab:menu_buttonSpacing="0dp"
            fab:menu_colorNormal="@color/btnColor"
            fab:menu_colorPressed="#038dc4"
            fab:menu_colorRipple="#99d4d4d4"
            fab:menu_fab_label="12bay"
            fab:menu_fab_size="normal"
            fab:menu_icon="@drawable/ic_send_white_36dp"
            fab:menu_labels_colorNormal="#333"
            fab:menu_labels_colorPressed="#444"
            fab:menu_labels_colorRipple="#66efecec"
            fab:menu_labels_cornerRadius="3dp"
            fab:menu_labels_ellipsize="none"
            fab:menu_labels_margin="0dp"
            fab:menu_labels_maxLines="-1"
            fab:menu_labels_padding="8dp"
            fab:menu_labels_position="left"
            fab:menu_labels_showShadow="true"
            fab:menu_labels_singleLine="false"
            fab:menu_labels_textColor="#f2f1f1"
            fab:menu_labels_textSize="15sp"
            fab:menu_openDirection="up"
            fab:menu_shadowColor="#66000000"
            fab:menu_shadowRadius="2dp"
            fab:menu_shadowXOffset="1dp"
            fab:menu_shadowYOffset="2dp"
            fab:menu_labels_style="@style/MenuLabelsStyle"
            fab:menu_labels_showAnimation="@anim/jump_from_down"
            fab:menu_labels_hideAnimation="@anim/jump_to_down"
            fab:menu_showShadow="true">

            <com.github.clans.fab.FloatingActionButton
                android:id="@+id/fab_call"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_action_call"
                fab:fab_colorNormal="@color/btnColor"
                fab:fab_label="Gọi điện thoại đặt vé"
                fab:fab_size="mini" />

            <com.github.clans.fab.FloatingActionButton
                android:id="@+id/fab_search"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_create_white_24dp"
                fab:fab_label="Tìm chuyến bay"
                fab:fab_colorNormal="@color/primary"
                fab:fab_size="mini" />

            <com.github.clans.fab.FloatingActionButton
                android:id="@+id/fab_sendSMS"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_flight_takeoff_white_24dp"
                fab:fab_colorNormal="@color/btnColor"
                fab:fab_label="Săn vé rẻ"
                fab:fab_size="mini" />

            <com.github.clans.fab.FloatingActionButton
                android:id="@+id/fab_pay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_contact_phone_white_24dp"
                fab:fab_label="Liên hệ"
                fab:fab_colorNormal="@color/primary"
                fab:fab_size="mini" />


        </com.github.clans.fab.FloatingActionMenu>


    </RelativeLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>