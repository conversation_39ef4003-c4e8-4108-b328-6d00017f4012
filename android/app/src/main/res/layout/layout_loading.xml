<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/close"
            android:background="@color/primary"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:padding="10dp"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                style="@style/TextAppearance.AppCompat.Widget.ActionBar.Title"
                android:text="<PERSON><PERSON> xử lý đơn hàng" />

            <LinearLayout
                android:background="@color/primary"
                android:layout_width="fill_parent"
                android:layout_height="match_parent"
                android:padding="10dp"
                android:gravity="end|center_vertical">

                <com.mikepenz.iconics.view.IconicsImageView
                    android:padding="2dp"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    app:iiv_color="@color/white"
                    app:iiv_icon="gmd_close" />

            </LinearLayout>

        </LinearLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="fill_parent">

            <LinearLayout
                android:paddingBottom="100dp"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:layout_width="match_parent"
                android:gravity="center_vertical|center_horizontal"
                android:orientation="vertical"
                android:layout_height="wrap_content">

                <com.airbnb.lottie.LottieAnimationView
                    android:scaleType="centerInside"
                    app:lottie_autoPlay="true"
                    app:lottie_loop="true"
                    app:lottie_rawRes="@raw/lott_animation_feed"
                    android:layout_width="200dp"
                    android:layout_height="200dp"
                    android:layout_marginBottom="20dp" />

                <TextView
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="12BAY ĐANG XỬ LÝ ĐẶT CHỖ CỦA BẠN" />

                <TextView
                    android:textSize="14sp"
                    android:textAlignment="center"
                    android:padding="10dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Hệ thống đang tiến hành xác nhận chỗ với hãng, Vui lòng đợi trong giây lát." />

                <TextView
                    android:textSize="12sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Gọi 19002642 nếu bạn cần hỗ trợ gấp." />

                <LinearLayout
                    android:padding="10dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical|center_horizontal">

                    <com.mikepenz.iconics.view.IconicsTextView
                        android:textAlignment="center"
                        android:paddingTop="7dp"
                        android:paddingBottom="7dp"
                        android:paddingStart="10dp"
                        android:paddingEnd="10dp"

                        android:background="@drawable/button_gradient"
                        android:id="@+id/callPhone"
                        android:textColor="#FFFFFF"
                        android:layout_width="wrap_content"
                        android:layout_gravity="center_vertical|center_horizontal"
                        android:textSize="16sp"
                        android:layout_height="wrap_content"
                        android:text="{faw_phone}" />

                    <ImageView
                        android:layout_marginStart="5dp"
                        android:id="@+id/send_zalo"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@drawable/zalo_logo"
                        android:layout_marginEnd="5dp" />
                </LinearLayout>
            </LinearLayout>

            <com.airbnb.lottie.LottieAnimationView
                android:rotation="180"
                android:scaleType="centerCrop"
                app:lottie_autoPlay="true"
                app:lottie_loop="true"
                app:lottie_rawRes="@raw/lott_wave"
                android:layout_alignParentBottom="true"
                android:layout_alignParentStart="true"
                android:id="@+id/animation_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </RelativeLayout>
    </LinearLayout>
</layout>