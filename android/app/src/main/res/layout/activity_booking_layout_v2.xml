<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewModel"
            type="com.hqt.view.ui.booking.ui.BookingViewModelV2" />

        <import type="com.hqt.datvemaybay.Common" />

        <import type="android.view.View" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true"
        android:background="@color/gbgray"
        android:id="@+id/coordinatorLayout"
        android:animateLayoutChanges="true">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar"
            android:layout_width="match_parent"
            android:background="@color/primary_dark"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true"
            app:layout_constraintTop_toTopOf="parent"
            app:elevation="5dp"
            android:theme="@style/AppTheme.AppBarOverlay">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/toolbar_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:title="12bay.vn"
                android:background="@color/primary"
                android:fitsSystemWindows="true"
                app:titleEnabled="false"
                app:layout_scrollFlags="enterAlwaysCollapsed">

                <androidx.appcompat.widget.Toolbar
                    android:id="@+id/toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize"
                    app:elevation="0dp"
                    app:layout_collapseMode="pin"
                    app:layout_scrollFlags="scroll|enterAlways"
                    app:popupTheme="@style/AppTheme.PopupOverlay" />


            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/swipe_refresh_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <LinearLayout

                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.facebook.shimmer.ShimmerFrameLayout
                    android:id="@+id/shimmer_view_container"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:orientation="vertical"
                    android:visibility="gone"
                    app:shimmer_duration="800">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <include layout="@layout/placeholder_item_pax_input" />

                        <include layout="@layout/placeholder_item_pax_input" />

                        <include layout="@layout/placeholder_item_pax_input" />

                        <include layout="@layout/placeholder_item_pax_input" />

                        <include layout="@layout/placeholder_item_pax_input" />

                        <include layout="@layout/placeholder_item_pax_input" />

                    </LinearLayout>
                </com.facebook.shimmer.ShimmerFrameLayout>

                <include
                    android:visibility="@{viewModel.isShowBookingView() ? View.VISIBLE : View.GONE}"
                    android:id="@+id/header"
                    bind:bindBgcorlor="@{viewModel.getStatusBackgoundColor()}"
                    bind:statusText="@{viewModel.booking.status_text}"
                    layout="@layout/head_status_layout" />

                <include
                    android:id="@+id/content"
                    layout="@layout/booking_layout_content_scrolling_v2"
                    bind:vm="@{viewModel}" />


            </LinearLayout>
        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

        <View
            android:id="@+id/behavior_dependency"
            android:layout_width="1dp"
            android:layout_height="1dp"
            android:layout_marginBottom="30dp"
            android:layout_gravity="bottom"
            app:layout_anchor="@id/got_it"
            app:layout_anchorGravity="bottom" />

        <FrameLayout
            android:visibility="@{viewModel.isShowBookingView() ? View.VISIBLE : View.GONE}"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:behavior_hideable="true"
            app:layout_behavior="com.hqt.util.helper.OutOfScreenBottomSheetBehavior">

            <LinearLayout
                android:id="@+id/bottom_sheet"
                android:outlineAmbientShadowColor="@color/textDark"
                android:orientation="vertical"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_gravity="center_horizontal|bottom"
                android:background="#FFFFFF">

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btnFloatBookVe"
                    android:background="@drawable/button_gradient_noborder"
                    android:textColor="#FFFFFF"
                    android:layout_width="match_parent"
                    android:textAlignment="center"
                    android:textSize="16sp"
                    android:visibility="visible"
                    android:layout_height="wrap_content"
                    app:textAllCaps="true"
                    android:textAllCaps="true"
                    android:text="@{viewModel.getBookButtonText()}" />

            </LinearLayout>
        </FrameLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>