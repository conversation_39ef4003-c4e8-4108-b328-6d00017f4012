<?xml version="1.0" encoding="UTF-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="route"
            type="com.hqt.view.ui.bus.BusRoute" />

        <variable
            name="pick_up"
            type="com.hqt.data.model.response.StationPoints" />

        <variable
            name="drop_off"
            type="com.hqt.data.model.response.StationPoints" />

        <!--        <variable-->
        <!--            name="viewHolder"-->
        <!--            type="com.hqt.view.adapter.TrainAdapter.ViewHolder" />-->

        <import type="com.hqt.datvemaybay.Common" />

        <variable
            name="handler"
            type="com.hqt.view.ui.bus.BusSelectHandler" />
    </data>

    <androidx.cardview.widget.CardView
        android:id="@+id/card_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"

        app:cardCornerRadius="2dp"
        app:cardElevation="2dp"
        android:layout_margin="5dp">

        <LinearLayout
            android:padding="5dp"
            android:background="@drawable/corner_full"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:visibility="visible"
                android:layout_margin="5dp"
                android:id="@+id/layoutTrainView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="5dp"
                android:layout_gravity="center_horizontal"
                android:orientation="horizontal">

                <ImageView
                    android:layout_gravity="center_horizontal|center_vertical"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:background="@drawable/ic_bus"
                    android:contentDescription="@string/Description" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_gravity="center_vertical"
                            android:paddingStart="5dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/textDark"
                            android:text="@{route.company.name}"
                            android:textStyle="bold" />

                        <com.mikepenz.iconics.view.IconicsImageView
                            android:layout_marginStart="5dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:iiv_size="15dp"
                            app:iiv_icon="gmd_star"
                            android:layout_gravity="center_vertical"
                            app:iiv_color="@color/stt_yellow" />

                        <TextView
                            android:layout_gravity="center_vertical"
                            android:paddingStart="5dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{route.company.ratings.text()}"
                            android:textStyle="bold" />


                        <TextView
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:gravity="end"
                            android:paddingEnd="5dp"
                            android:text="@{Common.getDayOfWeek(route.departureDate) + `, `+Common.dateToString(route.departureDate ,`dd/MM` )}" />
                    </LinearLayout>

                    <TextView
                        android:layout_gravity="center_vertical"
                        android:paddingStart="5dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@{route.schedules.vehicleType}"
                        tools:text=""
                        android:textStyle="bold" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:padding="5dp"
                android:onClick="@{() -> handler.onSelectRoute(route)}"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal">

                <View
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:layout_marginRight="5dp"
                    android:layout_marginLeft="5dp"
                    android:background="@drawable/gradientdiv" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.mikepenz.iconics.view.IconicsImageView
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:padding="5dp"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            app:iiv_color="@color/stt_gray"
                            app:iiv_icon="faw-clock" />

                        <TextView
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:padding="5dp"
                            android:id="@+id/txtGioDi"
                            style="@style/Text"
                            android:textStyle="bold"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{pick_up.time }" />

                        <TextView
                            style="@style/Text"
                            android:textStyle="bold"
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:padding="5dp"
                            android:paddingStart="0dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{pick_up.name }" />


                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.mikepenz.iconics.view.IconicsImageView
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:padding="5dp"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            app:iiv_color="@color/stt_gray"
                            app:iiv_icon="faw-bus" />

                        <TextView
                            android:visibility="visible"
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:padding="5dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{route.distance + `Km -`}" />

                        <TextView
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:padding="5dp"
                            android:paddingStart="0dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/stt_gray"
                            android:text="@{route.durationText}" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.mikepenz.iconics.view.IconicsImageView
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:padding="5dp"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            app:iiv_color="@color/stt_gray"
                            app:iiv_icon="faw-map-marker-alt" />

                        <TextView
                            style="@style/Text"
                            android:textStyle="bold"
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:padding="5dp"
                            android:id="@+id/txtGioDen"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{drop_off.time }" />

                        <TextView
                            style="@style/Text"
                            android:textStyle="bold"
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:padding="5dp"
                            android:paddingStart="0dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{drop_off.name }" />


                    </LinearLayout>

                </LinearLayout>
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginRight="5dp"
                android:layout_marginLeft="5dp"
                android:background="@drawable/gradientdiv" />

            <LinearLayout
                android:visibility="visible"
                android:layout_margin="5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="5dp"
                android:layout_gravity="center_horizontal"
                android:orientation="horizontal">

                <ImageView
                    android:layout_gravity="center_horizontal|center_vertical"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:background="@drawable/ic_bus_line"
                    android:contentDescription="@string/Description" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_gravity="center_vertical"
                            android:paddingStart="5dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/textDark"
                            android:text="Ghế đã chọn "
                            android:textStyle="bold" />

                        <LinearLayout
                            android:layout_width="fill_parent"
                            android:gravity="end"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_gravity="center_horizontal|center_vertical"
                                android:padding="5dp"
                                android:layout_width="wrap_content"
                                android:gravity="end"
                                android:text="x"
                                android:layout_height="wrap_content"
                                android:textColor="@color/stt_gray" />

                            <TextView
                                android:id="@+id/total_seat"
                                android:layout_gravity="center_horizontal|center_vertical"
                                android:padding="5dp"
                                android:layout_width="wrap_content"
                                android:gravity="end"
                                android:textStyle="bold"
                                android:layout_height="wrap_content"
                                android:textColor="@color/stt_gray" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_gravity="center_horizontal|center_vertical"
                                android:paddingRight="5dp"
                                android:layout_height="wrap_content"
                                android:textColor="@color/stt_gray"
                                android:text="Ghế" />
                        </LinearLayout>


                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/txt_seat_select"
                            android:layout_gravity="center_vertical"
                            android:paddingStart="5dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:text=""
                            android:textStyle="bold" />

                        <LinearLayout
                            android:gravity="right"
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content">


                            <TextView
                                android:id="@+id/total_seat_charges"
                                android:textStyle="bold"
                                android:paddingEnd="5dp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="#FF0000" />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>

        </LinearLayout>
    </androidx.cardview.widget.CardView>
</layout>