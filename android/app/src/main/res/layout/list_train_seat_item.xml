<?xml version="1.0" encoding="UTF-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="train"
            type="com.hqt.data.model.Train" />

        <variable
            name="trainSeat"
            type="com.hqt.data.model.TrainSeatFare" />


        <variable
            name="handler"
            type="com.hqt.view.ui.train.TrainSelectHanlder" />

        <import type="com.hqt.datvemaybay.Common" />
    </data>

    <androidx.cardview.widget.CardView
        android:id="@+id/card_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/corner_full"
        app:cardCornerRadius="2dp"
        app:cardElevation="0dp"
        android:layout_marginBottom="@dimen/cardMarginVertical"
        android:layout_marginLeft="@dimen/cardMarginHorizontal"
        android:layout_marginRight="@dimen/cardMarginHorizontal"
        android:layout_marginTop="@dimen/cardMarginVertical"
        app:cardPreventCornerOverlap="false">

        <LinearLayout
            android:onClick="@{() -> handler.onSelectSeat(trainSeat)}"
            android:padding="5dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/trainLogo"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:scaleType="centerCrop"
                android:background="@drawable/icon_train_car" />

            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:layout_marginLeft="5dp"
                android:background="@drawable/gradientdiv" />

            <LinearLayout
                android:padding="5dp"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/txtTenLoaiCho"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:text="@{trainSeat.seatClassName}"
                    android:textColor="#000000"
                    tools:text="Ghế ngồi mềm điều hòa"
                    android:textSize="18sp" />

                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        bind:isBold="@{trainSeat.seatCount > 0 ? true : false}"
                        android:paddingRight="5dp"
                        android:layout_width="wrap_content"
                        android:text="Số chỗ: "
                        android:layout_height="wrap_content" />

                    <TextView
                        bind:isBold="@{trainSeat.seatCount > 0 ? true : false}"
                        android:id="@+id/txtSoCho"
                        android:layout_width="wrap_content"
                        android:text="@{String.valueOf(trainSeat.seatCount)}"
                        android:layout_height="wrap_content" />

                    <TextView
                        android:paddingStart="5dp"
                        android:id="@+id/txtLoaiCho"
                        android:layout_width="wrap_content"
                        android:text="@{trainSeat.seatClass}"
                        android:layout_height="wrap_content" />

                    <TextView
                        android:gravity="end"
                        android:id="@+id/txtGiaNguoiLon"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#da281c"
                        android:text="@{Common.dinhDangTien(trainSeat.adult)}" />
                </LinearLayout>

            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>
</layout>