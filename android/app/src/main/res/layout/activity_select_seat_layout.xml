<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.hqt.viewmodel.BookingViewModel" />


    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:id="@+id/coordinatorLayout"
        android:fitsSystemWindows="true">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:theme="@style/AppTheme.AppBarOverlay">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">


                <androidx.appcompat.widget.Toolbar
                    android:id="@+id/toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:popupTheme="@style/AppTheme.PopupOverlay"
                    android:background="@color/primary" />


                <com.google.android.material.tabs.TabLayout
                    android:id="@+id/tabLayout"
                    android:layout_width="match_parent"
                    android:layout_height="32dp"
                    android:layout_below="@+id/toolbar"
                    android:background="@color/white"
                    app:tabGravity="fill"
                    app:tabIndicatorColor="@color/primary"
                    app:tabMaxWidth="0dp"
                    app:tabMode="fixed"
                    app:tabTextColor="@color/textDark" />


            </RelativeLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <androidx.viewpager.widget.ViewPager
            app:layout_behavior="@string/appbar_scrolling_view_behavior"
            android:layout_below="@+id/tabLayout"
            android:id="@+id/view_pager"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />


    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>

