<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layout_behavior="@string/appbar_scrolling_view_behavior"
    android:background="#F2F1F1">

    <LinearLayout
        android:id="@+id/view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <HorizontalScrollView
            android:background="@color/primary"
            android:scrollbars="none"
            android:id="@+id/horizalScroll"

            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"></LinearLayout>

                <LinearLayout
                    android:id="@+id/containerRt"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                </LinearLayout>

            </LinearLayout>
        </HorizontalScrollView>

        <androidx.cardview.widget.CardView
            android:layout_marginTop="5dp"
            android:id="@+id/card_view1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="2dp"
            app:cardElevation="0dp"
            style="@style/CardViewStyle.Light"
            android:layout_marginBottom="0dp"
            android:layout_marginLeft="5dp"
            android:layout_marginRight="5dp"
            app:cardPreventCornerOverlap="false"
            app:contentPadding="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="10dp"
                android:background="#FFFFFF"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="5dp"
                    android:orientation="vertical">

                    <TextView

                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_gravity="left"
                        android:textStyle="bold"
                        style="@style/Text"
                        android:text="@string/thongTinHanhKhach" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_margin="4dp"
                        android:background="@color/diver_color" />


                </LinearLayout>

                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:id="@+id/paxInPut"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal">


                </LinearLayout>


            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/card_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="2dp"
            app:cardElevation="0dp"
            style="@style/CardViewStyle.Light"
            android:layout_marginBottom="5dp"
            android:layout_marginLeft="5dp"
            android:layout_marginRight="5dp"
            android:layout_marginTop="5dp"
            app:cardPreventCornerOverlap="false"
            app:contentPadding="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="10dp"
                android:background="#FFFFFF"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="5dp"
                    android:orientation="vertical">


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:paddingBottom="2dp"
                            android:layout_height="wrap_content">

                            <TextView
                                android:id="@+id/textView8"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_gravity="left"
                                android:textStyle="bold"
                                style="@style/Text"
                                android:text="Thông tin đơn hàng" />

                        </LinearLayout>
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_margin="4dp"
                        android:background="@color/diver_color" />


                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout

                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingBottom="5dp"
                        android:weightSum="5">

                        <TextView
                            android:id="@+id/textView1"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="2"
                            android:text="@string/hoVaTen" />

                        <TextView
                            android:id="@+id/txtName"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="3"
                            android:background="@drawable/edit_text"
                            android:ems="10"
                            android:hint="@string/txtNamehint"
                            android:inputType="textCapCharacters"
                            android:padding="4dp"
                            android:singleLine="true"
                            android:textColor="#000">

                            <requestFocus />
                        </TextView>


                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingBottom="5dp"
                    android:weightSum="5">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="2"
                            android:text="@string/email" />

                        <TextView
                            android:id="@+id/txtEmail"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="3"
                            android:background="@drawable/edit_text"
                            android:editable="false"
                            android:ems="10"
                            android:inputType="textEmailAddress"
                            android:padding="4dp"
                            android:singleLine="true"
                            android:textColor="#000" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingBottom="5dp"
                    android:weightSum="5">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="2"
                            android:text="@string/soDienThoai" />

                        <TextView
                            android:id="@+id/txtPhone"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="3"
                            android:background="@drawable/edit_text"
                            android:ems="10"
                            android:inputType="phone"
                            android:padding="4dp"
                            android:textColor="#000" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingBottom="5dp">

                    <LinearLayout
                        android:id="@+id/waittingView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:paddingBottom="2dp">

                            <TextView
                                android:id="@+id/textVie"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/maDonHang" />


                            <TextView
                                android:id="@+id/txtBookingId"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="right"
                                android:paddingRight="5dp"
                                android:text="@string/maDonHang"
                                android:textColor="#14a9e5"
                                android:textStyle="bold" />

                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:paddingBottom="2dp"
                            android:layout_height="wrap_content">

                            <TextView
                                android:id="@+id/txtPnr"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Mã đặt chỗ (Code):" />


                            <TextView
                                android:id="@+id/pnr"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="right"
                                android:textColor="#ff0f0b"
                                android:textSize="14sp"
                                android:text="394857"
                                android:textStyle="bold" />

                        </LinearLayout>


                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:id="@+id/pnrReturnLayout"
                        android:visibility="gone"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:paddingBottom="2dp"
                            android:layout_height="wrap_content">

                            <TextView
                                android:id="@+id/txtPnrReturn"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Mã đặt chỗ lượt về:" />


                            <TextView
                                android:id="@+id/pnrReturn"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="right"
                                android:textColor="#ff0f0b"
                                android:textSize="14sp"
                                android:text="394857"
                                android:textStyle="bold" />

                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:paddingBottom="2dp"
                        android:orientation="horizontal"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/textView5"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Trạng thái:" />

                        <LinearLayout
                            android:id="@+id/statusPenddingLayout"
                            android:visibility="visible"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="right"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/txtBookingIdd"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:paddingRight="5dp"
                                android:text="Đang tiến hành đặt chỗ"
                                android:textColor="#14a9e5" />

                            <ProgressBar
                                android:id="@+id/marker_progress"
                                style="?android:attr/progressBarStyle"
                                android:layout_width="20dp"
                                android:layout_height="20dp"
                                android:paddingRight="5dp"
                                android:indeterminate="true" />
                        </LinearLayout>

                        <TextView
                            android:id="@+id/txtStatus"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="right"
                            android:textColor="#FF0000"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:id="@+id/timeLimit"
                        android:paddingBottom="2dp"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/textView6"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Hạn thanh toán:" />

                        <TextView
                            android:id="@+id/txtTimeLimit"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="right"
                            android:textColor="#14a9e5"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_margin="4dp"
                        android:background="@color/diver_color" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:paddingBottom="2dp"
                            android:layout_height="wrap_content">

                            <TextView
                                android:id="@+id/textView3"
                                android:layout_width="wrap_content"
                                style="@style/Text"
                                android:layout_height="wrap_content"
                                android:textStyle="bold"
                                android:text="@string/tongCong" />

                            <TextView
                                android:id="@+id/txtGiaTongCong"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="right"
                                android:textColor="#FF0000"
                                android:textStyle="bold" />

                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/layoutPointReward"
                            android:layout_width="match_parent"
                            android:paddingBottom="2dp"
                            android:gravity="end"
                            android:layout_height="wrap_content">

                            <TextView
                                android:id="@+id/txtPoint"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="end"
                                android:textColor="@color/primary"
                                android:text="Nhận 10 điểm" />

                            <com.mikepenz.iconics.view.IconicsTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/primary"
                                android:text="{gmd_stars} " />

                        </LinearLayout>

                        <View
                            android:layout_width="0dp"
                            android:layout_height="0dp"
                            android:id="@+id/got_it" />


                    </LinearLayout>


                </LinearLayout>

                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal|bottom"
                    android:background="#FFFFFF">

                    <LinearLayout
                        android:id="@+id/rebookLayout"
                        android:visibility="gone"
                        android:layout_width="match_parent"
                        android:gravity="center_horizontal"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <androidx.cardview.widget.CardView
                            android:id="@+id/btnCall"
                            app:cardCornerRadius="2dp"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            app:cardElevation="2dp"
                            android:layout_margin="2dp"
                            app:cardBackgroundColor="@color/btnColor"
                            android:foreground="?android:attr/selectableItemBackground"
                            android:clickable="true"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal|center_vertical">

                            <LinearLayout
                                android:padding="8dp"
                                android:layout_gravity="center_horizontal|center_vertical"
                                android:gravity="center_horizontal|center_vertical"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:layout_width="16dp"
                                    android:layout_height="16dp"
                                    app:iiv_color="@android:color/white"
                                    app:iiv_icon="gmd-call"
                                    android:layout_marginEnd="5dp" />

                                <TextView
                                    android:textSize="16sp"
                                    android:textColor="@color/white"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Liên lạc hỗ trợ" />
                            </LinearLayout>
                        </androidx.cardview.widget.CardView>


                        <androidx.cardview.widget.CardView
                            android:id="@+id/btnRebook"
                            app:cardCornerRadius="2dp"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            app:cardElevation="2dp"
                            android:layout_margin="2dp"
                            app:cardBackgroundColor="@color/btnColor"
                            android:foreground="?android:attr/selectableItemBackground"
                            android:clickable="true"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal|center_vertical">

                            <LinearLayout
                                android:padding="8dp"
                                android:layout_gravity="center_horizontal|center_vertical"
                                android:gravity="center_horizontal|center_vertical"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:layout_width="16dp"
                                    android:layout_height="16dp"
                                    app:iiv_color="@android:color/white"
                                    app:iiv_icon="gmd-refresh"
                                    android:layout_marginEnd="5dp" />

                                <TextView
                                    android:textSize="16sp"
                                    android:textColor="@color/white"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Đặt lại đơn hàng" />
                            </LinearLayout>
                        </androidx.cardview.widget.CardView>


                    </LinearLayout>

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/btnBookVe"
                        android:background="@drawable/button_gradient"
                        android:textColor="#FFFFFF"
                        android:layout_width="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center_vertical"
                        android:paddingLeft="50dp"
                        android:paddingRight="50dp"
                        android:textSize="16sp"
                        android:layout_height="wrap_content"
                        android:text="LIÊN LẠC HỖ TRỢ" />

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/btnPaynow"
                        android:background="@drawable/button_gradient"
                        android:textColor="#FFFFFF"
                        android:layout_width="match_parent"
                        android:textAlignment="center"
                        android:textSize="16sp"
                        android:visibility="gone"
                        android:layout_height="wrap_content"
                        app:textAllCaps="true"
                        android:textAllCaps="true"
                        android:text="NHẤN ĐỂ THANH TOÁN" />


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:weightSum="3"
                        android:visibility="gone"
                        android:id="@+id/addonlayout"
                        android:orientation="horizontal">

                        <com.mikepenz.iconics.view.IconicsButton
                            android:id="@+id/btnPrintTicket"
                            android:textColor="#FFFFFF"
                            style="@style/MyApp.Button.Big"
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:textAlignment="center"
                            android:textSize="16sp"
                            android:layout_height="wrap_content"
                            android:text="{faw_download} Tải vé" />

                        <com.mikepenz.iconics.view.IconicsButton
                            android:id="@+id/btnCheckve"
                            android:textColor="#FFFFFF"
                            style="@style/MyApp.Button.Big"
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:textAlignment="center"
                            android:textSize="16sp"
                            android:layout_height="wrap_content"
                            android:text="{faw_clipboard_check} Kiểm tra vé" />

                        <com.mikepenz.iconics.view.IconicsButton
                            android:id="@+id/btnCheckin"
                            android:textColor="#FFFFFF"
                            style="@style/MyApp.Button.Big"
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:textAlignment="center"
                            android:textSize="16sp"
                            android:layout_height="wrap_content"
                            android:text="{faw_user_check} Checkin" />
                    </LinearLayout>
                </LinearLayout>


            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/payment_success_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="2dp"
            app:cardElevation="0dp"
            android:visibility="gone"
            style="@style/CardViewStyle.Light"
            android:layout_marginBottom="5dp"
            android:layout_marginLeft="5dp"
            android:layout_marginRight="5dp"
            android:layout_marginTop="5dp"
            app:cardPreventCornerOverlap="false"
            app:contentPadding="2dp">

            <LinearLayout
                android:padding="10dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="12bay đã nhận được khoản thanh toán. Nhân viên CSKH sẽ liên hệ xác nhận xuất vé trong vòng 5p đến 10p. Vui lòng đợi giây lát." />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:id="@+id/widget_chat"
                    android:orientation="vertical" />

            </LinearLayout>


        </androidx.cardview.widget.CardView>


    </LinearLayout>

</androidx.core.widget.NestedScrollView>