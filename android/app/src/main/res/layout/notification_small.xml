<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">


    <TextView
        android:id="@+id/notification_small_title"
        style="@style/TextAppearance.Compat.Notification.Title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_alignParentStart="true"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="44dp"
        android:text="" />

    <TextView
        android:layout_marginEnd="44dp"
        style="@style/TextAppearance.Compat.Notification.Title"
        android:id="@+id/notification_small_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/notification_small_title"
        android:layout_alignParentStart="true"
        android:layout_marginStart="8dp"
        android:text=""
        android:maxLines="1" />

    <ImageView
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:id="@+id/notification_img"
        android:scaleType="centerCrop"
        android:layout_width="40dp"
        android:layout_height="40dp" />
</RelativeLayout>