<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:bind="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="paymentBank"
            type="com.hqt.data.model.Payment" />

        <variable
            name="order"
            type="com.hqt.data.model.PaymentOrderXml" />


        <import type="com.hqt.datvemaybay.Common" />

        <import type="android.view.View" />
    </data>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="10dp"
                android:text="<PERSON>ui lòng chuyể<PERSON> kho<PERSON>n đến" />


            <androidx.cardview.widget.CardView
                android:layout_margin="5dp"
                app:cardElevation="0dp"
                app:cardCornerRadius="5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">


                <LinearLayout
                    android:padding="5dp"
                    android:paddingEnd="10dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layoutDirection="rtl"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <ImageView
                            android:gravity="center_vertical|center_horizontal"
                            android:layout_margin="10dp"
                            bind:imageUrl="@{paymentBank.logo}"
                            android:layout_width="100dp"
                            android:layout_height="50dp"
                            android:src="@drawable/tk_vcb" />

                        <LinearLayout

                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content">

                            <LinearLayout
                                android:layout_gravity="end"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@color/white"
                                android:padding="5dp"
                                android:orientation="vertical">

                                <TextView
                                    android:paddingBottom="5dp"
                                    android:textAlignment="textStart"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="Ngân hàng" />

                                <TextView
                                    android:textColor="@color/black"
                                    android:background="@drawable/input_bg"
                                    android:padding="10dp"
                                    android:textStyle="bold"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:textSize="16sp"
                                    tools:text="Viet com bank"
                                    android:text="@{paymentBank.title}" />

                            </LinearLayout>
                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <LinearLayout

                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@color/white"
                            android:padding="5dp"
                            android:orientation="vertical">

                            <TextView
                                android:paddingBottom="5dp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                tools:text="Chủ tài khoản"
                                android:text="Chủ tài khoản" />

                            <TextView
                                android:textColor="@color/black"
                                android:background="@drawable/input_bg"
                                android:padding="10dp"
                                android:textStyle="bold"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textSize="16sp"
                                tools:text="NGUYEN VAN TRÌNH"
                                android:text="@{paymentBank.detail.accountName}" />

                        </LinearLayout>


                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <LinearLayout

                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@color/white"
                            android:padding="5dp"
                            android:orientation="vertical">

                            <TextView
                                android:paddingBottom="5dp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Chi nhánh" />

                            <TextView
                                android:textColor="@color/black"
                                android:background="@drawable/input_bg"
                                android:padding="10dp"
                                android:textStyle="bold"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textSize="16sp"
                                android:text="@{paymentBank.detail.branch}" />

                        </LinearLayout>


                    </LinearLayout>

                    <LinearLayout

                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/white"
                        android:padding="5dp"
                        android:orientation="vertical">

                        <TextView
                            android:paddingBottom="5dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:text="Số tài khoản"
                            android:text="Số tài khoản" />

                        <LinearLayout
                            android:background="@drawable/input_bg"
                            android:padding="10dp"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <TextView
                                android:textColor="@color/black"
                                android:textStyle="bold"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="16sp"
                                tools:text="Số tài khoản"
                                android:text="@{paymentBank.detail.accountNumber}" />

                            <TextView
                                bind:btnCopy="@{paymentBank.detail.accountNumber}"
                                android:textSize="12sp"
                                android:textStyle="bold"
                                android:layout_width="fill_parent"
                                android:textAlignment="textEnd"
                                android:layout_height="wrap_content"
                                android:text="Sao chép" />
                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/white"
                        android:padding="5dp"
                        android:orientation="vertical">

                        <TextView
                            android:paddingBottom="5dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:text="Nội dung"
                            android:text="Nội dung" />

                        <LinearLayout
                            android:background="@drawable/input_bg"
                            android:padding="10dp"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <TextView
                                android:textColor="@color/black"
                                android:textStyle="bold"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="16sp"
                                android:text="@{order.paymentNote }" />

                            <TextView
                                bind:btnCopy="@{order.paymentNote}"
                                android:textSize="12sp"
                                android:textStyle="bold"
                                android:layout_width="fill_parent"
                                android:textAlignment="textEnd"
                                android:layout_height="wrap_content"
                                android:text="Sao chép" />
                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/white"
                        android:padding="5dp"
                        android:orientation="vertical">

                        <TextView
                            android:paddingBottom="5dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            tools:text="Số tiền cần thanh toán"
                            android:text="Số tiền cần thanh toán" />

                        <LinearLayout
                            android:background="@drawable/input_bg"
                            android:padding="10dp"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <TextView
                                android:id="@+id/highlight"
                                android:textStyle="bold"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="16sp"
                                tools:text="100,000k"
                                android:textColor="@color/blue"
                                android:text="@{Common.splitTotalPrice(Common.dinhDangTien(order.grandTotal),true)}" />

                            <TextView

                                android:textStyle="bold"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="16sp"
                                tools:text="100,000k"
                                android:textColor="@color/red"
                                android:text="@{Common.splitTotalPrice(Common.dinhDangTien(order.grandTotal),false)}" />

                            <TextView
                                bind:btnCopy="@{order.grandTotal +``}"
                                android:textSize="12sp"
                                android:textStyle="bold"
                                android:layout_width="fill_parent"
                                android:textAlignment="textEnd"
                                android:layout_height="wrap_content"
                                android:text="Sao chép" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_marginTop="5dp"
                            android:background="@drawable/input_hightlight"
                            android:padding="10dp"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="14sp"
                                android:textColor="@color/textDark"
                                android:text="Vui lòng chuyển khoản đúng đến hàng đơn vị" />


                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/white"
                        android:padding="5dp"
                        android:orientation="horizontal">

                        <TextView
                            android:paddingBottom="5dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Vui lòng thanh toán trước:  " />

                        <TextView
                            style="@style/Text"
                            android:textStyle="bold"
                            android:textColor="@color/red"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:id="@+id/timeLimit"
                            tools:text="" />


                    </LinearLayout>

                    <Button
                        android:layout_gravity="center"
                        android:background="@drawable/button_gradient"
                        android:id="@+id/btn_open_app"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/white"
                        android:text="    Mở app ngân hàng của bạn    " />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="10dp"
                android:text="Lưu ý trước khi thanh toán" />

            <androidx.cardview.widget.CardView
                android:layout_margin="5dp"
                app:cardElevation="0dp"
                app:cardCornerRadius="5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:padding="10dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_marginBottom="10dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="- Bạn vui lòng chỉ chọn chuyển khoản cùng ngân hàng hoặc chuyển khoản nhanh 24/4" />

                    <TextView
                        android:layout_marginBottom="10dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="- Phí chuyển khoản sẽ do người chuyển trả" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="10dp"
                android:text="Bạn đã hoàn tất thanh toán" />

            <androidx.cardview.widget.CardView
                android:layout_margin="5dp"
                app:cardElevation="0dp"
                app:cardCornerRadius="5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:padding="5dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_marginBottom="10dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Sau khi xác nhận số tiền thanh toán của bạn. Chúng tôi sẽ gửi biên nhận và vé điện tử qua email của bạn" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:id="@+id/widget_chat"
                        android:orientation="vertical" />


                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <androidx.appcompat.widget.AppCompatButton
                android:layout_gravity="center_horizontal"
                android:paddingStart="20dp"
                android:paddingEnd="0dp"
                android:layout_marginBottom="20dp"
                android:id="@+id/btnDonePayment"
                android:textColor="@color/white"
                android:background="@drawable/button_gradient"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="   Tôi đã thanh toán xong  " />

        </LinearLayout>
    </ScrollView>
</layout>