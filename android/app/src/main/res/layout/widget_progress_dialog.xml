<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.cardview.widget.CardView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:orientation="vertical"
        app:cardBackgroundColor="#D9000000"
        app:cardCornerRadius="15dp"
        app:cardElevation="0px">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:minWidth="110dp"
            android:minHeight="110dp"
            android:orientation="vertical"
            android:padding="10dp">

            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/loadingLottie"
                android:layout_width="70dp"
                android:layout_height="70dp"
                android:layerType="software"
                app:lottie_autoPlay="true"
                app:lottie_loop="true"
                app:lottie_rawRes="@raw/lott_animation_feed"
                tools:ignore="MissingClass" />

            <TextView
                android:id="@+id/tv_wait_message"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="15dp"
                android:layout_marginTop="5dp"
                android:maxLines="3"
                android:text="Đang tải dữ liệu"
                android:textColor="@color/white"
                android:textSize="14sp" />
        </LinearLayout>

    </androidx.cardview.widget.CardView>
</layout>