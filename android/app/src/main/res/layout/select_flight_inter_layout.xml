<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/gbgray"
    android:layout_height="match_parent"
    android:layout_width="match_parent">

    <ScrollView
        android:layout_above="@+id/select_flight_sheet"
        android:layout_width="match_parent"
        android:layout_height="fill_parent">

        <LinearLayout
            android:orientation="vertical"
            android:id="@+id/flightInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">



        </LinearLayout>
    </ScrollView>

    <LinearLayout
        android:layout_alignParentBottom="true"
        android:id="@+id/select_flight_sheet"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:clickable="true"
        android:focusable="true"
        android:elevation="6dp"
        app:behavior_hideable="true"
        app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">

        <View
            android:layout_margin="2dp"
            android:layout_gravity="center_vertical|center_horizontal"
            android:layout_width="50dp"
            android:layout_height="4dp"
            android:background="@drawable/top_line" />

        <LinearLayout
            android:background="@color/gbgray"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">


            <androidx.cardview.widget.CardView
                android:id="@+id/quickViewLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:paddingTop="5dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:id="@+id/quickView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="left|center_vertical"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:paddingRight="5dp"
                                android:paddingLeft="5dp"
                                android:paddingTop="2dp"
                                android:paddingBottom="2dp"
                                android:layout_height="wrap_content">

                                <TextView
                                    android:id="@+id/textView2"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textStyle="bold"
                                    android:textColor="@color/black"
                                    android:text="Tổng số tiền " />

                                <TextView
                                    android:id="@+id/txtTotalPax"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="top|left"
                                    android:paddingRight="5dp" />


                            </LinearLayout>

                            <TextView
                                android:paddingRight="5dp"
                                android:paddingLeft="5dp"
                                android:paddingTop="2dp"
                                android:paddingBottom="2dp"
                                android:singleLine="true"
                                android:textSize="13sp"
                                android:text="Giá vé đã bao gồm thuế phí"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/txtGrandTotalPrice"
                                android:gravity="end"
                                android:singleLine="true"
                                android:textColor="#da281c"
                                android:textStyle="bold"
                                android:textSize="20sp"
                                android:paddingRight="5dp"
                                android:paddingLeft="15dp"
                                tools:text="900k"
                                android:layout_width="fill_parent"
                                android:layout_height="wrap_content" />

                            <LinearLayout
                                android:id="@+id/layoutPointReward"
                                android:layout_width="match_parent"
                                android:paddingBottom="2dp"
                                android:gravity="end"
                                android:layout_height="wrap_content">

                                <TextView
                                    android:id="@+id/txtPoint"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:gravity="end"
                                    android:textStyle="italic"
                                    android:textSize="13sp"
                                    android:textColor="@color/primary"
                                    android:text=" " />

                                <com.mikepenz.iconics.view.IconicsTextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/primary"
                                    android:text="{gmd_stars} " />

                            </LinearLayout>
                        </LinearLayout>

                    </LinearLayout>


                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:weightSum="10"
                        android:paddingBottom="10dp"
                        android:orientation="horizontal">

                        <Button
                            android:id="@+id/btnBack"
                            android:layout_width="0dp"
                            android:layout_weight="4"
                            android:layout_height="wrap_content"
                            android:textSize="15sp"
                            android:layout_gravity="right"
                            android:text="Trở về"
                            app:backgroundTint="@color/stt_gray"
                            android:textColor="#FFFFFF" />

                        <Button
                            android:id="@+id/btnBookVe"
                            android:layout_width="0dp"
                            android:layout_weight="6"
                            android:layout_height="wrap_content"
                            android:layout_gravity="right"
                            android:text="Chọn"
                            app:backgroundTint="@color/primary"
                            android:textColor="#FFFFFF" />

                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/close_view"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:paddingTop="5dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:weightSum="6"
                        android:paddingBottom="10dp"
                        android:orientation="horizontal">


                        <Button
                            android:id="@+id/btnBackFlight"
                            android:layout_width="0dp"
                            android:layout_weight="6"
                            android:layout_height="wrap_content"
                            android:layout_gravity="right"
                            android:text="Trở về"
                            app:backgroundTint="@color/primary"
                            android:textColor="#FFFFFF" />

                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>
    </LinearLayout>
</RelativeLayout>