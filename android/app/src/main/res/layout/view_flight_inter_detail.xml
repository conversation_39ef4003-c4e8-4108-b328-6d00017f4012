<?xml version="1.0" encoding="UTF-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:bind="http://schemas.android.com/tools">

    <data>

        <variable
            name="flightInfo"
            type="com.hqt.view.ui.flightSearch.model.Flight" />

        <import type="com.hqt.datvemaybay.Common" />

        <import type="android.view.View" />

    </data>


    <androidx.cardview.widget.CardView
        android:id="@+id/card_view_chieuDi"
        style="@style/CardViewStyle.Light"
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:layout_margin="5dp"
        android:animateLayoutChanges="false"
        app:cardCornerRadius="2dp"
        app:cardElevation="2dp"
        app:cardPreventCornerOverlap="false">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/activity_vertical_margin"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="5dp">

                <com.mikepenz.iconics.view.IconicsTextView
                    style="@style/Text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="{faw_paper_plane}  "
                    android:textSize="10dp" />

                <TextView
                    android:id="@+id/txtLuotdiTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:text="@string/luotDi"
                    android:text="@{flightInfo.title}"
                    android:textColor="#000000"
                    android:textStyle="bold" />

                <TextView
                    android:paddingStart="10dp"
                    android:id="@+id/txtDiHangBay"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@{flightInfo.airlineName}"
                    style="@style/Text" />

                <LinearLayout
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/txtDiNgayBay"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:text="@{(flightInfo.startDate.substring(0, 10))}"
                        android:textColor="#000"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/txtDiNgayBayAmLich"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:paddingLeft="5dp"
                        android:text="@{flightInfo.getLunarDate(true)}"
                        android:textColor="#0084ff"
                        android:textSize="10sp"
                        android:textStyle="italic" />
                </LinearLayout>

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginLeft="7dp"
                android:layout_marginRight="7dp"
                android:background="@drawable/gradientdiv_vertical" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginLeft="4dp"
                android:layout_marginTop="4dp"
                android:layout_marginRight="4dp"
                android:layout_marginBottom="4dp"
                android:orientation="horizontal"
                android:weightSum="9">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">


                    <TextView
                        tools:text="10:20"
                        android:id="@+id/txtDiThoiGianBay"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@{flightInfo.getStartTime()}"
                        android:textColor="#000000"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/txtDiFrom"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/code_rounded"
                        android:paddingLeft="3dp"
                        android:paddingTop="2dp"
                        android:paddingRight="3dp"
                        android:paddingBottom="2dp"
                        android:singleLine="true"
                        android:text="@{flightInfo.startPoint}"
                        android:textColor="#7C7C7C"
                        android:textSize="12sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:paddingLeft="3dp"
                        android:paddingTop="2dp"
                        android:paddingRight="3dp"
                        android:paddingBottom="2dp"
                        android:singleLine="true"
                        android:text="@{flightInfo.startPointInfo.name}"
                        android:textColor="#7C7C7C"
                        android:textSize="10sp"
                        android:textStyle="bold" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/txtDiDiemDung"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:text="@{flightInfo.getStopString()}"
                        android:textColor="#000000"
                        android:textSize="11sp" />

                    <com.hqt.util.DurationView
                        android:id="@+id/duration_view"
                        android:layout_width="match_parent"
                        android:layout_height="10dp" />

                    <TextView
                        android:id="@+id/txtDiDuration"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:text="@{``+flightInfo.flightTime}"
                        tools:text="1h15pd"
                        android:maxLines="1"
                        android:textColor="#000000"
                        android:textSize="12sp"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">


                    <TextView
                        android:layoutDirection="rtl"
                        android:id="@+id/txtDiThoiGianDen"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@{flightInfo.getEndTime()}"
                        tools:text="10:20"
                        android:textColor="#000000"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/code_rounded"
                        android:gravity="center"
                        android:paddingLeft="3dp"
                        android:paddingTop="2dp"
                        android:paddingRight="3dp"
                        android:paddingBottom="2dp"
                        android:singleLine="true"
                        android:text="@{flightInfo.endPoint}"
                        android:textColor="#7C7C7C"
                        android:textSize="12sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:paddingLeft="3dp"
                        android:paddingTop="2dp"
                        android:paddingRight="3dp"
                        android:paddingBottom="2dp"
                        android:singleLine="true"
                        android:text="@{flightInfo.endPointInfo.name}"
                        android:textColor="#7C7C7C"
                        android:textSize="10sp"
                        android:textStyle="bold" />


                </LinearLayout>

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginLeft="7dp"
                android:layout_marginRight="7dp"
                android:background="@drawable/gradientdiv_vertical" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:layout_marginRight="4dp"
                android:layout_marginLeft="4dp"
                android:weightSum="9">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="fill_parent"
                        android:layout_height="35dp"
                        bind:imageUrl="@{flightInfo.airlinesLogo}" />


                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="3"
                    android:gravity="center_horizontal|center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal|center_vertical"
                        android:textColor="#000000"
                        android:maxLines="1"
                        tools:text="1"
                        android:text="@{flightInfo.flightNumber}"
                        android:textSize="11sp"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Loại vé"
                        android:textColor="#000000"
                        android:textSize="11sp" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/txtClassIconDi"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="#000000"
                            android:textSize="11sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:singleLine="true"
                            android:maxLength="20"
                            android:ellipsize="end"
                            android:textColor="#000000"
                            android:text="@{flightInfo.fareRuleInfo}"
                            android:textSize="11sp"
                            android:textStyle="bold" />
                    </LinearLayout>


                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_marginTop="4dp"
                android:id="@+id/viewShowTotalDi"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginLeft="7dp"
                    android:layout_marginRight="7dp"
                    android:background="@drawable/gradientdiv_vertical" />

                <LinearLayout
                    android:id="@+id/viewTotalDi"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="5dp"
                    android:layout_weight="3"
                    android:animateLayoutChanges="true"
                    android:orientation="vertical">


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingLeft="2dp"
                        android:paddingRight="2dp">


                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{flightInfo.getStopInfoString()}"
                            android:textColor="#000000"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/viewFlightDetail"
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:gravity="end"
                            android:text="Xem chi tiết"
                            android:textColor="@color/primary_dark"
                            android:textStyle="bold" />


                    </LinearLayout>


                </LinearLayout>
            </LinearLayout>

        </LinearLayout>
    </androidx.cardview.widget.CardView>
</layout>