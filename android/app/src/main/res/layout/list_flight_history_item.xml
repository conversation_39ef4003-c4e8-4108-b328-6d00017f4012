<?xml version="1.0" encoding="UTF-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewmodel"
            type="com.hqt.view.ui.flighthistory.data.model.FlightHistoryItem" />


        <import type="com.hqt.datvemaybay.Common" />

    </data>

    <androidx.cardview.widget.CardView
        android:tag="@{viewmodel.id + `xxxx`}"
        android:id="@+id/item"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/corner_full"
        app:cardCornerRadius="2dp"
        app:cardElevation="1dp"
        android:layout_marginBottom="@dimen/cardMarginVertical"
        android:layout_marginLeft="@dimen/cardMarginHorizontal"
        android:layout_marginRight="@dimen/cardMarginHorizontal"
        android:layout_marginTop="@dimen/cardMarginVertical"
        android:clickable="true"
        app:cardPreventCornerOverlap="false"
        android:focusable="true">


        <LinearLayout
            android:background="?android:attr/selectableItemBackground"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal">

            <View
                android:layout_width="5dp"
                android:layout_height="match_parent"
                bind:viewColor="@{viewmodel.status.getIconColor()}" />

            <LinearLayout
                android:padding="5dp"
                android:paddingStart="0dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <com.mikepenz.iconics.view.IconicsImageView
                        android:layout_marginStart="5dp"
                        android:layout_marginEnd="5dp"
                        android:layout_width="15dp"
                        android:layout_height="15dp"
                        android:layout_gravity="center_vertical"
                        app:iiv_size="20dp"
                        app:iiv_color="@color/stt_gray"
                        app:iiv_icon="gmd_flight_takeoff" />

                    <TextView

                        android:layout_gravity="center_horizontal|center_vertical"
                        android:padding="5dp"
                        android:textColor="@color/textDark"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@{ viewmodel.airport.origin.city }" />

                    <TextView
                        android:paddingStart="0dp"
                        android:layout_gravity="center_horizontal|center_vertical"
                        android:padding="5dp"
                        android:textColor="@color/textDark"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@{ `(`+viewmodel.airport.origin.code +`)`}" />

                    <LinearLayout
                        android:gravity="right"
                        android:layout_width="fill_parent"
                        android:layout_height="match_parent">

                        <TextView
                            style="@style/Text"
                            android:textStyle="bold"
                            android:gravity="center_vertical"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:text="@{Common.dateToString(viewmodel.time.scheduled.departure, `dd/MM/yyyy`)}" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <com.mikepenz.iconics.view.IconicsImageView
                        android:layout_marginStart="5dp"
                        android:layout_marginEnd="5dp"
                        android:layout_width="15dp"
                        android:layout_height="15dp"
                        android:layout_gravity="center_vertical"
                        app:iiv_size="20dp"
                        app:iiv_color="@color/stt_gray"
                        app:iiv_icon="gmd_flight_land" />

                    <TextView

                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal|center_vertical"
                        android:padding="5dp"
                        android:textColor="@color/textDark"
                        android:text="@{viewmodel.airport.destination.city}" />

                    <TextView
                        android:textColor="@color/textDark"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal|center_vertical"
                        android:padding="5dp"
                        android:paddingStart="0dp"
                        android:text="@{`(`+viewmodel.airport.destination.code+`)`}" />

                    <LinearLayout
                        android:gravity="right|center_vertical"
                        android:layout_width="fill_parent"
                        android:layout_height="match_parent">

                        <TextView
                            bind:visibility="@{!viewmodel.status.live}"
                            style="@style/Text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@{viewmodel.status.getIconColor()}"
                            android:text="@{viewmodel.status.getFlightStatus()}" />

                        <LinearLayout
                            bind:visibility="@{viewmodel.status.live}"
                            android:background="@drawable/button_round_yellow"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <com.mikepenz.iconics.view.IconicsImageView
                                android:layout_marginStart="10dp"
                                android:layout_width="15dp"
                                android:layout_height="15dp"
                                android:rotation="45"
                                android:layout_gravity="center_vertical"
                                app:iiv_size="12dp"
                                app:iiv_color="@color/white"
                                app:iiv_icon="gmd_airplanemode_active" />

                            <TextView
                                android:gravity="center_horizontal|center_vertical"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/white"
                                android:textSize="10sp"
                                android:textStyle="bold"
                                android:paddingStart="2dp"
                                android:paddingEnd="15dp"
                                android:paddingTop="2dp"
                                android:paddingBottom="2dp"
                                android:text="Đang bay" />
                        </LinearLayout>

                    </LinearLayout>


                </LinearLayout>

            </LinearLayout>

        </LinearLayout>
    </androidx.cardview.widget.CardView>
</layout>