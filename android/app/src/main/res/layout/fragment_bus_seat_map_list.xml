<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:fab="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewModel"
            type="com.hqt.viewmodel.BookingViewModel" />

        <import type="com.hqt.datvemaybay.Common" />

        <import type="android.view.View" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout

        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/gbgray"
        android:id="@+id/coordinatorLayout"
        android:animateLayoutChanges="true">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:background="@color/primary_dark"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true"
            app:elevation="0dp"
            android:theme="@style/AppTheme.AppBarOverlay">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/toolbar_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:contentScrim="@color/primary_dark"
                app:title="12bay.vn"
                app:titleEnabled="false"
                app:layout_scrollFlags="enterAlwaysCollapsed">

                <androidx.appcompat.widget.Toolbar
                    android:id="@+id/toolbar"
                    android:background="@color/primary"
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize"
                    app:elevation="0dp"
                    app:layout_collapseMode="pin"
                    app:layout_scrollFlags="scroll|enterAlways"
                    app:popupTheme="@style/AppTheme.PopupOverlay" />


            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:background="@color/gbgray"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <com.facebook.shimmer.ShimmerFrameLayout
                android:id="@+id/shimmer_view_container"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:orientation="vertical"
                android:visibility="gone"
                app:shimmer_duration="800">

                <LinearLayout
                    android:visibility="visible"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:weightSum="10"
                    android:orientation="horizontal">


                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_weight="8"
                        android:layout_height="match_parent"
                        android:orientation="vertical">

                        <include layout="@layout/placeholder_seat_layout" />

                        <include layout="@layout/placeholder_seat_layout" />

                        <include layout="@layout/placeholder_seat_layout" />

                        <include layout="@layout/placeholder_seat_layout" />

                        <include layout="@layout/placeholder_seat_layout" />

                        <include layout="@layout/placeholder_seat_layout" />

                        <include layout="@layout/placeholder_seat_layout" />


                    </LinearLayout>

                    <LinearLayout
                        android:background="@color/white"
                        android:paddingTop="10dp"
                        android:layout_width="0dp"
                        android:layout_weight="2"
                        android:layout_height="match_parent"
                        android:gravity="center_horizontal"
                        android:orientation="vertical">


                        <ImageView
                            android:rotation="45"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/ic_bus" />

                        <TextView
                            android:layout_marginTop="10dp"
                            android:textStyle="bold"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:singleLine="true"
                            android:textColor="#003A6F"
                            tools:text="A321" />

                        <ImageView

                            android:layout_marginTop="10dp"
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:background="@drawable/ic_seat" />

                        <TextView
                            android:layout_marginTop="10dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textStyle="bold"
                            android:textColor="#003A6F"
                            tools:text="A" />

                        <TextView
                            android:layout_marginTop="10dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:singleLine="true"
                            android:textColor="#da281c"
                            tools:text="0d" />


                        <TextView

                            android:layout_marginTop="20dp"
                            android:background="@drawable/code_rounded"
                            android:padding="5dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="10dp"
                            android:text="        " />

                        <View
                            android:layout_height="50dp"
                            android:layout_width="2dp"
                            android:background="@color/primary" />


                        <ImageView
                            android:padding="2dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@drawable/outbound"
                            android:rotation="90" />

                        <View

                            android:layout_height="50dp"
                            android:layout_width="2dp"
                            android:background="@color/primary" />

                        <TextView
                            android:background="@drawable/code_rounded"
                            android:padding="5dp"
                            android:layout_marginTop="10dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="       " />


                    </LinearLayout>
                </LinearLayout>
            </com.facebook.shimmer.ShimmerFrameLayout>

            <LinearLayout
                android:visibility="gone"
                android:id="@+id/emptyState"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center_vertical|center_horizontal"
                android:orientation="vertical">

                <com.hqt.util.AspectRatioImageView
                    android:layout_margin="20dp"
                    android:layout_width="match_parent"
                    android:layout_height="200dp"
                    android:scaleType="centerInside"
                    android:src="@drawable/plane_seat_empty"
                    android:contentDescription="@string/no_seat" />

                <TextView
                    android:layout_width="match_parent"
                    android:gravity="center"
                    android:layout_height="wrap_content"
                    android:text="Chưa hỗ trợ chọn ghế với chuyến xe này!" />

                <TextView
                    android:layout_width="match_parent"
                    android:gravity="center"
                    android:layout_height="wrap_content"
                    android:text="Vui lòng liên hệ tổng đài để hỗ trợ" />

                <Button

                    android:layout_marginTop="10dp"
                    android:paddingStart="40dp"
                    android:paddingEnd="40dp"
                    android:id="@+id/btnBack"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/btn_back"
                    app:backgroundTint="@color/btnColor"
                    android:textColor="#FFFFFF" />


            </LinearLayout>

            <LinearLayout
                android:id="@+id/content"
                android:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:weightSum="10"
                android:orientation="horizontal">

                <androidx.core.widget.NestedScrollView

                    android:layout_width="0dp"
                    android:layout_weight="7.5"
                    android:id="@+id/data_scrollView"
                    app:layout_behavior="@string/hide_bottom_view_on_scroll_behavior"
                    android:layout_height="match_parent">


                    <LinearLayout
                        android:padding="5dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <com.mikepenz.iconics.view.IconicsImageView
                                android:layout_gravity="center_horizontal|center_vertical"
                                android:padding="5dp"
                                android:layout_width="20dp"
                                android:layout_height="20dp"
                                app:iiv_color="@color/primary_dark"
                                app:iiv_icon="faw-clock" />

                            <TextView
                                android:layout_gravity="center_horizontal|center_vertical"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Điểm đón: " />

                            <TextView
                                android:layout_gravity="center_horizontal|center_vertical"
                                android:padding="5dp"
                                android:id="@+id/originCode"
                                style="@style/Text"
                                android:textStyle="bold"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content" />


                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <com.mikepenz.iconics.view.IconicsImageView
                                android:layout_gravity="center_horizontal|center_vertical"
                                android:padding="5dp"
                                android:layout_width="20dp"
                                android:layout_height="20dp"
                                app:iiv_color="@color/primary_dark"
                                app:iiv_icon="faw-map-marker-alt" />

                            <TextView
                                android:layout_gravity="center_horizontal|center_vertical"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Điểm trả: " />

                            <TextView
                                android:layout_gravity="center_horizontal|center_vertical"
                                android:padding="5dp"
                                android:id="@+id/destinationCode"
                                style="@style/Text"
                                android:textStyle="bold"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content" />


                        </LinearLayout>


                        <com.google.android.flexbox.FlexboxLayout

                            android:id="@+id/flex_box_seat_title"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:alignContent="center"
                            app:alignItems="center"
                            app:justifyContent="flex_start"
                            app:showDivider="beginning|middle"
                            app:flexWrap="wrap">


                        </com.google.android.flexbox.FlexboxLayout>

                        <com.google.android.flexbox.FlexboxLayout
                            android:layout_marginBottom="100dp"
                            android:layout_marginStart="20dp"
                            android:layout_marginEnd="20dp"
                            android:id="@+id/flex_box_container"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:alignContent="center"
                            app:alignItems="center"
                            app:showDivider="beginning|middle"
                            app:flexWrap="wrap">


                        </com.google.android.flexbox.FlexboxLayout>


                    </LinearLayout>


                </androidx.core.widget.NestedScrollView>

                <LinearLayout
                    android:background="@color/white"
                    android:paddingTop="10dp"
                    android:layout_width="0dp"
                    android:layout_weight="2.5"
                    android:layout_height="match_parent"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">


                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/ic_bus_line" />

                    <TextView
                        android:layout_marginTop="10dp"
                        android:textStyle="bold"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:singleLine="true"
                        android:textColor="#003A6F"
                        android:text="Tối đa" />

                    <TextView
                        android:layout_marginTop="10dp"
                        android:id="@+id/maxSeatSelect"
                        android:textStyle="bold"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:singleLine="true"
                        android:textColor="#003A6F"
                        tools:text="A321" />

                    <ImageView

                        android:layout_marginTop="10dp"
                        android:padding="20dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_seat" />

                    <TextView
                        android:layout_marginTop="10dp"
                        android:id="@+id/currentCoachText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textStyle="bold"
                        android:textColor="#003A6F"
                        tools:text="10A" />

                    <TextView
                        android:layout_marginTop="10dp"
                        android:id="@+id/currentSeatSelecte"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textStyle="bold"
                        android:textColor="#003A6F"
                        tools:text="10A" />

                    <TextView
                        android:layout_marginTop="10dp"
                        android:id="@+id/currentSeatSelectText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textStyle="bold"
                        android:textColor="#003A6F"
                        tools:text="10A" />

                    <TextView
                        android:layout_marginTop="10dp"
                        android:id="@+id/seatPrice"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:singleLine="true"
                        android:textColor="#da281c"
                        tools:text="90000k" />


                </LinearLayout>
            </LinearLayout>


        </LinearLayout>


        <FrameLayout
            android:id="@+id/bottomLayout"
            android:layout_width="match_parent"
            app:layout_behavior="@string/hide_bottom_view_on_scroll_behavior"
            android:layout_height="match_parent">

            <LinearLayout
                android:id="@+id/bottom_sheet"
                android:orientation="vertical"
                android:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal|bottom"
                android:background="#FFFFFF">

                <include layout="@layout/select_seat_bottom_layout" />

            </LinearLayout>
        </FrameLayout>


    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>