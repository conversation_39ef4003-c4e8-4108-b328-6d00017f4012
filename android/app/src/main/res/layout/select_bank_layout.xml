<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:bind="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="paymentMethod"
            type="com.hqt.data.model.PaymentType" />

        <variable
            name="order"
            type="com.hqt.data.model.OrderInfo" />

        <variable
            name="payment"
            type="com.hqt.data.model.Payment" />

        <import type="com.hqt.datvemaybay.Common" />

        <import type="android.view.View" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:clickable="true"
        android:focusable="true"
        app:elevation="10dp"
        app:behavior_hideable="true"
        app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">


        <LinearLayout
            android:background="@color/gbgray"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">


            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:paddingTop="5dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:id="@+id/quickView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:paddingRight="5dp"
                                android:paddingLeft="5dp"
                                android:paddingTop="2dp"
                                android:paddingBottom="2dp"
                                android:gravity="center_vertical|center_horizontal"
                                android:layout_height="wrap_content">

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:orientation="vertical">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        tools:text="vui long chon hinh thưc thanh toan"
                                        android:text="@{paymentMethod.title ?? `Vui lòng chọn hình thức thanh toán` }" />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textStyle="bold"
                                        android:textColor="@color/black"
                                        android:text="@{payment.title ?? ``}" />
                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="fill_parent"
                                    android:layout_height="wrap_content"
                                    android:layoutDirection="rtl">

                                    <ImageView
                                        bind:visibility="@{payment.logo != null}"
                                        android:layout_marginStart="20dp"
                                        android:padding="5dp"
                                        android:layout_width="80dp"
                                        android:layout_height="50dp"
                                        bind:imageUrl="@{payment.logo ?? ``}"
                                        android:contentDescription="Logo thanh toán" />
                                </LinearLayout>


                            </LinearLayout>


                        </LinearLayout>


                    </LinearLayout>


                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:weightSum="10"
                        android:paddingBottom="10dp"
                        android:gravity="right"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_weight="7"
                            android:paddingRight="5dp"
                            android:paddingLeft="5dp"
                            android:paddingTop="2dp"
                            android:paddingBottom="2dp"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <View
                                android:layout_gravity="center_vertical|center_horizontal"
                                android:layout_width="match_parent"
                                android:layout_height="2dp"
                                android:background="@drawable/gradientdiv_vertical" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/black"
                                android:text="Tổng số tiền " />

                            <TextView
                                android:id="@+id/grandTotal"
                                android:gravity="start"
                                android:singleLine="true"
                                android:textColor="#da281c"
                                android:textStyle="bold"
                                android:textSize="20sp"
                                tools:text="900,000đ"
                                android:text="@{ Common.dinhDangTien(order.orderCashAmount + paymentMethod.fee)}"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content" />


                        </LinearLayout>

                        <Button
                            android:id="@+id/btnPayment"
                            android:layout_width="0dp"
                            android:layout_weight="3"
                            android:layout_height="wrap_content"
                            android:layout_gravity="right"
                            android:text="TIẾP TỤC"
                            app:backgroundTint="@color/primary"
                            android:textColor="#FFFFFF" />


                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>
    </LinearLayout>
</layout>