<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="10dp"
    android:orientation="vertical">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:weightSum="9"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_weight="3"
            android:orientation="vertical"
            android:gravity="center_horizontal"
            android:layout_height="wrap_content">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAlignment="center"
                android:textColor="#000000"
                android:text="Người lớn: " />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAlignment="center"
                android:text="@string/lon12tuoi" />


            <Spinner
                android:id="@+id/nguoiLon"
                android:textColor="#000000"
                android:paddingLeft="20dp"
                android:paddingRight="20dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_weight="3"
            android:orientation="vertical"
            android:paddingLeft="2sp"
            android:paddingRight="2sp"
            android:gravity="center_horizontal"
            android:layout_height="wrap_content">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#000000"
                android:text="Trẻ em: " />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAlignment="center"
                android:text="@string/treEmtuoi" />

            <Spinner

                android:id="@+id/treEm"
                android:textColor="#000000"
                android:paddingLeft="20dp"
                android:paddingRight="20dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />


        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_weight="3"
            android:orientation="vertical"
            android:gravity="center_horizontal"
            android:layout_height="wrap_content">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#000000"
                android:text="Em bé: " />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAlignment="center"
                android:text="@string/emBeTuoi" />

            <Spinner
                android:id="@+id/emBe"
                android:textColor="#000000"
                android:paddingLeft="20dp"
                android:paddingRight="20dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

        </LinearLayout>


    </LinearLayout>


</LinearLayout>