<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/notification_large_title"
        style="@style/TextAppearance.Compat.Notification.Title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_alignParentStart="false"
        android:layout_marginStart="8dp"
        android:layout_marginTop="0dp"
        android:layout_marginEnd="0dp"
        android:text="" />

    <TextView
        android:id="@+id/notification_large_message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/notification_large_title"
        android:layout_alignParentStart="false"
        android:layout_marginStart="8dp"
        android:layout_marginTop="0dp"
        android:layout_marginEnd="0dp"
        android:text="" />

    <ImageView
        android:layout_marginTop="10dp"
        android:visibility="gone"
        android:layout_below="@+id/notification_large_message"
        android:id="@+id/notification_img"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:adjustViewBounds="true"
        android:scaleType="centerCrop" />

</RelativeLayout>