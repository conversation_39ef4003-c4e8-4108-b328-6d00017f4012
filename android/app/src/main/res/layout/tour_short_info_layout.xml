<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:bind="http://schemas.android.com/tools">

    <data>

        <variable
            name="tour_info"
            type="com.hqt.view.ui.tour.TourInfo" />

        <import type="android.view.View" />

        <import type="android.text.Html" />
    </data>


    <LinearLayout
        android:padding="5dp"
        android:background="@drawable/round_stop_info"
        android:layout_margin="5dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:padding="5dp"
            android:weightSum="5"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_weight="5"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <com.mikepenz.iconics.view.IconicsImageView
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_gravity="center_vertical"
                    app:iiv_color="#003A6F"
                    app:iiv_icon="faw_clock"
                    android:layout_marginEnd="10dp"
                    app:iiv_size="20dp" />

                <TextView
                    android:textSize="14sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Thời gian:  "
                    android:lines="1"
                    android:textStyle="bold" />

                <TextView
                    android:lines="1"
                    android:text="@{tour_info.duration}"
                    android:textColor="#003A6F"
                    android:textSize="14sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:text="Ho Chi Minh (SGN)"
                    android:textStyle="bold" />

            </LinearLayout>


        </LinearLayout>

        <LinearLayout
            android:padding="5dp"
            android:weightSum="5"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_weight="5"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <com.mikepenz.iconics.view.IconicsImageView
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_gravity="center_vertical"
                    app:iiv_color="#003A6F"
                    app:iiv_icon="faw-calendar"
                    android:layout_marginEnd="10dp"
                    app:iiv_size="20dp" />

                <TextView
                    android:textSize="14sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Ngày khởi hành:  "
                    android:lines="1"
                    android:textStyle="bold" />

                <TextView
                    android:lines="1"
                    android:text="@{tour_info.departureDate}"
                    android:textColor="#003A6F"
                    android:textSize="14sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:text="Ho Chi Minh (SGN)"
                    android:textStyle="bold" />

            </LinearLayout>


        </LinearLayout>

        <LinearLayout
            android:padding="5dp"
            android:weightSum="5"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_weight="5"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <com.mikepenz.iconics.view.IconicsImageView
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_gravity="center_vertical"
                    app:iiv_color="#003A6F"
                    app:iiv_icon="faw_bus"
                    android:layout_marginEnd="10dp"
                    app:iiv_size="20dp" />

                <TextView
                    android:textSize="14sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Phương tiện:  "
                    android:lines="1"
                    android:textStyle="bold" />

                <TextView
                    android:lines="1"
                    android:text="@{tour_info.flightTour}"
                    android:textColor="#003A6F"
                    android:textSize="14sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:text="Máy bay"
                    android:textStyle="bold" />

            </LinearLayout>


        </LinearLayout>

        <LinearLayout
            android:padding="5dp"
            android:weightSum="5"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_weight="5"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <com.mikepenz.iconics.view.IconicsImageView
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_gravity="center_vertical"
                    app:iiv_color="#003A6F"
                    app:iiv_icon="faw_building"
                    android:layout_marginEnd="10dp"
                    app:iiv_size="20dp" />

                <TextView
                    android:textSize="14sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Khách sạn:  "
                    android:lines="1"
                    android:textStyle="bold" />

                <TextView
                    android:lines="1"
                    android:text="@{tour_info.hotelStar}"
                    android:textColor="#003A6F"
                    android:textSize="14sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:text="2-3 sao"
                    android:textStyle="bold" />

            </LinearLayout>


        </LinearLayout>

        <LinearLayout
            android:padding="5dp"
            android:weightSum="5"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_weight="5"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <com.mikepenz.iconics.view.IconicsImageView
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_gravity="center_vertical"
                    app:iiv_color="#003A6F"
                    app:iiv_icon="faw_map-marker"
                    android:layout_marginEnd="10dp"
                    app:iiv_size="20dp" />

                <TextView
                    android:textSize="14sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Điểm khởi hành:  "
                    android:lines="1"
                    android:textStyle="bold" />

                <TextView
                    android:lines="1"
                    android:text="@{tour_info.departureLocation}"
                    android:textColor="#003A6F"
                    android:textSize="14sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:text="Ho Chi Minh (SGN)"
                    android:textStyle="bold" />

            </LinearLayout>


        </LinearLayout>

        <LinearLayout
            android:padding="5dp"
            android:weightSum="5"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_weight="5"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <com.mikepenz.iconics.view.IconicsImageView
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_gravity="center_vertical"
                    app:iiv_color="#003A6F"
                    app:iiv_icon="faw_braille"
                    android:layout_marginEnd="10dp"
                    app:iiv_size="20dp" />

                <TextView
                    android:textSize="14sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Số chỗ trống:  "
                    android:lines="1"
                    android:textStyle="bold" />

                <TextView
                    android:lines="1"
                    android:text="@{tour_info.numSeats.toString()}"
                    android:textColor="#003A6F"
                    android:textSize="14sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:text="25"
                    android:textStyle="bold" />

            </LinearLayout>


        </LinearLayout>


    </LinearLayout>


</layout>