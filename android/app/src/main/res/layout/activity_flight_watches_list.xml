<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:bind="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewModel"
            type="com.hqt.viewmodel.WidgetFilterButtonViewModel" />

    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:id="@+id/coordinatorLayout"
        android:fitsSystemWindows="true"
        app:statusBarBackground="@color/primary_dark"
        android:background="@color/primary_dark"
        tools:context="com.hqt.view.ui.train.TrainSelectActivity">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:background="@color/primary_dark"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true"
            app:elevation="0dp"
            android:theme="@style/AppTheme.AppBarOverlay">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/toolbar_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:contentScrim="@color/primary_dark"
                app:title="12bay.vn"
                app:titleEnabled="false"
                app:layout_scrollFlags="enterAlwaysCollapsed">

                <androidx.appcompat.widget.Toolbar
                    android:id="@+id/toolbar"
                    android:background="@color/primary"
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize"
                    app:elevation="0dp"
                    app:layout_collapseMode="pin"
                    app:layout_scrollFlags="scroll|enterAlways"
                    app:popupTheme="@style/AppTheme.PopupOverlay" />


            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>


        <LinearLayout
            android:background="@color/gbgray"
            android:layout_marginTop="?attr/actionBarSize"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <androidx.recyclerview.widget.RecyclerView
                android:layout_marginBottom="20dp"
                android:paddingTop="5dp"
                android:paddingBottom="50dp"
                android:clipToPadding="false"
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clickable="true"
                android:focusable="true"
                app:layout_behavior="@string/appbar_scrolling_view_behavior" />

        </LinearLayout>

        <com.facebook.shimmer.ShimmerFrameLayout

            android:id="@+id/shimmer_view_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_marginTop="?attr/actionBarSize"
            android:orientation="vertical"
            android:visibility="visible"
            app:shimmer_duration="800">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <include layout="@layout/placeholder_item_train" />

                <include layout="@layout/placeholder_item_train" />

                <include layout="@layout/placeholder_item_train" />

                <include layout="@layout/placeholder_item_train" />

                <include layout="@layout/placeholder_item_train" />

                <include layout="@layout/placeholder_item_train" />

                <include layout="@layout/placeholder_item_train" />

                <include layout="@layout/placeholder_item_train" />

                <include layout="@layout/placeholder_item_train" />

                <include layout="@layout/placeholder_item_train" />

                <include layout="@layout/placeholder_item_train" />

            </LinearLayout>
        </com.facebook.shimmer.ShimmerFrameLayout>

        <LinearLayout
            android:layout_marginTop="?attr/actionBarSize"
            android:id="@+id/notfound"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <ImageView
                android:layout_marginTop="200dp"
                android:layout_gravity="center_horizontal"
                android:contentDescription="Chưa có thông báo giá vé"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:adjustViewBounds="true"
                android:background="@drawable/ic_price_alert"
                android:scaleType="centerCrop"
                app:layout_behavior="@string/appbar_scrolling_view_behavior" />

            <TextView
                android:textSize="16sp"
                android:padding="5dp"
                android:textStyle="bold"
                android:layout_width="match_parent"
                android:gravity="center"
                android:layout_height="wrap_content"
                android:text="Chưa có thông báo giá" />

            <TextView
                android:textSize="12sp"
                android:layout_width="match_parent"
                android:gravity="center"
                android:layout_height="wrap_content"
                android:text="Tạo thông báo ngay để được cập nhật giá vé khi phù hợp" />

            <LinearLayout
                android:layout_margin="20dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="right">

                <RadioGroup
                    android:id="@+id/radio"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="5dp"
                    android:layout_weight="1"
                    android:background="@drawable/corner_full"
                    android:orientation="horizontal">

                    <Button
                        android:id="@+id/btnAddFlightWatches"
                        style="@style/MyApp.Button.Big"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="0.5"
                        android:background="@drawable/button_one_way"
                        android:checked="true"
                        android:text="Tạo thông báo"
                        android:textAllCaps="true"
                        android:textColor="#FFFFFF" />

                    <Button
                        android:id="@+id/btnLogin"
                        style="@style/MyApp.Button.Big"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="0.5"
                        android:background="@color/fui_transparent"
                        android:text="@string/txt_login"
                        android:textAllCaps="true"
                        android:textColor="#00a2e3" />
                </RadioGroup>

            </LinearLayout>
        </LinearLayout>


        <androidx.cardview.widget.CardView
            android:id="@+id/bottomView"
            android:visibility="gone"
            android:layout_marginTop="5dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#ffffff"
            app:cardElevation="4dp"
            android:clickable="true"
            app:cardPreventCornerOverlap="true"
            android:layout_gravity="center_horizontal|bottom"
            android:focusable="true">

            <LinearLayout
                android:id="@+id/bottom_sheet"
                android:orientation="horizontal"
                android:visibility="visible"
                android:layout_width="match_parent"
                android:gravity="center_horizontal|center_vertical"
                android:layout_height="30dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Giá vé có thể thay đổi" />

                <com.mikepenz.iconics.view.IconicsImageView
                    android:layout_marginStart="5dp"
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_gravity="center_vertical"
                    app:iiv_size="20dp"
                    app:iiv_color="@color/stt_gray"
                    app:iiv_icon="faw-info_circle" />

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/btn_new_flight_watches"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|end"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="50dp"
            android:src="@drawable/fab_add"
            android:clickable="true"
            app:backgroundTint="#FB953B"
            app:layout_anchorGravity="bottom|end" />
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>