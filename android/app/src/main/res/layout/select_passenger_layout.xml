<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <LinearLayout
        android:id="@+id/select_passenger_sheet"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#EDEDED"
        android:orientation="vertical"
        app:behavior_hideable="true"
        app:layout_behavior="android.support.design.widget.BottomSheetBehavior">

        <LinearLayout
            android:paddingTop="10dp"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="<PERSON><PERSON><PERSON> h<PERSON>nh kh<PERSON>ch" />

            <LinearLayout
                android:layout_marginTop="10dp"
                android:layout_width="match_parent"
                android:background="@drawable/corner_full"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="fill_parent"
                        android:background="@drawable/border_left"
                        android:padding="10dp">

                        <com.mikepenz.iconics.view.IconicsImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            app:iiv_color="@color/white"
                            app:iiv_icon="faw_male" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_marginLeft="5dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@color/white"
                        android:padding="5dp"
                        android:orientation="vertical">

                        <TextView
                            android:textColor="@color/textDark"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/txt_adult" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="12sp"
                            android:text="@string/txt_adult_rule" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="fill_parent"
                        android:layout_marginRight="10dp"
                        android:gravity="center_vertical|right">

                        <View
                            android:layout_width="1dp"
                            android:layout_height="fill_parent"
                            android:layout_marginRight="20dp"
                            android:layout_marginLeft="10dp"
                            android:background="@drawable/gradientdiv" />

                        <LinearLayout
                            android:id="@+id/btn_de_adult"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/border_full"
                            android:padding="5dp">

                            <com.mikepenz.iconics.view.IconicsImageView

                                android:layout_width="18dp"
                                android:layout_height="18dp"
                                app:iiv_color="@color/white"
                                app:iiv_icon="gmd_remove" />
                        </LinearLayout>

                        <TextView
                            android:textColor="@color/textDark"
                            android:id="@+id/sheet_adult_number"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="1"
                            android:textSize="20sp"
                            android:paddingRight="20dp"
                            android:paddingLeft="20dp" />

                        <LinearLayout
                            android:id="@+id/btn_in_adult"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/border_full"
                            android:padding="5dp">

                            <com.mikepenz.iconics.view.IconicsImageView

                                android:layout_width="18dp"
                                android:layout_height="18dp"
                                app:iiv_color="@color/white"
                                app:iiv_icon="gmd_add" />
                        </LinearLayout>

                    </LinearLayout>


                </LinearLayout>


            </LinearLayout>

            <LinearLayout
                android:layout_marginTop="10dp"
                android:layout_width="match_parent"
                android:background="@drawable/corner_full"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="fill_parent"
                        android:background="@drawable/border_left"
                        android:padding="10dp">

                        <com.mikepenz.iconics.view.IconicsImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            app:iiv_color="@color/white"
                            app:iiv_icon="faw_child" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_marginLeft="5dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@color/white"
                        android:padding="5dp"
                        android:orientation="vertical">

                        <TextView
                            android:textColor="@color/textDark"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/txt_child" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="12sp"
                            android:text="@string/txt_child_rule" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="fill_parent"
                        android:layout_marginRight="10dp"
                        android:gravity="center_vertical|right">

                        <View
                            android:layout_width="1dp"
                            android:layout_height="fill_parent"
                            android:layout_marginRight="20dp"
                            android:layout_marginLeft="10dp"
                            android:background="@drawable/gradientdiv" />

                        <LinearLayout
                            android:id="@+id/btn_de_child"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/border_full"
                            android:padding="5dp">

                            <com.mikepenz.iconics.view.IconicsImageView
                                android:layout_width="18dp"
                                android:layout_height="18dp"
                                app:iiv_color="@color/white"
                                app:iiv_icon="gmd_remove" />
                        </LinearLayout>

                        <TextView
                            android:textColor="@color/textDark"
                            android:id="@+id/sheet_child_number"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="20sp"
                            android:paddingRight="20dp"
                            android:paddingLeft="20dp" />

                        <LinearLayout
                            android:id="@+id/btn_in_child"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/border_full"
                            android:padding="5dp">

                            <com.mikepenz.iconics.view.IconicsImageView
                                android:layout_width="18dp"
                                android:layout_height="18dp"
                                app:iiv_color="@color/white"
                                app:iiv_icon="gmd_add" />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/select_infant"
                android:layout_marginTop="10dp"
                android:layout_width="match_parent"
                android:background="@drawable/corner_full"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="fill_parent"
                        android:background="@drawable/border_left"
                        android:padding="10dp">

                        <com.mikepenz.iconics.view.IconicsImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            app:iiv_color="@color/white"
                            app:iiv_icon="gmd_child_care" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_marginLeft="5dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@color/white"
                        android:padding="5dp"
                        android:orientation="vertical">

                        <TextView
                            android:textColor="@color/textDark"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/txt_infant" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="12sp"
                            android:text="@string/txt_infant_rule" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="fill_parent"
                        android:layout_marginRight="10dp"
                        android:gravity="center_vertical|right">

                        <View
                            android:layout_width="1dp"
                            android:layout_height="fill_parent"
                            android:layout_marginRight="20dp"
                            android:layout_marginLeft="10dp"
                            android:background="@drawable/gradientdiv" />

                        <LinearLayout
                            android:id="@+id/btn_de_infant"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/border_full"
                            android:padding="5dp">

                            <com.mikepenz.iconics.view.IconicsImageView

                                android:layout_width="18dp"
                                android:layout_height="18dp"
                                app:iiv_color="@color/white"
                                app:iiv_icon="gmd_remove" />
                        </LinearLayout>

                        <TextView
                            android:textColor="@color/textDark"
                            android:id="@+id/sheet_infant_number"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="20sp"
                            android:paddingRight="20dp"
                            android:paddingLeft="20dp" />

                        <LinearLayout
                            android:id="@+id/btn_in_infant"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/border_full"
                            android:padding="5dp">

                            <com.mikepenz.iconics.view.IconicsImageView

                                android:layout_width="18dp"
                                android:layout_height="18dp"
                                app:iiv_color="@color/white"
                                app:iiv_icon="gmd_add" />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:visibility="gone"
                android:id="@+id/select_student"
                android:layout_marginTop="10dp"
                android:layout_width="match_parent"
                android:background="@drawable/corner_full"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="fill_parent"
                        android:background="@drawable/border_left"
                        android:padding="10dp">

                        <com.mikepenz.iconics.view.IconicsImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            app:iiv_color="@color/white"
                            app:iiv_icon="faw-user_graduate" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_marginLeft="5dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@color/white"
                        android:padding="5dp"
                        android:orientation="vertical">

                        <TextView
                            android:textColor="@color/textDark"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/txt_student" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="12sp"
                            android:text="@string/txt_student_rule" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="fill_parent"
                        android:layout_marginRight="10dp"
                        android:gravity="center_vertical|right">

                        <View
                            android:layout_width="1dp"
                            android:layout_height="fill_parent"
                            android:layout_marginRight="20dp"
                            android:layout_marginLeft="10dp"
                            android:background="@drawable/gradientdiv" />

                        <LinearLayout
                            android:id="@+id/btn_de_student"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/border_full"
                            android:padding="5dp">

                            <com.mikepenz.iconics.view.IconicsImageView

                                android:layout_width="18dp"
                                android:layout_height="18dp"
                                app:iiv_color="@color/white"
                                app:iiv_icon="gmd_remove" />
                        </LinearLayout>

                        <TextView
                            android:textColor="@color/textDark"
                            android:id="@+id/sheet_student_number"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="20sp"
                            android:paddingRight="20dp"
                            android:paddingLeft="20dp" />

                        <LinearLayout
                            android:id="@+id/btn_in_student"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/border_full"
                            android:padding="5dp">

                            <com.mikepenz.iconics.view.IconicsImageView

                                android:layout_width="18dp"
                                android:layout_height="18dp"
                                app:iiv_color="@color/white"
                                app:iiv_icon="gmd_add" />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:visibility="gone"
                android:id="@+id/select_older"
                android:layout_marginTop="10dp"
                android:layout_width="match_parent"
                android:background="@drawable/corner_full"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="fill_parent"
                        android:background="@drawable/border_left"
                        android:padding="10dp">

                        <com.mikepenz.iconics.view.IconicsImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            app:iiv_color="@color/white"
                            app:iiv_icon="faw-blind" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_marginLeft="5dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@color/white"
                        android:padding="5dp"
                        android:orientation="vertical">

                        <TextView
                            android:textColor="@color/textDark"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/txt_older" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="12sp"
                            android:text="@string/txt_older_rule" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="fill_parent"
                        android:layout_marginRight="10dp"
                        android:gravity="center_vertical|right">

                        <View
                            android:layout_width="1dp"
                            android:layout_height="fill_parent"
                            android:layout_marginRight="20dp"
                            android:layout_marginLeft="10dp"
                            android:background="@drawable/gradientdiv" />

                        <LinearLayout
                            android:id="@+id/btn_de_older"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/border_full"
                            android:padding="5dp">

                            <com.mikepenz.iconics.view.IconicsImageView

                                android:layout_width="18dp"
                                android:layout_height="18dp"
                                app:iiv_color="@color/white"
                                app:iiv_icon="gmd_remove" />
                        </LinearLayout>

                        <TextView
                            android:textColor="@color/textDark"
                            android:id="@+id/sheet_older_number"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="20sp"
                            android:paddingRight="20dp"
                            android:paddingLeft="20dp" />

                        <LinearLayout
                            android:id="@+id/btn_in_older"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/border_full"
                            android:padding="5dp">

                            <com.mikepenz.iconics.view.IconicsImageView

                                android:layout_width="18dp"
                                android:layout_height="18dp"
                                app:iiv_color="@color/white"
                                app:iiv_icon="gmd_add" />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_gravity="center_vertical|center_horizontal"
                android:layout_height="wrap_content">

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/select_passenger_button"
                    android:padding="5dp"
                    android:layout_width="match_parent"
                    android:elevation="0dp"
                    android:layout_height="wrap_content"
                    style="@style/MyApp.Button.Blue"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:text="Chọn"
                    android:textColor="#fff" />
            </LinearLayout>

        </LinearLayout>


    </LinearLayout>
</layout>