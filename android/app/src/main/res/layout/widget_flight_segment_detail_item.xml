<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="segment"
            type="com.hqt.view.ui.flightSearch.model.Segment" />

        <import type="android.view.View" />
    </data>


    <LinearLayout
        android:paddingTop="5dp"
        android:paddingBottom="5dp"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:visibility="@{segment.skipStart ? View.GONE : View.VISIBLE}"
            android:weightSum="10"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:gravity="center_horizontal"
                android:layout_weight="2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:textColor="#003A6F"
                    android:textSize="16sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:text="10:20"
                    android:text="@{segment.startTimeShort}"
                    android:textStyle="bold" />

                <TextView
                    android:textSize="12sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@{segment.startDateShort}"
                    tools:text="S" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="match_parent"
                android:gravity="center_horizontal|center_vertical">

                <com.mikepenz.iconics.view.IconicsImageView
                    android:rotation="90"
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_gravity="center_vertical"
                    app:iiv_size="20dp"
                    app:iiv_color="#003A6F"
                    app:iiv_icon="faw_plane" />


            </LinearLayout>

            <LinearLayout
                android:layout_weight="7"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:text="@{segment.startPointInfo.name}"
                    android:textColor="#003A6F"
                    android:textSize="16sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:text="Ho Chi Minh (SGN)"
                    android:textStyle="bold" />

                <TextView
                    android:text="@{segment.airlineInfo +` | `+ segment.flightNumber }"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:text="Carthay Pacific | VN110" />
            </LinearLayout>


        </LinearLayout>

        <LinearLayout
            android:weightSum="10"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_weight="2"
                android:layout_width="0dp"
                android:gravity="center_vertical|center_horizontal"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    bind:imageUrl="@{segment.airlinesLogo}" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="match_parent"
                android:gravity="center_horizontal|center_vertical">

                <View
                    android:layout_height="match_parent"
                    android:layout_width="2dp"
                    android:background="@color/red" />

            </LinearLayout>

            <LinearLayout
                android:background="@drawable/round_airport_code"
                android:padding="10dp"
                android:layout_weight="7"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:orientation="vertical">


                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Khai thác bởi: " />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="@{segment.operatingAirline }" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Hành lý: " />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="@{segment.allowanceBaggage }" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="@{` `+segment.classInfo }" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Máy bay: " />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="@{segment.planeInfo.fullName() }" />
                </LinearLayout>
            </LinearLayout>


        </LinearLayout>

        <LinearLayout
            android:visibility="@{segment.hasStop ? View.VISIBLE : View.GONE}"
            android:weightSum="10"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:gravity="center_horizontal"
                android:layout_weight="2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:text="@{segment.endTimeShort}"
                    android:textColor="#003A6F"
                    android:textSize="16sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:text="10:20" />

                <TextView
                    android:textSize="12sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@{segment.endDateShort}"
                    tools:text="10h20m" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="match_parent"
                android:gravity="center_horizontal|center_vertical">

                <com.mikepenz.iconics.view.IconicsImageView
                    android:visibility="@{segment.hasStop ? View.VISIBLE : View.GONE}"
                    android:rotation="90"
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_gravity="center_vertical"
                    app:iiv_size="20dp"
                    app:iiv_color="#003A6F"
                    app:iiv_icon="faw_dot-circle" />

                <com.mikepenz.iconics.view.IconicsImageView
                    android:visibility="@{segment.hasStop ? View.GONE : View.VISIBLE}"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_gravity="center_vertical"
                    app:iiv_size="20dp"
                    app:iiv_color="@color/red"
                    app:iiv_icon="faw_map-marker-alt" />

            </LinearLayout>

            <LinearLayout
                android:layout_gravity="center_vertical"
                android:layout_weight="7"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:textStyle="bold"
                    android:text="@{segment.endPointInfo.name}"
                    android:textColor="#003A6F"
                    android:textSize="16sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:text="Hong Kong (HKG)" />

            </LinearLayout>


        </LinearLayout>

        <LinearLayout
            android:visibility="@{segment.hasStop ? View.VISIBLE : View.GONE}"
            android:weightSum="10"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_weight="2"
                android:layout_width="0dp"
                android:gravity="center_vertical|center_horizontal"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <com.mikepenz.iconics.view.IconicsImageView
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:layout_gravity="center_vertical|center_horizontal"
                    app:iiv_size="15dp"
                    app:iiv_color="@color/blue"
                    app:iiv_icon="faw_random" />


            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="match_parent"
                android:gravity="center_horizontal|center_vertical">

                <View
                    android:layout_height="match_parent"
                    android:layout_width="2dp"
                    android:background="@drawable/dash_stop_vertical" />

            </LinearLayout>

            <LinearLayout
                android:background="@drawable/round_stop_info"
                android:padding="10dp"
                android:layout_weight="7"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@{segment.getStopString()}" />


                </LinearLayout>


            </LinearLayout>


        </LinearLayout>
    </LinearLayout>


</layout>