<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="orderInfo"
            type="com.hqt.data.model.PaymentOrderXml" />

        <import type="com.hqt.datvemaybay.Common" />

        <import type="android.view.View" />
    </data>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        tools:context="com.hqt.datvemaybay.PnrActivity"
        android:background="#F2F1F1"
        tools:showIn="@layout/activity_train_book">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="10dp"
                android:text="Mã thanh toán tại cửa hàng" />


            <androidx.cardview.widget.CardView
                android:layout_margin="5dp"
                app:cardElevation="0dp"
                app:cardCornerRadius="5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">


                    <LinearLayout
                        android:padding="10dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <LinearLayout

                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@color/white"
                                android:padding="5dp"
                                android:gravity="center_horizontal|center_vertical"
                                android:orientation="vertical">

                                <TextView
                                    android:textStyle="bold"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textSize="30sp"
                                    tools:text="8888888888"
                                    style="@style/Text"
                                    android:text="@{orderInfo.billingCode}" />

                                <TextView
                                    android:textStyle="bold"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textSize="12sp"
                                    android:text="Mã thanh toán" />

                            </LinearLayout>


                        </LinearLayout>

                        <LinearLayout
                            android:layout_marginLeft="5dp"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@color/white"
                            android:padding="5dp"
                            android:orientation="vertical">


                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <TextView
                                    android:textStyle="bold"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"

                                    android:text="Hạn thanh toán"
                                    tools:text="Hạn thanh toán" />

                                <TextView
                                    android:textStyle="bold"
                                    android:layout_width="fill_parent"
                                    android:textAlignment="textEnd"
                                    android:layout_height="wrap_content"
                                    android:text="@{orderInfo.expiredDate}" />
                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_marginLeft="5dp"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@color/white"
                            android:padding="5dp"
                            android:orientation="vertical">


                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <TextView
                                    android:textStyle="bold"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"

                                    android:text="Mã đơn hàng: " />

                                <TextView
                                    android:textStyle="bold"
                                    android:layout_width="fill_parent"
                                    android:textAlignment="textEnd"
                                    android:layout_height="wrap_content"
                                    android:text="@{orderInfo.bookingId}" />
                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_marginLeft="5dp"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@color/white"
                            android:padding="5dp"
                            android:orientation="vertical">


                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <TextView
                                    android:textStyle="bold"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Số tiền cần thanh toán" />

                                <TextView
                                    android:textStyle="bold"
                                    android:layout_width="fill_parent"
                                    android:textAlignment="textEnd"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/red"
                                    android:text="@{Common.dinhDangTien(orderInfo.grandTotal)}" />
                            </LinearLayout>
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="10dp"
                android:text="Lưu ý trước khi thanh toán" />

            <androidx.cardview.widget.CardView
                android:layout_margin="5dp"
                app:cardElevation="0dp"
                app:cardCornerRadius="5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:padding="10dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_marginBottom="10dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="- Thông báo với nhân viên là bạn thanh toán đơn hàng Payoo" />

                    <TextView
                        android:layout_marginBottom="10dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="- Sau đó cung cấp mã thanh toán trên cho nhân viên cửa hàng" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="10dp"
                android:text="Bạn đã hoàn tất thanh toán" />

            <androidx.cardview.widget.CardView
                android:layout_margin="5dp"
                app:cardElevation="0dp"
                app:cardCornerRadius="5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:padding="10dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_marginBottom="10dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Sau khi xác nhận số tiền thanh toán của bạn. Chúng tôi sẽ gửi biên nhận và vé điện tử qua email của bạn" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:id="@+id/widget_chat"
                        android:orientation="vertical" />

                    <androidx.appcompat.widget.AppCompatButton
                        android:layout_gravity="center_horizontal"
                        android:paddingStart="20dp"
                        android:paddingEnd="20dp"
                        android:id="@+id/btnDonePayment"
                        android:textColor="@color/white"
                        android:background="@drawable/button_gradient"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Tôi đã thanh toán xong" />
                </LinearLayout>

            </androidx.cardview.widget.CardView>


        </LinearLayout>

    </androidx.core.widget.NestedScrollView>
</layout>