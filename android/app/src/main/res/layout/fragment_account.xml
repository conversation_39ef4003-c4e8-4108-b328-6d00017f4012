<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:id="@+id/coordinatorLayout"
        android:fitsSystemWindows="true"
        android:background="@color/gbgray"
        tools:context="com.hqt.view.ui.HomeActivity">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true"
            app:elevation="0dp"
            android:theme="@style/AppTheme.AppBarOverlay">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/toolbar_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fitsSystemWindows="true"
                app:contentScrim="?attr/colorPrimary"
                app:title="12bay.vn"
                app:titleEnabled="false"
                app:layout_scrollFlags="scroll|exitUntilCollapsed">

                <ImageView
                    android:id="@+id/headerBG"
                    android:layout_width="match_parent"
                    android:layout_height="220dp"
                    android:scaleType="centerCrop"
                    android:contentDescription="@string/app_name"
                    android:fitsSystemWindows="true"
                    android:adjustViewBounds="true"
                    app:srcCompat="@drawable/world"
                    android:alpha="1" />

                <androidx.appcompat.widget.Toolbar
                    android:id="@+id/toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize"
                    app:elevation="0dp"
                    app:layout_collapseMode="pin"
                    app:layout_scrollFlags="scroll|exitUntilCollapsed"
                    app:popupTheme="@style/AppTheme.PopupOverlay">


                </androidx.appcompat.widget.Toolbar>


            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior"
            tools:context="com.hqt.view.ui.HomeActivity"
            app:behavior_overlapTop="140dp"
            tools:ignore="MissingPrefix"
            tools:showIn="@layout/activity_scrolling">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:visibility="visible"
                    android:id="@+id/layout_login"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:contentDescription="@string/app_name"
                    android:weightSum="3"
                    android:orientation="vertical"
                    android:scaleType="centerCrop">

                    <LinearLayout
                        android:gravity="center_vertical|center_horizontal"
                        android:layout_width="match_parent"
                        android:orientation="vertical"
                        android:paddingBottom="10dp"
                        android:layout_height="wrap_content">

                        <TextView
                            android:textSize="16sp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/white"
                            android:text="Đăng ký thành viên, hưởng nhiều ưu đãi" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_marginTop="10dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:paddingLeft="20dp"
                        android:paddingRight="20dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="right">

                            <RadioGroup
                                android:id="@+id/radio"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="5dp"
                                android:layout_weight="1"
                                android:background="@drawable/corner_full"
                                android:orientation="horizontal">

                                <Button
                                    android:id="@+id/btnLogin"
                                    style="@style/MyApp.Button.Big"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="0.5"
                                    android:background="@drawable/button_one_way"
                                    android:capitalize="characters"
                                    android:checked="true"
                                    android:text="Đăng nhập"
                                    android:textAllCaps="true"
                                    android:textColor="#FFFFFF" />

                                <Button
                                    android:id="@+id/btnRegister"
                                    style="@style/MyApp.Button.Big"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="0.5"
                                    android:background="@color/fui_transparent"
                                    android:text="Đăng ký"
                                    android:textAllCaps="true"
                                    android:textColor="#00a2e3" />
                            </RadioGroup>

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/showinfo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:contentDescription="@string/app_name"
                    android:weightSum="3"
                    android:orientation="vertical"
                    android:scaleType="centerCrop">

                    <LinearLayout
                        android:layout_marginTop="10dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:gravity="center_vertical|center_horizontal"
                        android:paddingLeft="20dp"
                        android:paddingRight="20dp">


                    </LinearLayout>

                    <androidx.cardview.widget.CardView
                        android:id="@+id/account_view"
                        android:layout_marginTop="10dp"
                        android:layout_marginLeft="20dp"
                        android:layout_marginRight="20dp"
                        style="@style/CardViewStyle.Light"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:cardCornerRadius="5dp"
                        app:cardElevation="2dp"
                        app:cardPreventCornerOverlap="false">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical">

                            <LinearLayout
                                android:padding="10dp"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_horizontal|center_vertical">

                                    <de.hdodenhof.circleimageview.CircleImageView
                                        android:id="@+id/profile_image"
                                        android:layout_width="60dp"
                                        android:layout_height="60dp"
                                        android:src="@drawable/logo_mini"
                                        app:civ_border_width="2dp"
                                        app:civ_border_color="#ffffff" />
                                </LinearLayout>

                                <LinearLayout
                                    android:layout_marginLeft="5dp"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:background="@color/white"
                                    android:padding="5dp"
                                    android:orientation="vertical">

                                    <TextView
                                        android:id="@+id/txt_customer_title"
                                        android:padding="5dp"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textStyle="bold"
                                        android:textColor="@color/black" />

                                    <LinearLayout
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:gravity="center_vertical|center_horizontal">

                                        <com.mikepenz.iconics.view.IconicsImageView
                                            android:layout_width="16dp"
                                            android:layout_height="16dp"
                                            android:paddingRight="5dp"
                                            app:iiv_color="@color/google_yellow"
                                            app:iiv_icon="gmd_stars" />

                                        <TextView
                                            android:id="@+id/text_point"
                                            android:padding="5dp"
                                            android:textStyle="bold"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:textSize="12sp"
                                            android:text="0 điểm" />
                                    </LinearLayout>

                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="fill_parent"
                                    android:layout_height="match_parent"
                                    android:gravity="center_vertical|end"
                                    android:padding="10dp">


                                </LinearLayout>

                            </LinearLayout>

                            <View
                                android:layout_width="match_parent"
                                android:layout_height="1dp"
                                android:layout_marginRight="10dp"
                                android:layout_marginLeft="10dp"
                                android:background="#dddddd" />


                        </LinearLayout>
                    </androidx.cardview.widget.CardView>
                </LinearLayout>

                <androidx.cardview.widget.CardView
                    android:layout_marginTop="10dp"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    style="@style/CardViewStyle.Light"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardCornerRadius="5dp"
                    app:cardElevation="2dp"
                    app:cardPreventCornerOverlap="false">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical">

                        <LinearLayout
                            android:id="@+id/txt_reward"
                            android:padding="10dp"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_gravity="center_horizontal"
                                android:padding="10dp">

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    app:iiv_size="20dp"
                                    app:iiv_color="@color/iconColor"
                                    app:iiv_icon="gmd_loyalty" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_marginLeft="5dp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@color/white"
                                android:padding="5dp"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textStyle="bold"
                                    android:textColor="@color/textDark"
                                    android:text="@string/txt_reward" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textSize="12sp"
                                    android:text="@string/txt_txt_reward_detail" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="fill_parent"
                                android:layout_height="match_parent"
                                android:gravity="center_vertical|end"
                                android:padding="10dp">

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:layout_width="16dp"
                                    android:layout_height="10dp"
                                    app:iiv_color="@color/primary"
                                    app:iiv_icon="gmd_keyboard_arrow_right" />
                            </LinearLayout>

                        </LinearLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginRight="10dp"
                            android:layout_marginLeft="10dp"
                            android:background="#dddddd" />

                        <LinearLayout
                            android:id="@+id/txt_checkin_online"
                            android:padding="10dp"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_gravity="center_horizontal"
                                android:padding="10dp">

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    app:iiv_size="20dp"
                                    app:iiv_color="@color/iconColor"
                                    app:iiv_icon="gmd_check_circle" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_marginLeft="5dp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@color/white"
                                android:padding="5dp"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textStyle="bold"
                                    android:textColor="@color/textDark"
                                    android:text="@string/txt_checkin_online" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textSize="12sp"
                                    android:text="@string/txt_checkin_online_detail" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="fill_parent"
                                android:layout_height="match_parent"
                                android:gravity="center_vertical|end"
                                android:padding="10dp">

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:layout_width="16dp"
                                    android:layout_height="16dp"
                                    app:iiv_color="@color/primary"
                                    app:iiv_icon="gmd_keyboard_arrow_right" />
                            </LinearLayout>

                        </LinearLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginRight="10dp"
                            android:layout_marginLeft="10dp"
                            android:background="#dddddd" />

                        <LinearLayout
                            android:id="@+id/txt_check_pnr"
                            android:padding="10dp"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_gravity="center_horizontal"
                                android:padding="10dp">

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    app:iiv_size="20dp"
                                    app:iiv_color="@color/iconColor"
                                    app:iiv_icon="gmd_done_all" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_marginLeft="5dp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@color/white"
                                android:padding="5dp"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textStyle="bold"
                                    android:textColor="@color/textDark"
                                    android:text="@string/txt_check_pnr" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textSize="12sp"
                                    android:text="@string/txt_check_pnr_detail" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="fill_parent"
                                android:layout_height="match_parent"
                                android:gravity="center_vertical|end"
                                android:padding="10dp">

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:layout_width="16dp"
                                    android:layout_height="16dp"
                                    app:iiv_color="@color/primary"
                                    app:iiv_icon="gmd_keyboard_arrow_right" />
                            </LinearLayout>

                        </LinearLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginRight="10dp"
                            android:layout_marginLeft="10dp"
                            android:background="#dddddd" />

                        <LinearLayout
                            android:id="@+id/txt_payment"
                            android:padding="10dp"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_gravity="center_horizontal"
                                android:padding="10dp">

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    app:iiv_size="20dp"
                                    app:iiv_color="@color/iconColor"
                                    app:iiv_icon="gmd_payment" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_marginLeft="5dp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@color/white"
                                android:padding="5dp"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textStyle="bold"
                                    android:textColor="@color/textDark"
                                    android:text="@string/menuThanhToan" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textSize="12sp"
                                    android:text="@string/txt_payment_detail" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="fill_parent"
                                android:layout_height="match_parent"
                                android:gravity="center_vertical|end"
                                android:padding="10dp">

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:layout_width="16dp"
                                    android:layout_height="16dp"
                                    app:iiv_color="@color/primary"
                                    app:iiv_icon="gmd_keyboard_arrow_right" />
                            </LinearLayout>

                        </LinearLayout>
                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    android:layout_marginTop="10dp"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    style="@style/CardViewStyle.Light"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardCornerRadius="5dp"
                    app:cardElevation="2dp"
                    app:cardPreventCornerOverlap="false">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical">

                        <LinearLayout
                            android:id="@+id/txt_rating_app_detail"
                            android:padding="10dp"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_gravity="center_horizontal"
                                android:padding="10dp">

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    app:iiv_size="20dp"
                                    app:iiv_icon="gmd_star"
                                    app:iiv_color="@color/iconColor" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_marginLeft="5dp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@color/white"
                                android:padding="5dp"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textStyle="bold"
                                    android:textColor="@color/textDark"
                                    android:text="@string/txt_rating_app" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textSize="12sp"
                                    android:text="@string/txt_rating_app_detail" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="fill_parent"
                                android:layout_height="match_parent"
                                android:gravity="center_vertical|end"
                                android:padding="10dp">

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:layout_width="16dp"
                                    android:layout_height="16dp"
                                    app:iiv_color="@color/primary"
                                    app:iiv_icon="gmd_keyboard_arrow_right" />
                            </LinearLayout>

                        </LinearLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginRight="10dp"
                            android:layout_marginLeft="10dp"
                            android:background="#dddddd" />

                        <LinearLayout
                            android:id="@+id/txt_share_app"
                            android:padding="10dp"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_gravity="center_horizontal"
                                android:padding="10dp">

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    app:iiv_size="20dp"
                                    app:iiv_icon="faw-share"
                                    app:iiv_color="@color/iconColor" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_marginLeft="5dp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@color/white"
                                android:padding="5dp"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textStyle="bold"
                                    android:textColor="@color/textDark"
                                    android:text="@string/txt_share_app" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textSize="12sp"
                                    android:text="@string/txt_share_app_detail" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="fill_parent"
                                android:layout_height="match_parent"
                                android:gravity="center_vertical|end"
                                android:padding="10dp">

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:layout_width="16dp"
                                    android:layout_height="16dp"
                                    app:iiv_color="@color/primary"
                                    app:iiv_icon="gmd_keyboard_arrow_right" />
                            </LinearLayout>

                        </LinearLayout>
                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    android:layout_marginTop="10dp"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    style="@style/CardViewStyle.Light"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardCornerRadius="5dp"
                    app:cardElevation="2dp"
                    app:cardPreventCornerOverlap="false">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">


                        <LinearLayout
                            android:id="@+id/txt_chat"
                            android:padding="10dp"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_gravity="center_horizontal"
                                android:padding="10dp">

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    app:iiv_size="20dp"
                                    app:iiv_icon="gmd_chat"
                                    app:iiv_color="@color/iconColor" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_marginStart="5dp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@color/white"
                                android:padding="5dp"
                                android:gravity="center_horizontal"
                                android:orientation="vertical">

                                <TextView
                                    android:paddingTop="5dp"
                                    android:paddingBottom="5dp"
                                    android:textColor="@color/textDark"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textStyle="bold"
                                    android:text="@string/txt_chat" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="fill_parent"
                                android:layout_height="match_parent"
                                android:gravity="center_vertical|end"
                                android:padding="5dp">

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:id="@+id/send_sms"
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    app:iiv_color="@color/primary"
                                    android:layout_marginEnd="15dp"
                                    app:iiv_icon="gmd_chat" />

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:id="@+id/send_facebook"
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    app:iiv_color="@color/primary"
                                    android:layout_marginEnd="15dp"
                                    app:iiv_icon="faw-facebook_messenger" />

                                <ImageView
                                    android:id="@+id/send_zalo"
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    android:src="@drawable/zalo_logo"
                                    android:layout_marginEnd="5dp" />
                            </LinearLayout>
                        </LinearLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginRight="10dp"
                            android:layout_marginLeft="10dp"
                            android:background="#dddddd" />

                        <LinearLayout
                            android:id="@+id/txt_phone_hotline"
                            android:padding="10dp"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_gravity="center_horizontal"
                                android:padding="10dp">

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    app:iiv_size="20dp"
                                    app:iiv_color="@color/iconColor"
                                    app:iiv_icon="faw-phone" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_marginStart="5dp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@color/white"
                                android:padding="5dp"
                                android:gravity="center_horizontal"
                                android:orientation="vertical">

                                <TextView
                                    android:paddingTop="5dp"
                                    android:paddingBottom="5dp"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/textDark"
                                    android:textStyle="bold"
                                    android:text="@string/txt_phone_hotline" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="fill_parent"
                                android:layout_height="match_parent"
                                android:gravity="center_vertical|end"
                                android:padding="10dp">

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:layout_width="16dp"
                                    android:layout_height="16dp"
                                    app:iiv_color="@color/primary"
                                    app:iiv_icon="gmd_keyboard_arrow_right" />
                            </LinearLayout>
                        </LinearLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginRight="10dp"
                            android:layout_marginLeft="10dp"
                            android:background="#dddddd" />

                        <LinearLayout
                            android:id="@+id/txt_general_rules"
                            android:padding="10dp"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_gravity="center_horizontal"
                                android:padding="10dp">

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    app:iiv_size="20dp"
                                    app:iiv_color="@color/iconColor"
                                    app:iiv_icon="gmd_security" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_marginStart="5dp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@color/white"
                                android:padding="5dp"
                                android:gravity="center_horizontal"
                                android:orientation="vertical">

                                <TextView
                                    android:paddingTop="5dp"
                                    android:paddingBottom="5dp"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/textDark"
                                    android:textStyle="bold"
                                    android:text="@string/txt_general_rules" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="fill_parent"
                                android:layout_height="match_parent"
                                android:gravity="center_vertical|end"
                                android:padding="10dp">

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:layout_width="16dp"
                                    android:layout_height="16dp"
                                    app:iiv_color="@color/primary"
                                    app:iiv_icon="gmd_keyboard_arrow_right" />
                            </LinearLayout>
                        </LinearLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginRight="10dp"
                            android:layout_marginLeft="10dp"
                            android:background="#dddddd" />

                        <LinearLayout
                            android:id="@+id/txt_privacy_policy"
                            android:padding="10dp"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_gravity="center_horizontal"
                                android:padding="10dp">

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    app:iiv_size="20dp"
                                    app:iiv_icon="faw-user_secret"
                                    app:iiv_color="@color/iconColor" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_marginStart="5dp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@color/white"
                                android:padding="5dp"
                                android:gravity="center_horizontal"
                                android:orientation="vertical">

                                <TextView
                                    android:paddingTop="5dp"
                                    android:paddingBottom="5dp"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/textDark"
                                    android:textStyle="bold"
                                    android:text="@string/txt_privacy_policy" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="fill_parent"
                                android:layout_height="match_parent"
                                android:gravity="center_vertical|end"
                                android:padding="10dp">

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:layout_width="16dp"
                                    android:layout_height="16dp"
                                    app:iiv_color="@color/primary"
                                    app:iiv_icon="gmd_keyboard_arrow_right" />
                            </LinearLayout>
                        </LinearLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginRight="10dp"
                            android:layout_marginLeft="10dp"
                            android:background="#dddddd" />

                        <LinearLayout
                            android:id="@+id/txt_terms_andconditions"
                            android:padding="10dp"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_gravity="center_horizontal"
                                android:padding="10dp">

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    app:iiv_size="20dp"
                                    app:iiv_icon="faw-bookmark"
                                    app:iiv_color="@color/iconColor" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_marginLeft="5dp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@color/white"
                                android:padding="5dp"
                                android:gravity="center_horizontal"
                                android:orientation="vertical">

                                <TextView
                                    android:paddingTop="5dp"
                                    android:paddingBottom="5dp"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textStyle="bold"
                                    android:textColor="@color/textDark"
                                    android:text="@string/txt_terms_andconditions" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="fill_parent"
                                android:layout_height="match_parent"
                                android:gravity="center_vertical|end"
                                android:padding="10dp">

                                <com.mikepenz.iconics.view.IconicsImageView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    app:iiv_size="20dp"
                                    app:iiv_color="@color/primary"
                                    app:iiv_icon="gmd_keyboard_arrow_right" />
                            </LinearLayout>
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/txtLogout"
                            android:layout_width="match_parent"
                            android:orientation="vertical"
                            android:layout_height="wrap_content">

                            <View
                                android:layout_width="match_parent"
                                android:layout_height="1dp"
                                android:layout_marginRight="10dp"
                                android:layout_marginLeft="10dp"
                                android:background="#dddddd" />

                            <LinearLayout

                                android:padding="10dp"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="match_parent"
                                    android:layout_gravity="center_horizontal"
                                    android:padding="10dp">

                                    <com.mikepenz.iconics.view.IconicsImageView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        app:iiv_size="20dp"
                                        app:iiv_icon="faw-sign_out_alt"
                                        app:iiv_color="@color/iconColor" />
                                </LinearLayout>

                                <LinearLayout
                                    android:layout_marginLeft="5dp"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:background="@color/white"
                                    android:padding="5dp"
                                    android:gravity="center_horizontal"
                                    android:orientation="vertical">

                                    <TextView
                                        android:paddingTop="5dp"
                                        android:paddingBottom="5dp"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textColor="@color/textDark"
                                        android:textStyle="bold"
                                        android:text="@string/txt_signout" />

                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="fill_parent"
                                    android:layout_height="match_parent"
                                    android:gravity="center_vertical|end"
                                    android:padding="10dp">

                                    <com.mikepenz.iconics.view.IconicsImageView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        app:iiv_size="20dp"
                                        app:iiv_color="@color/primary"
                                        app:iiv_icon="gmd_keyboard_arrow_right" />
                                </LinearLayout>

                            </LinearLayout>
                        </LinearLayout>
                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <LinearLayout
                    android:gravity="center_horizontal|center_vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="10dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/txtFooter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="" />

                    <TextView
                        android:gravity="center_horizontal|center_vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/build_type" />
                </LinearLayout>

                <!--  -->

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>