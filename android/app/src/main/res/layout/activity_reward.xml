<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/coordinatorLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context="com.hqt.view.ui.reward.ui.activity.RewardActivity">

        <com.github.florent37.materialviewpager.MaterialViewPager
            android:id="@+id/materialViewPager"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:viewpager_pagerTitleStrip="@layout/my_tabs"
            app:viewpager_color="@color/colorPrimary"
            app:viewpager_headerHeight="150dp"
            app:viewpager_headerAlpha="1.0"
            app:viewpager_imageHeaderDarkLayerAlpha="0.2"
            app:viewpager_hideLogoWithFade="true"
            app:viewpager_hideToolbarAndTitle="false"
            app:viewpager_enableToolbarElevation="false"
            app:viewpager_parallaxHeaderFactor="1.5"
            app:viewpager_headerAdditionalHeight="15dp"
            app:viewpager_displayToolbarWhenSwipe="true"
            app:viewpager_transparentToolbar="false"
            app:viewpager_animatedHeaderImage="true"
            android:background="#EDEDED" />
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>