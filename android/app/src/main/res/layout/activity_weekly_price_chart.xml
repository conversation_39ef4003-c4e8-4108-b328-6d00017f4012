<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true"
        android:background="@color/gbgray">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/primary_dark"
            android:fitsSystemWindows="true"
            app:elevation="5dp"
            android:theme="@style/AppTheme.AppBarOverlay">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/toolbar_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:title="<PERSON><PERSON><PERSON><PERSON> đồ giá vé theo tuần"
                android:background="@color/primary"
                android:fitsSystemWindows="true"
                app:titleEnabled="false"
                app:layout_scrollFlags="enterAlwaysCollapsed">

                <androidx.appcompat.widget.Toolbar
                    android:id="@+id/toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize"
                    app:elevation="0dp"
                    app:layout_collapseMode="pin"
                    app:layout_scrollFlags="scroll|enterAlways"
                    app:popupTheme="@style/AppTheme.PopupOverlay" />

            </com.google.android.material.appbar.CollapsingToolbarLayout>

        </com.google.android.material.appbar.AppBarLayout>

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="8dp">

                <!-- Route Selection Card -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="4dp"
                    app:cardUseCompatPadding="true">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Chọn tuyến bay"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/colorPrimaryDark"
                            android:layout_marginBottom="16dp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical">

                            <!-- Origin -->
                            <LinearLayout
                                android:id="@+id/layoutOrigin"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="2"
                                android:orientation="vertical"
                                android:background="?android:attr/selectableItemBackground"
                                android:padding="8dp">

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="Từ"
                                    android:textSize="12sp"
                                    android:textColor="@color/colorAccent" />

                                <TextView
                                    android:id="@+id/txtOriginCode"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="SGN"
                                    android:textSize="24sp"
                                    android:textStyle="bold"
                                    android:textColor="@color/colorPrimaryDark" />

                                <TextView
                                    android:id="@+id/txtOrigin"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="Hồ Chí Minh"
                                    android:textSize="14sp"
                                    android:textColor="@color/colorPrimaryDark" />

                            </LinearLayout>

                            <!-- Swap Button -->
                            <ImageButton
                                android:id="@+id/btnSwapRoute"
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:layout_weight="0"
                                android:background="?android:attr/selectableItemBackgroundBorderless"
                                android:src="@drawable/ic_action_exchange"
                                android:contentDescription="Đổi chiều"
                                android:layout_margin="8dp" />

                            <!-- Destination -->
                            <LinearLayout
                                android:id="@+id/layoutDestination"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="2"
                                android:orientation="vertical"
                                android:background="?android:attr/selectableItemBackground"
                                android:padding="8dp">

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="Đến"
                                    android:textSize="12sp"
                                    android:textColor="@color/colorAccent" />

                                <TextView
                                    android:id="@+id/txtDestinationCode"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="HAN"
                                    android:textSize="24sp"
                                    android:textStyle="bold"
                                    android:textColor="@color/colorPrimaryDark" />

                                <TextView
                                    android:id="@+id/txtDestination"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="Hà Nội"
                                    android:textSize="14sp"
                                    android:textColor="@color/colorPrimaryDark" />

                            </LinearLayout>

                        </LinearLayout>

                        <!-- Refresh Button -->
                        <Button
                            android:id="@+id/btnRefresh"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Tải lại dữ liệu"
                            android:layout_marginTop="16dp"
                            style="@style/MyApp.Button.Big"
                            android:background="@drawable/button_gradient"
                            android:textColor="@color/white" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- Weekly Price Chart -->
                <com.hqt.view.ui.chart.WeeklyPriceChartView
                    android:id="@+id/weeklyPriceChart"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp" />

                <!-- Instructions Card -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="2dp"
                    app:cardUseCompatPadding="true">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Hướng dẫn sử dụng"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/colorPrimaryDark"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="• Nhấn vào cột để xem chi tiết giá vé của tuần đó\n• Cột màu xanh lá cây là tuần có giá rẻ nhất\n• Biểu đồ hiển thị giá vé rẻ nhất của mỗi tuần\n• Chọn tuyến bay khác để xem giá vé tương ứng"
                            android:textSize="14sp"
                            android:textColor="@color/colorPrimaryDark"
                            android:lineSpacingExtra="4dp" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

            </LinearLayout>

        </ScrollView>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</layout>
