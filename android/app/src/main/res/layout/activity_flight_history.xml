<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto"
    xmlns:custom="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.hqt.view.ui.flighthistory.ui.FlightHistoryViewModel" />


    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/coordinatorLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:animateLayoutChanges="true"
        android:background="@color/gbgray"
        android:fitsSystemWindows="true"
        tools:context="com.hqt.view.ui.HomeActivity">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_gradient"
            android:fitsSystemWindows="true"
            android:theme="@style/AppTheme.AppBarOverlay"
            android:visibility="visible"
            app:elevation="0dp">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/toolbar_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fitsSystemWindows="true"
                app:contentScrim="?attr/colorPrimary"
                app:layout_scrollFlags="noScroll"
                app:title="12bay.vn"
                app:titleEnabled="false">

                <ImageView
                    android:id="@+id/headerBG"
                    android:layout_width="match_parent"
                    android:layout_height="150dp"
                    android:adjustViewBounds="true"
                    android:alpha="1"
                    android:contentDescription="@string/app_name"
                    android:fitsSystemWindows="true"
                    android:scaleType="centerCrop"
                    app:srcCompat="@drawable/world" />

                <androidx.appcompat.widget.Toolbar
                    android:id="@+id/toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize"
                    app:elevation="0dp"
                    app:layout_collapseMode="pin"
                    app:layout_scrollFlags="scroll|exitUntilCollapsed"
                    app:popupTheme="@style/AppTheme.PopupOverlay" />

            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="?attr/actionBarSize"
            android:orientation="vertical">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="16dp"
                android:layout_marginBottom="5dp"
                android:animateLayoutChanges="true"
                android:background="@drawable/corner_full"
                android:clickable="true"
                android:focusable="true"
                android:visibility="visible"
                app:cardCornerRadius="2dp"
                app:cardElevation="1dp"
                app:cardPreventCornerOverlap="false">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:focusable="true"
                    android:focusableInTouchMode="true"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="5dp">


                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="100dp"
                            android:layout_height="wrap_content">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/txtFlightNumber"
                                android:layout_width="100dp"
                                android:layout_height="wrap_content"
                                android:autofillHints="VN110"
                                android:hint="Mã chuyến bay"
                                android:inputType="textCapCharacters"
                                android:maxLines="1" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <com.tiper.MaterialSpinner
                            android:id="@+id/inputDays"
                            android:layout_width="100dp"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:hint="Thời gian"
                            app:spinnerMode="dropdown"
                            bind:customEntries="@{viewModel.getDays()}"
                            bind:selectedValue="@{viewModel.getSelectedDay()}" />


                        <LinearLayout
                            android:layout_width="fill_parent"
                            android:layout_height="match_parent"
                            android:gravity="right|center_vertical"
                            android:orientation="horizontal">

                            <Button
                                android:id="@+id/btnFind"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@drawable/button_gradient"
                                android:text="XEM"
                                android:textColor="@color/white" />
                        </LinearLayout>

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@drawable/gradientdiv_vertical" />

                    <LinearLayout
                        android:id="@+id/meta_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="10dp"
                        android:visibility="visible">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center_horizontal"
                            android:orientation="vertical"
                            android:visibility="gone">

                            <com.hqt.util.AspectRatioImageView
                                android:id="@+id/airLogo"
                                android:layout_width="80dp"
                                android:layout_height="50dp"
                                app:srcCompat="@drawable/logo_trans" />
                        </LinearLayout>

                        <View
                            android:layout_width="2dp"
                            android:layout_height="match_parent"
                            android:layout_marginStart="5dp"
                            android:layout_marginEnd="5dp"
                            android:background="@drawable/gradientdiv"
                            android:visibility="gone" />

                        <LinearLayout
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:layoutDirection="rtl"
                            android:orientation="horizontal">

                            <com.hqt.util.helper.ArcProgress
                                android:id="@+id/score_progress"
                                android:layout_width="50dp"
                                android:layout_height="50dp"
                                android:background="@color/white"
                                custom:arc_bottom_text="Đúng giờ"
                                custom:arc_finished_color="@color/primary"
                                custom:arc_progress="0"
                                custom:arc_suffix_text=" "
                                custom:arc_suffix_text_padding="-2dp"
                                custom:arc_suffix_text_size="12sp"
                                custom:arc_text_size="16sp"
                                custom:arc_unfinished_color="@color/light_primary" />

                            <View
                                android:layout_width="2dp"
                                android:layout_height="match_parent"
                                android:layout_marginStart="5dp"
                                android:layout_marginEnd="5dp"
                                android:background="@drawable/gradientdiv" />

                            <LinearLayout
                                android:layout_width="fill_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_horizontal|center_vertical"
                                android:layoutDirection="ltr"
                                android:orientation="vertical"
                                android:paddingEnd="5dp">

                                <LinearLayout
                                    android:layout_width="fill_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal"
                                    android:weightSum="9">

                                    <LinearLayout
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="Số hiệu: " />

                                        <TextView
                                            style="@style/Text"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="@{viewModel.flightHistory.meta.flight}"
                                            android:textStyle="bold" />
                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_width="fill_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="end"
                                        android:orientation="horizontal">


                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:singleLine="true"
                                            android:text="Số chuyến: " />

                                        <TextView
                                            style="@style/Text"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="@{viewModel.flightHistory.meta.total + ``}" />

                                    </LinearLayout>


                                </LinearLayout>

                                <RelativeLayout

                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal"
                                    android:weightSum="9">

                                    <LinearLayout
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="Đúng giờ: " />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="@{viewModel.flightHistory.meta.green.total + ``}"
                                            android:textColor="#4CAF50"
                                            android:textStyle="bold" />
                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_centerInParent="true"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginStart="30dp"
                                            android:text="Chậm: " />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="@{viewModel.flightHistory.meta.yellow.total + ``}"
                                            android:textColor="#FB953B"
                                            android:textStyle="bold" />
                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_alignParentEnd="true"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="Trễ: " />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="@{viewModel.flightHistory.meta.red.total + ``}"
                                            android:textColor="#d62d20"
                                            android:textStyle="bold" />
                                    </LinearLayout>

                                </RelativeLayout>
                            </LinearLayout>


                        </LinearLayout>


                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <com.facebook.shimmer.ShimmerFrameLayout
                android:id="@+id/shimmer_view_container"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:orientation="vertical"
                android:paddingLeft="5dp"
                android:paddingRight="5dp"
                android:visibility="gone"
                app:shimmer_duration="800">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/search"
                    android:orientation="vertical">

                    <include layout="@layout/placeholder_item_train" />

                    <include layout="@layout/placeholder_item_train" />

                    <include layout="@layout/placeholder_item_train" />

                    <include layout="@layout/placeholder_item_train" />

                    <include layout="@layout/placeholder_item_train" />

                    <include layout="@layout/placeholder_item_train" />

                    <include layout="@layout/placeholder_item_train" />

                    <include layout="@layout/placeholder_item_train" />

                    <include layout="@layout/placeholder_item_train" />

                    <include layout="@layout/placeholder_item_train" />
                </LinearLayout>
            </com.facebook.shimmer.ShimmerFrameLayout>


            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="fill_parent"
                android:background="@color/gbgray"
                android:paddingLeft="5dp"
                android:paddingRight="5dp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/notfound"
            android:layout_width="match_parent"
            android:layout_height="500dp"
            android:layout_marginTop="?attr/actionBarSize"
            android:orientation="vertical"
            android:visibility="visible">

            <ImageView
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="200dp"
                android:adjustViewBounds="true"
                android:background="@drawable/icon_flight_status_state"
                android:contentDescription="Chưa có thông báo giá vé"
                android:scaleType="centerCrop"
                app:layout_behavior="@string/appbar_scrolling_view_behavior" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:padding="5dp"
                android:text="Theo dõi chuyến bay"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="Nhập mã chuyến bay (vd: VN206, VJ120) "
                android:textSize="12sp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="Xem lại lịch sử, trạng thái chuyến bay, giờ cất cánh hạ cánh"
                android:textSize="12sp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="20dp"
                android:gravity="right">

                <RadioGroup
                    android:id="@+id/radio"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="5dp"
                    android:layout_weight="1"
                    android:background="@drawable/corner_full"
                    android:orientation="horizontal"
                    tools:ignore="UselessParent">

                    <Button
                        android:id="@+id/btnAddNew"
                        style="@style/MyApp.Button.Big"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="0.5"
                        android:background="@drawable/button_one_way"
                        android:checked="true"
                        android:text="Nhập mã chuyến bay"
                        android:textAllCaps="true"
                        android:textColor="#FFFFFF" />

                    <Button
                        android:id="@+id/btnSearchFlight"
                        style="@style/MyApp.Button.Big"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="0.5"
                        android:background="@color/fui_transparent"
                        android:text="@string/datCho"
                        android:textAllCaps="true"
                        android:textColor="#00a2e3" />
                </RadioGroup>

            </LinearLayout>
        </LinearLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</layout>