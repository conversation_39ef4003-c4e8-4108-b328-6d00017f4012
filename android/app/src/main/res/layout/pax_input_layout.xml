<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="pax"
            type="com.hqt.data.model.Passenger" />

        <variable
            name="viewModel"
            type="com.hqt.viewmodel.TypePaxViewModel" />

        <import type="android.view.View" />

        <import type="com.hqt.util.Helper.ValidatorType" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/coordinatorLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:animateLayoutChanges="true"
        android:fitsSystemWindows="true"
        android:focusableInTouchMode="true">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/primary_dark"
            android:fitsSystemWindows="true"
            android:theme="@style/AppTheme.AppBarOverlay"
            android:visibility="visible"
            app:elevation="5dp">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/toolbar_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:contentScrim="@color/primary_dark"
                app:layout_scrollFlags="enterAlwaysCollapsed"
                app:title="12bay.vn"
                app:titleEnabled="false">

                <androidx.appcompat.widget.Toolbar
                    android:id="@+id/toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize"
                    android:background="?attr/colorPrimary"
                    app:popupTheme="@style/AppTheme.PopupOverlay">

                    <com.mikepenz.iconics.view.IconicsImageView
                        android:id="@+id/inputPaxClose"
                        android:layout_width="?attr/actionBarSize"
                        android:layout_height="?attr/actionBarSize"
                        android:layout_gravity="right|end"
                        android:background="?android:selectableItemBackground"
                        app:iiv_color="@color/white"
                        app:iiv_icon="gmd_close"
                        app:iiv_size="24sp" />


                </androidx.appcompat.widget.Toolbar>


            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="?attr/actionBarSize"
            android:background="@color/gbgray">


            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/white"
                        android:orientation="vertical"
                        android:paddingStart="15dp"
                        android:paddingTop="10dp"
                        android:paddingEnd="15dp"
                        android:paddingBottom="15dp">

                        <!--                        @{(parent,view,pos,id)->viewModel.onSelectTitleItem(parent,view,pos,id)}-->
                        <com.tiper.MaterialSpinner
                            android:id="@+id/inputPaxGender"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:hint="Danh xưng: *"
                            app:selectedValue="@{viewModel.getSelectedPaxGender}"
                            app:spinnerMode="dropdown"
                            bind:customEntries="@{viewModel.getPaxGenderList}"
                            bind:onItemSelected="@{viewModel}" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">


                            <com.google.android.material.textfield.TextInputLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <AutoCompleteTextView
                                    android:id="@+id/inputPaxFullName"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:autofillHints="Họ và tên: (vd Nguyen Van Tuan)"
                                    android:completionThreshold="1"
                                    android:hint="Họ và tên: * (vd Nguyen Van Tuan)"
                                    android:inputType="textPersonName|textCapWords"
                                    android:maxLines="1"
                                    android:text="@{pax.fullName}"
                                    bind:textChange="@{`fullName`}" />

                                <!--                                <com.google.android.material.textfield.TextInputEditText-->
                                <!--                                    android:id="@+id/inputPaxFirstName"-->
                                <!--                                    android:layout_width="match_parent"-->
                                <!--                                    android:layout_height="wrap_content"-->
                                <!--                                    android:maxLines="1"-->
                                <!--                                    android:inputType="textPersonName|textCapWords"-->
                                <!--                                    android:hint="Họ: (vd Nguyen)"-->
                                <!--                                    bind:textChange="@{`firstName`}"-->
                                <!--                                    android:autofillHints="Họ: (vd Nguyen)"-->
                                <!--                                    android:text="@{pax.firstName}" />-->

                            </com.google.android.material.textfield.TextInputLayout>
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:visibility="gone">

                            <com.google.android.material.textfield.TextInputLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <AutoCompleteTextView
                                    android:id="@+id/inputPaxLastName"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:autofillHints="Họ: (vd Nguyen)"
                                    android:completionThreshold="1"
                                    android:hint="Họ: (vd Nguyen)"
                                    android:inputType="textPersonName|textCapWords"
                                    android:maxLines="1"
                                    android:text="@{pax.lastName}"
                                    bind:textChange="@{`lastName`}" />


                            </com.google.android.material.textfield.TextInputLayout>
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:visibility="gone">

                            <com.google.android.material.textfield.TextInputLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <com.google.android.material.textfield.TextInputEditText
                                    android:id="@+id/inputPaxFirstName"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:hint="Tên đệm và tên: (vd Van Thao)"
                                    android:inputType="textPersonName|textCapWords"
                                    android:maxLines="1"
                                    android:text="@{pax.firstName}"
                                    bind:textChange="@{`firstName`}" />

                            </com.google.android.material.textfield.TextInputLayout>
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:visibility="@{viewModel.getIsShowInput(ValidatorType.BIRTDATE) ? View.VISIBLE : View.GONE}">

                            <com.google.android.material.textfield.TextInputLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <com.google.android.material.textfield.TextInputEditText
                                    android:id="@+id/inputPaxBirthday"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:focusable="false"
                                    android:hint="Ngày sinh:"
                                    android:inputType="date"
                                    android:maxLines="1"
                                    android:text="@{pax.birthday}"
                                    bind:textChange="@{`birthDate`}" />


                            </com.google.android.material.textfield.TextInputLayout>
                        </LinearLayout>

                        <com.tiper.MaterialSpinner
                            android:id="@+id/nationalInput"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:hint="Quốc gia:"
                            app:selectedValue="@{viewModel.getSelectedPaxGender}"
                            app:spinnerMode="dropdown"
                            bind:customEntries="@{viewModel.getPaxGenderList}"
                            bind:onItemSelected="@{viewModel}" />


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:visibility="@{viewModel.getIsShowInput(ValidatorType.IDNUMBER) ? View.VISIBLE : View.GONE}">

                            <com.google.android.material.textfield.TextInputLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <com.google.android.material.textfield.TextInputEditText
                                    android:id="@+id/inputPaxIdNumber"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:hint="Số giấy tờ (CCCD hoặc Passport)"
                                    android:lines="1"
                                    android:maxLines="1"
                                    android:text="@{pax.idNumber}"
                                    bind:textChange="@{`required|idNumber`}" />

                            </com.google.android.material.textfield.TextInputLayout>


                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:paddingStart="10dp"
                                android:paddingEnd="10dp"
                                android:text="Đối với hành khách chưa có thông tin có thể bỏ qua trường này và bổ sung sau"
                                android:textSize="12sp" />
                        </LinearLayout>


                    </LinearLayout>

                    <LinearLayout

                        android:id="@+id/baggageSelectLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/white"
                        android:orientation="vertical"
                        android:paddingBottom="20dp"
                        android:visibility="visible">

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="@color/diver_color" />


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:padding="10dp"
                            android:paddingStart="15dp"
                            android:paddingEnd="15dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Hành lý ký gửi lượt đi"
                                android:textStyle="bold" />


                            <TextView
                                android:id="@+id/depBag"
                                android:layout_width="fill_parent"
                                android:layout_height="match_parent"
                                android:gravity="right"
                                android:textColor="@color/textDark"
                                android:visibility="gone" />
                        </LinearLayout>

                        <HorizontalScrollView
                            android:id="@+id/horizalScrollx"
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:background="@color/diver_color"
                            android:paddingTop="5dp"
                            android:paddingBottom="5dp"
                            android:scrollbars="none">

                            <LinearLayout
                                android:id="@+id/bagSelectContainer"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">


                            </LinearLayout>
                        </HorizontalScrollView>

                        <LinearLayout
                            android:id="@+id/selectBagReturnLayout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:visibility="gone">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:padding="10dp"
                                android:paddingStart="15dp"
                                android:paddingEnd="15dp">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Hành lý ký gửi lượt về"
                                    android:textStyle="bold" />


                                <TextView
                                    android:id="@+id/retBag"
                                    android:layout_width="fill_parent"
                                    android:layout_height="match_parent"
                                    android:gravity="right"
                                    android:textColor="@color/textDark"
                                    android:visibility="gone" />
                            </LinearLayout>

                            <HorizontalScrollView
                                android:id="@+id/horizalScrollRt"
                                android:layout_width="fill_parent"
                                android:layout_height="wrap_content"
                                android:background="@color/diver_color"
                                android:paddingTop="5dp"
                                android:paddingBottom="5dp"
                                android:scrollbars="none">

                                <LinearLayout
                                    android:id="@+id/bagSelectContainerRt"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal">

                                </LinearLayout>
                            </HorizontalScrollView>

                        </LinearLayout>


                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center|center_vertical"
                        android:orientation="vertical"
                        android:paddingTop="10dp"
                        android:paddingBottom="10dp">

                        <Button
                            android:id="@+id/btnSelectPax"
                            style="@style/Theme.MyApp.Button"
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:layout_marginLeft="20dp"
                            android:layout_marginRight="20dp"
                            android:background="@drawable/button_gradient"
                            android:text="Lưu"
                            android:textColor="#FFFFFF"
                            android:textSize="15sp" />

                    </LinearLayout>


                </LinearLayout>


            </ScrollView>
        </RelativeLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>