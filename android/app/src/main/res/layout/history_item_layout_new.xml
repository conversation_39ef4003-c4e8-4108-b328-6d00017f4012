<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:bind="http://schemas.android.com/apk/res-auto"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.hqt.viewmodel.HistoryItemViewModel" />

        <import type="com.hqt.datvemaybay.Common" />

        <import type="com.hqt.data.model.Booking.BookingType" />

        <import type="android.view.View" />
    </data>

    <LinearLayout
        android:id="@+id/bookingview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_margin="5dp"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            card_view:cardElevation="0dp"
            card_view:cardCornerRadius="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center_horizontal"
                android:weightSum="10">


                <TextView
                    android:id="@+id/pos"
                    android:layout_width="0dp"
                    android:layout_height="0dp" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_weight="10">

                    <LinearLayout
                        android:id="@+id/title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="10dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Đơn hàng: " />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            style="@style/Text"
                            android:textStyle="bold"
                            android:text="@{`#`+viewModel.booking.id}" />

                        <TextView
                            android:id="@+id/soHanhKhachDi"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="right"
                            bind:fawText="@{viewModel.getListPaxTitle()}" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/content"
                        android:layout_below="@+id/title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">


                        <LinearLayout

                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginRight="5dp"
                            android:orientation="vertical">

                            <LinearLayout
                                bind:visibility="@{viewModel.type == BookingType.BUS}"
                                android:id="@+id/busLayout"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:baselineAligned="false"
                                android:weightSum="10">

                                <LinearLayout
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_marginRight="5dp"
                                    android:layout_weight="4"
                                    android:gravity="center"
                                    android:orientation="vertical">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:singleLine="true"
                                        android:text="@{ viewModel.type == BookingType.BUS ? viewModel.bus.departure_f.originName : ``}"
                                        android:textColor="#000000"
                                        tools:text="Bến xe miền đông" />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="@{viewModel.type == BookingType.BUS ? Common.dateToString(viewModel.bus.departure_f.pickupDate, `HH:mm dd/MM`) : ``}"
                                        android:textColor="#969696"
                                        tools:text="08:12 /12" />


                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="0dp"
                                    android:layout_height="match_parent"
                                    android:layout_weight="2"
                                    android:layout_gravity="center_vertical|center_horizontal"
                                    android:orientation="vertical">

                                    <com.hqt.util.AspectRatioImageView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center"
                                        android:background="@drawable/ic_bus_line" />

                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_marginRight="5dp"
                                    android:layout_weight="4"
                                    android:gravity="center"
                                    android:orientation="vertical">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:singleLine="true"
                                        android:text="@{viewModel.type == BookingType.BUS ? viewModel.bus.departure_f.destinationName:``}"
                                        android:textColor="#000000"
                                        tools:text="Đà Lạt" />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="@{viewModel.type == BookingType.BUS ? Common.dateToString(viewModel.bus.departure_f.arrivalDateTime, `HH:mm dd/MM`):`` }"
                                        android:textColor="#969696"
                                        tools:text="12:12 20/12" />


                                </LinearLayout>
                            </LinearLayout>

                            <LinearLayout
                                bind:visibility="@{viewModel.type != BookingType.BUS}"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center"
                                    android:baselineAligned="false"
                                    android:weightSum="10">

                                    <LinearLayout
                                        android:layout_marginStart="5dp"
                                        android:layout_marginTop="10dp"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_marginRight="5dp"
                                        android:layout_weight="4"
                                        android:gravity="center"
                                        android:orientation="vertical">

                                        <TextView
                                            android:id="@+id/txtFromDi"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:singleLine="true"
                                            android:text="@{viewModel.isFlightDetail() ? viewModel.booking.origin_name: viewModel.train.departure_f.originName}"
                                            android:textColor="#000000"
                                            tools:text="DAD" />

                                        <TextView
                                            android:id="@+id/txtFromTimeDi"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="@{viewModel.isFlightDetail() ? Common.dateToString(viewModel.flight.departure_f.departureDate, `HH:mm dd/MM`):Common.dateToString(viewModel.train.departure_f.departureDateTime, `HH:mm dd/MM`) }"
                                            android:textColor="#969696"
                                            tools:text="DAD" />


                                    </LinearLayout>

                                    <LinearLayout
                                        android:paddingStart="5dp"
                                        android:paddingEnd="5dp"
                                        android:layout_width="0dp"
                                        android:layout_height="match_parent"
                                        android:layout_weight="2"
                                        android:gravity="center_vertical|center_horizontal"
                                        android:orientation="vertical">


                                        <ImageView
                                            bind:visibility="@{viewModel.isFlightDetail()}"
                                            android:layout_width="match_parent"
                                            android:layout_height="30dp"
                                            bind:imageUrl="@{viewModel.flight.departure_f.getAirlinesLogo()}" />


                                        <ImageView
                                            bind:visibility="@{!viewModel.isFlightDetail()}"
                                            android:layout_width="wrap_content"
                                            android:layout_height="30dp"
                                            android:layout_gravity="center"
                                            android:src="@{viewModel.getAirLogo(viewModel.train.departure_f.provider)}" />

                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_marginTop="10dp"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_marginRight="5dp"
                                        android:layout_weight="4"
                                        android:gravity="center"
                                        android:orientation="vertical">

                                        <TextView
                                            android:id="@+id/txtToDi"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:singleLine="true"
                                            android:text="@{viewModel.isFlightDetail() ? viewModel.booking.destination_name: viewModel.train.departure_f.destinationName}"
                                            android:textColor="#000000"
                                            tools:text="DAD" />

                                        <TextView
                                            android:id="@+id/txtToTimeDi"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="@{viewModel.isFlightDetail() ? Common.dateToString(viewModel.flight.departure_f.arriverDate, `HH:mm dd/MM`) : Common.dateToString(viewModel.train.departure_f.arrivalDateTime, `HH:mm dd/MM`) }"
                                            android:textColor="#969696"
                                            tools:text="DAD" />


                                    </LinearLayout>
                                </LinearLayout>

                                <LinearLayout
                                    android:paddingTop="10dp"
                                    bind:visibility="@{viewModel.booking._round_trip}"
                                    android:id="@+id/txtItemLuotve"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center"
                                    android:paddingBottom="10dp"
                                    android:baselineAligned="false"
                                    android:weightSum="10">

                                    <LinearLayout
                                        android:layout_marginStart="5dp"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_marginRight="5dp"
                                        android:layout_weight="4"
                                        android:gravity="center"
                                        android:orientation="vertical">


                                        <TextView
                                            android:id="@+id/txtFromVe"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:singleLine="true"
                                            android:text="@{viewModel.isFlightDetail() ? viewModel.booking.destination_name: viewModel.train.departure_f.destinationName}"
                                            android:textColor="#000000"
                                            tools:text="DAD" />

                                        <TextView
                                            android:id="@+id/txtFromTimeVe"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="@{viewModel.isFlightDetail() ? Common.dateToString(viewModel.flight.return_f.departureDate, `HH:mm dd/MM`):Common.dateToString(viewModel.train.return_f.departureDateTime, `HH:mm dd/MM`) }"
                                            android:textColor="#969696"
                                            tools:text="DAD" />


                                    </LinearLayout>


                                    <LinearLayout
                                        android:paddingStart="5dp"
                                        android:paddingEnd="5dp"
                                        bind:visibility="@{viewModel.booking._round_trip ? (viewModel.flightDetail ? true : false) : false}"
                                        android:layout_width="0dp"
                                        android:layout_height="match_parent"
                                        android:layout_weight="2"
                                        android:gravity="center_vertical|center_horizontal"
                                        android:orientation="vertical">

                                        <ImageView
                                            bind:visibility="@{viewModel.isFlightDetail()}"
                                            android:layout_width="match_parent"
                                            android:layout_height="30dp"
                                            android:layout_gravity="center"
                                            bind:imageUrl="@{viewModel.flight.return_f.getAirlinesLogo()}" />

                                        <ImageView
                                            bind:visibility="@{!viewModel.isFlightDetail()}"
                                            android:layout_width="wrap_content"
                                            android:layout_height="30dp"
                                            android:layout_gravity="center"
                                            android:src="@{viewModel.getAirLogo(viewModel.train.return_f.provider)}" />


                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_width="0dp"
                                        android:layout_height="match_parent"
                                        android:layout_marginRight="5dp"
                                        android:layout_weight="4"
                                        android:gravity="center"
                                        android:orientation="vertical">

                                        <TextView
                                            android:id="@+id/txtToVe"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:singleLine="true"
                                            android:text="@{viewModel.isFlightDetail() ? viewModel.booking.origin_name : viewModel.train.departure_f.originName}"
                                            android:textColor="#000000"
                                            tools:text="DAD" />

                                        <TextView
                                            android:id="@+id/txtToTimeVe"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="@{viewModel.isFlightDetail() ? Common.dateToString(viewModel.flight.return_f.arriverDate, `HH:mm dd/MM`):Common.dateToString(viewModel.train.return_f.arrivalDateTime, `HH:mm dd/MM`) }"
                                            android:textColor="#969696"
                                            tools:text="DAD" />


                                    </LinearLayout>
                                </LinearLayout>
                            </LinearLayout>

                        </LinearLayout>
                    </LinearLayout>

                    <View
                        android:id="@+id/tile_divider"
                        android:layout_width="match_parent"
                        android:layout_marginLeft="5dp"
                        android:layout_marginRight="5dp"
                        android:layout_marginTop="5dp"
                        android:layout_marginBottom="5dp"
                        android:layout_height="1dp"
                        android:background="@drawable/gradientdiv_vertical" />

                    <LinearLayout
                        android:id="@+id/bottomLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="0dp"
                        android:gravity="center_vertical"
                        android:paddingBottom="5dp"
                        android:paddingLeft="5dp"
                        android:paddingRight="5dp">

                        <LinearLayout
                            android:id="@+id/statusBackground"
                            android:gravity="center_horizontal|center_vertical"
                            android:layout_width="wrap_content"
                            android:paddingTop="1dp"
                            android:paddingBottom="1dp"
                            android:paddingLeft="5dp"
                            android:paddingRight="5dp"
                            android:layout_height="wrap_content">

                            <TextView
                                android:visibility="gone"
                                android:id="@+id/iconStatus"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center_horizontal"
                                android:singleLine="true"
                                android:text="@string/fa_shopping_cart"
                                android:textColor="@color/white"
                                tools:text="DAD" />

                            <TextView
                                android:id="@+id/txtStatus"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_margin="5dp"
                                android:textStyle="bold"
                                android:singleLine="true"
                                android:text="@{viewModel.booking.status_text}"
                                android:textSize="14sp"
                                bind:statusTextColor="@{viewModel.booking.status}"
                                tools:text="DAD" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical|right"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/txtTotal"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:gravity="right"
                                android:text="@{Common.dinhDangTien(viewModel.getGrandTotal())}"
                                android:textColor="@color/black"
                                android:textStyle="bold"
                                tools:text="0đ" />

                            <ImageView
                                android:id="@+id/overflow"
                                android:layout_width="18dp"
                                android:layout_height="18dp"
                                android:scaleType="centerCrop"
                                card_view:srcCompat="@drawable/ic_dots" />
                        </LinearLayout>
                    </LinearLayout>


                </LinearLayout>
            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>
</layout>