<?xml version="1.0" encoding="UTF-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="route"
            type="com.hqt.view.ui.bus.BusRoute" />

        <!--        <variable-->
        <!--            name="viewHolder"-->
        <!--            type="com.hqt.view.adapter.TrainAdapter.ViewHolder" />-->

        <import type="com.hqt.datvemaybay.Common" />

        <variable
            name="handler"
            type="com.hqt.view.ui.bus.BusSelectHandler" />
    </data>

    <androidx.cardview.widget.CardView
        android:onClick="@{() -> handler.onSelectRoute(route)}"
        android:id="@+id/card_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="5dp"
        app:cardElevation="0dp"
        android:layout_marginBottom="@dimen/cardMarginVertical"
        android:layout_marginLeft="@dimen/cardMarginHorizontal"
        android:layout_marginRight="@dimen/cardMarginHorizontal"
        android:layout_marginTop="@dimen/cardMarginVertical">

        <LinearLayout
            android:background="@drawable/corner_full"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:visibility="visible"
                android:layout_margin="5dp"
                android:id="@+id/layoutTrainView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="5dp"
                android:paddingTop="5dp"
                android:layout_gravity="center_horizontal"
                android:orientation="horizontal">

                <com.hqt.util.AspectRatioImageView
                    android:id="@+id/bus_img"
                    app:riv_corner_radius="10dp"
                    app:riv_border_width="0dip"
                    app:riv_mutate_background="true"
                    android:layout_gravity="center_horizontal|center_vertical"
                    android:layout_width="78dp"
                    android:layout_height="60dp"
                    bind:imageUrl="@{route.company.image}"
                    bind:imageStyle="@{`ROUND`}"
                    android:contentDescription="@string/Description" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:paddingStart="5dp"
                            android:layout_gravity="center_vertical"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="16sp"
                            android:textColor="@color/textDark"
                            android:text="@{route.company.name}"
                            tools:text="xxxxxx"
                            android:textStyle="bold" />

                        <com.mikepenz.iconics.view.IconicsImageView
                            android:layout_marginStart="5dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:iiv_size="15dp"
                            app:iiv_icon="gmd_star"
                            android:layout_gravity="center_vertical"
                            app:iiv_color="@color/stt_yellow" />

                        <TextView
                            android:layout_gravity="center_vertical"
                            android:paddingStart="5dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{route.company.ratings.text()}"
                            android:textStyle="bold" />


                        <TextView
                            android:visibility="gone"
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:gravity="end"
                            android:paddingEnd="5dp"
                            android:text="@{Common.getDayOfWeek(route.departureDate) + `, `+Common.dateToString(route.departureDate ,`dd/MM` )}" />
                    </LinearLayout>

                    <TextView

                        android:layout_gravity="center_vertical"
                        android:padding="5dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@{route.schedules.vehicleType}"
                        tools:text="xxxx"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:paddingStart="5dp"
                        android:paddingEnd="5dp"
                        android:layout_height="wrap_content">

                        <TextView
                            bind:isBold="@{route.schedules.availableSeats  > 0 ? true : false}"
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textStyle="bold"
                            android:textColor="@color/stt_gray"
                            android:text="@{route.schedules.availableSeats + ``}" />

                        <TextView
                            bind:isBold="@{route.schedules.availableSeats > 0 ? true : false}"
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:paddingStart="5dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Chỗ trống" />

                        <LinearLayout
                            android:gravity="right"
                            android:layout_width="fill_parent"
                            android:layout_height="match_parent">


                            <TextView
                                android:textStyle="bold"
                                android:textSize="15sp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:textColor="#FF0000"
                                android:text="@{Common.dinhDangTien(route.schedules.fare)}" />
                        </LinearLayout>


                    </LinearLayout>

                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:padding="5dp"

                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal">

                <View
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:layout_marginRight="5dp"
                    android:layout_marginLeft="5dp"
                    android:background="@drawable/gradientdiv" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.mikepenz.iconics.view.IconicsImageView
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:padding="5dp"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            app:iiv_color="@color/stt_gray"
                            app:iiv_icon="faw-clock" />

                        <TextView
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:padding="5dp"
                            android:id="@+id/txtGioDi"
                            style="@style/Text"
                            android:textStyle="bold"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{Common.dateToString(route.schedules.pickupDate,`HH:mm -`) }" />

                        <TextView
                            style="@style/Text"
                            android:textStyle="bold"
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:padding="5dp"
                            android:paddingStart="0dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{route.from.name }" />


                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.mikepenz.iconics.view.IconicsImageView
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:padding="5dp"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            app:iiv_color="@color/stt_gray"
                            app:iiv_icon="faw-bus" />

                        <TextView
                            android:visibility="visible"
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:padding="5dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{route.distance + `Km -`}" />

                        <TextView
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:padding="5dp"
                            android:paddingStart="0dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/stt_gray"
                            android:text="@{route.durationText}" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.mikepenz.iconics.view.IconicsImageView
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:padding="5dp"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            app:iiv_color="@color/stt_gray"
                            app:iiv_icon="faw-map-marker-alt" />

                        <TextView
                            style="@style/Text"
                            android:textStyle="bold"
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:padding="5dp"
                            android:id="@+id/txtGioDen"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{Common.dateToString(route.schedules.arrivalTime,`HH:mm -`) }" />

                        <TextView
                            style="@style/Text"
                            android:textStyle="bold"
                            android:layout_gravity="center_horizontal|center_vertical"
                            android:padding="5dp"
                            android:paddingStart="0dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{route.to.name }" />


                    </LinearLayout>

                </LinearLayout>
            </LinearLayout>

        </LinearLayout>
    </androidx.cardview.widget.CardView>
</layout>