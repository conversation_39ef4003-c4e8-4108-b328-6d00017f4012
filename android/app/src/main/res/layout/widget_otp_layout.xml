<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:paddingLeft="@dimen/activity_horizontal_margin"
        android:paddingTop="@dimen/activity_vertical_margin"
        android:paddingRight="@dimen/activity_horizontal_margin"
        android:paddingBottom="@dimen/activity_vertical_margin">

        <com.airbnb.lottie.LottieAnimationView
            android:layout_marginTop="10dp"
            android:id="@+id/loadding"
            android:layout_width="80dp"
            android:layout_height="80dp"
            app:lottie_rawRes="@raw/avatar_loading"
            app:lottie_loop="true"
            android:scaleType="centerCrop"
            app:lottie_autoPlay="true" />

        <LinearLayout
            android:padding="10dp"
            android:gravity="center_horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:id="@+id/otpPhoneInputView"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:padding="14dp"
                android:text="Đăng nhập / đăng ký"
                android:textColor="@color/textDark"
                android:textSize="16sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Vui lòng nhập số điện thoại của bạn để tiếp tục"
                    android:textAlignment="center" />

            </LinearLayout>

            <TextView
                android:visibility="gone"
                android:id="@+id/phone_error"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/red"
                android:layout_marginTop="10dp"
                android:text="" />

            <EditText
                android:layout_marginTop="10dp"
                android:id="@+id/phone_number"
                android:maxLength="10"
                android:textSize="24sp"
                android:layout_marginHorizontal="40dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="#000000"
                android:inputType="phone"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:background="@drawable/bg_otp_box" />


            <Button
                android:id="@+id/phoneSendBtn"
                android:layout_marginTop="10dp"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:layout_marginBottom="20dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="15sp"
                android:background="@drawable/button_gradient"
                android:layout_gravity="center_horizontal"
                style="@style/Theme.MyApp.Button"
                android:text="    Tiếp tục    "
                android:textColor="#FFFFFF" />

        </LinearLayout>

        <LinearLayout
            android:padding="10dp"
            android:gravity="center_horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:id="@+id/otpConfirmView"
            android:visibility="gone"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:padding="14dp"
                android:text="Vui lòng nhập mã xác nhận"
                android:textColor="@color/textDark"
                android:textSize="16sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/send_status"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Mã OTP 6 chữ số đã được gửi đến số điện thoại 0909123123"
                    android:textAlignment="center" />

            </LinearLayout>

            <in.aabhasjindal.otptextview.OtpTextView
                android:layout_marginTop="20dp"
                android:id="@+id/otp_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#000000"
                app:height="40dp"
                app:width="40dp"
                app:bar_enabled="false"
                app:bar_height="2dp"
                app:length="4"
                app:otp_box_background="@drawable/bg_otp_box"
                app:otp_box_background_active="@drawable/bg_otp_box_active"
                app:otp_text_size="24dp" />

            <TextView
                android:visibility="visible"
                android:id="@+id/otp_error"
                android:layout_margin="10dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/red"
                android:text="" />

            <Button
                android:layout_marginTop="10dp"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:layout_marginBottom="20dp"
                android:id="@+id/btnVerify"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="15sp"
                android:background="@drawable/button_gradient"
                android:layout_gravity="center_horizontal"
                style="@style/Theme.MyApp.Button"
                android:text="    XÁC THỰC    "
                android:textColor="#FFFFFF" />

        </LinearLayout>
    </LinearLayout>
</layout>