<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:paddingTop="5dp"
    android:paddingBottom="5dp"
    android:paddingStart="5dp"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <com.makeramen.roundedimageview.RoundedImageView
            android:id="@+id/image"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:scaleType="centerCrop"
            android:src="@drawable/top_banner"
            app:riv_corner_radius="5dip"
            app:riv_border_width="0dip"
            app:riv_mutate_background="true"
            app:riv_oval="false" />

        <LinearLayout
            android:paddingStart="10dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:orientation="vertical">

            <TextView
                android:id="@+id/stationName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="18sp"
                android:text="Ho Chi MInh City"
                android:textColor="#000000" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="fill_parent"
            android:gravity="end|center_vertical"
            android:layout_height="match_parent">

            <TextView
                android:background="@drawable/round_airport_code"
                android:padding="2dp"
                android:id="@+id/stationCode"
                android:layout_width="75dp"
                android:gravity="center"
                android:layout_gravity="center_horizontal|center_vertical"
                android:layout_height="wrap_content"
                android:textSize="16sp"
                android:text="SGN" />

        </LinearLayout>
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="5dp"
        android:background="@drawable/gradientdiv_vertical" />
</LinearLayout>
