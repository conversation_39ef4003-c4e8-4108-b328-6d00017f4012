<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="120dp"
    android:layout_height="120dp"
    android:layout_margin="4dp"
    android:focusable="true"
    app:cardCornerRadius="5dp"
    app:cardElevation="0dp"
    android:foreground="?attr/selectableItemBackground"

    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="0dp">

        <ImageView
            android:id="@+id/imageView"
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:scaleType="centerCrop"
            app:srcCompat="@drawable/top_banner" />

        <TextView
            android:id="@+id/nameTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignBottom="@+id/imageView"
            android:layout_alignParentLeft="true"
            android:maxLines="1"
            android:padding="5dp"
            android:text="Item"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <ImageView
            android:visibility="gone"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_alignParentRight="true"
            android:layout_alignParentTop="true"
            android:layout_marginTop="-16dp"
            android:layout_marginRight="-10dp"
            android:background="@drawable/circle" />

        <TextView
            android:visibility="gone"
            android:id="@+id/subtitleTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignParentTop="true"
            android:drawablePadding="2dp"
            android:gravity="center"
            android:text="300K"
            android:textStyle="bold|italic"
            android:maxLines="1"
            android:ellipsize="end"
            android:maxLength="5"
            android:padding="5dp"
            android:textColor="@color/white"
            android:textAppearance="@style/TextAppearance.AppCompat.Body1"
            android:textSize="12sp" />

    </RelativeLayout>

</androidx.cardview.widget.CardView>