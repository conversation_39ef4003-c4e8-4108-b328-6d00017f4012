<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="itemViewState"
            type="com.hqt.view.ui.reward.ui.state.PromotionItemState" />
    </data>

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:layout_marginRight="15dp"
        android:layout_marginLeft="15dp"
        app:cardElevation="0dp"
        app:cardCornerRadius="5dp"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_gravity="center"
            android:padding="0dp">

            <LinearLayout
                android:layout_weight="10"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <androidx.cardview.widget.CardView
                    android:layout_width="0dp"
                    android:layout_weight="7"
                    android:layout_height="wrap_content"
                    app:cardElevation="0dp"
                    app:cardCornerRadius="5dp">

                    <ImageView
                        android:id="@+id/banner"
                        android:layout_width="match_parent"
                        android:layout_height="150dp"
                        android:contentDescription="@string/txt_chat"
                        android:scaleType="centerCrop"
                        app:srcCompat="@drawable/top_banner" />
                </androidx.cardview.widget.CardView>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_weight="3"
                    android:gravity="bottom|center_horizontal"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <com.hqt.util.AspectRatioImageView
                        android:id="@+id/logo"
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:layout_marginBottom="20dp"
                        android:background="@drawable/corner_full_boder"
                        android:gravity="center_vertical|center_horizontal" />

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/getVoucher"
                        android:padding="10dp"
                        android:background="@drawable/button_gradient"
                        android:textColor="@color/white"
                        android:textSize="10sp"
                        android:layout_width="wrap_content"
                        android:layout_height="30dp"
                        android:textStyle="bold"
                        android:gravity="center_vertical|center_horizontal"
                        android:text="Lấy mã" />
                </LinearLayout>
            </LinearLayout>

            <TextView
                android:id="@+id/txtName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingStart="15dp"
                android:layout_marginTop="10dp"
                android:maxLines="1"
                tools:text="Item"
                android:text="@{itemViewState.item.name}"
                android:ellipsize="end"
                android:textStyle="bold"
                android:textAppearance="@style/TextAppearance.AppCompat.Body1" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingStart="15dp"
                android:gravity="center_vertical|center_horizontal">

                <com.mikepenz.iconics.view.IconicsImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:paddingRight="5dp"
                    app:iiv_color="@color/google_yellow"
                    app:iiv_icon="gmd_stars" />

                <TextView
                    android:id="@+id/txtPoint"
                    android:padding="5dp"
                    android:textStyle="bold"
                    style="@style/Text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="12sp"
                    android:text="@{itemViewState.point}"
                    tools:text="0 điểm" />
            </LinearLayout>


        </LinearLayout>

    </androidx.cardview.widget.CardView>
</layout>