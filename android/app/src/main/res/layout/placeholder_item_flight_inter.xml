<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.cardview.widget.CardView
        android:id="@+id/card_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/corner_full"
        app:cardCornerRadius="2dp"
        app:cardElevation="0dp"
        style="@style/CardViewStyle.Light"
        android:layout_marginBottom="@dimen/cardMarginVertical"
        android:layout_marginLeft="@dimen/cardMarginHorizontal"
        android:layout_marginRight="@dimen/cardMarginHorizontal"
        android:layout_marginTop="@dimen/cardMarginVertical"
        app:cardPreventCornerOverlap="false">

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:padding="5dp"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content">

                <View

                    android:layout_width="match_parent"
                    android:layout_height="15dp"
                    android:background="#dddddd" />

            </LinearLayout>


            <LinearLayout
                android:background="@drawable/background_selector"
                android:layout_width="match_parent"
                android:gravity="center_horizontal|center_vertical"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:padding="5dp">

                <ImageView
                    android:id="@+id/logo"
                    android:layout_width="50dp"
                    android:layout_height="50dp"
                    android:background="#dddddd" />

                <View
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:layout_marginRight="5dp"
                    android:layout_marginLeft="5dp"
                    android:background="@drawable/gradientdiv" />

                <LinearLayout
                    android:id="@+id/main"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:paddingBottom="2dp">

                        <View
                            android:id="@+id/title"
                            android:layout_width="match_parent"
                            android:layout_height="15dp"
                            android:background="#dddddd" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content">

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:background="#dddddd" />

                    </LinearLayout>


                </LinearLayout>

                <View
                    android:layout_marginLeft="2dp"
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="right|center_vertical"
                    android:background="#dddddd" />

            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>
</FrameLayout>