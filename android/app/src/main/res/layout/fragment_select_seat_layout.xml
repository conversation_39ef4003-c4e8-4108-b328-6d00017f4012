<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:fab="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewModel"
            type="com.hqt.viewmodel.BookingViewModel" />

        <import type="com.hqt.datvemaybay.Common" />

        <import type="android.view.View" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout

        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/gbgray"
        android:id="@+id/coordinatorLayout"
        android:animateLayoutChanges="true">


        <LinearLayout
            android:id="@+id/linearLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/gbgray"
            android:orientation="vertical"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <com.facebook.shimmer.ShimmerFrameLayout
                android:id="@+id/shimmer_view_container"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:orientation="vertical"
                android:visibility="visible"
                app:shimmer_duration="800">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="horizontal"
                    android:visibility="visible"
                    android:weightSum="10">


                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="8"
                        android:orientation="vertical">

                        <include layout="@layout/placeholder_seat_layout" />

                        <include layout="@layout/placeholder_seat_layout" />

                        <include layout="@layout/placeholder_seat_layout" />

                        <include layout="@layout/placeholder_seat_layout" />

                        <include layout="@layout/placeholder_seat_layout" />

                        <include layout="@layout/placeholder_seat_layout" />

                        <include layout="@layout/placeholder_seat_layout" />


                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="2"
                        android:background="@color/white"
                        android:gravity="center_horizontal"
                        android:orientation="vertical"
                        android:paddingTop="10dp">


                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/ic_airplane"
                            android:rotation="45" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:singleLine="true"
                            android:textColor="#003A6F"
                            android:textStyle="bold"
                            tools:text="A321" />

                        <ImageView

                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:layout_marginTop="10dp"
                            android:background="@drawable/ic_seat" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:textColor="#003A6F"
                            android:textStyle="bold"
                            tools:text="A" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:singleLine="true"
                            android:textColor="#da281c"
                            tools:text="0d" />


                        <TextView

                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="20dp"
                            android:layout_marginBottom="10dp"
                            android:background="@drawable/code_rounded"
                            android:padding="5dp"
                            android:text="        " />

                        <View
                            android:layout_width="2dp"
                            android:layout_height="50dp"
                            android:background="@color/primary" />


                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:padding="2dp"
                            android:rotation="90"
                            android:src="@drawable/outbound" />

                        <View

                            android:layout_width="2dp"
                            android:layout_height="50dp"
                            android:background="@color/primary" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:background="@drawable/code_rounded"
                            android:padding="5dp"
                            android:text="       " />


                    </LinearLayout>
                </LinearLayout>
            </com.facebook.shimmer.ShimmerFrameLayout>

            <LinearLayout
                android:id="@+id/emptyState"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center_vertical|center_horizontal"
                android:orientation="vertical"
                android:visibility="visible">

                <com.hqt.util.AspectRatioImageView
                    android:layout_width="match_parent"
                    android:layout_height="200dp"
                    android:layout_margin="20dp"
                    android:contentDescription="@string/no_seat"
                    android:scaleType="centerInside"
                    android:src="@drawable/plane_seat_empty" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="Chưa hỗ trợ chọn ghế với chuyến bay này!" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="Vui lòng liên hệ tổng đài để hỗ trợ" />

                <Button

                    android:id="@+id/btnBack"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:paddingStart="40dp"
                    android:paddingEnd="40dp"
                    android:text="@string/btn_back"
                    android:textColor="#FFFFFF"
                    app:backgroundTint="@color/btnColor" />


            </LinearLayout>

            <LinearLayout
                android:id="@+id/content"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:visibility="visible"
                android:weightSum="10">

                <androidx.core.widget.NestedScrollView

                    android:id="@+id/data_scrollView"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="8"
                    app:layout_behavior="@string/hide_bottom_view_on_scroll_behavior">

                    <LinearLayout

                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="10dp"
                        android:orientation="vertical">


                        <com.google.android.flexbox.FlexboxLayout

                            android:id="@+id/flex_box_seat_title"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:alignContent="center"
                            app:alignItems="center"
                            app:flexWrap="wrap"
                            app:justifyContent="flex_start"
                            app:showDivider="beginning|middle">


                        </com.google.android.flexbox.FlexboxLayout>

                        <com.google.android.flexbox.FlexboxLayout

                            android:id="@+id/flex_box_container"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:alignContent="center"
                            app:alignItems="center"
                            app:flexWrap="wrap"
                            app:justifyContent="space_around"
                            app:showDivider="beginning|middle">


                        </com.google.android.flexbox.FlexboxLayout>


                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/recyclerView"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content" />
                    </LinearLayout>


                </androidx.core.widget.NestedScrollView>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="2"
                    android:background="@color/white"
                    android:gravity="center_horizontal"
                    android:orientation="vertical"
                    android:paddingTop="10dp">


                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/ic_airplane"
                        android:rotation="45" />

                    <TextView
                        android:id="@+id/airCraft"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:singleLine="true"
                        android:textColor="#003A6F"
                        android:textStyle="bold"
                        tools:text="A321" />

                    <ImageView

                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:background="@drawable/ic_seat" />

                    <TextView
                        android:id="@+id/currentSeatSelecte"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:textColor="#003A6F"
                        android:textStyle="bold"
                        tools:text="10A" />

                    <TextView
                        android:id="@+id/seatPrice"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:singleLine="true"
                        android:textColor="#da281c"
                        tools:text="90000k" />


                    <TextView

                        android:id="@+id/originCode"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:layout_marginBottom="10dp"
                        android:background="@drawable/code_rounded"
                        android:padding="5dp"
                        android:text="SGN" />

                    <View
                        android:layout_width="2dp"
                        android:layout_height="50dp"
                        android:background="@color/primary" />


                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:padding="2dp"
                        android:rotation="90"
                        android:src="@drawable/outbound" />

                    <View

                        android:layout_width="2dp"
                        android:layout_height="50dp"
                        android:background="@color/primary" />

                    <TextView
                        android:id="@+id/destinationCode"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:background="@drawable/code_rounded"
                        android:padding="5dp"
                        android:text="SGN" />


                </LinearLayout>
            </LinearLayout>


        </LinearLayout>


        <FrameLayout
            android:id="@+id/bottomLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_anchor="@+id/linearLayout"
            app:layout_anchorGravity="center"
            app:layout_behavior="@string/hide_bottom_view_on_scroll_behavior">

            <LinearLayout
                android:id="@+id/bottom_sheet"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal|bottom"
                android:background="#FFFFFF"
                android:orientation="vertical"
                android:visibility="visible">

                <include
                    android:id="@+id/selectSeat"
                    layout="@layout/select_seat_bottom_layout" />

            </LinearLayout>
        </FrameLayout>


    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>