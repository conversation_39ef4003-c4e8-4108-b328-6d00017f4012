<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:bind="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewModel"
            type="com.hqt.viewmodel.BookingBusViewModel" />

        <import type="com.hqt.datvemaybay.Common" />

        <import type="android.view.View" />
    </data>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        tools:context="com.hqt.datvemaybay.PnrActivity"
        android:background="#F2F1F1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingBottom="20dp">

                <LinearLayout
                    android:id="@+id/tripContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical" />


            </LinearLayout>

            <androidx.cardview.widget.CardView
                android:layout_marginTop="-15dp"
                android:id="@+id/card_view1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="2dp"
                app:cardElevation="2dp"
                style="@style/CardViewStyle.Light"
                android:layout_marginBottom="5dp"
                android:layout_marginLeft="5dp"
                android:layout_marginRight="5dp"
                app:cardPreventCornerOverlap="false"
                app:contentPadding="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="10dp"
                    android:background="#FFFFFF"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_gravity="left"
                            android:textStyle="bold"
                            style="@style/Text"
                            android:text="@{viewModel.getInputPaxTitle()}" />

                    </LinearLayout>

                    <LinearLayout
                        android:orientation="vertical"
                        android:layout_width="match_parent"
                        android:id="@+id/paxInPut"
                        android:layout_height="wrap_content"
                        android:animateLayoutChanges="true"
                        android:layout_gravity="center_horizontal">

                        <ProgressBar
                            android:indeterminate="true"
                            android:id="@+id/load_bag"
                            android:layout_gravity="center_horizontal"
                            android:layout_width="20dp"
                            style="?android:attr/progressBarStyle"
                            android:layout_height="20dp" />

                    </LinearLayout>


                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:visibility="@{viewModel.isShowLogin() ? View.VISIBLE : View.GONE}"
                android:id="@+id/loginLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="2dp"
                app:cardElevation="2dp"
                style="@style/CardViewStyle.Light"
                android:layout_marginBottom="5dp"
                android:layout_marginLeft="5dp"
                android:layout_marginRight="5dp"
                app:cardPreventCornerOverlap="false"
                app:contentPadding="2dp">

                <LinearLayout
                    android:padding="10dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_horizontal"
                        android:padding="10dp">

                        <com.mikepenz.iconics.view.IconicsImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            app:iiv_size="20dp"
                            app:iiv_icon="gmd_account_circle" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_marginLeft="5dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@color/white"
                        android:padding="5dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textStyle="bold"
                            style="@style/Text"
                            android:text="@string/txt_not_login" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="12sp"
                            android:text="@string/txt_txt_not_login" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical|end"
                        android:padding="10dp">

                        <com.mikepenz.iconics.view.IconicsImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            app:iiv_color="@color/primary"
                            app:iiv_icon="gmd_keyboard_arrow_right" />
                    </LinearLayout>

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/card_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="2dp"
                app:cardElevation="2dp"
                style="@style/CardViewStyle.Light"
                android:layout_marginBottom="5dp"
                android:layout_marginLeft="5dp"
                android:layout_marginRight="5dp"
                app:cardPreventCornerOverlap="false"
                app:contentPadding="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="10dp"
                    android:background="#FFFFFF"
                    android:orientation="vertical">


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/textView8"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_gravity="left"
                            android:textStyle="bold"
                            style="@style/Text"
                            android:text="@{viewModel.getInputContactTitle()}" />


                    </LinearLayout>

                    <LinearLayout
                        android:visibility="@{viewModel.isShowBookingView() ? View.VISIBLE : View.GONE}"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <LinearLayout

                            android:layout_width="match_parent"
                            android:weightSum="5"
                            android:paddingBottom="5dp"
                            android:gravity="center_vertical"
                            android:layout_height="wrap_content">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_weight="2"
                                android:textColor="@color/textDark"
                                android:layout_height="wrap_content"
                                android:text="Liên hệ" />

                            <LinearLayout
                                android:layout_weight="3"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="right"
                                android:orientation="vertical">

                                <TextView
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@{viewModel.booking.contact_name}" />

                                <TextView
                                    android:textSize="12sp"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@{viewModel.booking.contact_phone +` - `+viewModel.booking.contact_email}" />
                            </LinearLayout>


                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:visibility="@{viewModel.isShowBookingView() ? View.GONE : View.VISIBLE}"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <LinearLayout

                                android:layout_width="match_parent"
                                android:weightSum="5"
                                android:paddingBottom="5dp"
                                android:layout_height="wrap_content">

                                <TextView
                                    android:id="@+id/textView1"
                                    android:layout_width="0dp"
                                    android:layout_weight="2"
                                    android:textColor="@color/textDark"
                                    android:layout_height="wrap_content"
                                    android:text="@string/hoVaTen" />

                                <EditText
                                    android:focusable="@{!viewModel.isShowBookingView()}"
                                    android:enabled="@{!viewModel.isShowBookingView()}"
                                    android:id="@+id/txtContactName"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="3"
                                    android:singleLine="true"
                                    android:paddingTop="2dp"
                                    android:paddingBottom="2dp"
                                    android:paddingRight="10dp"
                                    android:paddingLeft="10dp"
                                    android:inputType="textCapCharacters"
                                    android:text="@={viewModel.booking.contact_name}"
                                    android:hint="@string/txtNamehint"
                                    android:textColor="#000"
                                    android:background="@drawable/edit_text"
                                    android:ems="10">


                                </EditText>


                            </LinearLayout>
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:weightSum="5"
                            android:paddingBottom="5dp"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_weight="2"
                                    android:textColor="@color/textDark"
                                    android:layout_height="wrap_content"
                                    android:text="@string/soDienThoai" />

                                <EditText
                                    android:focusable="@{!viewModel.isShowBookingView()}"
                                    android:enabled="@{!viewModel.isShowBookingView()}"
                                    android:id="@+id/txtContactPhone"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="3"
                                    android:textColor="#000"
                                    android:paddingTop="2dp"
                                    android:paddingBottom="2dp"
                                    android:paddingRight="10dp"
                                    android:paddingLeft="10dp"
                                    android:inputType="phone"
                                    android:text="@={viewModel.booking.contact_phone}"
                                    android:background="@drawable/edit_text"
                                    android:ems="10" />

                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:weightSum="5"
                            android:paddingBottom="5dp"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_weight="2"
                                    android:textColor="@color/textDark"
                                    android:layout_height="wrap_content"
                                    android:text="@string/email" />

                                <EditText
                                    android:focusable="@{!viewModel.isShowBookingView()}"
                                    android:enabled="@{!viewModel.isShowBookingView()}"
                                    android:id="@+id/txtContactEmail"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="3"
                                    android:textColor="#000"
                                    android:paddingTop="2dp"
                                    android:paddingBottom="2dp"
                                    android:paddingRight="10dp"
                                    android:paddingLeft="10dp"
                                    android:inputType="textEmailAddress|textCapCharacters"
                                    android:text="@={viewModel.booking.contact_email}"
                                    android:hint="@string/txtEmailhint"
                                    android:background="@drawable/edit_text"
                                    android:ems="10" />

                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:paddingBottom="0dp"
                            android:weightSum="5"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <TextView
                                    android:id="@+id/textView2"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="2"
                                    android:text="@string/maGiamGia"
                                    android:textColor="@color/textDark" />

                                <EditText
                                    android:focusable="@{!viewModel.isShowBookingView()}"
                                    android:enabled="@{!viewModel.isShowBookingView()}"
                                    android:id="@+id/txtVoucherCode"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1.5"
                                    android:background="@drawable/edit_text"
                                    android:paddingTop="2dp"
                                    android:paddingBottom="2dp"
                                    android:inputType="textCapCharacters"
                                    android:paddingRight="10dp"
                                    android:paddingLeft="10dp"
                                    android:ems="10"
                                    android:textColor="#000"
                                    android:text="@={viewModel.booking.voucher}"
                                    android:layout_marginTop="2dp" />

                                <androidx.appcompat.widget.AppCompatButton
                                    android:layout_width="0dp"
                                    android:backgroundTint="#FF5959"
                                    android:textColor="#FFFFFF"
                                    android:layout_height="31dp"
                                    android:text="Áp dụng"
                                    android:padding="2dp"
                                    android:layout_weight="1.5"
                                    android:textSize="10sp"
                                    android:id="@+id/btnGetVoucher"
                                    style="@style/MyApp.Button.Big"
                                    android:layout_margin="2dp" />


                            </LinearLayout>

                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:paddingBottom="5dp">

                        <LinearLayout
                            android:visibility="@{viewModel.isShowBookingView() ? View.VISIBLE : View.GONE}"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:orientation="vertical">

                            <LinearLayout
                                android:id="@+id/showBookingStatus"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <LinearLayout
                                    android:id="@+id/waittingView"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:paddingTop="2dp"
                                    android:paddingBottom="5dp"
                                    android:orientation="vertical">

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:paddingBottom="2dp">

                                        <TextView
                                            android:textColor="@color/textDark"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="@string/maDonHang" />

                                        <TextView
                                            android:id="@+id/txtBookingId"
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:gravity="right"
                                            android:paddingRight="5dp"
                                            android:text="@{`#`+viewModel.booking.id}"
                                            android:textColor="#14a9e5"
                                            android:textStyle="bold" />

                                    </LinearLayout>

                                </LinearLayout>

                                <LinearLayout
                                    android:paddingTop="2dp"
                                    android:paddingBottom="2dp"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:orientation="vertical">

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:paddingBottom="5dp"
                                        android:layout_height="wrap_content">

                                        <TextView
                                            android:textColor="@color/textDark"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="Mã đặt chỗ (Code):" />


                                        <TextView
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:gravity="right"
                                            android:textColor="#ff0f0b"
                                            android:textSize="14sp"
                                            android:text="@{viewModel.booking.pnr}"
                                            android:textStyle="bold" />

                                    </LinearLayout>


                                </LinearLayout>

                                <LinearLayout
                                    android:paddingTop="2dp"
                                    android:paddingBottom="2dp"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:id="@+id/pnrReturnLayout"
                                    android:visibility="gone"
                                    android:orientation="vertical">

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:paddingBottom="2dp"
                                        android:layout_height="wrap_content">

                                        <TextView
                                            android:id="@+id/txtPnrReturn"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="Mã đặt chỗ lượt về:" />


                                        <TextView
                                            android:id="@+id/pnrReturn"
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:gravity="right"
                                            android:textColor="#ff0f0b"
                                            android:textSize="14sp"
                                            android:text="@{viewModel.booking.pnr_return}"
                                            android:textStyle="bold" />

                                    </LinearLayout>
                                </LinearLayout>

                                <LinearLayout

                                    android:layout_width="match_parent"
                                    android:paddingBottom="5dp"
                                    android:orientation="horizontal"
                                    android:layout_height="wrap_content">

                                    <TextView
                                        android:textColor="@color/textDark"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Trạng thái:" />

                                    <LinearLayout
                                        android:visibility="@{viewModel.isCreatedBooking() ? View.VISIBLE : View.GONE}"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="right"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:id="@+id/txtBookingIdd"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:paddingRight="5dp"
                                            android:text="Đang tiến hành đặt chỗ"
                                            android:textColor="#14a9e5" />

                                        <ProgressBar
                                            android:id="@+id/marker_progress"
                                            style="?android:attr/progressBarStyle"
                                            android:layout_width="20dp"
                                            android:layout_height="20dp"
                                            android:paddingRight="5dp"
                                            android:indeterminate="true" />
                                    </LinearLayout>

                                    <TextView
                                        android:visibility="@{viewModel.isCreatedBooking() ? View.GONE : View.VISIBLE}"
                                        android:text="@{viewModel.booking.status_text}"
                                        android:id="@+id/txtStatus"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="right"
                                        android:textColor="#FF0000"
                                        android:textStyle="bold" />

                                </LinearLayout>

                                <LinearLayout
                                    android:paddingTop="2dp"
                                    android:paddingBottom="5dp"
                                    android:visibility="@{viewModel.booking.payment.status ? View.VISIBLE : View.GONE}"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content">

                                    <TextView
                                        android:textColor="@color/textDark"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Hạn thanh toán:" />

                                    <TextView
                                        android:id="@+id/txtTimeLimit"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:gravity="right"
                                        android:textColor="#14a9e5"
                                        android:text="@{viewModel.getTimeLimitText()}"
                                        android:textStyle="bold" />

                                </LinearLayout>
                            </LinearLayout>
                        </LinearLayout>

                        <LinearLayout
                            android:paddingTop="2dp"
                            android:layout_width="match_parent"
                            android:paddingBottom="5dp"
                            android:layout_height="wrap_content">

                            <TextView
                                android:id="@+id/textViews3"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/textDark"
                                android:text="@string/giaVe" />

                            <TextView
                                android:textColor="#000000"
                                android:id="@+id/txtGiaVe"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="@{viewModel.formatTotal(viewModel.booking.grandTotal)}"
                                android:gravity="right" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:paddingBottom="2dp"
                            android:visibility="gone"
                            android:id="@+id/bagLayout"
                            android:layout_height="wrap_content">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/textDark"
                                android:text="@string/hanhLy" />

                            <TextView
                                android:id="@+id/txtbagFee"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="#FF0000"
                                android:gravity="right" />

                        </LinearLayout>

                        <LinearLayout
                            android:visibility="@{viewModel.booking.discount > 0 ? View.VISIBLE : View.GONE}"
                            android:paddingTop="2dp"
                            android:layout_width="match_parent"
                            android:paddingBottom="5dp"
                            android:id="@+id/voucherLayout"
                            android:layout_height="wrap_content">

                            <TextView
                                android:layout_width="wrap_content"
                                android:textColor="@color/textDark"
                                android:layout_height="wrap_content"
                                android:text="@string/giamGia" />

                            <TextView
                                android:id="@+id/txtdiscount"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textColor="#FF0000"
                                android:text="@{viewModel.discountText}"
                                android:gravity="right" />

                        </LinearLayout>


                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_margin="2dp"
                            android:background="@color/diver_color" />

                        <LinearLayout
                            android:paddingTop="2dp"
                            android:layout_width="match_parent"
                            android:paddingBottom="2dp"
                            android:layout_height="wrap_content">

                            <TextView
                                android:id="@+id/textView3"
                                android:layout_width="wrap_content"
                                style="@style/Text"
                                android:layout_height="wrap_content"
                                android:textStyle="bold"
                                android:text="@string/tongCong" />


                            <TextView
                                android:id="@+id/txtGiaTongCong"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="right"
                                android:text="@{viewModel.formatTotal(viewModel.booking.grandTotal-viewModel.booking.discount)}"
                                android:textColor="#FF0000"
                                android:textStyle="bold" />

                        </LinearLayout>

                        <View
                            android:layout_width="0dp"
                            android:layout_height="0dp"
                            android:id="@+id/got_it" />

                        <LinearLayout
                            bind:visibility="@{viewModel.booking.getRewardPointTotal() > 0}"
                            android:id="@+id/layoutPointReward"
                            android:layout_width="match_parent"
                            android:paddingBottom="2dp"
                            android:gravity="end"
                            android:layout_height="wrap_content">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="end"
                                android:textColor="@color/primary"
                                android:text="Nhận" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="end"
                                android:textColor="@color/primary"

                                android:paddingEnd="5dp"
                                android:paddingStart="5dp"
                                android:text="@{viewModel.booking.getRewardPointTotal() +``}" />

                            <com.mikepenz.iconics.view.IconicsTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/primary"
                                android:text="điểm {gmd_stars} " />

                        </LinearLayout>

                    </LinearLayout>

                    <androidx.appcompat.widget.AppCompatButton
                        android:layout_marginTop="5dp"
                        android:layout_marginBottom="5dp"
                        android:layout_marginLeft="25dp"
                        android:layout_marginRight="25dp"
                        android:padding="10dp"
                        android:id="@+id/btnBookVe"
                        android:background="@drawable/button_gradient"
                        android:textColor="@color/white"
                        android:layout_width="match_parent"
                        android:textAlignment="gravity"
                        android:textSize="16sp"
                        android:layout_height="wrap_content"
                        android:gravity="center_horizontal|center_vertical"
                        android:text="@{viewModel.getBookButtonText()}" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <LinearLayout
                bind:visibility="@{viewModel.isShowPayment()}"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <include layout="@layout/payment_new_layout" />
            </LinearLayout>
        </LinearLayout>

    </androidx.core.widget.NestedScrollView>
</layout>