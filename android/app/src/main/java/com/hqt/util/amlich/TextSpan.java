package com.hqt.util.amlich;

import android.content.res.Resources;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Typeface;
import android.text.TextPaint;
import android.text.style.LineBackgroundSpan;

/**
 * Created by NT on 4/15/2016.
 */
public class TextSpan implements LineBackgroundSpan {

    /**
     * Default text used
     */
    public static final String DEFAULT_text = "";

    private final String text;
    private final int color;

    /**
     * Create a span to draw a dot using default text and color
     *
     * @see #TextSpan(String, int)
     * @see #DEFAULT_text
     */
    public TextSpan() {
        this.text = DEFAULT_text;
        this.color = 0;
    }

    /**
     * Create a span to draw a dot using a specified color
     *
     * @param color color of the dot
     * @see #TextSpan(String, int)
     * @see #DEFAULT_text
     */
    public TextSpan(int color) {
        this.text = DEFAULT_text;
        this.color = color;
    }

    /**
     * Create a span to draw a dot using a specified text
     *
     * @param text text for the dot
     * @see #TextSpan(String, int)
     */
    public TextSpan(String text) {
        this.text = text;
        this.color = 0;
    }

    /**
     * Create a span to draw a dot using a specified text and color
     *
     * @param text  text for the dot
     * @param color color of the dot
     */
    public TextSpan(String text, int color) {
        this.text = text;
        this.color = color;
    }

    @Override
    public void drawBackground(
            Canvas canvas, Paint paint,
            int left, int right, int top, int baseline, int bottom,
            CharSequence charSequence,
            int start, int end, int lineNum
    ) {
        TextPaint p = new TextPaint(TextPaint.ANTI_ALIAS_FLAG);
        int oldColor = paint.getColor();
        if (color != 0) {
            paint.setColor(color);
            p.setColor(color);
        }
        float MYTEXTSIZE = 11.0f;
// Get the screen's density scale
        final float scale = Resources.getSystem().getDisplayMetrics().density;
// Convert the dps to pixels, based on density scale

        float tz = (int) (MYTEXTSIZE * scale + 0.1f);
        p.setTextAlign(Paint.Align.CENTER);
        p.setTypeface(Typeface.SERIF);
        p.setTextSize(tz);


        //canvas.drawCircle((left + right) / 2, bottom + text, text, paint);
        canvas.drawText(text, (left + right) / 2, bottom + tz / 2, p);
        paint.setColor(oldColor);
    }
}
