package com.hqt.util.tableview;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Color;
import android.net.Uri;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;


import com.evrencoskun.tableview.adapter.recyclerview.holder.AbstractViewHolder;
import com.hqt.datvemaybay.Common;
import com.hqt.datvemaybay.R;
import com.hqt.util.AppConfigs;
import com.hqt.view.ui.priceboard.MonthViewActivity;

/**
 * Created by evrencoskun on 23/10/2017.
 */

public class CellViewHolder extends AbstractViewHolder {
    @NonNull
    public final TextView cell_textview;
    @NonNull
    private final LinearLayout cell_container;
    private final LinearLayout cell_background;
    private final LinearLayout cell_price_background;
    private Cell mCell = null;

    public CellViewHolder(@NonNull View itemView) {
        super(itemView);
        cell_textview = itemView.findViewById(R.id.cell_data);
        cell_container = itemView.findViewById(R.id.cell_container);
        cell_background = itemView.findViewById(R.id.cell_background);
        cell_price_background = itemView.findViewById(R.id.cell_price_background);

        itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                try {

                    String date = Common.dateToString(mCell.getData().getDate(), "yyyyMM");
                    String link = "https://12bay.vn/san-ve-re/" + mCell.getData().getOrigin() + "/" + mCell.getData().getDestination() + "/" + date + "/" + date;
                    Intent i2 = Common.ConvertLinkAction(view.getContext(), Uri.parse(link));

                    Intent i3 = new Intent(view.getContext(), MonthViewActivity.class);
                    i3.putExtra("origin", mCell.getData().getOrigin());
                    i3.putExtra("destination", mCell.getData().getDestination());
                    i3.putExtra("monthName", date);

                    view.getContext().startActivity(i2);
                    //   ((Activity) view.getContext()).overridePendingTransition(R.anim.enter, R.anim.exit);

                } catch (Exception e) {
                    e.printStackTrace();
                    AppConfigs.logException(e);
                }
            }
        });
    }

    public void setCellBackground(@Nullable Cell cell) {
        mCell = cell;
        try {

            String[] pos = cell.getId().split("-");
            int col = Integer.parseInt(pos[0]);
            int row = Integer.parseInt(pos[1]);
            cell_background.setBackgroundResource(R.drawable.cell_background_off);

            if (col % 2 != 0) {
                cell_background.setBackgroundResource(R.drawable.cell_background_on);
            }

            if (row % 2 != 0) {
                cell_background.setBackgroundResource(R.drawable.cell_background_on);
            }
            if (col % 2 != 0 && row % 2 != 0) {
                cell_background.setBackgroundResource(R.drawable.cell_background_hight_light);
            }
        } catch (Exception e) {
            AppConfigs.logException(e);
        }
    }

    public void setCell(@Nullable Cell cell) {
        setCellBackground(cell);

        cell_textview.setText(cell.getData().getShortPrice());
        cell_price_background.setBackgroundResource(cell.getData().getPriceColor());
        // It is necessary to remeasure itself.
        cell_container.getLayoutParams().width = LinearLayout.LayoutParams.WRAP_CONTENT;
        cell_textview.requestLayout();
    }
}