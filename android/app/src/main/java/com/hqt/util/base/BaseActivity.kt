package com.hqt.util.base


import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.inputmethod.InputMethodManager
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.LayoutRes
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.auth.FirebaseAuth
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.ThanhToan
import com.hqt.util.AppConfigs
import com.hqt.util.Log
import com.hqt.view.ui.search.ui.activity.SearchActivityV2


abstract class BaseActivity<DB : ViewDataBinding> : AppCompatActivity() {

    val autoDisposable by lazy {
        AutoDisposable()
    }


    lateinit var firebaseAnalytics: FirebaseAnalytics
    private var mAuthListener: FirebaseAuth.AuthStateListener? = null
    private var auth: FirebaseAuth? = null
    private lateinit var mToolbar: Toolbar

    @LayoutRes
    abstract fun getLayoutRes(): Int

//    @Inject
//    var myAppPreferences: ApplicationModule? = null

    val binding by lazy {
        DataBindingUtil.setContentView(this, getLayoutRes()) as DB
    }







    /**
     * If you want to inject Dependency Injection
     * on your activity, you can override this.
     */
    open fun onInject() {


    }

    open fun onInit() {


    }


    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        val inflater = menuInflater
        inflater.inflate(R.menu.main, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean { // Take appropriate action for each action item click
        when (item.itemId) {
            android.R.id.home -> return true
            R.id.action_search -> {
//                val intent = Intent(this, SearchActivity::class.java)
                val intent = Intent(this, SearchActivityV2::class.java)
                startActivity(intent)
                return true
            }

            R.id.thanhToan -> {
                val in2 = Intent(this, ThanhToan::class.java)
                startActivity(in2)
                return true
            }

            else -> {
            }
        }
        return false
    }

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        Log.d("className", this.javaClass.simpleName)

        setContentView(binding.root)
        setUpToolbar()
        onInject()
        onInit()
        firebaseAuthInit()


        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                onBackPressCall()
            }
        })


    }

    private fun setUpToolbar() {
        try {

            mToolbar = binding.root.findViewById(R.id.toolbar)
            setSupportActionBar(mToolbar)
            mToolbar.setNavigationIcon(R.drawable.ic_action_back_home)
            supportActionBar!!.setDisplayShowHomeEnabled(true)
            mToolbar.bringToFront()
            mToolbar.setNavigationOnClickListener {
                if (isTaskRoot) {
//                    val `in` = Intent(applicationContext, MainActivity::class.java)
//                    startActivity(`in`)
//                    finish()
                } else {
                    finish()
                }
            }


        } catch (e: Exception) {
            // e.printStackTrace()
        }
    }

    private fun firebaseAuthInit() {
        try {
            firebaseAnalytics = FirebaseAnalytics.getInstance(this)
            auth = FirebaseAuth.getInstance()
            auth!!.firebaseAuthSettings.setAppVerificationDisabledForTesting(true)


            mAuthListener = FirebaseAuth.AuthStateListener { firebaseAuth ->
                val user = firebaseAuth.currentUser
                if (user != null) {
                    Common.commonSave(
                        applicationContext,
                        user.displayName,
                        user.email,
                        user.uid,
                        if (user.photoUrl == null) "" else user.photoUrl!!.toString(),
                        ""
                    )
                    firebaseAnalytics.setUserId(user.uid)
                } else { // User is signed out
                    AppConfigs.Log("onAuthStateChanged", "onAuthStateChanged:signed_out")
                }
            }
        } catch (e: Exception) {
            AppConfigs.logException(e)
        }

    }

    fun getToolbar(): Toolbar? {
        if (::mToolbar.isInitialized) {
            return mToolbar
        }
        return null
    }

    open fun requestPermissionResult(isGranted: Boolean) {

    }

    val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission(),
    ) { isGranted: Boolean ->
        if (isGranted) {
            // FCM SDK (and your app) can post notifications.

        } else {
            // TODO: Inform user that that your app will not show notifications.
        }
        requestPermissionResult(isGranted)
    }


    /**
     * Runtime Broadcast receiver inner class to capture internet connectivity events
     */





    override fun onDestroy() {
        super.onDestroy()
    }

    open fun onBackPressCall() {
        finish()
    }


    private fun hideKeyboardOnTouch(event: MotionEvent): Boolean {
        if (event.action == MotionEvent.ACTION_DOWN) {
            val v = currentFocus
            if (v is View && isTouchOutsideEditText(v, event)) {
                // Ẩn bàn phím nếu người dùng chạm vào vùng bên ngoài bàn phím
                hideKeyboard(v)
                return true // Trả về true để chỉ định rằng sự kiện đã được xử lý
            }
        }
        return false
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        super.dispatchTouchEvent(ev)

        // Kiểm tra sự kiện chạm và ẩn bàn phím nếu cần
        if (ev?.action == MotionEvent.ACTION_DOWN) {
            val v = currentFocus
            if (v is View && isTouchOutsideEditText(v, ev)) {
                // Ẩn bàn phím nếu người dùng chạm vào vùng bên ngoài bàn phím
                hideKeyboard(v)
                return true // Trả về true để chỉ định rằng sự kiện đã được xử lý
            }
        }
        return false
    }

    private fun isTouchOutsideEditText(view: View, event: MotionEvent): Boolean {
        // Kiểm tra xem sự kiện chạm có nằm trong vùng EditText không
        val location = intArrayOf(0, 0)
        view.getLocationOnScreen(location)
        val x = event.rawX + view.left - location[0]
        val y = event.rawY + view.top - location[1]
        return (x < view.left || x > view.right || y < view.top || y > view.bottom)
    }

    private fun hideKeyboard(view: View) {
        // Ẩn bàn phím
        val inputMethodManager =
            getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        inputMethodManager.hideSoftInputFromWindow(view.windowToken, 0)
    }
}





