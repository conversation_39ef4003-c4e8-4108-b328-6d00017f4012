package com.hqt.util.base

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.LayoutRes
import androidx.appcompat.widget.Toolbar
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.fragment.app.Fragment
import com.hqt.datvemaybay.R
import com.hqt.view.ui.BaseActivityKt


abstract class BaseFragment<DB : ViewDataBinding>() : Fragment() {
    open lateinit var binding: DB

    open fun init(inflater: LayoutInflater, container: ViewGroup?) {
        binding = DataBindingUtil.inflate(inflater, getLayoutRes(), container, false)
    }

    open fun init() {}

    @LayoutRes
    abstract fun getLayoutRes(): Int

    open fun onInject() {}


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        //NEED OVERRIDE FRAGMENT IN BASE FRAGMENT
        init(inflater, container)
        init()
        super.onCreateView(inflater, container, savedInstanceState)

        return binding.root
    }

    open fun refresh() {}
    fun getToolbar(): Toolbar? {
        try {
            if (binding.root.findViewById<Toolbar>(R.id.toolbar) != null) {
                return binding.root.findViewById(R.id.toolbar)
            }
            val aToolbar = (activity as BaseActivityKt<*>).getToolbar()

            if (aToolbar != null) {
                return aToolbar
            }

        } catch (e: Exception) {

            return null;
        }
        return null;
    }


}
