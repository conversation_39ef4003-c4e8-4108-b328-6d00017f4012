package com.hqt.util

import android.graphics.Color
import com.hqt.view.ui.flighthistory.data.model.ColorByAlt
import kotlin.math.roundToInt

object FlightMapUtils {
    fun getAltitudeColor(mAltitude: Int): Int {
        var altitude = (mAltitude * 3.2808f).roundToInt()
        //in metet
        var h = 0
        var s = 85
        var l = 50
        var hpoints = ArrayList<ColorByAlt>()
        hpoints.add(ColorByAlt(2000, 20))
        hpoints.add(ColorByAlt(10000, 140))
        hpoints.add(ColorByAlt(40000, 300))
        h = hpoints[0].value
        for (i in hpoints.size - 1 downTo 0 step 1) {
            if (altitude > hpoints[i].alt) {
                if (i == hpoints.size - 1) {
                    h = hpoints[i].value
                } else {
                    h = hpoints[i].value + (hpoints[i + 1].value - hpoints[i].value) * (altitude - hpoints[i].alt) / (hpoints[i + 1].alt - hpoints[i].alt)
                }
                break
            }
        }

        if (h < 0) {
            h = (h % 360) + 360
        } else if (h >= 360) {
            h %= 360
        }

        if (s < 5) s = 5
        else if (s > 95) s = 95

        if (l < 5) l = 5
        else if (l > 95) l = 95

        return Color.HSVToColor(floatArrayOf(h + 0f, s + 0f, l + 0f))
    }
}