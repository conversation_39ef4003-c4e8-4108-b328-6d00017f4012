package com.hqt.util

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View

class DurationView(context: Context?, attrs: AttributeSet?) : View(context, attrs) {
    private val paintColor = Color.GRAY
    private var drawPaint: Paint? = null
    private var stopList: ArrayList<Int> = ArrayList()

    private fun setupPaint() {
        drawPaint = Paint()
        drawPaint!!.color = paintColor
        drawPaint!!.isAntiAlias = true
        drawPaint!!.strokeWidth = measuredWidthAndState.toFloat()
        drawPaint!!.style = Paint.Style.FILL
        drawPaint!!.strokeJoin = Paint.Join.ROUND
        drawPaint!!.strokeCap = Paint.Cap.ROUND
        drawPaint!!.strokeWidth = 3f
    }

    @SuppressLint("DrawAllocation") override fun onDraw(canvas: Canvas) {

        canvas.drawLine(0f, 8f, measuredWidth.toFloat(), 8f, drawPaint!!)
        canvas.drawCircle(5f, 8f, 5f, drawPaint!!)
        canvas.drawCircle(measuredWidth.toFloat() - 5f, 8f, 5f, drawPaint!!)

        if (stopList.size > 0) {
            val sum = stopList.sumBy { it }
            var i = 0
            var length = 0f
            stopList.forEach {
                length = length + (it.div(sum.toFloat())) * measuredWidth
                if (i < stopList.size - 1) canvas.drawCircle(length.toFloat(), 8f, 5f, drawPaint!!)
                i++
            }
        }
    }

    fun drawDuration(stops: ArrayList<Int>) {

        stopList = stops
        postInvalidate()

    }

    init {
        isFocusable = true
        isFocusableInTouchMode = true
        setupPaint()
    }
}