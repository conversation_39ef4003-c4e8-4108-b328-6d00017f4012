package com.hqt.util

import android.view.View
import com.hqt.data.model.Passenger
import com.hqt.data.model.PassengerType
import com.hqt.view.ui.booking.data.model.PassengerV2
import com.prolificinteractive.materialcalendarview.CalendarDay
import org.threeten.bp.LocalDate
import java.util.Calendar

object Helper {

    fun View.clickWithDebounce(debounceTime: Long, onClick: (View) -> Unit) {
        try {
            setOnClickListener {
                if (isClickable) {
                    isClickable = false
                    onClick(it)
                    it.postDelayed({
                        isClickable = true
                    }, debounceTime)
                }
            }
        } catch (e: Exception) {

        }
    }

    @JvmStatic
    fun convertToCalendarDay(date: Calendar): CalendarDay {
        return CalendarDay.from(
            LocalDate.of(
                date[Calendar.YEAR], date[Calendar.MONTH] + 1, date[Calendar.DATE]
            )
        )
    }

    enum class ValidatorType(val text: String) {
        REQUIRED("required"), FIRSTNAME("firstName"), LASTNAME("lastName"), BIRTDATE("birthDate"), IDNUMBER(
            "idNumber"
        ),
        FULLNAME(
            "fullName"
        ),
        EMAIL("email"), PHONE("phone")
    }

    @JvmStatic
    fun isInputRequired(
        passenger: Passenger,
        validatorType: ValidatorType,
        isTrain: Boolean
    ): Boolean {

        if (isTrain) {
            when (validatorType) {
                ValidatorType.FIRSTNAME -> return true
                ValidatorType.LASTNAME -> return true
                ValidatorType.BIRTDATE -> return true
                ValidatorType.IDNUMBER -> {
                    return !(passenger.type == PassengerType.CHILD || passenger.type == PassengerType.STUDENT || passenger.type == PassengerType.INFANT)

                }

                else -> false
            }
        } else {
            if (passenger.type == PassengerType.INFANT) {
                when (validatorType) {
                    ValidatorType.BIRTDATE -> return true
                    else -> false
                }
            }
            when (validatorType) {
                ValidatorType.FIRSTNAME -> return true
                ValidatorType.LASTNAME -> return true
                ValidatorType.FULLNAME -> return true
                ValidatorType.BIRTDATE -> return false
                ValidatorType.IDNUMBER -> return false
                ValidatorType.REQUIRED -> return true
                ValidatorType.EMAIL -> return false
                ValidatorType.PHONE -> return false
                else -> false
            }
        }
        return true
    }
    @JvmStatic
    fun isInputRequired(
        passenger: PassengerV2,
        validatorType: ValidatorType,
        isTrain: Boolean
    ): Boolean {

        if (isTrain) {
            when (validatorType) {
                ValidatorType.FIRSTNAME -> return true
                ValidatorType.LASTNAME -> return true
                ValidatorType.BIRTDATE -> return true
                ValidatorType.IDNUMBER -> {
                    return !(passenger.type == com.hqt.view.ui.booking.data.model.PassengerType.CHILD || passenger.type == com.hqt.view.ui.booking.data.model.PassengerType.STUDENT || passenger.type == com.hqt.view.ui.booking.data.model.PassengerType.INFANT)

                }

                else -> false
            }
        } else {
            if (passenger.type == com.hqt.view.ui.booking.data.model.PassengerType.INFANT) {
                when (validatorType) {
                    ValidatorType.BIRTDATE -> return true
                    else -> false
                }
            }
            when (validatorType) {
                ValidatorType.FIRSTNAME -> return true
                ValidatorType.LASTNAME -> return true
                ValidatorType.FULLNAME -> return true
                ValidatorType.BIRTDATE -> return false
                ValidatorType.IDNUMBER -> return false
                ValidatorType.REQUIRED -> return true
                ValidatorType.EMAIL -> return false
                ValidatorType.PHONE -> return false
                else -> false
            }
        }
        return true
    }

    @JvmStatic
    fun validatorInput(text: String, validateType: String): String? {

        if (text.isEmpty()) {
            if (validateType.contains(ValidatorType.LASTNAME.text)) {
                return "Vui lòng nhập họ (Nguyen)"
            } else if (validateType.contains(ValidatorType.FIRSTNAME.text)) {
                return "Vui lòng nhập tên đệm và tên (Anh Tuan)"
            } else if (validateType.contains(ValidatorType.FULLNAME.text)) {
                return "Vui lòng nhập họ và tên (vd: Nguyễn Văn A)"
            } else if (validateType.contains(ValidatorType.REQUIRED.text)) {
                return "Vui lòng nhập thông tin"
            } else if (validateType.contains(ValidatorType.IDNUMBER.text)) {
                return "Vui lòng nhập thông tin"
            } else if (validateType.contains(ValidatorType.BIRTDATE.text)) {
                return "Vui lòng nhập ngày sinh"
            }

        } else if (!("[A-Za-z ]+".toRegex().matches(text))) {
            if (!("[A-Za-z0-9 ]+".toRegex().matches(text))) {
                if (validateType.contains(ValidatorType.IDNUMBER.text)) {
                    return "Vui lòng nhập đúng định dạng số giấy tờ trên CMND hoặc Passport"
                }

            } else {
                if (validateType.contains(ValidatorType.IDNUMBER.text)) {
                    return null
                }
            }


            if (validateType.contains(ValidatorType.FULLNAME.text)) {
                return if (!("[\\p{L}\\s\\-.',]+".toRegex().matches(text))) {
                    "Vui lòng nhập đúng định dạng họ và tên (vd: Nguyễn Văn A)"
                } else {
                    null
                }
            }



            if (validateType.contains(ValidatorType.FIRSTNAME.text)) {
                return "Họ không dấu (Vd: Nguyen)"
            } else if (validateType.contains(ValidatorType.LASTNAME.text)) {
                return "Tên đệm và tên không dấu (Anh Tuan)"
            } else if (validateType.contains(ValidatorType.BIRTDATE.text)) {
                return null
            } else if (validateType.contains(ValidatorType.PHONE.text)) {
                if (!("^\\+?\\d+(\\d+)*\$".toRegex().matches(text))) {
                    return "Vui lòng nhập đúng định dạng số điện thoại 0909123123"
                }
                return null
            } else if (validateType.contains(ValidatorType.EMAIL.text)) {
                if (!("^([a-zA-Z0-9!#\$%&'*+\\/=?^_`{|}~-]+(?:\\.[a-zA-Z0-9!#\$%&'*+\\/=?^_`{|}~-]+)*@(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?\\.)+[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?)\$".toRegex()
                        .matches(text))
                ) {
                    return "Vui lòng nhập đúng định dạng <EMAIL>"
                }
                return null
            } else {
                return "Vui lòng chỉ nhập chữ cái abc"
            }

        }

        return null
    }
}