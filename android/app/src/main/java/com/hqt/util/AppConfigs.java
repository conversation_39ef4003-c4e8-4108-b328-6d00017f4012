package com.hqt.util;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Bundle;

import com.google.firebase.analytics.FirebaseAnalytics;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.google.firebase.remoteconfig.FirebaseRemoteConfig;

import java.util.HashMap;

import javax.inject.Singleton;

@Singleton
public class AppConfigs {
    private static AppConfigs _instance;
    private static FirebaseAnalytics analytics;
    private FirebaseRemoteConfig config;
    private FirebaseCrashlytics crashlytics;
    public static final Boolean DSVNAPI = false;

    private AppConfigs() {

    }

    public FirebaseCrashlytics Crashlytic() {
        return crashlytics;
    }

    public static void logException(Throwable throwable) {
        try {
            FirebaseCrashlytics.getInstance().recordException(throwable);
            FirebaseAuth auth = FirebaseAuth.getInstance();

            if (auth.getUid() != null && !auth.getUid().isEmpty()) {
                FirebaseCrashlytics.getInstance().setUserId(auth.getUid());
            }

        } catch (Exception e) {
            FirebaseCrashlytics.getInstance().recordException(e);


        }
    }

    public static void logException(Throwable throwable, String data) {
        try {
            FirebaseCrashlytics.getInstance().recordException(throwable);
            FirebaseCrashlytics.getInstance().log(data);
            FirebaseAuth auth = FirebaseAuth.getInstance();

            if (auth.getUid() != null && !auth.getUid().isEmpty()) {
                FirebaseCrashlytics.getInstance().setUserId(auth.getUid());
            }

        } catch (Exception e) {
            FirebaseCrashlytics.getInstance().recordException(e);


        }
    }

    public static void logMessage(String message, String uid) {
        try {
            FirebaseCrashlytics.getInstance().log(message);
            FirebaseCrashlytics.getInstance().setUserId(uid);

        } catch (Exception e) {
            FirebaseCrashlytics.getInstance().recordException(e);
        }
    }

    public static void logMessage(String message) {
        try {
            FirebaseCrashlytics.getInstance().log(message);

        } catch (Exception e) {
            FirebaseCrashlytics.getInstance().recordException(e);
        }
    }


    public void initAnalytic(Context context) {
        analytics = FirebaseAnalytics.getInstance(context);

    }

    public static void analyticScreenView(String screen_name) {
        try {
            Bundle bundle = new Bundle();
            bundle.putString(FirebaseAnalytics.Param.SCREEN_NAME, screen_name);
            analytics.logEvent(FirebaseAnalytics.Event.SCREEN_VIEW, bundle);

        } catch (Exception e) {
            FirebaseCrashlytics.getInstance().recordException(e);

        }
    }

    public static void logAnalyticEvent(String event_name, Bundle bundle) {
        try {
            analytics.logEvent(event_name, bundle);

        } catch (Exception e) {
            FirebaseCrashlytics.getInstance().recordException(e);

        }
    }

    public FirebaseRemoteConfig getConfig() {
        return this.config;
    }

    public void setConfig(FirebaseRemoteConfig config) {
        this.config = config;
    }

    public static AppConfigs getInstance() {
        if (_instance == null) {
            _instance = new AppConfigs();
        }
        return _instance;
    }

    public static void Log(String Event, String Message) {
        Log.d(Event, Message);
    }

    public static void setStringLocalCache(Context mContext, String key, String value) {

        SharedPreferences settings = mContext.getSharedPreferences("12BAY-APP-CONFIG", 0);
        SharedPreferences.Editor editor = settings.edit();
        editor.putString(key, value);
        editor.apply();
    }

    public static String getStringLocalCache(Context mContext, String key) {
        SharedPreferences settings = mContext.getSharedPreferences("12BAY-APP-CONFIG", 0);
        return settings.getString(key, "");
    }

    public static HashMap getHeaderRequest() {

        HashMap headers = new HashMap();
        headers.put("Content-Type", "application/json");
        headers.put("X-Auth-ID", "9B1B13952BD9FF446AB569BBB49B3");
        headers.put("Origin", "https://12bay.vn");
        headers.put("Referer", "https://12bay.vn");
        headers.put("X-Requested-With", "XMLHttpRequest");

        return headers;
    }

    public static enum SystemSettingType {
        INTERNET,
        USER,
        BOOKING,
        ERROR
    }

    ;
}