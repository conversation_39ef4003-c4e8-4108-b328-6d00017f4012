package com.hqt.util.amlich;

/**
 * Created by NT on 4/17/2016.
 */

import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.widget.Button;
import android.widget.CompoundButton;
import android.widget.LinearLayout;
import android.widget.Switch;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.coordinatorlayout.widget.CoordinatorLayout;

import com.android.volley.AuthFailureError;
import com.android.volley.Request;
import com.android.volley.Response;
import com.android.volley.VolleyError;
import com.android.volley.toolbox.JsonObjectRequest;
import com.hqt.datvemaybay.BuildConfig;
import com.hqt.datvemaybay.Common;
import com.hqt.datvemaybay.R;
import com.hqt.util.AppConfigs;
import com.hqt.util.AppController;
import com.hqt.util.Log;
import com.hqt.view.ui.BaseActivity;
import com.prolificinteractive.materialcalendarview.CalendarDay;
import com.prolificinteractive.materialcalendarview.MaterialCalendarView;
import com.prolificinteractive.materialcalendarview.OnDateSelectedListener;
import com.prolificinteractive.materialcalendarview.OnMonthChangedListener;
import com.prolificinteractive.materialcalendarview.OnRangeSelectedListener;
import com.prolificinteractive.materialcalendarview.format.ArrayWeekDayFormatter;
import com.prolificinteractive.materialcalendarview.format.MonthArrayTitleFormatter;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.threeten.bp.LocalDate;
import org.threeten.bp.Month;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Shows off the most basic usage
 */
public class AmLich extends BaseActivity implements OnDateSelectedListener, OnMonthChangedListener {


    Button btnOk;
    Calendar returnDate;
    Calendar birthDay;
    Calendar depDate;
    Calendar reDate;
    int inId = 0;
    Boolean onRangeSelect = false;
    Boolean onChange = false;
    List<CalendarDay> listRangeDateSelect = new ArrayList<>();
    Boolean isReturn = false;
    String txtDestination = null;
    String txtOrigin = null;
    LinearLayout showCheapSwitch;
    Switch swCheap;
    CoordinatorLayout coordinatorLayout;

    MaterialCalendarView widget;

    Boolean isShowLunarDates = true;
    String loadedMonth = "";
    String act = "";

    SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");

    @Override
    public int getLayoutId() {
        return R.layout.activity_calendar;
    }

    ;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        getToolbar().setTitle("Chọn ngày khởi hành");

        getToolbar().setNavigationIcon(R.drawable.ic_action_back_home);


        showCheapSwitch = findViewById(R.id.txtShowCheap);
        swCheap = findViewById(R.id.cheapSearch);

        coordinatorLayout = findViewById(R.id.coordinatorLayout);
        widget = findViewById(R.id.calendarView);


        Intent in = getIntent();
        String txtdepDate = in.getStringExtra("depDate");

        if (in.hasExtra("origin")) txtOrigin = in.getStringExtra("origin");
        if (in.hasExtra("destination")) txtDestination = in.getStringExtra("destination");

        String txtreDate = in.getStringExtra("reDate");
        act = in.getStringExtra("act");
        inId = in.getIntExtra("id", 0);
        boolean isSearchTrain = in.getBooleanExtra("isSearchTrain", false);
        btnOk = findViewById(R.id.btnOk);
        btnOk.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                onBackPressed();
            }
        });

        if ((txtOrigin != null && txtOrigin.length() == 3) && (txtDestination != null && txtDestination.length() == 3) && !isSearchTrain) {
            if (showCheapSwitch != null) showCheapSwitch.setVisibility(View.VISIBLE);
            swCheap.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton compoundButton, boolean b) {
                    if (b) {
                        getMonthPrices();
                    } else {
                        isShowLunarDates = true;
                        widget.removeDecorators();
                        loadedMonth = "";
                        widget.setShowLunarDates(isShowLunarDates);
                        widget.state().edit().commit();
                    }
                }
            });

        } else {
            if (showCheapSwitch != null) showCheapSwitch.setVisibility(View.GONE);
        }


        if (act != null) {
            if (act.equals("RANGEDATE")) {
                String txtdepDateEnd = in.getStringExtra("depDateEnd");
                onRangeSelect = true;
                Calendar startDate = Common.getDateFromString(txtdepDate);
                Calendar endDate = Common.getDateFromString(txtdepDateEnd);
                getToolbar().setTitle("Chọn khoảng thời gian đi");
                genCalendarRangeSelect(startDate, endDate);

            } else {
                birthDay = Common.stringToDate(txtdepDate, "yyyy-MM-dd");
                showCheapSwitch.setVisibility(View.GONE);
                Calendar minDate = Common.stringToDate(in.getStringExtra("minDate"), "yyyy-MM-dd");
                genCalendarBirthDate(minDate, birthDay);
                getToolbar().setTitle("Chọn ngày sinh em bé");
            }

        } else if (txtreDate == null) {
            depDate = Common.getDateFromString(txtdepDate);
            genCalendar(Calendar.getInstance(), depDate);
            getToolbar().setTitle("Chọn ngày đi");


        } else {
            depDate = Common.getDateFromString(txtdepDate);
            reDate = Common.getDateFromString(txtreDate);

            getToolbar().setTitle("Chọn ngày về");
            genCalendar(depDate, reDate, depDate);

        }

    }

    public void getMonthPrices() {

        CalendarDay curentMonth = widget.getCurrentDate();
        String month = Common.dateToString(convertToDate(curentMonth.getDate()).getTime(), "yyyyMM");
        if (loadedMonth.contains(month)) {
            AppConfigs.Log("loaded", loadedMonth);
        } else {
            loadedMonth = loadedMonth + "-" + month;
            String tag_json_obj = "json_month_prices";
            String url = AppConfigs.getInstance().getConfig().getString("root_api") + "/api/v1/AirLines/PricesBoard";

            JSONObject postParam = new JSONObject();
            JSONArray route = new JSONArray();
            route.put(txtOrigin + txtDestination);

            try {
                postParam.put("startDate", month + "01");
                postParam.put("endDate", month + "31");
                postParam.put("maxPrice", "0");
                postParam.put("minPrice", "10000");
                postParam.put("routes", route);
                postParam.put("source", "ANDROID");
                postParam.put("type", "date");
                postParam.put("key", Common.getKeyHash());
                postParam.put("ver", BuildConfig.VERSION_CODE + "");

            } catch (JSONException e) {

            }

            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            logCurlRequest(url, postParam, headers);


            AppConfigs.Log("postjson ", postParam.toString());
            AppConfigs.Log("url ", url);
            JsonObjectRequest jsonObjReq = new JsonObjectRequest(Request.Method.POST,
                    url, postParam,
                    response -> {

                        try {


                            if (!response.isNull("data")) {
                                JSONObject data = response.getJSONObject("data");
                                JSONObject fullData = data.getJSONObject("full");
                                String route1 = txtOrigin + txtDestination;


                                if (!fullData.isNull(route1) && fullData.length() > 0) {
                                    addMonthPricesDecord(fullData.getJSONArray(route1));
                                }
                            }
                        } catch (JSONException e) {
                            AppConfigs.logException(e);
                            e.printStackTrace();
                        }
                    }, new Response.ErrorListener() {

                @Override
                public void onErrorResponse(VolleyError e) {
                    AppConfigs.logException(e);
                    AppConfigs.Log("TAG", e.getMessage());
                }
            }) {
                /**
                 * Passing some request headers*
                 */
                @Override
                public Map getHeaders() throws AuthFailureError {
                    return AppConfigs.getHeaderRequest();
                }
            };

            // Adding request to request queue
            AppController.getInstance().addToRequestQueue(jsonObjReq, tag_json_obj);
        }
    }

    private void logCurlRequest(String url, JSONObject body, Map<String, String> headers) {
        StringBuilder curlCmd = new StringBuilder("curl -X POST ");

        // Append headers
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            curlCmd.append("-H \"").append(entry.getKey()).append(": ").append(entry.getValue()).append("\" ");
        }

        // Append body
        if (body != null) {
            curlCmd.append("-d '").append(body.toString()).append("' ");
        }

        // Append URL
        curlCmd.append("\"").append(url).append("\"");

        Log.d("CURL_LOG", curlCmd.toString());
    }


    public void genCalendar(Calendar minDate, Calendar selectedDate) {

        returnDate = selectedDate;
        widget.setOnDateChangedListener(this);
        widget.setShowOtherDates(MaterialCalendarView.SHOW_OUT_OF_RANGE);

        Calendar maxDate = Calendar.getInstance();
        maxDate.add(Calendar.MONTH, 18);
        maxDate.set(maxDate.get(Calendar.YEAR), maxDate.get(Calendar.MONTH), maxDate.get(Calendar.DATE));

        widget.setSelectedDate(CalendarDay.from(selectedDate.get(Calendar.YEAR), selectedDate.get(Calendar.MONTH) + 1, selectedDate.get(Calendar.DATE)));


        widget.setWeekDayFormatter(new ArrayWeekDayFormatter(getResources().getTextArray(R.array.custom_weekdays)));
        widget.setTitleFormatter(new MonthArrayTitleFormatter(getResources().getTextArray(R.array.custom_months)));
        widget.setSelectionMode(MaterialCalendarView.SELECTION_MODE_SINGLE);
        widget.setDateTextAppearance(R.style.CustomDayTextAppearance);
        widget.setCurrentDate(convertToCalendarDay(selectedDate));
        widget.setOnMonthChangedListener(this);
        widget.setShowLunarDates(true);
        widget.state().edit().isCacheCalendarPositionEnabled(true)
                .setMinimumDate(LocalDate.now())
                .setMaximumDate(convertToCalendarDay(maxDate))
                .commit();
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DATE, 1);

        //addMonthPrcesDecord(selectedDate);

    }

    public CalendarDay convertToCalendarDay(Calendar date) {
        return CalendarDay.from(LocalDate.of(date.get(Calendar.YEAR), date.get(Calendar.MONTH) + 1, date.get(Calendar.DATE)));
    }

    public void genCalendarBirthDate(Calendar minDate, Calendar birthDay) {

        widget.setOnDateChangedListener(this);
        widget.setShowOtherDates(MaterialCalendarView.SHOW_OUT_OF_RANGE);
        Calendar maxDate = Calendar.getInstance();
        maxDate.set(minDate.get(Calendar.YEAR) + 2, minDate.get(Calendar.MONTH), minDate.get(Calendar.DATE));
        if (birthDay != null) {
            widget.setSelectedDate(convertToCalendarDay(birthDay));
        }

        widget.setWeekDayFormatter(new ArrayWeekDayFormatter(getResources().getTextArray(R.array.custom_weekdays)));
        widget.setTitleFormatter(new MonthArrayTitleFormatter(getResources().getTextArray(R.array.custom_months)));
        widget.setSelectionMode(MaterialCalendarView.SELECTION_MODE_SINGLE);
        widget.setDateTextAppearance(R.style.CustomDayTextAppearance);
        widget.setOnMonthChangedListener(this);
        widget.setCurrentDate(convertToCalendarDay(birthDay));
        widget.setShowLunarDates(true);
        widget.state().edit().isCacheCalendarPositionEnabled(true)
                .setMinimumDate(convertToCalendarDay(minDate))
                .setMaximumDate(convertToCalendarDay(maxDate))
                .commit();

        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DATE, 1);


    }

    public void genCalendar(Calendar minDate, Calendar selectedDate, Calendar departDate) {

        returnDate = selectedDate;

        final LocalDate ca = LocalDate.now();
        final LocalDate min = LocalDate.of(ca.getYear(), Month.JANUARY, 1);
        final LocalDate max = LocalDate.of(ca.getYear() + 1, Month.DECEMBER, 31);

        widget.setOnDateChangedListener(this);
        widget.setShowOtherDates(MaterialCalendarView.SHOW_OUT_OF_RANGE);
        Calendar maxDate = Calendar.getInstance();
        maxDate.add(Calendar.MONTH, 18);
        maxDate.set(maxDate.get(Calendar.YEAR), maxDate.get(Calendar.MONTH), maxDate.get(Calendar.DATE));

        widget.setWeekDayFormatter(new ArrayWeekDayFormatter(getResources().getTextArray(R.array.custom_weekdays)));
        widget.setTitleFormatter(new MonthArrayTitleFormatter(getResources().getTextArray(R.array.custom_months)));
        widget.setDateTextAppearance(R.style.CustomDayTextAppearance);
        widget.setCurrentDate(convertToCalendarDay(selectedDate));
        widget.setSelectionMode(MaterialCalendarView.SELECTION_MODE_RANGE);
        widget.setOnMonthChangedListener(this);
        widget.setSelectedDate(convertToCalendarDay(selectedDate));
        widget.setShowLunarDates(true);
        widget.setSelectionColor(getResources().getColor(R.color.primary));
        widget.selectRange(convertToCalendarDay(departDate), convertToCalendarDay(selectedDate));
        widget.state().edit().isCacheCalendarPositionEnabled(true)
                .setMinimumDate(LocalDate.of(minDate.get(Calendar.YEAR), minDate.get(Calendar.MONTH) + 1, minDate.get(Calendar.DATE)))
                .setMaximumDate(LocalDate.of(maxDate.get(Calendar.YEAR), maxDate.get(Calendar.MONTH) + 1, maxDate.get(Calendar.DATE)))
                .commit();


        OneDayDecorator oneDayDecorator = new OneDayDecorator(this);
        oneDayDecorator.setDate(Calendar.getInstance().getTime());
        widget.addDecorator(new EventDecorator(this.getResources().getDrawable(R.drawable.max_select_date_background), convertToCalendarDay(selectedDate)));
        widget.addDecorator(new EventDecorator(this.getResources().getDrawable(R.drawable.min_select_date_background), convertToCalendarDay(departDate)));

    }

    public void genCalendarRangeSelect(Calendar startDate, Calendar endDate) {

        final LocalDate ca = LocalDate.now();
        final LocalDate min = LocalDate.of(ca.getYear(), Month.JANUARY, 1);
        final LocalDate max = LocalDate.of(ca.getYear() + 1, Month.DECEMBER, 31);
        Calendar now = Calendar.getInstance();
        Calendar maxDate = Calendar.getInstance();
        maxDate.add(Calendar.MONTH, 18);
        maxDate.set(maxDate.get(Calendar.YEAR), maxDate.get(Calendar.MONTH), maxDate.get(Calendar.DATE));

        widget.setOnRangeSelectedListener(new OnRangeSelectedListener() {
            @Override
            public void onRangeSelected(@NonNull MaterialCalendarView widget, @NonNull List<CalendarDay> dates) {
                if (dates.size() <= 15) {
                    listRangeDateSelect = dates;
                    onChange = true;

                } else {
                    Toast.makeText(getApplicationContext(), "Khoảng thời gian tối đa không quá 15 ngày", Toast.LENGTH_SHORT).show();
                }
            }
        });
        widget.setOnDateChangedListener(new OnDateSelectedListener() {
            @Override
            public void onDateSelected(@NonNull MaterialCalendarView widget, @NonNull CalendarDay date, boolean selected) {

            }
        });

        widget.setShowOtherDates(MaterialCalendarView.SHOW_OUT_OF_RANGE);
        widget.setWeekDayFormatter(new ArrayWeekDayFormatter(getResources().getTextArray(R.array.custom_weekdays)));
        widget.setTitleFormatter(new MonthArrayTitleFormatter(getResources().getTextArray(R.array.custom_months)));
        widget.setDateTextAppearance(R.style.CustomDayTextAppearance);
        widget.setCurrentDate(convertToCalendarDay(now));
        widget.setSelectionMode(MaterialCalendarView.SELECTION_MODE_RANGE);
        widget.setOnMonthChangedListener(this);

        widget.setShowLunarDates(true);
        widget.setSelectionColor(getResources().getColor(R.color.primary));
        widget.state().edit().isCacheCalendarPositionEnabled(true)
                .setMinimumDate(LocalDate.of(now.get(Calendar.YEAR), now.get(Calendar.MONTH) + 1, now.get(Calendar.DATE)))
                .setMaximumDate(LocalDate.of(maxDate.get(Calendar.YEAR), maxDate.get(Calendar.MONTH) + 1, maxDate.get(Calendar.DATE)))
                .commit();

        widget.selectRange(convertToCalendarDay(startDate), convertToCalendarDay(endDate));
        widget.addDecorator(new EventDecorator(this.getResources().getDrawable(R.drawable.min_select_date_background), convertToCalendarDay(startDate)));
        widget.addDecorator(new EventDecorator(this.getResources().getDrawable(R.drawable.max_select_date_background), convertToCalendarDay(endDate)));

        Toast.makeText(this, "Chọn khoảng thời gian bạn có thể bay\nTối đa 15 ngày", Toast.LENGTH_SHORT).show();

    }

    public void addRangeSelectDate(Date startDate, Date endDate) {

        int days = (int) (getUnitBetweenDates(startDate, endDate, TimeUnit.DAYS));
        OneDayDecorator oneDayDecorator = new OneDayDecorator(this);

        for (int i = 0; i < days; i++) {
            Calendar c = Calendar.getInstance();
            c.setTime(startDate);
            c.add(Calendar.DATE, i);
            oneDayDecorator.setDate(c.getTime());
            if (c.getTime().before(endDate)) {
                widget.addDecorator(oneDayDecorator);
            }

        }

    }

    private static long getUnitBetweenDates(Date startDate, Date endDate, TimeUnit unit) {
        long timeDiff = endDate.getTime() - startDate.getTime();
        return unit.convert(timeDiff, TimeUnit.MILLISECONDS);
    }

    public void addMonthPricesDecord(JSONArray pricesData) {

        if (isShowLunarDates) {
            widget.setShowLunarDates(!isShowLunarDates);
            isShowLunarDates = false;
            widget.state().edit().commit();
        }
        ArrayList<TextDecorator> listDecor = new ArrayList<TextDecorator>();
        try {

            for (int i = 0; i < pricesData.length(); i++) {
                JSONObject day = pricesData.getJSONObject(i);


                String mCode = day.getString("mCode");
                int price = day.getInt("mPrice") / 1000;

                CalendarDay calDay = CalendarDay.from(Integer.valueOf(mCode.substring(7, 11)), Integer.valueOf(mCode.substring(11, 13)), Integer.valueOf(mCode.substring(13, 15)));
                if (CalendarDay.today().isBefore(calDay)) {
                    if (price < 200) {
                        listDecor.add((new TextDecorator(Color.parseColor("#2980b9"), calDay, String.valueOf(price) + "k")));
                    } else {
                        listDecor.add((new TextDecorator(Color.parseColor("#848383"), calDay, String.valueOf(price) + "k")));
                    }
                }
            }
            widget.addDecorators(listDecor);
        } catch (Exception e) {

            AppConfigs.logException(e);

        }

    }

    public Calendar convertToDate(LocalDate date) {
        Calendar d = Calendar.getInstance();
        d.set(date.getYear(), date.getMonthValue() - 1, date.getDayOfMonth());
        return d;
    }

    @Override
    public void onDateSelected(@NonNull MaterialCalendarView widget, @NonNull CalendarDay date, boolean selected) {

        onChange = true;
        if (reDate != null) {
            widget.selectRange(convertToCalendarDay(depDate), date);
        }

        returnDate = convertToDate(date.getDate());
        btnOk.setText("Chọn");
    }


    @Override
    public void onBackPressed() {
        try {
            if (!onChange) {
                finish();
            } else {
                if (onRangeSelect) {
                    if (!listRangeDateSelect.isEmpty()) {
                        Intent data = new Intent();
                        data.putExtra("date", dateFormat.format(convertToDate(listRangeDateSelect.get(0).getDate()).getTime()));
                        data.putExtra("dateEnd", dateFormat.format(convertToDate(listRangeDateSelect.get(listRangeDateSelect.size() - 1).getDate()).getTime()));
                        setResult(RESULT_OK, data);
                    }
                    finish();

                } else if (returnDate != null) {
                    Intent data = new Intent();
                    data.putExtra("date", dateFormat.format(returnDate.getTime()));
                    setResult(RESULT_OK, data);
                    finish();

                } else {
                    Toast.makeText(this, "Vui lòng chọn ngày sinh em bé", Toast.LENGTH_LONG).show();
                }
            }
        } catch (Exception e) {
            AppConfigs.logException(e);
        }
    }

    @Override
    public void onMonthChanged(MaterialCalendarView widget, CalendarDay date) {

        Calendar cal = Calendar.getInstance();
        cal.set(date.getYear(), date.getMonth(), 1);
        widget.setCurrentDate(date);
        if (swCheap.isChecked()) {
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    getMonthPrices();
                }
            }, 1000);

        }

    }

//    @Override
//    protected void attachBaseContext(Context newBase) {
//        super.attachBaseContext(IconicsContextWrapper.wrap(newBase));
//    }
}

/**
 * Simulate an API call to show how to add decorators
 */

