package com.hqt.util

import com.hqt.data.model.DatePrice
import com.hqt.data.model.WeeklyPriceData
import com.hqt.datvemaybay.Common
import org.json.JSONArray
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.*
import kotlin.collections.ArrayList

/**
 * Utility class for processing daily price data into weekly aggregates
 * for the Weekly Cheapest Ticket Price Chart feature
 */
object WeeklyPriceProcessor {
    
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.US)
    private val weekLabelFormat = SimpleDateFormat("yyyy-MM-dd", Locale.US)
    
    /**
     * Processes monthly price data from the API response into weekly aggregated data
     * @param pricesData JSONArray containing daily price data from getMonthPrices API
     * @param origin Origin airport code
     * @param destination Destination airport code
     * @return List of WeeklyPriceData representing cheapest prices per week
     */
    fun processMonthlyDataToWeekly(
        pricesData: JSONArray,
        origin: String,
        destination: String
    ): List<WeeklyPriceData> {
        try {
            // Convert JSONArray to list of DatePrice objects
            val dailyPrices = mutableListOf<DatePrice>()
            
            for (i in 0 until pricesData.length()) {
                val dayData = pricesData.getJSONObject(i)
                val mCode = dayData.getString("mCode")
                val price = dayData.getInt("mPrice")
                
                // Parse date from mCode (format: XXXXXXX_YYYYMMDD)
                val dateStr = mCode.substring(7) // Extract YYYYMMDD
                val date = Common.getDateTimeFromFormat(dateStr, "yyyyMMdd")
                
                val datePrice = DatePrice().apply {
                    this.origin = origin
                    this.destination = destination
                    this.price = price
                    this.date = date
                    this.code = mCode
                }
                
                dailyPrices.add(datePrice)
            }
            
            return aggregateToWeekly(dailyPrices)
            
        } catch (e: Exception) {
            AppConfigs.logException(e)
            return emptyList()
        }
    }
    
    /**
     * Aggregates daily price data into weekly data
     * @param dailyPrices List of DatePrice objects
     * @return List of WeeklyPriceData with cheapest price per week
     */
    private fun aggregateToWeekly(dailyPrices: List<DatePrice>): List<WeeklyPriceData> {
        if (dailyPrices.isEmpty()) return emptyList()
        
        // Group prices by week
        val weeklyGroups = mutableMapOf<String, MutableList<DatePrice>>()
        
        for (datePrice in dailyPrices) {
            datePrice.date?.let { date ->
                val weekStart = getWeekStart(date)
                val weekKey = weekLabelFormat.format(weekStart)
                
                weeklyGroups.getOrPut(weekKey) { mutableListOf() }.add(datePrice)
            }
        }
        
        // Convert groups to WeeklyPriceData
        val weeklyData = mutableListOf<WeeklyPriceData>()
        
        for ((weekKey, prices) in weeklyGroups) {
            if (prices.isNotEmpty()) {
                val weekStart = weekLabelFormat.parse(weekKey)!!
                val weekEnd = getWeekEnd(weekStart)
                val cheapestPrice = prices.minByOrNull { it.price }?.price ?: -1
                val origin = prices.first().origin
                val destination = prices.first().destination
                
                if (cheapestPrice > 0) {
                    weeklyData.add(
                        WeeklyPriceData(
                            weekStartDate = weekStart,
                            weekEndDate = weekEnd,
                            weekLabel = weekKey,
                            cheapestPrice = cheapestPrice,
                            origin = origin,
                            destination = destination
                        )
                    )
                }
            }
        }
        
        // Sort by week start date and mark cheapest in range
        val sortedData = weeklyData.sortedBy { it.weekStartDate }
        markCheapestInRange(sortedData)
        
        return sortedData
    }
    
    /**
     * Gets the start of the week (Monday) for a given date
     */
    private fun getWeekStart(date: Date): Date {
        val calendar = Calendar.getInstance()
        calendar.time = date
        calendar.firstDayOfWeek = Calendar.MONDAY
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY)
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        return calendar.time
    }
    
    /**
     * Gets the end of the week (Sunday) for a given week start date
     */
    private fun getWeekEnd(weekStart: Date): Date {
        val calendar = Calendar.getInstance()
        calendar.time = weekStart
        calendar.add(Calendar.DAY_OF_WEEK, 6) // Add 6 days to get Sunday
        calendar.set(Calendar.HOUR_OF_DAY, 23)
        calendar.set(Calendar.MINUTE, 59)
        calendar.set(Calendar.SECOND, 59)
        calendar.set(Calendar.MILLISECOND, 999)
        return calendar.time
    }
    
    /**
     * Marks the cheapest price in the entire range
     */
    private fun markCheapestInRange(weeklyData: List<WeeklyPriceData>) {
        if (weeklyData.isEmpty()) return
        
        val cheapestWeek = weeklyData.minByOrNull { it.cheapestPrice }
        cheapestWeek?.let { week ->
            // Find the index and update the isCheapestInRange flag
            val index = weeklyData.indexOf(week)
            if (index >= 0 && weeklyData is MutableList) {
                weeklyData[index] = week.copy(isCheapestInRange = true)
            }
        }
    }
    
    /**
     * Converts WeeklyPriceData to chart data format for williamchart library
     * @param weeklyData List of WeeklyPriceData
     * @return Pair of labels and values for chart
     */
    fun convertToChartData(weeklyData: List<WeeklyPriceData>): Pair<List<String>, List<Float>> {
        val labels = weeklyData.map { formatWeekLabel(it.weekStartDate) }
        val values = weeklyData.map { it.getPriceInThousands() }
        return Pair(labels, values)
    }
    
    /**
     * Formats week start date for chart labels
     */
    private fun formatWeekLabel(weekStart: Date): String {
        val calendar = Calendar.getInstance()
        calendar.time = weekStart
        val month = calendar.get(Calendar.MONTH) + 1
        val day = calendar.get(Calendar.DAY_OF_MONTH)
        return String.format("%02d/%02d", day, month)
    }
}
