package com.hqt.util.payment

import vn.payoo.model.Order
import vn.payoo.model.OrderConverter

class PayooConverter : OrderConverter {

    override fun <T> convert(data: T): Order? {
        val (checksum, orderInfo, cashAmount) = data as OrderResponse
        return Order(checksum, orderInfo, cashAmount)
    }

    companion object {
        fun create(): PayooConverter {
            return PayooConverter()
        }
    }
}
