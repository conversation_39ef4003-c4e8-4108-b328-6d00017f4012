package com.hqt.util.tableview;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.evrencoskun.tableview.filter.IFilterableModel;
import com.evrencoskun.tableview.sort.ISortableModel;
import com.hqt.data.model.DatePrice;

public class Cell implements ISortableModel, IFilterableModel {
    @NonNull
    private String mId;
    @NonNull
    private String mHeader;
    @Nullable
    private DatePrice mData;
    @NonNull
    private String mFilterKeyword;

    public Cell(@NonNull String id, @Nullable DatePrice data) {
        this.mId = id;
        this.mData = data;
        this.mFilterKeyword = String.valueOf(data);
    }

    public Cell(@NonNull String id, @Nullable String data) {
        this.mId = id;
        this.mHeader = data;
        this.mFilterKeyword = String.valueOf(data);
    }

    /**
     * This is necessary for sorting process.
     * See ISortableModel
     */
    @NonNull
    @Override
    public String getId() {
        return mId;
    }

    /**
     * This is necessary for sorting process.
     * See ISortableModel
     */
    @Nullable
    @Override
    public Object getContent() {
        return mData;
    }

    @Nullable
    public DatePrice getData() {
        return mData;
    }

    @Nullable
    public String getmHeader() {
        return mHeader;
    }

    public void setData(@Nullable DatePrice data) {
        mData = data;
    }

    @NonNull
    @Override
    public String getFilterableKeyword() {
        return mFilterKeyword;
    }
}