package com.hqt.util.amlich;

/**
 * Created by NT on 4/15/2016.
 */

import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.ShapeDrawable;
import android.graphics.drawable.StateListDrawable;
import android.graphics.drawable.shapes.OvalShape;

import androidx.appcompat.app.AppCompatActivity;

import com.hqt.datvemaybay.R;
import com.prolificinteractive.materialcalendarview.CalendarDay;
import com.prolificinteractive.materialcalendarview.DayViewDecorator;
import com.prolificinteractive.materialcalendarview.DayViewFacade;
import com.prolificinteractive.materialcalendarview.MaterialCalendarView;

import java.util.Date;

/**
 * Decorate a day by making the text big and bold
 */
public class OneDayDecorator implements DayViewDecorator {

    private CalendarDay date;

    private final Drawable drawable;

    public OneDayDecorator(AppCompatActivity context) {
        drawable = context.getResources().getDrawable(R.drawable.select_date_decor);
        date = CalendarDay.today();
    }


    @Override
    public boolean shouldDecorate(CalendarDay day) {
        return date != null && day.equals(date);
    }

    private static Drawable generateSelector() {
        StateListDrawable drawable = new StateListDrawable();
//        drawable.setExitFadeDuration(100);
//        drawable.addState(new int[]{android.R.attr.state_checked}, generateCircleDrawable(Color.RED));
//        drawable.addState(new int[]{android.R.attr.state_pressed}, generateCircleDrawable(Color.BLUE));
        drawable.addState(new int[]{}, generateCircleDrawable(Color.TRANSPARENT));
        return drawable;
    }

    @Override
    public void decorate(DayViewFacade view) {
        view.setSelectionDrawable(drawable);
    }

    private static Drawable generateCircleDrawable(final int color) {
        ShapeDrawable drawable = new ShapeDrawable(new OvalShape());
        drawable.getPaint().setColor(color);
        return drawable;
    }

    /**
     * We're changing the internals, so make sure to call {@linkplain MaterialCalendarView#invalidateDecorators()}
     */
    public void setDate(Date date) {

        this.date = CalendarDay.from(date.getYear(), date.getMonth() + 1, date.getDate());


    }

}