package com.hqt.util.tableview;

import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;


import com.evrencoskun.tableview.adapter.recyclerview.holder.AbstractViewHolder;
import com.hqt.datvemaybay.R;
import com.hqt.util.AppConfigs;


public class RowHeaderViewHolder extends AbstractViewHolder {
    @NonNull
    public final TextView row_header_textview;
    public final TextView row_header_textview_line_2;
    public final LinearLayout row_header_container;

    public RowHeaderViewHolder(@NonNull View itemView) {
        super(itemView);
        row_header_container = itemView.findViewById(R.id.row_header_container);
        row_header_textview = itemView.findViewById(R.id.row_header_textview);
        row_header_textview_line_2 = itemView.findViewById(R.id.row_header_textview_line_2);
    }

    public void setCellBackground(@Nullable Cell cell) {
        try {

            int row = Integer.parseInt(cell.getId());
            row_header_container.setBackgroundResource(R.drawable.cell_background_on);

            if (row % 2 != 0) {
                row_header_container.setBackgroundResource(R.drawable.cell_background_hight_light);
            }

        } catch (Exception e) {
            AppConfigs.logException(e);
            e.printStackTrace();
        }
    }

    public void setTextLine(RowHeader header) {
        try {
            String[] line = header.getmHeader().split("\\|");
            row_header_textview.setText(line[0]);
            row_header_textview_line_2.setText(line[1]);
            setCellBackground(header);
        } catch (Exception e) {
            row_header_textview.setText(header.getmHeader());
        }
    }
}