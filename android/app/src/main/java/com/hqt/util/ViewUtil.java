package com.hqt.util;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.text.Html;
import android.text.InputType;
import android.text.style.BackgroundColorSpan;
import android.text.style.ForegroundColorSpan;
import android.text.style.RelativeSizeSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.Transformation;
import android.view.animation.TranslateAnimation;
import android.widget.AbsListView;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.annotation.RawRes;
import androidx.databinding.DataBindingUtil;

import com.airbnb.lottie.LottieAnimationView;

import com.bumptech.glide.Glide;
import com.hqt.data.model.AddOnInfo;
import com.hqt.data.model.Baggage;
import com.hqt.data.model.Passenger;
import com.hqt.data.model.PaxInfoList;
import com.hqt.datvemaybay.Common;
import com.hqt.datvemaybay.R;
import com.hqt.data.model.Flight;
import com.hqt.datvemaybay.databinding.ListMealItemBinding;
import com.mikepenz.iconics.Iconics;

import org.json.JSONException;
import org.json.JSONObject;

import q.rorbin.badgeview.Badge;
import q.rorbin.badgeview.QBadgeView;


public final class ViewUtil {
    public static View genBagView(int pos, JSONObject bagInfo, Context mContext) {
        View v;
        LayoutInflater inflater = LayoutInflater.from(mContext);
        v = inflater.inflate(R.layout.list_bag_item, null);
        try {

            int value = bagInfo.getInt("value");
            int price = bagInfo.getInt("price");
            String priceText = Common.dinhDangTien(price);
            if (price < 1000) {
                priceText = "Miễn phí";
            }
            if (value == 0) {
                priceText = "Không thêm";
            }

            ((TextView) v.findViewById(R.id.txtBagValue)).setText(value + " kg");
            ((TextView) v.findViewById(R.id.pos)).setText(pos + "");
            ((TextView) v.findViewById(R.id.txtBagPrice)).setText(priceText);

        } catch (JSONException e) {
            e.printStackTrace();
        }

        return v;
    }

    public static View genMealView(int pos, AddOnInfo mealInfo, Context mContext) {
        View v;
        LayoutInflater inflater = LayoutInflater.from(mContext);
        v = inflater.inflate(R.layout.list_meal_item, null);

        ListMealItemBinding binding = DataBindingUtil.inflate(inflater,
                R.layout.list_meal_item,
                null,
                false);
        binding.setMeal(mealInfo);

        return v;
    }

    /**
     * @param view     view that will be animated
     * @param duration for how long in ms will it shake
     * @param offset   start offset of the animation
     * @return returns the same view with animation properties
     */
    public static View makeMeShake(View view, int duration, int offset) {
        Animation anim = new TranslateAnimation(-offset, offset, 0, 0);
        anim.setDuration(duration);
        anim.setRepeatMode(Animation.REVERSE);
        anim.setRepeatCount(5);
        view.startAnimation(anim);
        return view;
    }

    public static LinearLayout getPaxView(PaxInfoList pax_list, Context mContext) {
        //NEW ITEM PAX INPUT
        LinearLayout paxInPut;
        LinearLayout item;
        TextView spinItem;
        TextView paxNameView, txtPaxBag;
        String paxNameList = "";

        try {
            paxInPut = new LinearLayout(mContext);
            paxInPut.setOrientation(LinearLayout.VERTICAL);
            paxInPut.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT,
                    AbsListView.LayoutParams.WRAP_CONTENT));
            //ALDUT PAX INPUT
            for (int i = 0; i < pax_list.getAdult().size(); i++) {

                Passenger pax = pax_list.getAdult().get(i);

                Widget.showPaxList(
                        false,
                        pax,
                        false,
                        mContext,
                        paxInPut,
                        false
                );


            }
            for (int i = 0; i < pax_list.getChild().size(); i++) {

                Passenger pax = pax_list.getChild().get(i);

                Widget.showPaxList(
                        false,
                        pax,
                        false,
                        mContext,
                        paxInPut,
                        false
                );

            }
            for (int i = 0; i < pax_list.getInfant().size(); i++) {

                Passenger pax = pax_list.getInfant().get(i);
                Widget.showPaxList(
                        false,
                        pax,
                        false,
                        mContext,
                        paxInPut,
                        false
                );

            }

            for (int i = 0; i < pax_list.getStudent().size(); i++) {

                Passenger pax = pax_list.getStudent().get(i);
                Widget.showPaxList(
                        false,
                        pax,
                        false,
                        mContext,
                        paxInPut,
                        false
                );

            }
            for (int i = 0; i < pax_list.getOlder().size(); i++) {

                Passenger pax = pax_list.getOlder().get(i);
                Widget.showPaxList(
                        false,
                        pax,
                        false,
                        mContext,
                        paxInPut,
                        false
                );

            }
            return paxInPut;
        } catch (Exception e) {
            AppConfigs.logException(e);
            e.printStackTrace();
            return null;
        }
    }

    public static View bindFlightDataToView(Flight flightDetail, ViewGroup parent, Context mContext, Boolean isReturn, int adult, int child, int infant) {
        View v; // Creating an instance for View Object
        LayoutInflater inflater = LayoutInflater.from(mContext);
        v = inflater.inflate(R.layout.view_flight_detail, parent);

        ((TextView) v.findViewById(R.id.txtDiThoiGianBay)).setText(flightDetail.getDepartureDateTime().toString().substring(0, 5));
        ((TextView) v.findViewById(R.id.txtLuotdiTitle)).setText(Common.getAirPortName(flightDetail.getOriginCode(), true) + " → " + Common.getAirPortName(flightDetail.getDestinationCode(), true));
        ((TextView) v.findViewById(R.id.txtDiNgayBay)).setText(Common.getDayOfWeek(flightDetail.getDepartureDateTime().toString().substring(6, 16)));
        ((TextView) v.findViewById(R.id.txtDiNgayBayAmLich)).setText(Common.getLunarDate(Common.getDateFromString(flightDetail.getDepartureDateTime().toString().substring(6, 16)).getTime()));
        ((TextView) v.findViewById(R.id.txtDiFrom)).setText(flightDetail.getOriginCode());
        ((TextView) v.findViewById(R.id.txtDiTo)).setText(flightDetail.getDestinationCode());
        ((TextView) v.findViewById(R.id.txtDiThoiGianBay)).setText(flightDetail.getDepartureDateTime().toString().substring(0, 5));
        ((TextView) v.findViewById(R.id.txtDiThoiGianDen)).setText(flightDetail.getArriverDateTime().toString().substring(0, 5));
        ((TextView) v.findViewById(R.id.txtDiChuyenBay)).setText(flightDetail.getFlightNumber().toString());
        ((TextView) v.findViewById(R.id.txtDiDuration)).setText(flightDetail.getDuration().toString());
        ((TextView) v.findViewById(R.id.txtDiLoaiVe)).setText(flightDetail.getSeatClass().toString());
        TextView txtClassIconDi = v.findViewById(R.id.txtClassIconDi);

        if (flightDetail.getQuickDep()) {
            txtClassIconDi.setText(("{faw_history} "));
            new Iconics.Builder().style(new ForegroundColorSpan(mContext.getResources().getColor(R.color.google_yellow)), new BackgroundColorSpan(Color.TRANSPARENT), new RelativeSizeSpan(0.8f)).on(txtClassIconDi).build();
        } else if (flightDetail.getPromo()) {
            txtClassIconDi.setText(("{faw_star} "));
            new Iconics.Builder().style(new ForegroundColorSpan(mContext.getResources().getColor(R.color.google_yellow)), new BackgroundColorSpan(Color.TRANSPARENT), new RelativeSizeSpan(0.8f)).on(txtClassIconDi).build();
        }
        ((TextView) v.findViewById(R.id.txtDiHangBay)).setText(flightDetail.getProvider().toString());
        ((TextView) v.findViewById(R.id.txtDiDiemDung)).setText("{faw_circle} " + flightDetail.getStopsText().toString());
        new Iconics.Builder().style(new ForegroundColorSpan(flightDetail.getStopsText().toLowerCase().equals("bay thẳng") ? mContext.getResources().getColor(R.color.primary) : Color.RED), new BackgroundColorSpan(Color.TRANSPARENT), new RelativeSizeSpan(0.8f)).on(((TextView) v.findViewById(R.id.txtDiDiemDung))).build();

        Glide.with(mContext).load(flightDetail.getAirlinesLogo()).into(((ImageView) v.findViewById(R.id.diLogo)));

        int giaNguoiLon = flightDetail.getAdult();
        int giaTreEm = flightDetail.getChild();
        int giaEmBe = flightDetail.getInfant();

        // TONG CONG GIA VE
        int tongCong = adult * flightDetail.getAdult() + child * flightDetail.getChild() + infant * flightDetail.getInfant();

        ((TextView) v.findViewById(R.id.txtDiTongGia)).setText(Common.dinhDangTien(tongCong));
        ((TextView) v.findViewById(R.id.txtDiGiaNguoiLon)).setText(adult + " x " + Common.dinhDangTien(giaNguoiLon));
        ((TextView) v.findViewById(R.id.txtDiGiaTreem)).setText(child + " x " + Common.dinhDangTien(giaTreEm));
        ((TextView) v.findViewById(R.id.txtDiGiaEmBe)).setText(infant + " x " + Common.dinhDangTien(giaEmBe));

        LinearLayout viewTotalDi = v.findViewById(R.id.viewTotalDi);
        TextView txtDiTotalTile = v.findViewById(R.id.txtTotalTitle);
        txtDiTotalTile.setText(("Tổng giá vé lượt " + (isReturn ? "về" : "đi") + " +"));
        viewTotalDi.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                View vi = v.findViewById(R.id.viewTotalDiDetail);
                if (vi.getVisibility() == View.GONE) {
                    vi.setVisibility(View.VISIBLE);
                    txtDiTotalTile.setText(("Tổng giá vé lượt " + (isReturn ? "về" : "đi") + " -"));

                } else {
                    vi.setVisibility(View.GONE);
                    txtDiTotalTile.setText(("Tổng giá vé lượt " + (isReturn ? "về" : "đi") + " +"));
                }
            }
        });
        if (child <= 0) {
            (v.findViewById(R.id.diTreEm)).setVisibility(View.GONE);
        } else {
            (v.findViewById(R.id.diTreEm)).setVisibility(View.VISIBLE);
        }
        if (infant <= 0) {
            (v.findViewById(R.id.diEmbe)).setVisibility(View.GONE);
        } else {
            (v.findViewById(R.id.diEmbe)).setVisibility(View.VISIBLE);
        }

        return v;
    }

    public static void addBadgeAt(Context mContext, View view, String text) {
        try {
            if (view != null)
                new QBadgeView(mContext)
                        .setBadgeText(text)
                        .setGravityOffset(12, 2, true)
                        .bindTarget(view)
                        .setOnDragStateChangedListener(new Badge.OnDragStateChangedListener() {
                            @Override
                            public void onDragStateChanged(int dragState, Badge badge, View targetView) {
                                if (Badge.OnDragStateChangedListener.STATE_SUCCEED == dragState) {


                                }
                            }
                        });
        } catch (Exception e) {
            e.printStackTrace();
            AppConfigs.logException(e);

        }
    }

    public static void initEmptyState(View view, Context mContext, String textString, Drawable background, @RawRes int animationResId, EmptyStateCallBackInterface callBackInterface) {
        view.setVisibility(View.VISIBLE);

        LottieAnimationView animationView = view.findViewById(R.id.animation_view);
        ImageView emptyStateBackground = view.findViewById(R.id.emptyStateBackground);
        if (animationResId != -1) {

            animationView.setAnimation(animationResId);
            animationView.loop(true);
            animationView.playAnimation();
            animationView.setVisibility(View.VISIBLE);
            emptyStateBackground.setVisibility(View.GONE);
        } else {

            emptyStateBackground.setImageDrawable(background);
            emptyStateBackground.setVisibility(View.VISIBLE);
            animationView.setVisibility(View.GONE);
        }


        LinearLayout emptyStateLayout = view.findViewById(R.id.emptyStateLayout);
        emptyStateLayout.setVisibility(View.GONE);
        TextView emptyStateTitle = view.findViewById(R.id.emptyStateTitle);
        emptyStateTitle.setText(Html.fromHtml(textString));

        Button btnNegative = view.findViewById(R.id.btnNegative);
        Button btnPositive = view.findViewById(R.id.btnPositive);

        callBackInterface.negativeButton(btnNegative);
        callBackInterface.positiveButton(btnPositive);

    }

    public interface EmptyStateCallBackInterface {
        public void negativeButton(Button button);

        public void positiveButton(Button button);
    }

    public static void expand(final View v) {
        v.measure(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        final int targtetHeight = v.getMeasuredHeight();

        v.getLayoutParams().height = 0;
        v.setVisibility(View.VISIBLE);
        Animation a = new Animation() {
            @Override
            protected void applyTransformation(float interpolatedTime, Transformation t) {
                v.getLayoutParams().height = interpolatedTime == 1
                        ? ViewGroup.LayoutParams.WRAP_CONTENT
                        : (int) (targtetHeight * interpolatedTime);
                v.requestLayout();
            }

            @Override
            public boolean willChangeBounds() {
                return true;
            }
        };

        a.setDuration((int) (targtetHeight / v.getContext().getResources().getDisplayMetrics().density));
        v.startAnimation(a);
    }

    public static void collapse(final View v) {
        final int initialHeight = v.getMeasuredHeight();

        Animation a = new Animation() {
            @Override
            protected void applyTransformation(float interpolatedTime, Transformation t) {
                if (interpolatedTime == 1) {
                    v.setVisibility(View.GONE);
                } else {
                    v.getLayoutParams().height = initialHeight - (int) (initialHeight * interpolatedTime);
                    v.requestLayout();
                }
            }

            @Override
            public boolean willChangeBounds() {
                return true;
            }
        };

        a.setDuration((int) (initialHeight / v.getContext().getResources().getDisplayMetrics().density));
        v.startAnimation(a);
    }


}




