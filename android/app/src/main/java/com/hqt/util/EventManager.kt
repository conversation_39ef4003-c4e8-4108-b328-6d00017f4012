package com.hqt.util

import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow

object EventManager {

    enum class Event {
        SELECTED_TAB



    }

    private val _eventFlow = MutableSharedFlow<EventFlow>()
    val eventFlow = _eventFlow.asSharedFlow()

    suspend fun emitEvent(event: EventFlow) {
        _eventFlow.emit(event)
    }

    data class EventFlow(
        val event : Event? = null,
        val data : Any? = null


    )

}