/*
 * Copyright (c) 2016.
 * 12bay.vn  - 2012 - <EMAIL>
 */
package com.hqt.util

import android.app.Application
import android.text.TextUtils
import com.android.volley.Request
import com.android.volley.RequestQueue
import com.android.volley.toolbox.Volley
import com.google.android.gms.security.ProviderInstaller
import com.google.android.gms.tasks.OnCompleteListener
import com.google.firebase.FirebaseApp
import com.google.firebase.appcheck.FirebaseAppCheck
import com.google.firebase.appcheck.playintegrity.PlayIntegrityAppCheckProviderFactory
import com.google.firebase.installations.FirebaseInstallations
import com.google.firebase.messaging.FirebaseMessaging
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.google.firebase.remoteconfig.FirebaseRemoteConfigSettings
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.hqt.data.api.SSLAppService
import com.hqt.data.model.PassengerTitle
import com.hqt.data.model.PassengerType
import com.hqt.data.model.User
import com.hqt.datvemaybay.BuildConfig
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.util.common.ConnectionState
import com.hqt.util.payment.PayooConverter
import com.hqt.view.adapter.EnumTypeAdapter
import com.hqt.view.adapter.JSONArrayAdapter
import com.hqt.view.adapter.JSONObjectAdapter
import dagger.hilt.android.HiltAndroidApp
import io.reactivex.schedulers.Schedulers
import okhttp3.HttpUrl
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import org.json.JSONArray
import org.json.JSONObject
import retrofit2.Retrofit
import retrofit2.adapter.rxjava2.RxJava2CallAdapterFactory
import retrofit2.converter.gson.GsonConverterFactory
import vn.payoo.PayooSdk
import vn.payoo.model.PayooEnvironment
import vn.payoo.model.PayooMerchant
import vn.payoo.paymentsdk.PayooPaymentSDK
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.Date
import java.util.concurrent.TimeUnit
import javax.inject.Singleton




@HiltAndroidApp
@Singleton
class AppController : Application() {
    lateinit var gSon: Gson
    var user: User? = null
    var trackingCode = ""
    fun isUserLogin(): Boolean {
        if (user == null) return false
        return true
    }

    private val payooMerchant: PayooMerchant by lazy(LazyThreadSafetyMode.NONE) {

        PayooSdk().loadMerchantFromMetaData(this.applicationContext)
            .converter(PayooConverter.create())
            .environment(PayooEnvironment.PRODUCTION).build()

    }
    private var sslService: SSLAppService? = null



    override fun onCreate() {
        super.onCreate()

        FirebaseApp.initializeApp(this)
        val firebaseAppCheck = FirebaseAppCheck.getInstance()
        if (BuildConfig.DEBUG) {

        } else {
            firebaseAppCheck.installAppCheckProviderFactory(PlayIntegrityAppCheckProviderFactory.getInstance())
        }

        mInstance = this
        gSon = Gson()

        val gsonBuilder = GsonBuilder().setPrettyPrinting()
        gsonBuilder.registerTypeAdapter(JSONObject::class.java, JSONObjectAdapter.sInstance)
        gsonBuilder.registerTypeAdapter(JSONArray::class.java, JSONArrayAdapter.sInstance)
        gsonBuilder.registerTypeAdapter(Date::class.java, DateDeserializer.sInstance)
        gsonBuilder.registerTypeAdapter(DateOnly::class.java, DateOnlyDeserializer.sInstance)
        gsonBuilder.registerTypeAdapter(PassengerType::class.java, EnumTypeAdapter.sInstance)
        gsonBuilder.registerTypeAdapter(PassengerTitle::class.java, EnumTypeAdapter.sInstance)
        gSon = gsonBuilder.create()

        initRemoteConfig()
        initTrackingCode()

        AppConfigs.getInstance().initAnalytic(this)

        ProviderInstaller.installIfNeeded(this);

    }

    private val okHttpClientBuilder: OkHttpClient.Builder by lazy(LazyThreadSafetyMode.NONE) {

        val builder = OkHttpClient.Builder()
            .dns(CorePlaneOkHttpDNSSelector(CorePlaneOkHttpDNSSelector.IPvMode.IPV4_FIRST))

        builder.connectTimeout(60, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .addInterceptor(HttpLoggingInterceptor().apply {
                if (BuildConfig.DEBUG) {
                    level = HttpLoggingInterceptor.Level.BODY

                }
            }).addInterceptor(getDefaultInterceptor())

    }

//    private fun getDefaultInterceptor(): Interceptor {
//        return Interceptor { chain ->
//            val original: okhttp3.Request = chain.request()
//            val originalHttpUrl: HttpUrl = original.url()
//
//            val url = originalHttpUrl.newBuilder().addQueryParameter("ver", BuildConfig.VERSION_NAME)
//                    .addQueryParameter("source", "ANDROID").build()
//
//            val requestBuilder = original.newBuilder().url(url)
//
//            val request: okhttp3.Request = requestBuilder.build()
//            chain.proceed(request)
//        }
//    }

    private fun getDefaultInterceptor(): Interceptor {
        return Interceptor { chain ->
            val originalRequest = chain.request()
            val originalUrl = originalRequest.url

            val newUrl = originalUrl.newBuilder()
                .addQueryParameter("ver", BuildConfig.VERSION_NAME)
                .addQueryParameter("source", "ANDROID")
                .build()

            val newRequest = originalRequest.newBuilder()
                .url(newUrl)
                .build()

            chain.proceed(newRequest)
        }
    }


    private fun initTrackingCode() {
        try {
            if (trackingCode.isEmpty()) {
                val formatter = DateTimeFormatter.ofPattern("yyyyMMdd")
                val current = LocalDateTime.now().format(formatter)
                trackingCode = AppConfigs.getStringLocalCache(this, "track_code_$current")
            }
        } catch (e: Exception) {

        }
    }

    fun getService(): SSLAppService {
        if (sslService == null) {
            sslService = Retrofit.Builder().baseUrl(SSLSendRequest.getAPILINK())
                .addConverterFactory(GsonConverterFactory.create())
                .addCallAdapterFactory(RxJava2CallAdapterFactory.createWithScheduler(Schedulers.io()))
                .client(okHttpClientBuilder.build()).build().create(SSLAppService::class.java)
        }
        return sslService!!
    }

    private fun initRemoteConfig() {
        try {
            AppConfigs.getInstance().config = FirebaseRemoteConfig.getInstance()
            val settings =
                FirebaseRemoteConfigSettings.Builder().setMinimumFetchIntervalInSeconds(3600)
                    .build()
            AppConfigs.getInstance().config.setConfigSettingsAsync(settings)
            AppConfigs.getInstance().config.setDefaultsAsync(R.xml.remote_config_defaults)
            AppConfigs.getInstance().config.fetchAndActivate().addOnCompleteListener { task ->
                if (task.isSuccessful) {
                    try {
                        sslService = Retrofit.Builder().baseUrl(SSLSendRequest.getAPILINK())
                            .addConverterFactory(GsonConverterFactory.create())
                            .addCallAdapterFactory(
                                RxJava2CallAdapterFactory.createWithScheduler(
                                    Schedulers.io()
                                )
                            )
                            .client(okHttpClientBuilder.build()).build()
                            .create(SSLAppService::class.java)

                        PayooPaymentSDK.initialize(this, payooMerchant)
                    } catch (e: Exception) {
                        AppConfigs.logException(e)
                    }

                } else {
                    AppConfigs.Log("LoadSetting", "Fetch failed")
                }
            }

            FirebaseInstallations.getInstance().id.addOnCompleteListener { task ->
                Common.ID_DEVICE = task.result
            }
            FirebaseMessaging.getInstance().token.addOnCompleteListener(OnCompleteListener { task ->
                if (!task.isSuccessful) {
                    AppConfigs.Log("Fetching FCM registration token failed", "xx")
                    Common.commonInit(applicationContext)
                    return@OnCompleteListener
                } else {
                    val token = task.result
                    AppConfigs.Log("fcm id x", token + "")
                    Common.FCM_TOKEN = token
                    SharedPrefs.getInstance().put("FCM-TOKEN", Common.FCM_TOKEN)
                    Common.commonInit(applicationContext)
                }
            })


        } catch (e: Exception) {
            AppConfigs.logException(e)
        }
    }

    private var mRequestQueue: RequestQueue? = null
    private val requestQueue: RequestQueue?
        get() {
            if (mRequestQueue == null) {
                mRequestQueue = Volley.newRequestQueue(applicationContext)
            }
            return mRequestQueue
        }

    fun <T> addToRequestQueue(
        req: Request<T>,
        tag: String?
    ) { // set the default tag if tag is empty
        req.tag = if (TextUtils.isEmpty(tag)) TAG else tag
        requestQueue!!.add(req)
    }

    companion object {
        var mInstance: AppController? = null
        val TAG = AppController::class.java.simpleName
        var internetConnectionState = ConnectionState.CONNECTED

        @JvmStatic
        @get:Synchronized
        val instance: AppController
            get() {
                if (mInstance == null) {
                    mInstance = AppController()
                }
                return mInstance as AppController
            }
    }


}