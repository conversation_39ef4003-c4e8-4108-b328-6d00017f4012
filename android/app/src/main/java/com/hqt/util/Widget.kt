package com.hqt.util

import android.Manifest
import android.app.Activity
import android.app.Application
import android.app.DatePickerDialog
import android.app.Dialog
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.content.res.ColorStateList
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.net.Uri
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.view.animation.TranslateAnimation
import android.widget.AdapterView
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.FragmentActivity
import com.airbnb.lottie.LottieAnimationView
import com.android.volley.VolleyError
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.transition.DrawableCrossFadeFactory
import com.google.android.flexbox.FlexboxLayout
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.snackbar.Snackbar
import com.google.gson.reflect.TypeToken
import com.hqt.data.model.Baggage
import com.hqt.data.model.Booking
import com.hqt.data.model.BookingV2
import com.hqt.data.model.BusSeatFare
import com.hqt.data.model.Flight
import com.hqt.data.model.Passenger
import com.hqt.data.model.PassengerTitle
import com.hqt.data.model.PassengerType
import com.hqt.data.model.Payment
import com.hqt.data.model.SliderItem
import com.hqt.data.model.Train
import com.hqt.data.model.TrainSeatFare
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.BottomSheetContactTipBinding
import com.hqt.datvemaybay.databinding.BusTripViewBinding
import com.hqt.datvemaybay.databinding.FlightHistoryDetailBinding
import com.hqt.datvemaybay.databinding.FlightHistoryDetailMiniBinding
import com.hqt.datvemaybay.databinding.FlightTicketPrintBinding
import com.hqt.datvemaybay.databinding.LayoutLoadingBinding
import com.hqt.datvemaybay.databinding.LayoutSelectBankAppBinding
import com.hqt.datvemaybay.databinding.ListTrainItemBinding
import com.hqt.datvemaybay.databinding.PaxInputItemListBinding
import com.hqt.datvemaybay.databinding.PaxInputLayoutBinding
import com.hqt.datvemaybay.databinding.TrainTripViewBinding
import com.hqt.datvemaybay.databinding.ViewFlightInterDetailBinding
import com.hqt.datvemaybay.databinding.WidgetChatBinding
import com.hqt.datvemaybay.databinding.WidgetFareDetailInfoBinding
import com.hqt.datvemaybay.databinding.WidgetFlightInfoBinding
import com.hqt.datvemaybay.databinding.WidgetFlightSegmentDetailItemBinding
import com.hqt.datvemaybay.databinding.WidgetPriceAlertInfoBinding
import com.hqt.datvemaybay.databinding.WidgetTourPlanDetailItemBinding
import com.hqt.datvemaybay.databinding.WidgetTourScheduleRowItemBinding
import com.hqt.datvemaybay.databinding.WidgetTourShortInfoItemBinding
import com.hqt.datvemaybay.databinding.WidgetTourSubInfoItemBinding
import com.hqt.util.common.ConnectionState
import com.hqt.util.helper.ProgressBarAnimation
import com.hqt.view.adapter.PassengerAdapter
import com.hqt.view.adapter.SliderAdapterCustom
import com.hqt.view.ui.flightSearch.model.FareData
import com.hqt.view.ui.flighthistory.data.model.FlightHistory
import com.hqt.view.ui.flighthistory.data.model.FlightHistoryItem
import com.hqt.view.ui.flighthistory.other.PermissionUtils.createNotificationChannel
import com.hqt.view.ui.flighthistory.other.PermissionUtils.requestPermission
import com.hqt.view.ui.flighthistory.ui.activity.FlightHistoryActivityV2
import com.hqt.view.ui.flighthistory.ui.activity.MapViewActivity
import com.hqt.view.ui.flightwaches.NewFlightWatchesActivity
import com.hqt.view.ui.tour.DepartureDates
import com.hqt.view.ui.tour.Plans
import com.hqt.view.ui.tour.SubCat
import com.hqt.view.ui.tour.TourDetailActivity
import com.hqt.view.ui.tour.TourSubInfo
import com.hqt.viewmodel.TypePaxViewModel
import org.json.JSONArray
import org.json.JSONException
import org.json.JSONObject
import vn.payoo.core.util.dpToPx
import java.util.Calendar
import java.util.Locale
import kotlin.math.roundToInt

object Widget {
    fun View.expand() {

        val animate = TranslateAnimation(0f, 0f, -height.toFloat(), 0f)
        animate.duration = 200
        animate.fillAfter = false
        startAnimation(animate)
        this.visibility = View.VISIBLE
    }

    fun View.collapse() {
        val animate = TranslateAnimation(0f, 0f, 0f, -height.toFloat())
        animate.duration = 200
        animate.fillAfter = false
        startAnimation(animate)
        this.visibility = View.GONE

    }

    fun showHelperSheet(mContext: Context, bookingDetail: Booking) {
        try {
            var dialogPromotionView = BottomSheetDialog(mContext)
            val layoutInflater = LayoutInflater.from(mContext)
            val binding = DataBindingUtil.inflate<BottomSheetContactTipBinding>(
                layoutInflater,
                R.layout.bottom_sheet_contact_tip,
                null,
                false
            )
            binding.booking = bookingDetail
            dialogPromotionView.setContentView(binding.root)
            binding.btnCallHotline.setOnClickListener {
                val i = Intent(Intent.ACTION_DIAL)
                var p = "tel:" + AppConfigs.getInstance().config.getString("hotline")
                i.data = Uri.parse(p)
                mContext.startActivity(i)
            }
            binding.btnSendSMS.setOnClickListener {
                Common.sendSms(
                    mContext,
                    Common.unAccent("Chao 12BAY. Toi can ho tro don hang #${bookingDetail.id}, Cua hanh khach ${bookingDetail.contact_name}"),
                    AppConfigs.getInstance().config.getString("sms")
                )
            }
            binding.btnClose.setOnClickListener {
                if (dialogPromotionView.isShowing) dialogPromotionView.dismiss()
            }
            binding.btnCopy.setOnClickListener {
                Common.CopyClipboard(mContext, bookingDetail.id)
            }
            dialogPromotionView.show()
        } catch (e: Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }
    }

    @JvmStatic
    fun showPaxList(
        isTrain: Boolean,
        pax: Passenger,
        isRoundTrip: Boolean,
        mContext: Context,
        parent: ViewGroup,
        isAllowEdit: Boolean = true
    ): View {

        val dialogPromotionView = BottomSheetDialog(mContext)

        val layoutInflater = LayoutInflater.from(mContext)
        val binding = DataBindingUtil.inflate<PaxInputItemListBinding>(
            layoutInflater,
            R.layout.pax_input_item_list,
            parent,
            true
        )
        binding.pax = pax
        binding.isAllowEdit = isAllowEdit

        binding.root.setOnClickListener {
            if (isAllowEdit) {
                showPaxInput(isTrain, pax, isRoundTrip, mContext, dialogPromotionView)
            } else {
                if (isTrain) binding.paxDetailLayout.visibility =
                    if (binding.paxDetailLayout.visibility == View.GONE) View.VISIBLE else View.GONE
            }
        }

        if (!isAllowEdit) {
            pax.isValidated = true
        }
        dialogPromotionView.setOnDismissListener {
            binding.pax = pax
            parent.performClick()
        }

        return binding.root
    }

    @JvmStatic
    fun getPassengerList(): List<Passenger> {
        var list: List<Passenger> = ArrayList()
        try {
            val savedPassenger = SharedPrefs.getInstance().get("PASSENGERLIST", String::class.javaObjectType, "{[]}")
            val passengerListType = object : TypeToken<ArrayList<Passenger>>() {}.type

            list = AppController.instance.gSon.fromJson<List<Passenger>>(
                savedPassenger,
                passengerListType
            )
        } catch (e: java.lang.Exception) {
            AppConfigs.Log("e", e.message)
        }
        return list
    }

    @JvmStatic
    fun retrievePassenger(mContext: Context, uid: String?) {
        if (uid != null) SSLSendRequest(mContext).GET(false,
            "users/passengers/" + uid,
            JSONObject(),
            object : SSLSendRequest.CallBackInterface {
                override fun onSuccess(response: JSONObject, cached: Boolean) {
                    if (response.has("data") && response.getJSONArray("data").length() > 0) {
                        SharedPrefs.getInstance()
                            .put("PASSENGERLIST", response.getJSONArray("data"))
                    }
                }

                override fun onFail(er: VolleyError) {

                }
            })

    }

    private fun showBaggageSelect(
        pax: Passenger,
        mContext: Context,
        baggageSelectLayout: LinearLayout,
        isReturnTrip: Boolean
    ) {
        var depFee = Common.BAGGAGE_FEE
        var selectBagagge = pax.baggage
        var bagSelectcontainer =
            baggageSelectLayout.findViewById<LinearLayout>(R.id.bagSelectContainer)
        if (isReturnTrip) {
            bagSelectcontainer =
                baggageSelectLayout.findViewById<LinearLayout>(R.id.bagSelectContainerRt)
            depFee = Common.BAGGAGE_RETURN
            selectBagagge = pax.returnBaggage
        }

        bagSelectcontainer!!.removeAllViews()


        val listBagView: ArrayList<View> = ArrayList()
        for (i in 0 until depFee.length()) {

            val bagfee = depFee.getJSONObject(i)
            val bag = ViewUtil.genBagView(i, bagfee, mContext)

            if (selectBagagge?.value == bagfee.getInt("value")) {
                bag.findViewById<View>(R.id.bagViewBg).isSelected = true
                (bag.findViewById<View>(R.id.txtBagValue) as TextView).setTextColor(
                    mContext.resources.getColor(
                        R.color.primary_dark
                    )
                )
                (bag.findViewById<View>(R.id.txtBagPrice) as TextView).setTextColor(
                    mContext.resources.getColor(
                        R.color.primary_dark
                    )
                )
            } else {
                bag.findViewById<View>(R.id.bagViewBg).isSelected = false
                (bag.findViewById<View>(R.id.txtBagValue) as TextView).setTextColor(
                    mContext.resources.getColor(
                        R.color.textDark
                    )
                )
                (bag.findViewById<View>(R.id.txtBagPrice) as TextView).setTextColor(
                    mContext.resources.getColor(
                        R.color.textDark
                    )
                )
            }

            bag.setOnClickListener { view ->
                clearSelectBagView(listBagView)

                val txtBagDepKg = view.findViewById<TextView>(R.id.txtBagValue)
                val txtBagDepText = view.findViewById<TextView>(R.id.txtBagPrice)
                view.findViewById<View>(R.id.bagViewBg).isSelected = true
                txtBagDepKg.setTextColor(mContext.resources.getColor(R.color.primary_dark))
                txtBagDepText.setTextColor(mContext.resources.getColor(R.color.primary_dark))

                selectBagagge = Baggage()
                selectBagagge?.text = bagfee.getString("text")
                selectBagagge?.value = bagfee.getInt("value")
                selectBagagge?.price = bagfee.getInt("price")

                if (isReturnTrip) {
                    pax.returnBaggage = selectBagagge
                } else {
                    pax.baggage = selectBagagge
                }
            }

            listBagView.add(bag)
            bagSelectcontainer.addView(bag)
        }

    }

    fun clearSelectBagView(listBagView: List<View>) {
        for (bagView in listBagView) {
            (bagView.findViewById<View>(R.id.txtBagPrice) as TextView).setTextColor(
                bagView.context.getResources()
                    .getColor(R.color.textDark)
            )
            (bagView.findViewById<View>(R.id.txtBagValue) as TextView).setTextColor(
                bagView.context.getResources()
                    .getColor(R.color.textDark)
            )
            bagView.findViewById<View>(R.id.bagViewBg).isSelected = false
        }
    }

    private fun showPaxInput(
        isTrain: Boolean,
        pax: Passenger,
        isRoundTrip: Boolean,
        mContext: Context,
        dialogPromotionView: BottomSheetDialog
    ): View {

        val layoutInflater = LayoutInflater.from(mContext)
        val binding = DataBindingUtil.inflate<PaxInputLayoutBinding>(
            layoutInflater,
            R.layout.pax_input_layout,
            null,
            false
        )
        binding.pax = pax
        val paxViewModel = TypePaxViewModel(pax, isTrain)
        binding.viewModel = paxViewModel
        val toolbar = (binding.root.findViewById<androidx.appcompat.widget.Toolbar>(R.id.toolbar))
        toolbar.title = ("Thông tin hành khách")
        var showYear = false

        var curentBirthDay = Common.stringToDate(pax.birthday, "yyyy-MM-dd")

        if (curentBirthDay == null) {
            curentBirthDay = Calendar.getInstance()
            showYear = true
        }
        val dateSetListener = DatePickerDialog.OnDateSetListener { view, year, monthOfYear, dayOfMonth ->
                curentBirthDay.set(Calendar.YEAR, year)
                curentBirthDay.set(Calendar.MONTH, monthOfYear)
                curentBirthDay.set(Calendar.DAY_OF_MONTH, dayOfMonth)
                binding.inputPaxBirthday.setText(
                    Common.dateToString(
                        curentBirthDay.time,
                        "yyyy-MM-dd"
                    )
                )

            }
        if (isTrain || pax.type == PassengerType.INFANT) {
            binding.baggageSelectLayout.visibility = View.GONE
        } else {
            binding.baggageSelectLayout.visibility = View.VISIBLE

        }

        binding.inputPaxBirthday.setOnClickListener {
            var dialog = DatePickerDialog(
                mContext,
                dateSetListener,
                curentBirthDay.get(Calendar.YEAR),
                curentBirthDay.get(Calendar.MONTH),
                curentBirthDay.get(Calendar.DAY_OF_MONTH)
            )

            dialog.datePicker.maxDate = pax.getMaxBirthDate(isTrain).timeInMillis
            dialog.datePicker.minDate = pax.getMinBirthDate(isTrain).timeInMillis
            dialog.show()

            try {
                if (showYear) dialog.findViewById<TextView>(
                    mContext.resources?.getIdentifier(
                        "android:id/date_picker_header_year",
                        null,
                        null
                    ) ?: -1
                ).performClick()
            } catch (e: Exception) {
                e.printStackTrace()
            }

        }

        dialogPromotionView.setContentView(binding.root)

        var mBehavior = BottomSheetBehavior.from(binding.root.parent as View)
        mBehavior.state = BottomSheetBehavior.STATE_EXPANDED
        mBehavior.isHideable = false
        mBehavior.isDraggable = false
        dialogPromotionView.show()

        binding.btnSelectPax.setOnClickListener {

            if (binding.inputPaxGender.selectedItem == null) {
                binding.inputPaxGender.error = "Vui lòng chọn danh xưng"

            } else if (Helper.validatorInput(
                    binding.inputPaxFullName.text.toString(),
                    Helper.ValidatorType.FULLNAME.text
                ) != null && Helper.isInputRequired(
                    pax,
                    Helper.ValidatorType.FULLNAME,
                    isTrain
                )
            ) {
                binding.inputPaxFullName.setText(binding.inputPaxFullName.text.toString())
                binding.inputPaxFullName.requestFocus()

                //            } else if (Helper.validatorInput(binding.inputPaxFirstName.text.toString(),
                //                    Helper.ValidatorType.FIRSTNAME.text) != null && Helper.isInputRequired(pax,
                //                    Helper.ValidatorType.FIRSTNAME,
                //                    isTrain)) {
                //                binding.inputPaxFirstName.setText(binding.inputPaxFirstName.text.toString())
                //                binding.inputPaxFirstName.requestFocus()
                //            } else if (Helper.validatorInput(binding.inputPaxLastName.text.toString(),
                //                    Helper.ValidatorType.LASTNAME.text) != null && Helper.isInputRequired(pax,
                //                    Helper.ValidatorType.LASTNAME,
                //                    isTrain)) {
                //                binding.inputPaxLastName.setText(binding.inputPaxLastName.text.toString())
                //                binding.inputPaxLastName.requestFocus() //SKIP required birthdate
            } else if (Helper.validatorInput(
                    binding.inputPaxBirthday.text.toString(),
                    Helper.ValidatorType.BIRTDATE.text
                ) != null && Helper.isInputRequired(
                    pax,
                    Helper.ValidatorType.BIRTDATE,
                    isTrain
                )
            ) {
                binding.inputPaxBirthday.setText(binding.inputPaxBirthday.text.toString())
            } else if (Helper.validatorInput(
                    binding.inputPaxIdNumber.text.toString(),
                    Helper.ValidatorType.IDNUMBER.text
                ) != null && Helper.isInputRequired(
                    pax,
                    Helper.ValidatorType.IDNUMBER,
                    isTrain
                )
            ) {
                binding.inputPaxIdNumber.setText(binding.inputPaxIdNumber.text.toString())
                binding.inputPaxIdNumber.requestFocus()
            } else {
                savePaxInfo(binding, pax, paxViewModel)
                if (pax.firstName.uppercase(Locale.ROOT)
                        .contains(pax.lastName.uppercase(Locale.ROOT)) || pax.lastName.uppercase(
                        Locale.ROOT
                    )
                        .contains(pax.firstName.uppercase(Locale.ROOT))
                ) {
                    MaterialAlertDialogBuilder(mContext).setIcon(R.drawable.ic_bell_alert)
                        .setTitle("Kiểm tra thông tin hành khách")
                        .setMessage(Common.convertHTML("<strong> &emsp;" + pax.getFullNameDetail() + "</strong><br/><strong> &emsp;Ngày sinh:  " + pax.birthday + "</strong>"))
                        .setPositiveButton("Đúng rồi") { _, _ ->
                            dialogPromotionView.dismiss()
                        }.setNegativeButton("Nhập lại", null).show()
                } else {
                    dialogPromotionView.dismiss()
                }
            }
        }

        binding.inputPaxClose.setOnClickListener {
            if (binding.inputPaxGender.selectedItem != null && Helper.validatorInput(
                    binding.inputPaxFullName.text.toString(),
                    Helper.ValidatorType.FULLNAME.text
                ) == null && Helper.validatorInput(
                    binding.inputPaxBirthday.text.toString(),
                    Helper.ValidatorType.BIRTDATE.text
                ) == null
            ) {

                savePaxInfo(binding, pax, paxViewModel)
            }
            dialogPromotionView.dismiss()

        }

        val txtSearch = binding.inputPaxFullName
        val adapter = PassengerAdapter(mContext, R.layout.pax_input_item_list, getPassengerList(), false)
        txtSearch.setAdapter(adapter)
        txtSearch.onItemClickListener =
            AdapterView.OnItemClickListener { adapterView, view, pos, id ->
                var selectedPerson = adapterView.getItemAtPosition(pos) as Passenger
                binding.pax = selectedPerson
                if (selectedPerson.title === PassengerTitle.MR || selectedPerson.title === PassengerTitle.MSTR) binding.inputPaxGender.selection =
                    0 else binding.inputPaxGender.selection = 1

                binding.inputPaxIdNumber.requestFocus()
            }

        if (pax.type != PassengerType.INFANT) {
            binding.baggageSelectLayout.visibility = View.VISIBLE
            if (!isTrain) {
                showBaggageSelect(pax, mContext, binding.baggageSelectLayout, false)
                if (isRoundTrip) {
                    showBaggageSelect(pax, mContext, binding.baggageSelectLayout, true)

                    binding.selectBagReturnLayout.visibility = View.VISIBLE
                }
            }
        }

        return binding.root
    }

    fun busTripView(
        fare: BusSeatFare,
        mContext: Context,
        parent: ViewGroup,
        shortView: Boolean = false
    ): View {
        var v: View
        val layoutInflater = LayoutInflater.from(mContext)
        val binding: BusTripViewBinding =
            DataBindingUtil.inflate(layoutInflater, R.layout.bus_trip_view, parent, true)
        binding.layoutTrainView.visibility = View.VISIBLE
        binding.trip = fare
        if (shortView) {
            binding.viewTotalDi.visibility = View.GONE
        }
        binding.viewTotalDi.setOnClickListener {
            if (binding.viewTotalDetail.visibility == View.GONE) {
                binding.viewTotalDetail.visibility = View.VISIBLE
            } else {
                binding.viewTotalDetail.visibility = View.GONE
            }
        }
        return binding.root
    }

    fun trainTripView(
        fare: TrainSeatFare,
        mContext: Context,
        parent: ViewGroup,
        shortView: Boolean = false
    ): View {
        var v: View
        val layoutInflater = LayoutInflater.from(mContext)
        val binding: TrainTripViewBinding = DataBindingUtil.inflate(
            layoutInflater,
            R.layout.train_trip_view,
            parent,
            true
        )
        binding.layoutTrainView.visibility = View.VISIBLE
        binding.trip = fare
        if (shortView) {
            binding.viewTotalDi.visibility = View.GONE
        }
        binding.viewTotalDi.setOnClickListener {
            if (binding.viewTotalDetail.visibility == View.GONE) {
                binding.viewTotalDetail.visibility = View.VISIBLE
            } else {
                binding.viewTotalDetail.visibility = View.GONE
            }
        }
        return binding.root
    }

    fun trainView(train: Train, parent: ViewGroup, mContext: Context): View {


        val layoutInflater = LayoutInflater.from(mContext)
        var binding: ListTrainItemBinding = DataBindingUtil.inflate(
            layoutInflater,
            R.layout.list_train_item,
            parent,
            true
        )
        binding.train = train
        return binding.root
    }

    private fun savePaxInfo(
        binding: PaxInputLayoutBinding,
        pax: Passenger,
        paxViewModel: TypePaxViewModel
    ) { //        pax.lastName = binding.inputPaxLastName.text.toString()
        //        pax.firstName = binding.inputPaxFirstName.text.toString()

        pax.fullName = binding.inputPaxFullName.text.toString()
        pax.splitPaxName()

        pax.birthday = binding.inputPaxBirthday.text.toString()
        pax.idNumber = binding.inputPaxIdNumber.text.toString()
        pax.title = paxViewModel.getTitleByText(binding.inputPaxGender.selectedItem.toString())
        pax.isValidated = true //        pax.saveFullName()
    }

    fun showFlightHistoryShort(
        mContext: Context,
        parent: ViewGroup,
        flightHistory: FlightHistoryItem,
        bindingView: FlightHistoryDetailMiniBinding?
    ): FlightHistoryDetailMiniBinding? {
        try {

            val layoutInflater = LayoutInflater.from(mContext)
            var binding = bindingView
            if (binding == null) {
                binding = DataBindingUtil.inflate(
                    layoutInflater,
                    R.layout.flight_history_detail_mini,
                    parent,
                    true
                )
            }
            binding!!.flightHistory = flightHistory
            var onReload = true
            val adapter = SliderAdapterCustom(mContext, false)

            if (!flightHistory.aircraft?.registration.isNullOrEmpty() && onReload) {

                val param = JSONObject()
                param.put("size", "small")
                SSLSendRequest(mContext).GET(false,
                    "Flight/Images/" + flightHistory.aircraft?.registration,
                    param,
                    object : SSLSendRequest.CallBackInterface {
                        override fun onSuccess(response: JSONObject, cached: Boolean) {

                            if (!response.isNull("data")) {
                                binding.imageSlider.setSliderAdapter(adapter)
                                adapter.renewItems(getListSlider(response.getJSONArray("data")))
                                adapter.ItemsKey = flightHistory.aircraft?.registration
                            }
                        }

                        override fun onFail(er: VolleyError) {
                        }
                    })
            }
            binding.closeText.setOnClickListener {
                binding.root.visibility = View.GONE
            }

            if (flightHistory.status.live && flightHistory.live != null) {
                val anim =
                    ProgressBarAnimation(binding.flightProgress, 0, flightHistory.live!!.progress)
                anim.duration = 500
            }
            binding.mainContainer.setOnClickListener {
                showFlightHistory(mContext, flightHistory, true)
            }
            return binding
        } catch (e: java.lang.Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }
        return null
    }

    @JvmStatic
    fun chatInfo(mContext: Context, parent: ViewGroup) {
        try {
            parent.removeAllViews()
            val layoutInflater = LayoutInflater.from(mContext)
            val binding: WidgetChatBinding =
                DataBindingUtil.inflate(layoutInflater, R.layout.widget_chat, parent, true)
            binding.callPhone.setOnClickListener {

                val i = Intent(Intent.ACTION_DIAL)
                i.data = Uri.parse("tel:19002642")
                i.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                mContext.startActivity(i)

            }
            binding.sendSms.setOnClickListener {

                Common.sendSms(
                    mContext,
                    "Hi 12bay. Toi can ho tro don hang ",
                    AppConfigs.getInstance().config.getString("sms")
                )

            }
            binding.sendFacebook.setOnClickListener {

                try {
                    Toast.makeText(mContext, "Gửi hỗ trợ facebook", Toast.LENGTH_LONG).show()
                    val uri = Uri.parse("fb-messenger://user/1522635601351119")
                    val intent = Intent(Intent.ACTION_VIEW, uri)
                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    mContext.startActivity(intent)
                } catch (e: ActivityNotFoundException) {
                    val uri = Uri.parse("https://www.facebook.com/12bay.vn/")
                    val intent = Intent(Intent.ACTION_VIEW, uri)
                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    mContext.startActivity(intent)
                }

            }
            binding.sendZalo.setOnClickListener {

                Toast.makeText(mContext, "Gửi hỗ trợ Zalo", Toast.LENGTH_LONG).show()
                val uri = Uri.parse("https://zalo.me/4377741633721725644")
                val intent = Intent(Intent.ACTION_VIEW, uri)
                intent.putExtra(Intent.EXTRA_TEXT, "Hello")
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                mContext.startActivity(intent)

            }
        } catch (e: java.lang.Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }

    }

    @JvmStatic
    fun createViewFlightInterInfo(
        mContext: Context,
        flight: com.hqt.view.ui.flightSearch.model.Flight,
        parent: ViewGroup
    ): View? {
        try {
            parent.removeAllViews()
            val layoutInflater = LayoutInflater.from(mContext)
            val binding: ViewFlightInterDetailBinding = DataBindingUtil.inflate(
                layoutInflater,
                R.layout.view_flight_inter_detail,
                parent,
                true
            )
            binding.flightInfo = flight
            return binding.root
        } catch (e: java.lang.Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }
        return null
    }

    @JvmStatic
    fun createPriceAlertInfo(mContext: AppCompatActivity, booking: BookingV2, parent: ViewGroup) {
        try {
            parent.removeAllViews()
            val layoutInflater = LayoutInflater.from(mContext)
            val binding: WidgetPriceAlertInfoBinding = DataBindingUtil.inflate(
                layoutInflater,
                R.layout.widget_price_alert_info,
                parent,
                true
            )
//            binding.booking = booking


            binding.btnPriceAlertAdd.setOnClickListener {
                if (ContextCompat.checkSelfPermission(
                        mContext,
                        Manifest.permission.POST_NOTIFICATIONS
                    ) == PackageManager.PERMISSION_GRANTED
                ) {

                    val `in` = Intent(mContext, NewFlightWatchesActivity::class.java)
                    `in`.putExtra("origin", booking.origin_code)
                    `in`.putExtra("destination", booking.destination_code)
                    `in`.putExtra("departureDate", booking.departure_date)
                    `in`.putExtra("adult", booking.adult)
                    `in`.putExtra("child", booking.child)
                    `in`.putExtra("infant", booking.infant)
                    `in`.putExtra("autoSaved", true)

                    mContext.startActivity(`in`)

                } else {
                    createNotificationChannel(mContext)
                    requestPermission(mContext, 9999, Manifest.permission.POST_NOTIFICATIONS, false)
                }


            }


        } catch (e: java.lang.Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }

    }





    @JvmStatic
    fun createFlightInfo(mContext: Context, flight: Flight, parent: ViewGroup) {
        try {
            parent.removeAllViews()
            val layoutInflater = LayoutInflater.from(mContext)
            val binding: WidgetFlightInfoBinding = DataBindingUtil.inflate(
                layoutInflater,
                R.layout.widget_flight_info,
                parent,
                true
            )
//            binding.flightDetail = flight
            binding.airlineLogo.setImageResource(
                Integer.valueOf(
                    mContext.resources.getIdentifier(
                        flight.logo,
                        "drawable",
                        mContext.packageName
                    )
                )
            )

            if (!flight.description.isNullOrEmpty()) {
                binding.txtDescription.text = Common.convertHTML(flight.description)
            }

            binding.metaLayout.setOnClickListener {
                val flin = Intent(mContext, FlightHistoryActivityV2::class.java)
                flin.putExtra("flightNumber", flight.flightNumber)
                mContext.startActivity(flin)
            }

            showFlightHistoryMeta(mContext, flight, binding)
        } catch (e: java.lang.Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }

    }

    fun createTourDetailPlan(mContext: Context, plan: Plans, parent: ViewGroup) {
        val layoutInflater = LayoutInflater.from(mContext)
        val binding: WidgetTourPlanDetailItemBinding = DataBindingUtil.inflate(
            layoutInflater,
            R.layout.widget_tour_plan_detail_item,
            parent,
            true
        )
        binding.plans = plan

    }

    fun createTourDetailSchedule(
        mContext: Context,
        departureDate: DepartureDates,
        parent: ViewGroup
    ) {
        val layoutInflater = LayoutInflater.from(mContext)
        val binding: WidgetTourScheduleRowItemBinding = DataBindingUtil.inflate(
            layoutInflater,
            R.layout.widget_tour_schedule_row_item,
            parent,
            true
        )
        binding.schedule = departureDate
        binding.btnSelectTour.setOnClickListener {
            (mContext as TourDetailActivity).selectTourSchedule(departureDate)
        }

    }

    fun createTourShortInfoItem(mContext: Context, info: TourSubInfo, parent: ViewGroup) {
        val layoutInflater = LayoutInflater.from(mContext)
        val binding: WidgetTourShortInfoItemBinding = DataBindingUtil.inflate(
            layoutInflater,
            R.layout.widget_tour_short_info_item,
            parent,
            true
        )
        binding.info = info


    }

    fun createTourSubTitleItem(mContext: Context, sub: SubCat, parent: ViewGroup) {
        val layoutInflater = LayoutInflater.from(mContext)
        val binding: WidgetTourSubInfoItemBinding = DataBindingUtil.inflate(
            layoutInflater,
            R.layout.widget_tour_sub_info_item,
            parent,
            true
        )
        binding.subTitle.text = sub.text
        binding.subTitle.setOnClickListener {
            val notificationIntent = Common.ConvertLinkAction(mContext, Uri.parse(sub.link))
            notificationIntent.putExtra("title", sub.text)
            mContext.startActivity(notificationIntent)
        }

    }


    @JvmStatic
    fun createFareDetailInfo(mContext: Context, fare: FareData, parent: ViewGroup) {
        try {
            parent.removeAllViews()
            val layoutInflater = LayoutInflater.from(mContext)
            val binding: WidgetFareDetailInfoBinding = DataBindingUtil.inflate(
                layoutInflater,
                R.layout.widget_fare_detail_info,
                parent,
                true
            )
            binding.viewmodel = fare
            binding.departureContainer.title.setOnClickListener {
                binding.departureContainer.expandableLayout.toggle()
            }


            binding.departureContainer.segmentContainer.removeAllViews()
            if (fare.getDepartureFlight()?.listSegment!!.size > 0) {
                fare.getDepartureFlight()!!.listSegment.forEach {
                    val segmentBinding: WidgetFlightSegmentDetailItemBinding =
                        DataBindingUtil.inflate(
                            layoutInflater,
                            R.layout.widget_flight_segment_detail_item,
                            binding.departureContainer.segmentContainer,
                            true
                        )
                    segmentBinding.segment = it
                }
            }
            if (fare.isRoundTrip) {
                binding.returnContainer.title.setOnClickListener {
                    binding.returnContainer.expandableLayout.toggle()
                }
                binding.returnContainer.segmentContainer.removeAllViews()
                if (fare.getReturnFlight() != null && fare.getReturnFlight()!!.listSegment.size > 0) {

                    fare.getReturnFlight()!!.listSegment.forEach {
                        val segmentBinding: WidgetFlightSegmentDetailItemBinding =
                            DataBindingUtil.inflate(
                                layoutInflater,
                                R.layout.widget_flight_segment_detail_item,
                                binding.returnContainer.segmentContainer,
                                true
                            )
                        segmentBinding.segment = it
                    }
                }

            }

            // binding.flightDetail = flight
            //            binding.airlineLogo.setImageResource(Integer.valueOf(mContext.resources.getIdentifier(flight.logo,
            //                "drawable",
            //                mContext.packageName)))

            //            if (!flight.description.isNullOrEmpty()) {
            //                binding.txtDescription.text = Common.convertHTML(flight.description)
            //            }

            //            binding.metaClick.setOnClickListener {
            //                val flin = Intent(mContext, FlightHistoryActivity::class.java)
            //                flin.putExtra("flightNumber", flight.flightNumber)
            //                mContext.startActivity(flin)
            //            }


        } catch (e: java.lang.Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }

    }

    fun Context?.isAvailable(): Boolean {
        if (this == null) {
            return false
        } else if (this !is Application) {
            if (this is FragmentActivity) {
                return !this.isDestroyed
            } else if (this is Activity) {
                return !this.isDestroyed
            }
        }
        return true
    }

    private fun showFlightHistoryMeta(
        mContext: Context,
        flight: Flight,
        flightInfoView: WidgetFlightInfoBinding
    ) {
        var request = JSONObject()

        try {

            request.put("flightNumber", flight.flightNumber)
            request.put("days", 14)

            SSLSendRequest(mContext).GET(
                false,
                "Flight/History",
                request,
                object : SSLSendRequest.CallBackInterface {
                    override fun onSuccess(response: JSONObject, cached: Boolean) {
                        if (mContext.isAvailable()) {
                            if (response.has("data") && response.getJSONObject("data")
                                    .length() > 0
                            ) {
                                try {
                                    val historyType = object : TypeToken<FlightHistory>() {}.type
                                    val flightHistory =
                                        AppController.instance.gSon.fromJson<FlightHistory>(
                                            response.getJSONObject(
                                                "data"
                                            ).toString(), historyType
                                        )

                                    if (flightHistory.meta != null) {

                                        val score = flightHistory.meta!!.score * 100
                                        flightInfoView.scoreProgress.progress = score
                                        flightInfoView.scoreProgress.text = "" + score.roundToInt()
                                        flightInfoView.txtMetaFlightNumber.text = flightHistory.meta?.flight
                                        flightInfoView.txtMetaGreen.text = flightHistory.meta?.green?.total.toString()
                                        flightInfoView.txtMetaRed.text = flightHistory.meta?.red?.total.toString()
                                        flightInfoView.txtMetaYellow.text = flightHistory.meta?.yellow?.total.toString()
                                        flightInfoView.txtMetaTotalFlight.text = flightHistory.meta?.total.toString()

                                        //                                    Glide.with(mContext).load(flightHistory.meta!!.logo)
                                        //                                        .apply(RequestOptions().centerInside()).skipMemoryCache(true)
                                        //                                        .placeholder(R.drawable.logo_gray).into(flightInfoView.airlineLogo)

                                    }
                                } catch (e: Exception) {
                                    AppConfigs.logException(e)
                                    AppConfigs.Log("E", e.message);
                                }
                            }

                            flightInfoView.metaLayout.visibility = View.VISIBLE
                            flightInfoView.metaShimmer.visibility = View.GONE
                        }
                    }

                    override fun onFail(er: VolleyError) {
                        flightInfoView.metaLayout.visibility = View.VISIBLE
                        flightInfoView.metaShimmer.visibility = View.GONE
                    }
                })
        } catch (e: JSONException) {
            AppConfigs.logException(e, request.toString())
            e.printStackTrace()
            flightInfoView.metaLayout.visibility = View.VISIBLE
            flightInfoView.metaShimmer.visibility = View.GONE
        }
    }

    @JvmStatic
    fun showBankAppSelect(mContext: Context, paymentMethod: ArrayList<Payment>): BottomSheetDialog {
        val dialog = BottomSheetDialog(mContext)
        try {
            val layoutInflater = LayoutInflater.from(mContext)
            val binding: LayoutSelectBankAppBinding = DataBindingUtil.inflate(
                layoutInflater,
                R.layout.layout_select_bank_app,
                null,
                false
            )
            dialog.setContentView(binding.root)
            val mBehavior = BottomSheetBehavior.from(binding.root.parent as View)
            setupFullHeight(binding.root.parent as View)

            mBehavior.state = BottomSheetBehavior.STATE_COLLAPSED
            mBehavior.isHideable = true
            mBehavior.isDraggable = true

            binding.flexBox.removeAllViews()
            binding.close.setOnClickListener {
                if (dialog.isShowing) {
                    dialog.hide()
                }
            }
            if (paymentMethod.size > 0) {
                paymentMethod.forEach { it ->
                    val bankButton = ImageView(mContext)
                    val lpFlexItem =
                        FlexboxLayout.LayoutParams(dpToPx(120f).toInt(), dpToPx(40f).toInt())

                    lpFlexItem.flexBasisPercent = 0.25f
                    bankButton.setPadding(0, 10, 0, 10)
                    bankButton.layoutParams = lpFlexItem
                    bankButton.background =
                        ContextCompat.getDrawable(mContext, R.drawable.background_bank_selector)
                    bankButton.tag = it.Bank

                    val factory =
                        DrawableCrossFadeFactory.Builder().setCrossFadeEnabled(true).build()

                    Glide.with(mContext).load(it.Logo).apply(RequestOptions.centerInsideTransform())
                        .placeholder(R.drawable.logo_gray)
                        .transition(DrawableTransitionOptions.withCrossFade(factory))
                        .into(bankButton)

                    binding.flexBox.addView(bankButton)
                    var deepLink = it.DeepLink
                    bankButton.setOnClickListener {
                        try {
                            mContext.startActivity(Intent(Intent.ACTION_VIEW, Uri.parse(deepLink)))
                        } catch (e: Exception) {

                        }
                    }
                    binding.close.setOnClickListener {
                        if (dialog.isShowing) {
                            dialog.hide()
                        }
                    }

                }


            }

            if (!dialog.isShowing) dialog.show()
            return dialog
        } catch (e: Exception) {
            return dialog
        }
    }


    fun progressDialog(context: Context): Dialog {
        try {
            val dialog = Dialog(context)
            val inflate =
                LayoutInflater.from(context).inflate(R.layout.widget_progress_dialog, null)
            dialog.setContentView(inflate)
            dialog.setCancelable(false)

            val lottie = inflate.findViewById<LottieAnimationView>(R.id.loadingLottie)
            dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            dialog.setOnShowListener {
                lottie?.playAnimation()
            }

            return dialog
        } catch (e: Exception) {
            Log.logException(e)
        }
        return Dialog(context)
    }


    @JvmStatic
    fun showLoading(mContext: Context): BottomSheetDialog {
        val dialog = BottomSheetDialog(mContext)
        try {
            val layoutInflater = LayoutInflater.from(mContext)
            val binding: LayoutLoadingBinding = DataBindingUtil.inflate(
                layoutInflater,
                R.layout.layout_loading,
                null,
                false
            )
            dialog.setContentView(binding.root)
            val mBehavior = BottomSheetBehavior.from(binding.root.parent as View)
            setupFullHeight(binding.root.parent as View)

            mBehavior.state = BottomSheetBehavior.STATE_EXPANDED
            mBehavior.isHideable = false
            mBehavior.isDraggable = false

            if (!dialog.isShowing) dialog.show()

            binding.callPhone.setOnClickListener {
                val i = Intent(Intent.ACTION_DIAL)
                i.data = Uri.parse("tel:19002642")
                i.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                mContext.startActivity(i)
            }
            binding.sendZalo.setOnClickListener {
                val uri = Uri.parse("https://zalo.me/4377741633721725644")
                val intent = Intent(Intent.ACTION_VIEW, uri)
                intent.putExtra("message", "xxxxx")
                mContext.startActivity(intent)
            }

            binding.close.setOnClickListener {
                if (dialog.isShowing) {
                    dialog.hide()
                }
            }
            return dialog
        } catch (e: Exception) {
            return dialog
        }
    }

    @JvmStatic
    fun showTicket(mContext: Context, token: String, isRoundTrip: Boolean) {

        val dialog = BottomSheetDialog(mContext)
        val layoutInflater = LayoutInflater.from(mContext)
        val binding: FlightTicketPrintBinding = DataBindingUtil.inflate(
            layoutInflater,
            R.layout.flight_ticket_print,
            null,
            false
        )

        var link =
            SSLSendRequest.getAPILINK() + "/api/v1/AirLines/Booking/TicketImage/" + token + "/?view=true&rotate=true";
        if (isRoundTrip) {
            link =
                SSLSendRequest.getAPILINK() + "/api/v1/AirLines/Booking/TicketImage/" + token + "/true?view=true&rotate=true"
        }
        Glide.with(mContext).load(link).centerInside().skipMemoryCache(true).timeout(60000)
            .error(R.drawable.ic_empty_my_reward_list).diskCacheStrategy(DiskCacheStrategy.NONE)
            .placeholder(R.drawable.transparent).into(binding.ivImageActivity)


        dialog.setContentView(binding.root)


        var mBehavior = BottomSheetBehavior.from(binding.root.parent as View)

        setupFullHeight(binding.root.parent as View)

        mBehavior.state = BottomSheetBehavior.STATE_EXPANDED
        mBehavior.isHideable = false
        mBehavior.isDraggable = false

        if (!dialog.isShowing) dialog.show()

        binding.close.setOnClickListener {
            if (dialog.isShowing) {
                dialog.hide()
            }
        }
    }

    fun showFlightHistory(
        mContext: Context,
        flightHistory: FlightHistoryItem,
        fromShortView: Boolean
    ) {
        try {
            var dialog = BottomSheetDialog(mContext)

            val layoutInflater = LayoutInflater.from(mContext)
            var binding: FlightHistoryDetailBinding = DataBindingUtil.inflate(
                layoutInflater,
                R.layout.flight_history_detail,
                null,
                false
            )
            binding.flightHistory = flightHistory

            var adapter = SliderAdapterCustom(mContext, true)
            binding.imageSlider.setSliderAdapter(adapter)
            dialog.setContentView(binding.root)

            var mBehavior = BottomSheetBehavior.from(binding.root.parent as View)
            mBehavior.state = BottomSheetBehavior.STATE_EXPANDED
            mBehavior.isHideable = true
            mBehavior.isDraggable = true



            if (flightHistory.aircraft?.registration != null) {
                SSLSendRequest(mContext).GET(false,
                    "Flight/Images/" + flightHistory.aircraft?.registration,
                    JSONObject(),
                    object : SSLSendRequest.CallBackInterface {
                        override fun onSuccess(response: JSONObject, cached: Boolean) {

                            if (!response.isNull("data")) {
                                adapter.renewItems(getListSlider(response.getJSONArray("data")))
                            }
                        }

                        override fun onFail(er: VolleyError) { //Common.showAlertDialog(mContext, "Thông báo", "Không tìm thấy chuyến đi phù hợp! Vui lòng chọn lại chuyến đi khác", true)
                        }
                    })
            }
            if (!fromShortView && flightHistory.aircraft?.registration != null) {
                binding.btnViewOnMap.visibility = View.VISIBLE
                binding.btnViewOnMap.setOnClickListener {
                    if (flightHistory.aircraft?.registration != null) {
                        val intent = Intent(mContext, MapViewActivity::class.java)
                        intent.putExtra("flightId", flightHistory.id)
                        intent.putExtra("onMoveCamera", true)
                        intent.putExtra("isHistory", true)
                        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                        mContext.startActivity(intent)
                        (mContext as Activity).overridePendingTransition(R.anim.enter, R.anim.exit)
                    }

                }
            }

            if (!dialog.isShowing) dialog.show()

            binding.closeSheet.setOnClickListener {
                if (dialog.isShowing) dialog.dismiss()
            }

            val param = JSONObject()
            param.put("flightId", flightHistory.id)

            if (flightHistory.status.live) {
                SSLSendRequest(mContext).GET(
                    false,
                    "Flight/Track",
                    param,
                    object : SSLSendRequest.CallBackInterface {
                        override fun onSuccess(response: JSONObject, cached: Boolean) {
                            if (!response.isNull("data")) {
                                val historyItem = object : TypeToken<FlightHistoryItem>() {}.type
                                val track = AppController.instance.gSon.fromJson<FlightHistoryItem>(
                                    response.getJSONObject("data")
                                        .toString(), historyItem
                                )
                                binding.flightHistory = track
                                if (track.live != null) {
                                    val anim = ProgressBarAnimation(
                                        binding.flightProgress,
                                        0,
                                        track.live!!.progress
                                    )
                                    anim.duration = 500
                                    binding.flightProgress.startAnimation(anim)
                                }
                            }
                        }

                        override fun onFail(er: VolleyError) { //Common.showAlertDialog(mContext, "Thông báo", "Không tìm thấy chuyến đi phù hợp! Vui lòng chọn lại chuyến đi khác", true)
                        }
                    })
            }

        } catch (e: java.lang.Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }

    }

    fun getListSlider(jsonImages: JSONArray): List<SliderItem> {
        var listSlider: ArrayList<SliderItem> = ArrayList()

        try {
            for (i in 0 until jsonImages.length() - 1) {
                var image = jsonImages.getJSONObject(i)
                var slide = SliderItem(
                    image.getString("imageUrl"),
                    image.getString("description"),
                    image.getString("link")
                )
                listSlider.add(slide)
            }

        } catch (e: JSONException) {
            e.printStackTrace()
        }
        return listSlider
    }

    fun setupFullHeight(bottomSheet: View) {
        val layoutParams = bottomSheet.layoutParams
        layoutParams.height = WindowManager.LayoutParams.MATCH_PARENT
        bottomSheet.layoutParams = layoutParams
    }


    fun showInternetStatus(state: ConnectionState, showBar: Boolean, activity: Activity) {
        try {
            val internetStatus = when (state) {
                ConnectionState.CONNECTED -> {
                    "Đã kết nối Internet"
                }

                ConnectionState.SLOW -> {
                    "Kết nối Internet chậm"
                }

                else -> {
                    "Mất kết nối Internet"
                }
            }
            var snackbar = Snackbar.make(
                activity.findViewById(android.R.id.content),
                internetStatus,
                Snackbar.LENGTH_LONG
            )

//            if (activity.findViewById<View>(R.id.viewSnack) != null) {
//                snackbar = Snackbar.make(
//                    activity.findViewById(R.id.viewSnack),
//                    internetStatus,
//                    Snackbar.LENGTH_LONG
//                )
//            }

            snackbar.setActionTextColor(Color.WHITE)

            val sbView: View = snackbar.view
            val textView =
                sbView.findViewById<TextView>(com.google.android.material.R.id.snackbar_text)
            textView.setTextColor(Color.WHITE)

            textView.textAlignment = View.TEXT_ALIGNMENT_CENTER
            textView.gravity = Gravity.CENTER_HORIZONTAL

            val snackBarView = snackbar.view as FrameLayout
            val params = snackBarView.getChildAt(0).layoutParams as FrameLayout.LayoutParams
            params.setMargins(params.leftMargin, -24, params.rightMargin, -24)
            snackBarView.getChildAt(0).layoutParams = params


            if (state == ConnectionState.DISCONNECTED) {
                snackbar.duration = 20000
                snackbar.setBackgroundTintList(ColorStateList.valueOf(Color.GRAY))
                snackbar.show()

            } else {
                if (snackbar.isShown) snackbar.dismiss()

                snackbar.setBackgroundTint(ContextCompat.getColor(activity, R.color.green))
                snackbar.duration = 2000
                snackbar.show()
//
            }
        } catch (e: java.lang.Exception) {
        }
    }

}

class ProgressDialog {
    companion object {
        fun progressDialog(context: Context): Dialog {
            val dialog = Dialog(context)
            val inflate = LayoutInflater.from(context).inflate(R.layout.progress_dialog, null)
            dialog.setContentView(inflate)
            dialog.setCancelable(false)
            dialog.window!!.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            return dialog
        }
    }
}
