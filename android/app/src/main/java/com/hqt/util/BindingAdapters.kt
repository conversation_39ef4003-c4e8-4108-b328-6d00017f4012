package com.hqt.util

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.graphics.Color
import android.graphics.Paint.STRIKE_THRU_TEXT_FLAG
import android.graphics.Typeface
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.widget.*
import androidx.appcompat.widget.AppCompatSpinner
import androidx.core.content.ContextCompat
import androidx.databinding.BindingAdapter
import androidx.databinding.InverseBindingAdapter
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions.withCrossFade
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.transition.DrawableCrossFadeFactory
import com.google.android.material.textfield.TextInputEditText
import com.google.android.material.textfield.TextInputLayout
import com.hqt.datvemaybay.R
import com.hqt.view.ui.booking.ui.TypePaxViewModelV2
import com.hqt.viewmodel.TypePaxViewModel
import com.mikepenz.iconics.IconicsDrawable
import com.mikepenz.iconics.typeface.IIcon
import com.mikepenz.iconics.utils.colorInt
import com.mikepenz.iconics.view.IconicsImageView
import com.tiper.MaterialSpinner
import java.text.DecimalFormat
import java.text.DecimalFormatSymbols


object BindingAdapters {


    @JvmStatic @BindingAdapter(value = ["textChange"], requireAll = false) fun setTextChange(view: TextInputEditText,
        validateType: String) {
        view.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) { //TODO nothing
            }

            override fun onTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {
                try {
                    (view.parent.parent as TextInputLayout).error = Helper.validatorInput(charSequence.toString(),
                        validateType)
                } catch (e: Exception) {
                    AppConfigs.logException(e)
                }
            }

            override fun afterTextChanged(editable: Editable) { //                var text = editable.toString().toUpperCase(Locale.ROOT)
                //                editable.clear()
            }
        })
    }

    @JvmStatic @BindingAdapter(value = ["textChange"], requireAll = false) fun setTextChange(view: AutoCompleteTextView,
        validateType: String) {
        view.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) { //TODO nothing
            }

            override fun onTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {
                try {
                    (view.parent.parent as TextInputLayout).error = Helper.validatorInput(charSequence.toString(),
                        validateType)
                } catch (e: Exception) {
                    AppConfigs.logException(e)
                }
            }

            override fun afterTextChanged(editable: Editable) { //                var text = editable.toString().toUpperCase(Locale.ROOT)
                //                editable.clear()
            }
        })
    }

    @JvmStatic @BindingAdapter(value = ["autoformat"], requireAll = false) fun setAutoFormat(view: TextInputEditText,
        validateType: String) {
        view.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) { //TODO nothing
            }

            override fun onTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {
                try {


                } catch (e: Exception) {
                    AppConfigs.logException(e)
                }
            }

            override fun afterTextChanged(editable: Editable) {
                view.removeTextChangedListener(this)
                try {
                    if (validateType == "number") {
                        val df = DecimalFormat()
                        val symbols = DecimalFormatSymbols()
                        symbols.decimalSeparator = ','
                        symbols.groupingSeparator = '.'
                        df.decimalFormatSymbols = symbols

                        var value = view.text.toString()
                        value = value.replace(Regex("[^0-9]"), "")

                        if (value != "") {
                            view.setText(df.format(value.toInt()))
                            view.setSelection(view.text!!.length)
                        }
                    }
                } catch (nfe: NumberFormatException) {
                    nfe.printStackTrace();
                }
                view.addTextChangedListener(this)
            }
        })
    }

    @JvmStatic @BindingAdapter("iconis") fun iconis(view: IconicsImageView, icon: IIcon) {

        view.icon?.icon = icon
    }

    @JvmStatic @BindingAdapter("iconis") fun iconis(view: IconicsImageView, icon: String) {
        try {
            view.setImageDrawable(IconicsDrawable(view.context, icon))
        } catch (e: Exception) {

        }

    }

    @JvmStatic @BindingAdapter("strike") fun strike(view: TextView, value: String) {
        view.text = value
        view.paintFlags = STRIKE_THRU_TEXT_FLAG
    }

    @JvmStatic @BindingAdapter("iconisColor") fun iconisColor(view: IconicsImageView, color: Int) {
        view.icon?.colorInt = color
    }

    @JvmStatic @BindingAdapter("viewColor") fun viewColor(view: View, color: Int) {
        view.setBackgroundColor(color)
    }

    @JvmStatic @BindingAdapter("btnCopy") fun btnCopy(view: View, text: String?) {

        if (text != null) {
            view.setOnClickListener {
                val clipboard = view.context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                val clip = ClipData.newPlainText("text", text)
                clipboard.setPrimaryClip(clip)
                val toast = Toast.makeText(view.context, "Đã sao chép", Toast.LENGTH_SHORT)
                toast.show()

            }
        }

    }

    @JvmStatic @BindingAdapter(value = ["imageUrl", "imageStyle"], requireAll = false) fun setImageUrl(view: ImageView,
        imageUrl: String?,
        imageStyle: String? = "NONE") { //CIRCLE - ROUND - NONE
        val factory = DrawableCrossFadeFactory.Builder().setCrossFadeEnabled(true).build()

        if (imageUrl == null || imageUrl.isEmpty()) {
            Glide.with(view.context).load(R.drawable.logo_gray).apply(RequestOptions()).skipMemoryCache(true)
                .placeholder(R.drawable.placeholder_backgroud_gray).transition(withCrossFade(factory)).into(view)
        } else {

            when (imageStyle) {
                "CIRCLE" -> {
                    Glide.with(view.context).load(imageUrl).apply(RequestOptions().circleCrop()).skipMemoryCache(true)
                        .placeholder(R.drawable.placeholder_backgroud_gray).transition(withCrossFade(factory))
                        .into(view)
                }
                "ROUND" -> {
                    Glide.with(view.context).load(imageUrl).transform(CenterCrop(), RoundedCorners(10))
                        .skipMemoryCache(true).transition(withCrossFade(factory))
                        .placeholder(R.drawable.placeholder_backgroud_gray).into(view)
                }
                else -> {
                    Glide.with(view.context).load(imageUrl).apply(RequestOptions()).skipMemoryCache(true)
                        .transition(withCrossFade(factory)).placeholder(R.drawable.placeholder_backgroud_gray)
                        .into(view)
                }
            }
        }

    }

    @JvmStatic @BindingAdapter("onItemSelected", requireAll = false) fun onItemSelected(spinner: MaterialSpinner,
        viewModel: TypePaxViewModel) {
        spinner.onItemSelectedListener = object : MaterialSpinner.OnItemSelectedListener {
            override fun onItemSelected(parent: MaterialSpinner, view: View?, position: Int, id: Long) {
                parent.error = null
                viewModel.onSelectTitleItem(parent, view, position, id)
            }

            override fun onNothingSelected(parent: MaterialSpinner) { // sometimes you need nothing here
            }
        }
    }

    @JvmStatic @BindingAdapter("onItemSelected", requireAll = false) fun onItemSelected(spinner: MaterialSpinner, viewModel: TypePaxViewModelV2) {
        spinner.onItemSelectedListener = object : MaterialSpinner.OnItemSelectedListener {
            override fun onItemSelected(parent: MaterialSpinner, view: View?, position: Int, id: Long) {
                parent.error = null
                viewModel.onSelectTitleItem(parent, view, position, id)
            }

            override fun onNothingSelected(parent: MaterialSpinner) { // sometimes you need nothing here
            }
        }
    }

    @JvmStatic @BindingAdapter("customEntries") fun <T> setcustomEntries(view: MaterialSpinner,
        entries: ArrayList<String>?) {
        if (entries != null) {
            val adapter = ArrayAdapter<String>(view.context, android.R.layout.simple_spinner_dropdown_item, entries)
            view.adapter = adapter
        } else {
            view.adapter = null
        }
    }

    @JvmStatic
    @BindingAdapter(value = ["selectedValue"], requireAll = false)
    fun bindSpinnerData(pAppCompatSpinner: MaterialSpinner, newSelectedValue: String?) {
        if (newSelectedValue != null && pAppCompatSpinner.adapter != null) {
            pAppCompatSpinner.post {
                for (i in 0 until pAppCompatSpinner.adapter!!.count) {
                    val item = pAppCompatSpinner.adapter!!.getItem(i)
                    if (item != null && item.toString() == newSelectedValue) {
                        pAppCompatSpinner.selection = i
                        break
                    }
                }
            }
        }
    }



    @JvmStatic @BindingAdapter("arrayEntries") fun <T> setEntries(view: Spinner, entries: ArrayList<String>?) {
        if (entries != null) {
            val adapter = ArrayAdapter<String>(view.context, android.R.layout.simple_spinner_dropdown_item, entries)
            view.adapter = adapter
        } else {
            view.adapter = null
        }
    }

    @JvmStatic @BindingAdapter(value = ["selectedValue"],
        requireAll = false) fun bindSpinnerData(pAppCompatSpinner: AppCompatSpinner, newSelectedValue: String?) {

        if (newSelectedValue != null && pAppCompatSpinner.adapter != null) {
            val pos = (pAppCompatSpinner.adapter as ArrayAdapter<String>).getPosition(newSelectedValue)
            pAppCompatSpinner.setSelection(pos, false)
        }
    }

    @JvmStatic @BindingAdapter("fawText") fun setFawText(view: TextView, text: String) {
        val fontAwesome = Typeface.createFromAsset(view.context.assets, "fonts/fontawesome-webfont.ttf")
        view.typeface = fontAwesome
        view.text = text
    }

    @JvmStatic @BindingAdapter("statusTextColor") fun statusTextColor(view: TextView, status: String) {
        var statusBackground = (view.parent as View)
        view.setTextColor(ContextCompat.getColor(view.context, R.color.stt_gray))
        if (status == "done") {
            view.setTextColor(ContextCompat.getColor(view.context, R.color.green))
        } else if (status == "expired") {
            view.setTextColor(Color.parseColor("#B1B1B1"))
        } else if (status == "created") {
            view.setTextColor(Color.parseColor("#FB953B"))
        } else if (status == "waiting_payment") {
            view.setTextColor(ContextCompat.getColor(view.context, R.color.primary))
        } else if (status == "payment_success") {
            view.setTextColor(Color.parseColor("#FB953B"))
        } else {
            view.setTextColor(ContextCompat.getColor(view.context, R.color.stt_gray))
        }

    }

    @JvmStatic @BindingAdapter("statusTextBg") fun setStatusTextBg(view: TextView, status: String) {
        var statusBackground = (view.parent as View)
        view.setTextColor(ContextCompat.getColor(view.context, R.color.white))
        if (status == "done") {
            statusBackground.background = ContextCompat.getDrawable(view.context, R.drawable.corner_full_green)
        } else if (status == "expired") {
            statusBackground.background = ContextCompat.getDrawable(view.context, R.drawable.corner_full_gray)
        } else if (status == "created") {
            statusBackground.background = ContextCompat.getDrawable(view.context, R.drawable.corner_full_yellow)
        } else if (status == "waiting_payment") {
            statusBackground.background = ContextCompat.getDrawable(view.context, R.drawable.corner_full_primary)
        } else if (status == "payment_success") {
            statusBackground.background = ContextCompat.getDrawable(view.context, R.drawable.corner_full_yellow)
        } else {
            statusBackground.background = ContextCompat.getDrawable(view.context, R.drawable.corner_full_gray)
        }

    }

    @JvmStatic @BindingAdapter("isBold") fun setBold(view: TextView, isBold: Boolean) {
        if (isBold) {
            view.setTypeface(null, Typeface.BOLD)
        } else {
            view.setTypeface(null, Typeface.NORMAL)
        }
    }

    @JvmStatic @InverseBindingAdapter(attribute = "selectedValue") fun captureSelectedValue(pAppCompatSpinner: AppCompatSpinner): String {
        return pAppCompatSpinner.selectedItem as String
    }

    @JvmStatic @BindingAdapter("visibility") fun setVisibility(view: View, isVisible: Boolean) {
        view.visibility = if (isVisible) View.VISIBLE else View.GONE
    }

}