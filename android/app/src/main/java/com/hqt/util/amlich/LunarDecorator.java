package com.hqt.util.amlich;

/**
 * Created by NT on 4/15/2016.
 */

import android.content.res.Resources;
import android.graphics.Color;

import com.prolificinteractive.materialcalendarview.CalendarDay;
import com.prolificinteractive.materialcalendarview.DayViewDecorator;
import com.prolificinteractive.materialcalendarview.DayViewFacade;

import java.util.Calendar;
import java.util.concurrent.TimeUnit;


/**
 * Decorate several days with a dot
 */
public class LunarDecorator implements DayViewDecorator {

    private int color;
    private CalendarDay date;


    public LunarDecorator(int color, CalendarDay dates) {
        this.color = color;
        this.date = dates;
    }

    @Override
    public boolean shouldDecorate(CalendarDay day) {
        return date != null && day.equals(date);
    }

    @Override
    public void decorate(DayViewFacade view) {

        Calendar cal = Calendar.getInstance();
        cal.set(date.getYear(), date.getMonth() + 1, date.getDay());

        float tz = TimeUnit.HOURS.convert(cal.getTimeZone().getRawOffset(), TimeUnit.MILLISECONDS);
        YMD tmp = LunarCalendarUtil.convertSolar2Lunar(date.getYear(), date.getMonth(), date.getDay(), tz);

        String ngayAm = tmp.day + "";
        if (tmp.day == 1) {
            ngayAm = tmp.day + "/" + tmp.month;
            color = Color.RED;
        }

        if (tmp.day == 15) {
            ngayAm = tmp.day + "";
            color = Color.RED;
        }

        final float scale = Resources.getSystem().getDisplayMetrics().density;
        view.addSpan(new LunarSpan(ngayAm + "", color));
        if (tmp.month == 1 && (tmp.day == 1 || tmp.day == 2 || tmp.day == 3)) {
            view.addSpan(new TopDotSpan(5, Color.RED));
        }

    }
}