package com.hqt.util

import com.hqt.data.prefs.SharedPrefsHelper
import com.hqt.datvemaybay.BuildConfig

object ApiUtil {

    /**
     * Response pcCode
     */
    const val SUCCESS: Int = 200



    private fun getBaseHeader(): HashMap<String, String> {
        val map = HashMap<String, String>()
//        map.addHasMap("Accept", "application/json")
//        map.addHasMap("Platform", "ANDROID")
//        map.addHasMap("Ver", BuildConfig.VERSION_NAME)
//        map.addHasMap("VerCode", BuildConfig.VERSION_CODE.toString())
//        map.addHasMap(
//            "Device-Info",
//            android.os.Build.MANUFACTURER + " - " + android.os.Build.MODEL + " - " + android.os.Build.VERSION.RELEASE
//        )
//        map.addHasMap("App-Version", BuildConfig.VERSION_NAME)
        map.addHasMap("Content-Type" , "application/json")
        map.addHasMap("X-Auth-ID", "9B1B13952BD9FF446AB569BBB49B3")
        map.addHasMap("Origin", "https://12bay.vn")
        map.addHasMap("Referer", "https://12bay.vn")
        map.addHasMap("X-Requested-With", "XMLHttpRequest")


        return map

    }
    fun HashMap<String, String>.addHasMap(key: String, value: String): HashMap<String, String> {
        this[key] = value
        return this
    }

    fun header(sharedPrefs: SharedPrefsHelper): Map<String, String> {
//        val tkn = AppConstants.ACCESS_TKN
//        if (BuildConfig.DEBUG) {
//            Log.d("TKN", "Bearer $tkn")
//        }
        val map = getBaseHeader()
//        map.addHasMap("Authorization", "Bearer $tkn")
        return map
    }






}