package com.hqt.util.amlich;

/**
 * Created by NT on 4/15/2016.
 */

import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;

import com.prolificinteractive.materialcalendarview.CalendarDay;
import com.prolificinteractive.materialcalendarview.DayViewDecorator;
import com.prolificinteractive.materialcalendarview.DayViewFacade;

/**
 * Decorate several days with a dot
 */
public class EventDecorator implements DayViewDecorator {

    private int color;
    private CalendarDay date;
    private final Drawable drawable;
    private final Bitmap bitmap;

    public EventDecorator(Drawable dr, Bitmap bm, CalendarDay dates) {

        drawable = dr;
        bitmap = bm;
        this.date = dates;
    }

    public EventDecorator(Drawable dr, CalendarDay dates) {

        drawable = dr;
        bitmap = null;
        this.date = dates;
    }

    @Override
    public boolean shouldDecorate(CalendarDay day) {
        return date != null && day.equals(date);
    }

    @Override
    public void decorate(DayViewFacade view) {
        // view.addSpan(new DotSpan(5, color));
        view.setBackgroundDrawable(drawable);
        // view.addSpan(new SelectSpan(5, Color.parseColor("#F1F1F1"),bitmap));

    }
}