package com.hqt.util.amlich;

/**
 * Created by NT on 4/15/2016.
 */

import android.content.res.Resources;

import com.prolificinteractive.materialcalendarview.CalendarDay;
import com.prolificinteractive.materialcalendarview.DayViewDecorator;
import com.prolificinteractive.materialcalendarview.DayViewFacade;

import java.util.Calendar;


/**
 * Decorate several days with a dot
 */
public class TextDecorator implements DayViewDecorator {

    private String text;
    private int color;
    private CalendarDay date;


    public TextDecorator(int color, CalendarDay dates, String txt) {
        this.color = color;
        this.date = dates;
        this.text = txt;
    }

    @Override
    public boolean shouldDecorate(CalendarDay day) {
        return date != null && day.equals(date);
    }

    @Override
    public void decorate(DayViewFacade view) {

        Calendar cal = Calendar.getInstance();
        cal.set(date.getYear(), date.getMonth() + 1, date.getDay());

        final float scale = Resources.getSystem().getDisplayMetrics().density;
        view.addSpan(new TextSpan(this.text + "", color));

        //view.addSpan(new TopDotSpan(5, Color.RED));


    }
}