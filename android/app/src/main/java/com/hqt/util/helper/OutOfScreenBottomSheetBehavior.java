package com.hqt.util.helper;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.FrameLayout;

import androidx.coordinatorlayout.widget.CoordinatorLayout;

import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.hqt.datvemaybay.R;

/**
 * {@link BottomSheetBehavior} that shows automatically when the dependency goes out of the screen
 * and hides when it comes back in.
 */

public class OutOfScreenBottomSheetBehavior extends BottomSheetBehavior<FrameLayout> {

    private int statusBarHeight;
    private int screenHeight;

    public OutOfScreenBottomSheetBehavior(Context context, AttributeSet attrs) {
        super(context, attrs);

        int resourceId = context.getResources().getIdentifier("status_bar_height", "dimen", "android");
        if (resourceId > 0) {
            statusBarHeight = context.getResources().getDimensionPixelSize(resourceId);
        }
        screenHeight = context.getResources().getDisplayMetrics().heightPixels;
    }

    @Override
    public boolean layoutDependsOn(CoordinatorLayout parent, FrameLayout child, View dependency) {
        return dependency.getId() == R.id.behavior_dependency;
    }

    @Override
    public boolean onDependentViewChanged(CoordinatorLayout parent, FrameLayout child, View dependency) {
        int[] dependencyLocation = new int[2];

        dependency.getLocationInWindow(dependencyLocation);

        if (dependencyLocation[1] <= statusBarHeight) {
            if (getState() != STATE_EXPANDED) {
                setState(STATE_EXPANDED);
            }
        } else if (dependencyLocation[1] > (screenHeight - statusBarHeight)) {
            if (getState() != STATE_EXPANDED) {
                setState(STATE_EXPANDED);
            }
        } else {
            setState(STATE_HIDDEN);
        }


        return false;
    }

}
