package com.hqt.util;

import static com.hqt.datvemaybay.Common.getMd5Hash;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;

import com.android.volley.AuthFailureError;
import com.android.volley.DefaultRetryPolicy;
import com.android.volley.Request;
import com.android.volley.Response;
import com.android.volley.VolleyError;
import com.android.volley.toolbox.JsonObjectRequest;
import com.hqt.datvemaybay.BuildConfig;
import com.hqt.datvemaybay.Common;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

public class SSLSendRequest {
    public Context mContext;
    public static String APILINK = AppConfigs.getInstance().getConfig().getString("root_api");
    
    public SSLSendRequest(Context mContext) {
        this.mContext = mContext;
    }

    public interface CallBackInterface {
        public void onSuccess(JSONObject response, boolean cached);

        public void onFail(VolleyError error);
    }

    public static String getAPILINK() {
        if (APILINK == null || APILINK.isEmpty())
            return "https://ssl.12bay.vn";
        return APILINK;
    }

    public void GET(boolean saveCache, String service, JSONObject request, CallBackInterface callBackInterface) {
        String cacheKey = getMd5Hash(service.toString());

        if (isNetworkAvailable(this.mContext)) {
            String fullUrl = getAPILINK() + "/api/v1/" + service + '/' + convertToJSONObjecttoQueryString(formatResquest(request));
            AppConfigs.Log("SEND ", fullUrl);
            JsonObjectRequest jsonObjReq = new JsonObjectRequest(Request.Method.GET,
                    fullUrl, null,
                    new Response.Listener<JSONObject>() {
                        @Override
                        public void onResponse(JSONObject response) {

                            if (saveCache) {
                                SharedPrefs.getInstance().put(cacheKey, response);
                            }
                            callBackInterface.onSuccess(response, false);
                        }
                    }, new Response.ErrorListener() {
                @Override
                public void onErrorResponse(VolleyError error) {

                    AppConfigs.logException(error, fullUrl + " ==> " + request.toString());
                    callBackInterface.onFail(error);
                }
            }) {
                @Override
                public Map getHeaders() throws AuthFailureError {
                    return getHeaderRequest();
                }
            };

            jsonObjReq.setRetryPolicy(new DefaultRetryPolicy(
                    120000,
                    0,
                    DefaultRetryPolicy.DEFAULT_BACKOFF_MULT));

            AppController.getInstance().addToRequestQueue(jsonObjReq, service);
        } else {
            try {
                if (saveCache) {
                    JSONObject response = SharedPrefs.getInstance().get(cacheKey, JSONObject.class);
                    callBackInterface.onSuccess(response, true);
                    AppConfigs.Log("Load Data from cache", response.toString());
                } else {
                    callBackInterface.onSuccess(new JSONObject(), false);
                }
            } catch (Exception e) {
                AppConfigs.logException(e);
                e.printStackTrace();
                callBackInterface.onSuccess(new JSONObject(), false);
            }
        }
    }

    public void POST(boolean saveCache, String service, JSONObject postParam, CallBackInterface callBackInterface) {
        String cacheKey = getMd5Hash(service + postParam.toString());

        if (isNetworkAvailable(this.mContext)) {
            String fullUrl = getAPILINK() + "/api/v1/" + service;
            AppConfigs.Log("POST ", " -> " + fullUrl);

            JsonObjectRequest jsonObjReq = new JsonObjectRequest(Request.Method.POST,
                    fullUrl, formatResquest(postParam),
                    new Response.Listener<JSONObject>() {
                        @Override
                        public void onResponse(JSONObject response) {
                            callBackInterface.onSuccess(response, false);
                            AppConfigs.Log("POST RESPONSE ", " -> " + response.toString());
                            if (saveCache) SharedPrefs.getInstance().put(cacheKey, response);
                        }
                    }, new Response.ErrorListener() {

                @Override
                public void onErrorResponse(VolleyError e) {
                    AppConfigs.logException(e, fullUrl + " ==> " + postParam.toString());
                    e.printStackTrace();
                    callBackInterface.onFail(e);
                }
            }) {
                /** Passing some request headers* */
                @Override
                public Map getHeaders() throws AuthFailureError {
                    return getHeaderRequest();
                }
            };
            //request SETTING
            jsonObjReq.setRetryPolicy(new DefaultRetryPolicy(
                    120000, 0,
                    DefaultRetryPolicy.DEFAULT_BACKOFF_MULT));

            AppController.getInstance().addToRequestQueue(jsonObjReq, service);
        } else {
            try {
                if (saveCache) {
                    JSONObject response = SharedPrefs.getInstance().get(cacheKey, JSONObject.class);
                    callBackInterface.onSuccess(response, true);
                    AppConfigs.Log("Load Data from cache", response.toString());
                } else {
                    callBackInterface.onSuccess(new JSONObject(), false);
                }
            } catch (Exception e) {
                AppConfigs.logException(e);
                e.printStackTrace();
                callBackInterface.onSuccess(new JSONObject(), false);
            }


        }
    }

    private static JSONObject formatResquest(JSONObject request) {
        try {

            request.put("ver", BuildConfig.VERSION_NAME);
            request.put("source", "ANDROID");
            request.put("key", Common.getKeyHash());
            request.put("device_id", Common.DEVICE_ID);
            request.put("track", AppController.getInstance().getTrackingCode());

            if (!request.has("uid")) {
                request.put("uid", Common.Uid);
            }
            AppConfigs.Log("POST ", " -> " + request.toString());

        } catch (JSONException e) {
            AppConfigs.logException(e);
        }

        return request;
    }

    public HashMap getHeaderRequest() {
        HashMap headers = new HashMap();
        headers.put("Content-Type", "application/json");
        headers.put("X-Auth-ID", "9B1B13952BD9FF446AB569BBB49B3");
        headers.put("Origin", "https://12bay.vn");
        headers.put("Referer", "https://12bay.vn");
        headers.put("X-Requested-With", "XMLHttpRequest");

        return headers;
    }

    public String convertToJSONObjecttoQueryString(JSONObject params) {
        StringBuilder sb = new StringBuilder();
        Iterator<String> iter = params.keys();
        while (iter.hasNext()) {
            String key = iter.next();
            try {
                Object value = params.get(key);
                if (sb.length() > 0) {
                    sb.append('&');
                } else {
                    sb.append('?');
                }
                sb.append(key).append("=").append(value);
            } catch (JSONException e) {
                // Something went wrong!
            }
        }
        return sb.toString();
    }

    public boolean isNetworkAvailable(Context ct) {
        ConnectivityManager connectivityManager = (ConnectivityManager) ct.getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
        return activeNetworkInfo != null;
    }


}
