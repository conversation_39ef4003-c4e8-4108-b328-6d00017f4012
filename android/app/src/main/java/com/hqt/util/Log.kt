package com.hqt.util

import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.gson.Gson
import com.hqt.datvemaybay.BuildConfig
import java.util.concurrent.TimeUnit
import java.util.concurrent.locks.ReentrantLock

object Log {

    const val TAG = "12BAY-APP"   // TODO change default tag for logging
    var DEBUG = BuildConfig.DEBUG
    private val lockHeader = ReentrantLock(true)

    fun getHeader(): String {
        if (!lockHeader.tryLock(50, TimeUnit.MILLISECONDS)) {
            return "[]: "
        }

        try {
            val currentClassName = Log.javaClass.name
            val traces = Thread.currentThread().stackTrace
            var found = false

            for (trace in traces) {
                try {
                    if (found) {
                        if (!trace.className.startsWith(currentClassName)) {
                            val targetClassName = Class.forName(trace.className)
                            return "[${getClassName(targetClassName)}.${trace.methodName}.${trace.lineNumber}]: "
                        }
                    } else if (trace.className.startsWith(currentClassName)) {
                        found = true
                        continue
                    }
                } catch (e: ClassNotFoundException) {
                } catch (e2: IncompatibleClassChangeError) {
                }
            }
        } catch (eThread: InterruptedException) {
            e(eThread)
        } finally {
            lockHeader.unlock()
        }
        return "[]: "
    }

    private fun getClassName(clazz: Class<*>?): String {
        if (clazz != null) {
            if (!clazz.simpleName.isNullOrEmpty()) {
                return clazz.simpleName
            } else {
                return getClassName(clazz.enclosingClass)
            }
        } else {
            return ""
        }
    }

    fun convertMessage(msg: Any?): String {
        if (msg is String || msg is Int || msg is Float || msg == null) {
            return msg.toString()

        } else {
            try {
                return Gson().toJson(msg).toString()
            } catch (e: Exception) {
                return msg.toString()
            }


        }

    }

    @JvmStatic
    inline fun d(tag: String = TAG, msg: Any?) {
        if (DEBUG) {
            android.util.Log.d(
                "[12BAY-APP]$tag",
                getHeader() + convertMessage(msg)
            )

        }


    }

    fun logException(throwable: Throwable?) {

        try {
            FirebaseCrashlytics.getInstance().recordException(throwable!!)
            if (DEBUG) {
                throwable.printStackTrace()
            }
        } catch (e: java.lang.Exception) {
            FirebaseCrashlytics.getInstance().recordException(e)
        }
    }

    inline fun i(tag: String = TAG, msg: Any) {
        if (DEBUG) android.util.Log.i("[12BAY-APP]$tag", getHeader() + convertMessage(msg))
    }

    inline fun v(tag: String = TAG, msg: Any) {
        if (DEBUG) android.util.Log.v("[12BAY-APP]$tag", getHeader() + convertMessage(msg))
    }

    inline fun w(tag: String = TAG, msg: Any, toString: String) {
        if (DEBUG) android.util.Log.w("[12BAY-APP]$tag", getHeader() + convertMessage(msg))
    }

    @JvmStatic
    inline fun e(tag: String = TAG, cause: Throwable, noinline msg: (() -> String)? = null) {
        if (DEBUG) {
            android.util.Log.e("[12BAY-APP]$tag", msg?.let { it.invoke() } ?: "", cause)
        }
        logException(cause)
    }

    inline fun e(cause: Throwable) {
        if (DEBUG) android.util.Log.e("[12BAY-APP]$TAG", "", cause)
    }


}