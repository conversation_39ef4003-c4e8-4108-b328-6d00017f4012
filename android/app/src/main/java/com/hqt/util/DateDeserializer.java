package com.hqt.util;

import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;

import java.lang.reflect.Type;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.FormatterClosedException;
import java.util.Locale;

public class DateDeserializer implements JsonDeserializer<Date>, JsonSerializer<Date> {
    public static final DateDeserializer sInstance = new DateDeserializer();

    @Override
    public Date deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
        if (json != null) {

            String jsonString = json.getAsString();
            String format = "yyyy-MM-dd";
            if (jsonString.length() == 10) {
                format = "yyyy-MM-dd";
            } else if (jsonString.length() == 16) {
                format = "HH:mm dd/MM/yyyy";
            } else if (jsonString.length() == 19) {
                format = "yyyy-MM-dd HH:mm:ss";
                jsonString = jsonString.replace("T", " ");
            } else if (jsonString.length() > 19) {
                format = "yyyy-MM-dd'T'HH:mm:ss";
                jsonString = jsonString.substring(0, 19);
            }
            SimpleDateFormat sServerDateDateFormat = new SimpleDateFormat(format, Locale.US);

            try {
                return sServerDateDateFormat.parse(jsonString);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    @Override
    public JsonElement serialize(Date src, Type typeOfSrc, JsonSerializationContext context) {
        if (src == null) {
            return null;
        }
        String format = "yyyy-MM-dd HH:mm:ss";

        if (typeOfSrc == Date.class) {
            String now = "";

            try {
                SimpleDateFormat dateFormat = new SimpleDateFormat(format, Locale.US);
                now = dateFormat.format(src.getTime());
                return new JsonPrimitive(now);

            } catch (FormatterClosedException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
                return null;
            }
        }

        return null;
    }
}
