package com.hqt.base


import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.res.Configuration
import android.graphics.Color
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.Button
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.LayoutRes
import androidx.annotation.RawRes
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import com.airbnb.lottie.LottieAnimationView
import com.bumptech.glide.Glide
import com.firebase.ui.auth.AuthUI
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.util.AppConfigs
import com.hqt.util.Log
import com.hqt.util.TypefaceUtil
import com.hqt.util.Widget
import com.hqt.util.common.ConnectionState
import com.hqt.util.common.checkConnection
import com.hqt.view.ui.BaseActivityKt.EmptyStateCallBackInterface
import com.hqt.view.ui.account.LoginActivity


abstract class BaseActivity<DB : ViewDataBinding> : AppCompatActivity() {


    val loadingProgress by lazy {
        Widget.progressDialog(this)
    }

//    private lateinit var firebaseAnalytics: FirebaseAnalytics
    private var mToolbar: Toolbar? = null


    private var auth: FirebaseAuth? = null
    val isUserSigned: Boolean get() = auth?.currentUser != null

    val firebaseUser: FirebaseUser?
        get() = auth?.currentUser

    @LayoutRes
    abstract fun getLayoutRes(): Int

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
//        menuInflater.inflate(R.menu.menu, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {

//        handelCall(
//            this,
//            BaseConstants.HOTLINE
//        )

        return super.onOptionsItemSelected(item)
    }


//    @Inject
//    var myAppPreferences: ApplicationModule? = null

    val binding by lazy {
        DataBindingUtil.setContentView(this, getLayoutRes()) as DB
    }

    /**
     * If you want to inject Dependency Injection
     * on your activity, you can override this.
     */
    open fun onInject() {

    }

    open fun onInit() {

    }

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        Log.d("ClassName", this.javaClass.simpleName)

        adjustFontScale(getResources().configuration)
        setContentView(binding.root)

        setUpToolbar()
        onInject()
        onInit()







        registerInternetCheckReceiver()
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                onBackPressCall()
            }
        })

    }



    fun signIn() {

        if (auth?.currentUser == null) {
            val login = Intent(this, LoginActivity::class.java)
            login.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            startActivity(login)
        } else {
            Toast.makeText(this, "Bạn đã đăng nhập rồi", Toast.LENGTH_SHORT).show()
        }
    }

    fun signOut() {
        if (auth!!.currentUser != null) {
            AuthUI.getInstance().signOut(this).addOnCompleteListener {
                Toast.makeText(applicationContext, "Đăng xuất thành công", Toast.LENGTH_SHORT)
                    .show()
                refreshLayout()
            }
            FirebaseAuth.getInstance().signOut()
        }
    }
    open fun refreshLayout() {}


    private fun setUpToolbar() {
        try {
            mToolbar = binding.root.findViewById(R.id.toolbar)
            setSupportActionBar(mToolbar)
            mToolbar?.setNavigationIcon(R.drawable.ic_action_back_home)
            mToolbar?.setTitleTextColor(Color.WHITE)
            mToolbar?.setSubtitleTextColor(Color.WHITE)
            supportActionBar?.setDisplayShowHomeEnabled(true)
            mToolbar?.bringToFront()


//            mToolbar?.setTitleTextAppearance(this, R.style.ToolbarTitleText)
//            mToolbar?.setSubtitleTextAppearance(this, R.style.ToolbarSubtitleText)


            mToolbar?.setNavigationOnClickListener {
                if (isTaskRoot) {
//                    val `in` = Intent(applicationContext, MainActivity::class.java)
//                    startActivity(`in`)
//                    finish()
                } else {
                    finish()
                }
            }


        } catch (e: Exception) {
            // e.printStackTrace()
        }
    }

    @Suppress("DEPRECATION")
    fun setWindowFlag(activity: Activity, bits: Int, on: Boolean) {
        val win = activity.window
        val winParams = win.attributes
        if (on) {
            winParams.flags = winParams.flags or bits
        } else {
            winParams.flags = winParams.flags and bits.inv()
        }
        win.attributes = winParams
    }


    fun getToolbar(): Toolbar? {
        return mToolbar
    }

    open fun requestPermissionResult(isGranted: Boolean) {

    }

    val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission(),
    ) { isGranted: Boolean ->
        if (isGranted) {
            // FCM SDK (and your app) can post notifications.

        } else {
            // TODO: Inform user that that your app will not show notifications.
        }
        requestPermissionResult(isGranted)
    }

    open fun registerInternetCheckReceiver() {
        val internetFilter = IntentFilter()
        internetFilter.addAction("android.net.wifi.STATE_CHANGE")
        internetFilter.addAction("android.net.conn.CONNECTIVITY_CHANGE")
        registerReceiver(broadcastReceiver, internetFilter)
    }

    /**
     * Runtime Broadcast receiver inner class to capture internet connectivity events
     */
    private var broadcastReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            checkConnection()
        }
    }

//    private fun checkConnection() {
//        checkConnection(this) {
////            Widget.showInternetStatus(it, false, this)
//        }
//    }

    override fun onDestroy() {
        super.onDestroy()
//        removeCallbacks()
    }

    open fun onBackPressCall() {
        finish()
    }

    private fun hideKeyboardOnTouch(event: MotionEvent): Boolean {
        if (event.action == MotionEvent.ACTION_DOWN) {
            val v = currentFocus
            if (v is View && isTouchOutsideEditText(v, event)) {
                // Ẩn bàn phím nếu người dùng chạm vào vùng bên ngoài bàn phím
                hideKeyboard(v)
                return true // Trả về true để chỉ định rằng sự kiện đã được xử lý
            }
        }
        return false
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        super.dispatchTouchEvent(ev)

        // Kiểm tra sự kiện chạm và ẩn bàn phím nếu cần
        if (ev?.action == MotionEvent.ACTION_DOWN) {
            val v = currentFocus
            if (v is View && isTouchOutsideEditText(v, ev)) {
                // Ẩn bàn phím nếu người dùng chạm vào vùng bên ngoài bàn phím
                hideKeyboard(v)
                return true // Trả về true để chỉ định rằng sự kiện đã được xử lý
            }
        }
        return false
    }

    private fun isTouchOutsideEditText(view: View, event: MotionEvent): Boolean {
        // Kiểm tra xem sự kiện chạm có nằm trong vùng EditText không
        val location = intArrayOf(0, 0)
        view.getLocationOnScreen(location)
        val x = event.rawX + view.left - location[0]
        val y = event.rawY + view.top - location[1]
        return (x < view.left || x > view.right || y < view.top || y > view.bottom)
    }

    private fun hideKeyboard(view: View) {
        // Ẩn bàn phím
        val inputMethodManager =
            getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        inputMethodManager.hideSoftInputFromWindow(view.windowToken, 0)
    }

    private fun adjustFontScale(configuration: Configuration) {


        TypefaceUtil.overrideFont(applicationContext, "SERIF", "fonts/Roboto-Regular.ttf")


        configuration.fontScale = 0.92.toFloat()
        val metrics = resources.displayMetrics
        val wm = getSystemService(WINDOW_SERVICE) as WindowManager
        wm.defaultDisplay.getMetrics(metrics)
        metrics.scaledDensity = configuration.fontScale * metrics.density
        baseContext.resources.updateConfiguration(configuration, metrics)
    }


    fun initEmptyState(
        textString: String,
        background: Int,
        @RawRes animationResId: Int,
        callBackInterface: EmptyStateCallBackInterface
    ): LinearLayout {
        val emptyStateLayout = findViewById<LinearLayout>(R.id.emptyStateLayout)
        try {
            val animationView = findViewById<LottieAnimationView>(R.id.animation_view)
            val emptyStateBackground = findViewById<ImageView>(R.id.emptyStateBackground)
            if (animationResId != -1) {

                animationView.setAnimation(animationResId)
                animationView.loop(true)
                animationView.playAnimation()
                animationView.visibility = View.VISIBLE
                emptyStateBackground.visibility = View.GONE
            } else {
                Glide.with(this).load(background).into(emptyStateBackground)
                emptyStateBackground.visibility = View.VISIBLE
                animationView.visibility = View.GONE
            }


            emptyStateLayout.visibility = View.GONE
            val emptyStateTitle = findViewById<TextView>(R.id.emptyStateTitle)
            emptyStateTitle.text = Common.convertHTML(textString)


            val btnNegative = findViewById<Button>(R.id.btnNegative)
            val btnPositive = findViewById<Button>(R.id.btnPositive)

            callBackInterface.negativeButton(btnNegative)
            callBackInterface.positiveButton(btnPositive)
        } catch (e: Exception) {
            AppConfigs.logException(e)
        }

        return emptyStateLayout

    }


    open fun onConnectionChange(it: ConnectionState) {

    }
    private fun checkConnection() {
        checkConnection(this) {
            Widget.showInternetStatus(it, false, this)
        }
    }



}





