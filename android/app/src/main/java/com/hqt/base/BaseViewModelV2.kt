package com.hqt.base

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel

open class BaseViewModelV2 : ViewModel() {

    val isUserSigned = MutableLiveData<Boolean>()
    val isInternetConneted = MutableLiveData<Boolean>()

    fun updateUserStatus(isLogin: <PERSON><PERSON><PERSON>) {
        isUserSigned.value = isLogin
    }

    fun updateInternetStatus(isConnected: Boolean) {
        isInternetConneted.value = isConnected
    }

    init {
        isUserSigned.value = false
        isInternetConneted.value = true
    }
}
