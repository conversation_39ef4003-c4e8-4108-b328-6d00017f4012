package com.hqt.base

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.annotation.LayoutRes
import androidx.appcompat.widget.Toolbar
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.fragment.app.Fragment
import com.firebase.ui.auth.AuthUI
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import com.hqt.datvemaybay.R
import com.hqt.util.Widget
import com.hqt.util.base.AutoDisposable
import com.hqt.view.ui.account.LoginActivity


abstract class BaseFragment<DB : ViewDataBinding>() : Fragment() {
    open lateinit var binding: DB


    val autoDisposable by lazy {
        AutoDisposable()
    }
    val loadingProgress by lazy {
        Widget.showLoading(requireContext())
    }


    private var auth: FirebaseAuth? = null
    val isUserSigned: Boolean get() = auth?.currentUser != null

    val firebaseUser: FirebaseUser?
        get() = auth?.currentUser


    open fun init(inflater: LayoutInflater, container: ViewGroup?) {
        binding = DataBindingUtil.inflate(inflater, getLayoutRes(), container, false)
    }




    open fun init() {
    }

    @LayoutRes
    abstract fun getLayoutRes(): Int

    open fun onInject() {}

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        //NEED OVERRIDE FRAGMENT IN BASE FRAGMENT
        init(inflater, container)
        init()
        super.onCreateView(inflater, container, savedInstanceState)

        return binding.root
    }

    fun signIn() {

        if (auth?.currentUser == null) {
            val login = Intent(requireContext(), LoginActivity::class.java)
            login.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            startActivity(login)
        } else {
            Toast.makeText(requireContext(), "Bạn đã đăng nhập rồi", Toast.LENGTH_SHORT).show()
        }
    }

    fun signOut() {
        if (auth!!.currentUser != null) {
            AuthUI.getInstance().signOut(requireContext()).addOnCompleteListener {
                Toast.makeText(requireContext(), "Đăng xuất thành công", Toast.LENGTH_SHORT)
                    .show()
                refreshLayout()
            }
            FirebaseAuth.getInstance().signOut()
        }
    }
    open fun refreshLayout() {}

    open fun refresh() {}



    fun getToolbar(): Toolbar? {
        try {
            if (binding.root.findViewById<Toolbar>(R.id.toolbar) != null) {
                return binding.root.findViewById(R.id.toolbar)

            }
            return (activity as? BaseActivity<*>)?.getToolbar()
        } catch (e: Exception) {
            return null
        }
    }

}
