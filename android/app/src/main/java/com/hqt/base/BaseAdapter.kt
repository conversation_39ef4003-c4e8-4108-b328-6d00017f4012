package com.hqt.base

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.annotation.LayoutRes
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.recyclerview.widget.RecyclerView
import com.hqt.datvemaybay.R
import com.hqt.util.Helper.clickWithDebounce
import java.io.Serializable

abstract class BaseAdapter<Model : Serializable, DB : ViewDataBinding>(private val listener: (Model) -> Unit) :
    RecyclerView.Adapter<BaseAdapter.BaseViewHolder<DB>>() {

    init {
        this.setHasStableIds(true)
    }

    lateinit var _context: Context

    var modelListData = mutableListOf<Model>()
    private lateinit var binding: DB
    var onBindCLick = true

    @SuppressLint("NotifyDataSetChanged")
    fun setData(list: MutableList<Model>?) {
        list?.let {
            modelListData = list
        } ?: modelListData.clear()

        notifyDataSetChanged()
    }

    @SuppressLint("NotifyDataSetChanged")
    fun clearData() {
        this.modelListData.apply {
            clear()
        }
        notifyDataSetChanged()
    }

    @SuppressLint("NotifyDataSetChanged")
    fun addData(list: List<Model>) {
        val countFirst = modelListData.size
        this.modelListData.apply {
            addAll(list)
            notifyItemRangeInserted(countFirst, modelListData.size)
        }
        notifyDataSetChanged()
    }

    /**
     * Lấy dữ liệu hiện tại
     */
    open fun getData(): MutableList<Model> {
        return modelListData
    }

    /**
     * Nhận tổng số dữ liệu
     */
    open fun getCount(): Int {
        return modelListData.size
    }

    @LayoutRes
    abstract fun getLayoutRes(): Int
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder<DB> {
        _context = parent.context
        binding = DataBindingUtil.inflate(
            LayoutInflater.from(parent.context),
            getLayoutRes(), parent, false
        )

        return BaseViewHolder(
            binding
        )
    }

    override fun onBindViewHolder(holder: BaseViewHolder<DB>, position: Int) {
        holder.binding.root.setTag(R.string.position, position)

        bind(holder.binding, position, modelListData[position])

        if (onBindCLick) {
            holder.binding.root.clickWithDebounce(500) {
                onItemClickListener(modelListData[position])
            }
        }

    }

    open fun onItemClickListener(model: Model) {
    }


    abstract fun bind(binding: DB, position: Int, model: Model)

    override fun getItemCount(): Int = modelListData.size

    open fun init() {

    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    fun getItem(position: Int): Model {
        return modelListData[position]
    }


    class BaseViewHolder<DB : ViewDataBinding>(
        val binding: DB
    ) :
        RecyclerView.ViewHolder(binding.root) {
    }

    open fun onItemClickListener(model: Model, position: Int) {
    }

}