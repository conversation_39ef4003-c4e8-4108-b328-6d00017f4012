package com.hqt.base.model

import com.google.gson.annotations.SerializedName

open class HttpData<T>(
    @field:SerializedName("status")
    open var status: Boolean = false,

    @field:SerializedName("message")
    open var message: String? = null,


    @field:SerializedName("code")
    open var code: String? = "",

//    @field:SerializedName("meta")
//    open var meta: MetaDataModel? = null,

    @field:SerializedName("data")
    open var data: T? = null,


    @field:SerializedName("value")
    open var value: T? = null,

    ) {


}