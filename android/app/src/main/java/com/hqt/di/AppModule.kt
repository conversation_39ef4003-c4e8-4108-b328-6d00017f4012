package com.hqt.di

import android.content.Context
import android.content.SharedPreferences
import android.os.Build
import android.security.keystore.KeyGenParameterSpec
import android.security.keystore.KeyProperties
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey
import com.google.gson.Gson
import com.hqt.data.prefs.ISharedPrefsHelper
import com.hqt.data.prefs.SharedPrefsHelper
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class AppModule {

    @Provides
    @Singleton
    internal fun provideSharedPreferences(@ApplicationContext context: Context): SharedPreferences {
        try {


            val appContext = context.applicationContext
//            val prefsName = appContext.packageName + "_12BAY.VN"
            val prefsName = "12BAY-APP-CONFIG"
            val sharedPrefsFile: String = prefsName

            lateinit var masterKeyAlias: MasterKey
            try {
                masterKeyAlias = MasterKey.Builder(appContext, MasterKey.DEFAULT_MASTER_KEY_ALIAS)
                    .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
                    .build()

            } catch (e: Exception) {
                val spec = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    KeyGenParameterSpec.Builder(
                        MasterKey.DEFAULT_MASTER_KEY_ALIAS,
                        KeyProperties.PURPOSE_ENCRYPT or KeyProperties.PURPOSE_DECRYPT
                    ).setBlockModes(KeyProperties.BLOCK_MODE_GCM)
                        .setEncryptionPaddings(KeyProperties.ENCRYPTION_PADDING_NONE)
                        .setKeySize(MasterKey.DEFAULT_AES_GCM_MASTER_KEY_SIZE)
                        .build()
                } else {
                    TODO("VERSION.SDK_INT < M")
                }
                masterKeyAlias = MasterKey.Builder(appContext)
                    .setKeyGenParameterSpec(spec)
                    .build()
            }


            return EncryptedSharedPreferences.create(
                appContext,
                sharedPrefsFile,
                masterKeyAlias,
                EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
                EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
            )
        } catch (e: Exception) {

            val appContext = context.applicationContext
//            val prefsName = appContext.packageName + "_062025"
            val prefsName = "12BAY-APP-CONFIG"
            return appContext.getSharedPreferences(prefsName, Context.MODE_PRIVATE)

        }


    }

//    @Provides
//    @Singleton
//    internal fun provideGson(): Gson {
//        return Gson()
//    }
}

@Module
@InstallIn(SingletonComponent::class)
abstract class AppModuleAbs {

    @Binds
    @Singleton
    abstract fun bindISharedPrefsHelper(
        sharedPrefsHelper: SharedPrefsHelper
    ): ISharedPrefsHelper

}