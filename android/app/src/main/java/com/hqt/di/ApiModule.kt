package  com.hqt.di

import android.content.Context
import android.os.Handler
import android.widget.Toast
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.hqt.data.model.PassengerTitle
import com.hqt.data.model.PassengerType
import com.hqt.data.prefs.ISharedPrefsHelper
import com.hqt.data.prefs.SharedPrefsHelper
import com.hqt.datvemaybay.BuildConfig
import com.hqt.util.CorePlaneOkHttpDNSSelector
import com.hqt.util.DateDeserializer
import com.hqt.util.DateOnly
import com.hqt.util.DateOnlyDeserializer
import com.hqt.util.Log
import com.hqt.util.SSLSendRequest
import com.hqt.view.adapter.EnumTypeAdapter
import com.hqt.view.adapter.JSONArrayAdapter
import com.hqt.view.adapter.JSONObjectAdapter
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import okhttp3.*
import okhttp3.ResponseBody.Companion.toResponseBody
import okhttp3.logging.HttpLoggingInterceptor
import okio.BufferedSource
import org.json.JSONArray
import org.json.JSONObject
import retrofit2.Retrofit
import retrofit2.adapter.rxjava3.RxJava3CallAdapterFactory
import retrofit2.converter.gson.GsonConverterFactory
import java.io.IOException
import java.nio.charset.Charset
import java.util.Date
import java.util.concurrent.TimeUnit
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
class ApiModule {
    @Provides
    @Singleton
    fun provideRetrofit(
        jsonParser: Gson,
        okHttpClient: OkHttpClient,
        sharedPrefsHelper: ISharedPrefsHelper
    ): Retrofit {
        val baseUrl = sharedPrefsHelper.getBaseUrl() ?: SSLSendRequest.getAPILINK()

        return Retrofit.Builder().baseUrl(baseUrl)
            .addConverterFactory(GsonConverterFactory.create(jsonParser))
            .addCallAdapterFactory(RxJava3CallAdapterFactory.create())
            .client(okHttpClient)
            .build()
    }


    @Provides
    @Singleton
    fun provideGson(): Gson {
        val gsonBuilder = GsonBuilder().setPrettyPrinting()
        gsonBuilder.registerTypeAdapter(JSONObject::class.java, JSONObjectAdapter.sInstance)
        gsonBuilder.registerTypeAdapter(JSONArray::class.java, JSONArrayAdapter.sInstance)
        gsonBuilder.registerTypeAdapter(Date::class.java, DateDeserializer.sInstance)
        gsonBuilder.registerTypeAdapter(DateOnly::class.java, DateOnlyDeserializer.sInstance)
        gsonBuilder.registerTypeAdapter(PassengerType::class.java, EnumTypeAdapter.sInstance)
        gsonBuilder.registerTypeAdapter(PassengerTitle::class.java, EnumTypeAdapter.sInstance)
        return gsonBuilder.create()
    }




    @Provides
    @Singleton
    fun provideOkHttpClient(
        @ApplicationContext context: Context,
        sharedPrefsHelper: SharedPrefsHelper
    ): OkHttpClient {
        try {
            // Create a trust manager that does not validate certificate chains
            val builder = OkHttpClient.Builder()
                .dns(CorePlaneOkHttpDNSSelector(CorePlaneOkHttpDNSSelector.IPvMode.IPV4_FIRST))

            builder.connectTimeout(60, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .addInterceptor(HttpLoggingInterceptor().apply {
                    if (BuildConfig.DEBUG) {
                        level = HttpLoggingInterceptor.Level.BODY
                    }

                })
                .addInterceptor(object : Interceptor {
                    @Throws(IOException::class)
                    override fun intercept(chain: Interceptor.Chain): Response {

                        val request: Request = chain.request()
                        try {

                            val response: Response = chain.proceed(request)
                            val mainHandler = Handler(context.mainLooper)

                            if (response.code == 401) {
                                sharedPrefsHelper.setAccessToken("")
                                try {
                                    mainHandler.post {
                                        Toast.makeText(
                                            context,
                                            "Bạn vui lòng đăng nhập để tiếp tục",
                                            Toast.LENGTH_LONG
                                        ).show()
                                        sharedPrefsHelper.setAccessToken("")
                                        sharedPrefsHelper.setRemember(false)

                                    }
                                } catch (e: Exception) {
                                    return response
                                }
                                return response
                            }
                            else if (response.code != 200) {
                                val responseBody = response.body
                                val source: BufferedSource? = responseBody?.source()
                                source?.request(Long.MAX_VALUE) // Buffer the entire body.
                                val buffer = source?.buffer
                                val message = buffer?.clone()?.readString(Charset.forName("UTF-8"))
                                try {
                                    if(BuildConfig.DEBUG) {
                                        mainHandler.post {
                                            Toast.makeText(context, message, Toast.LENGTH_SHORT)
                                                .show()
                                        }
                                    }
                                } catch (e: Exception) {
                                    Log.logException(e)

                                }

                                return response
                            }

                            return response
                        } catch (e: Exception) {
                            val mainHandler = Handler(context.mainLooper)
                            mainHandler.post {
                                Toast.makeText(
                                    context,
                                    "Không kết nối được đến hệ thống. Vui lòng thử lại sau",
                                    Toast.LENGTH_LONG
                                ).show()
                            }

                            return Response.Builder()
                                .request(request)
                                .protocol(Protocol.HTTP_2)
                                .code(999)
                                .message("Không kết nối được đến server")
                                .body("{${e}}".toResponseBody(null)).build()
                        }


                    }
                })

            return builder.build()
        } catch (e: Exception) {
            e.printStackTrace()
            throw RuntimeException(e)
        }
    }


}
