package com.hqt.data.model

import com.hqt.datvemaybay.Common
import java.io.Serializable
import java.util.*

class TrainSeatFare : Serializable {
    var id: Int? = null
    var trainId: Int? = null
    var trainVLId: Int? = null
    var coachId: Int? = null
    var originCode: String = ""
    var originName: String = ""
    var destinationCode: String = ""
    var destinationName: String = ""
    var originKm: Int = 0
    var destinationKm: Int = 0
    var seatClass: String = ""
    var seatNumber: Int? = null
    var seatClassName: String = ""
    var seatCount: Int = 0
    var trainNumber: String = ""
    var departureDateTime: Date? = null
    var arrivalDateTime: Date? = null
    var promo: Boolean = false
    var isQuickDep: Boolean = false
    var provider: String = ""
    var stops: Int = 0
    var rewardPoint: Int = 0
    var uuid: String = ""
    var adult: Int = 0
    var child: Int = 0
    var student: Int = 0
    var older: Int = 0
    var adultCount: Int = 0
    var childCount: Int = 0
    var studentCount: Int = 0
    var olderCount: Int = 0
    var netPrice: Int = 0
    var tax: Int = 0
    var insurance: Int = 0
    var fee: Int = 0
    var discountPT: Int = 0
    var discountTG: Int = 0

    fun getTotalFare(): Int {
        return adultCount * adult + childCount * child + studentCount * student + olderCount * older;
    }

    fun getAdultFare(): String {
        return "" + adultCount + "x" + Common.dinhDangTien(adult)
    }

    fun getChildFare(): String {
        return "" + childCount + "x" + Common.dinhDangTien(child)
    }

    fun getStudentFare(): String {
        return "" + studentCount + "x" + Common.dinhDangTien(student)
    }

    fun getOlderFare(): String {
        return "" + olderCount + "x" + Common.dinhDangTien(older)
    }

    fun getTripDuration(): String {
        return Common.getDuration(arrivalDateTime, departureDateTime)
    }

    fun getTripName(): String {
        return "$originName → $destinationName"
    }

    fun getRewardPointTotal(): Int {
        var point = 0;
        point = rewardPoint * (student + adultCount)
        return point
    }

    fun setPaxCount(booking: BookingTrain) {
        adultCount = booking.adult
        childCount = booking.child
        studentCount = booking.student
        olderCount = booking.older
    }
}