package com.hqt.data.model

import com.hqt.datvemaybay.Common.getDuration
import com.hqt.util.AppConfigs
import java.io.Serializable
import java.lang.Math.abs
import java.util.*
import kotlin.collections.ArrayList


class Train : Serializable {
    var id: Int? = null
    var trainVLId: Int? = null
    var originCode: String = ""
    var originName: String = ""
    var destinationCode: String = ""
    var destinationName: String = ""
    var originKm: Int = 0
    var destinationKm: Int = 0
    var departureDateTime: Date? = null
    var arrivalDateTime: Date? = null
    var trainNumber: String = ""
    var departureTime: String? = ""
    var arrivedTime: String? = ""
    var seatCount: Int = 0
    var seatLockCount: Int = 0
    var departureDate: String = ""
    var fareOptions: List<TrainSeatFare> = ArrayList()
    var fareOptionKey: String? = null
    var coachs: List<TrainCoach> = ArrayList()

    fun getRemainSeat(): Int {
        return seatCount - seatLockCount
    }

    fun getDuration(): String {
        return getDuration(arrivalDateTime, departureDateTime)
    }

    fun getTripLength(): String {
        return abs(originKm - destinationKm).toString() + "KM"
    }

    fun getTripName(): String {
        return "$originName → $destinationName"
    }

    fun getCompare(): String {
        AppConfigs.Log("departureDateTime", departureDateTime.toString())
        return departureDateTime.toString()
    }

    fun getTrainSeatFare(): TrainSeatFare {
        val trainSeatFare = TrainSeatFare()
        trainSeatFare.trainVLId = trainVLId
        trainSeatFare.trainNumber = trainNumber
        trainSeatFare.departureDateTime = departureDateTime
        trainSeatFare.arrivalDateTime = arrivalDateTime
        trainSeatFare.destinationName = destinationName
        trainSeatFare.destinationCode = destinationCode
        trainSeatFare.originCode = originCode
        trainSeatFare.originName = originName
        trainSeatFare.destinationKm = destinationKm
        trainSeatFare.provider = "DSVN"
        trainSeatFare.trainId = id
        return trainSeatFare


    }

}
