package com.hqt.data.model

import com.google.gson.annotations.SerializedName

data class PaymentType(
        @field:SerializedName("img") val Img: String? = null,
        @field:SerializedName("on") val On: Boolean = true,
        @field:SerializedName("order_number") val OrderNumber: String? = null,
        @field:SerializedName("order_cash_amount") val OrderCashAmount: Int = 0,
        @field:SerializedName("payment_type") val PaymentType: String? = null,
        @field:SerializedName("title") val Title: String? = null,
        @field:SerializedName("fee") val Fee: Int = 0,
        @field:SerializedName("extra") val Extra: Int = 0,
        @field:SerializedName("list") val list: ArrayList<Payment> = ArrayList()
) {


}