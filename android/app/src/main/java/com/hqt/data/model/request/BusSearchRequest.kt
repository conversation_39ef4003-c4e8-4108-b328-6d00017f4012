package com.hqt.data.model.request

import com.google.gson.annotations.SerializedName

data class BusSearchRequest(


    @field:SerializedName("departureDate") var departureDate: String? = "",
    @field:SerializedName("adultCount") var adultCount: Int = 1,
    @field:SerializedName("childCount") var childCount: Int = 0,
    @field:SerializedName("infantCount") var infantCount: Int = 0,
    @field:SerializedName("originCode") var originCode: String = "",
    @field:SerializedName("destinationCode") var destinationCode: String = "",

    ) : BaseModel()