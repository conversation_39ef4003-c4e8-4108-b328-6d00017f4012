package com.hqt.data.model.response

import com.google.gson.annotations.SerializedName
import com.hqt.view.ui.bus.BusRoute
import java.util.ArrayList

data class BusBookingResponse(
    @field:SerializedName("data") val data: BookingShortInfo? = null,
    @field:SerializedName("message") val message: String? = null,
)

data class BookingShortInfo(

    @SerializedName("id") var id: Int? = null,
    @SerializedName("token") var token: String? = null,
    @SerializedName("next") var next: String? = null


)