package com.hqt.data.model.request

import com.google.gson.annotations.SerializedName

data class TrainsSelectSeatRequest(


    @field:SerializedName("bookingCode") var bookingCode: String? = "",
    @field:SerializedName("isReturn") var isReturn: Boolean = false,
    @field:SerializedName("selectKey") var selectKey: String = "",
    @field:SerializedName("select") var select: Boolean = true,
) : BaseModel()