package com.hqt.data.model;

public class Notification {
    private String id;
    private String type;
    private String title;
    private String body;
    private Boolean read;
    private String action_link;
    private String click_action;
    private String created_at;

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    private String image;

    public String getAction_link() {
        return action_link;
    }

    public void setAction_link(String action_link) {
        this.action_link = action_link;
    }

    public Boolean getRead() {
        return read;
    }

    public String getCreated_at() {
        return created_at;
    }

    public void setCreated_date(String created_date) {
        this.created_at = created_date;
    }

    public String getTitle() {
        return title;
    }

    public String getClick_action() {
        return click_action;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public String getId() {
        return id;
    }

    public String getType() {
        return type;
    }

    public void setClick_action(String click_action) {
        this.click_action = click_action;
    }

    public void setId(String id) {
        this.id = id;
    }

    public void setRead(<PERSON><PERSON><PERSON> read) {
        this.read = read;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getBody() {
        return body;
    }
}
