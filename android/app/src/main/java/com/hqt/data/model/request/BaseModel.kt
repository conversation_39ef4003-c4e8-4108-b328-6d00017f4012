package com.hqt.data.model.request

import android.os.Build
import com.google.gson.annotations.SerializedName
import com.hqt.datvemaybay.BuildConfig
import com.hqt.datvemaybay.Common
import com.hqt.util.AppController

abstract class BaseModel(@field:SerializedName("source") var source: String = "ANDROID",
    @field:SerializedName("ver") var ver: String = BuildConfig.VERSION_NAME,
    @field:SerializedName("versionCode") var versionCode: Int = BuildConfig.VERSION_CODE,
    @field:SerializedName("uid") var uid: String = Common.Uid,
    @field:SerializedName("track") var track: String = AppController.instance.trackingCode

) {}
