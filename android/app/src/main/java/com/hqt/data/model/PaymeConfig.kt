package com.hqt.data.model

import android.content.Context
import android.content.SharedPreferences


object PaymeConfig {
    @kotlin.jvm.JvmField
    val StoreId: Long = 54606412
    val AppId = 20
    var AppToken: String =
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcHBJZCI6MjAsImlhdCI6MTYyODc4MjY1M30.LDTtSUaECM4NWp3br2nNkhGU2SlnIv149uAtHD4D7eY"
    const val PrivateKey: String = "-----BEGIN RSA PRIVATE KEY-----\n" +
            "MIIBOgIBAAJBAKknEfXQriEGxs7DKit3wPq+9Bml/sUUPls1KjzenhlEOFP/uBsx\n" +
            "2+94JxwjZNHFM9gCJUDtxj7LRqzY3hjBIjsCAwEAAQJATujgEwmXweZ0Zk5bZM3H\n" +
            "4/Gi1DhA4tVvxYLGwoCjxmrrspteKIorFgxJfSkxCJG7x6kVyI1cBkS4cHPtWYz+\n" +
            "wQIhAP6V7qxk2dxBkoxZZZxCF0VDaHyB/bO0xlCYfLt3KqxhAiEAqhei5Ud13qtm\n" +
            "A7ew7buXa9wpEzd4wUdEzXEkeptFdBsCIFzycnQgUeX3TsDM9qsI+iQAi0pBz1Vn\n" +
            "36uXMJrk1AChAiEAofax2UtrlKV3FZEju6xGaqGQx0iqBrlbbZkQGGOkBgMCIHZK\n" +
            "0ZN/cjelIGRkA+t+y/4bXe5Xi4NLTlAaoDCLfK07\n" +
            "-----END RSA PRIVATE KEY-----"
    const val SecretKey: String = "487782005c966c4019924f8a84c88195"
    const val PublicKey: String = "-----BEGIN PUBLIC KEY-----\n" +
            "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAIySd31aR/4eAA9E8Y4V0nivtxaX9Oo/\n" +
            "a7BOERBeny5pz7cNSuJUCtC+reD6pzjUz7q6pONVvmxqI51d161YlfECAwEAAQ==\n" +
            "-----END PUBLIC KEY-----"


//var StoreId: Long = 10581207
//    var AppToken: String =
//        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcHBJZCI6MzAsImlhdCI6MTYyMTgyMzQ2N30.02jQIG7fqUckNzQnx0ya52ley4nWCHWt3w6tUrrRAtQ"
//    var PrivateKey: String = "-----BEGIN RSA PRIVATE KEY-----\n" +
//            "      MIIBOgIBAAJBAMRQjlsYp5aR5IliyM/WsK6JtP79wdXCkyZ/PRV1ZcvyWx5/4A6f\n" +
//            "      9e4G+rGF8tSWjbYs1aRkyd/NY41QX+VBULECAwEAAQJAN5TDKUGKuVOnC8q/JjEX\n" +
//            "      puLwLr2zsoy7Usv1hGzPnHUK+GtCyROvG88K3EM7ouE2amk0BMJY1XZ8x1KkZnuw\n" +
//            "      vQIhAPkFALcE+dsV9G8gDGgTBr8PRmqpkinFzIHcev/wwMhjAiEAydFWDoeCwT2d\n" +
//            "      bbUt/fU/KSaGomp5slt+FZxd9A/tzNsCIQCGj9OBEqlJYCXD3teVbaKZn9F3VcZr\n" +
//            "      2DzYd6Hnp9sk7QIgfgE7b7rfwnML1bFnU8ZJdxHcwY8lCFzjbe7BIl7HpD0CICB5\n" +
//            "      KQhd7pUO2s5oPAvuzi30eI2NIISncH0xGxYAS+nu\n" +
//            "-----END RSA PRIVATE KEY-----"
//    var SecretKey: String = "d0b1240e28a109e052fa34354e9915f9"
//    var PublicKey: String = "-----BEGIN PUBLIC KEY-----\n" +
//            "      MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAO4QQwo0WqZONzlJ5CMWDb6eSrO5q14r\n" +
//            "      D05Fc6JeC/ZfjdoO+9+G9RZrpa8eh8hIhdJ4siqHKcSiM/xlXIm6ddECAwEAAQ==\n" +
//            "      -----END PUBLIC KEY-----"
}