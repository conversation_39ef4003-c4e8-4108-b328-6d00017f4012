package com.hqt.data.model

import java.io.Serializable


class BookingTrain : Booking(), Serializable {
    var departure_f: TrainSeatFare = TrainSeatFare()
    var return_f: TrainSeatFare = TrainSeatFare()


    override fun getGrandTotal(): Int {
        type = BookingType.TRAIN
        var departTotal = 0
        var returnTotal = 0
        departTotal = adult * departure_f.adult + child * departure_f.child + student * departure_f.student + older * departure_f.older
        if (is_round_trip) {
            returnTotal = adult * return_f.adult + child * return_f.child + student * return_f.student + older * return_f.older
        }
        total = departTotal + returnTotal + getTotalAddOn()
        return total
    }

    override fun getRewardPointTotal(): Int {
        var point = 0
        point += departure_f.getRewardPointTotal()
        if (is_round_trip) point += return_f.getRewardPointTotal()
        return point
    }

    override fun getTotalAddOn(): Int {
        var total = 0
        for (i in 0 until pax_info.adult.count()) {
            val pax = pax_info.adult[i]

            pax.addOn.forEach {
                total += it.price
            }

            pax.addOnReturn.forEach {
                total += it.price
            }
        }
        for (i in 0 until pax_info.child.count()) {
            val pax = pax_info.child[i]
            pax.addOn.forEach {
                total += it.price
            }

            pax.addOnReturn.forEach {
                total += it.price
            }
        }
        for (i in 0 until pax_info.student.count()) {
            val pax = pax_info.student[i]
            pax.addOn.forEach {
                total += it.price
            }

            pax.addOnReturn.forEach {
                total += it.price
            }
        }
        for (i in 0 until pax_info.older.count()) {
            val pax = pax_info.older[i]
            pax.addOn.forEach {
                total += it.price
            }

            pax.addOnReturn.forEach {
                total += it.price
            }
        }
        addon_fee = total
        return total
    }
}










