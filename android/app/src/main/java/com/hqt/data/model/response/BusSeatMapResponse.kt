package com.hqt.data.model.response

import com.google.gson.annotations.SerializedName
import com.hqt.view.ui.bus.BusRoute

data class BusSeatMapResponse(
    @field:SerializedName("data") val data: BusSeatMapInfo? = null,
)

data class BusSeatMapInfo(@SerializedName("max_total_seats") var maxTotalSeats: Int? = null,
    @SerializedName("total_available_seats") var totalAvailableSeats: Int? = null,
    @SerializedName("fare") var fare: Int? = null,
    @SerializedName("pickup_points") var pickupPoints: ArrayList<StationPoints> = arrayListOf(),
    @SerializedName("drop_off_points") var dropOffPoints: ArrayList<StationPoints> = arrayListOf(),
    @SerializedName("seat_groups") var seatGroups: ArrayList<SeatGroups> = arrayListOf())


data class StationPoints(

    @SerializedName("point_id") var pointId: Int? = null,
    @SerializedName("name") var name: String? = null,
    @SerializedName("address") var address: String? = null,
    @SerializedName("time") var time: String? = null,
    @SerializedName("real_time") var realTime: String? = null,
    @SerializedName("location") var location: String? = null,
    @SerializedName("selected") var selected: Boolean = false

)

data class Seats(

    @SerializedName("seat_type") var seatType: Int? = null,
    @SerializedName("seat_number") var seatNumber: Int? = null,
    @SerializedName("seat_code") var seatCode: String? = null,
    @SerializedName("row_num") var rowNum: Int? = null,
    @SerializedName("col_num") var colNum: Int? = null,
    @SerializedName("is_available") var isAvailable: Boolean? = null,
    @SerializedName("seat_group") var seatGroup: String? = null,
    @SerializedName("fare") var fare: Int? = null,
    @SerializedName("seat_color") var seatColor: String? = null,
    @SerializedName("seat_value") var seatValue: String? = null

)

data class SeatGroups(

    @SerializedName("coach_num") var coachNum: Int? = null,
    @SerializedName("coach_name") var coachName: String? = null,
    @SerializedName("num_rows") var numRows: Int? = null,
    @SerializedName("num_cols") var numCols: Int? = null,
    @SerializedName("seats") var seats: ArrayList<Seats> = arrayListOf()

)