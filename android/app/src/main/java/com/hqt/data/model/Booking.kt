package com.hqt.data.model

import java.io.Serializable

open class Booking : Serializable {
    open var type: BookingType = BookingType.FLIGHT
    open var id: String = ""
    open var destination_code = ""
    open var destination_name = ""
    open var origin_code = ""
    open var origin_name = ""
    open var departure_date = ""
    open var return_date = ""
    open var is_round_trip: Boolean = false
    open var adult = 0
    open var child = 0
    open var infant = 0
    open var student = 0
    open var older = 0
    open var total = 0
    open var bag_fee = 0
    open var addon_fee = 0
    open var voucher: String? = null
    open var discount = 0
    open var contact_phone: String = ""
    open var contact_name: String = ""
    open var contact_email: String = ""
    open var created_at: String? = null
    open var is_active = false
    open var status: String = ""
    open var status_text: String = ""
    open var pnr: String = ""
    open var pnr_return: String = ""
    open var token: String = ""
    open var expired_date: String? = null
    open var uuid: String = ""
    open var payment: PaymentInfo = PaymentInfo()

    open var pax_info: PaxInfoList = PaxInfoList()

    enum class BookingType : Serializable {
        FLIGHT, TRAIN, BUS, INTER, TOUR
    }

    class PaymentInfo : Serializable {
        var status: Boolean = false
        var url: String = ""
    }

    open fun getGrandTotal(): Int {
        return total
    }

    open fun getTotalBagFee(): Int {

        return 0
    }

    open fun getTotalAddOn(): Int {

        return 0
    }

    open fun getRewardPointTotal(): Int {
        return 0
    }

    fun getCalculatorFinalPrice(): Int {
        return total - discount + bag_fee + addon_fee
    }

    fun getTotalPax(): Int {
        return adult + child + infant + older + student
    }

    fun getBaseBookingSortInfo(): String {
        return "$type|$origin_code|$destination_code|$departure_date|$return_date|$adult-$child-$infant-$older-$student|$contact_phone"

    }
}