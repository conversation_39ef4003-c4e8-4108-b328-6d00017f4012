package com.hqt.data.model.response

import com.google.gson.annotations.SerializedName


class GetTrainSeatMapResponse(
    @field:SerializedName("data") val data: List<TrainSeatDetail>? = ArrayList(),
)

data class TrainSeatDetail(

    @SerializedName("id") var id: Int? = null,
    @SerializedName("coachId") var coachId: Int? = null,
    @SerializedName("seatTypeId") var seatTypeId: Int? = null,
    @SerializedName("seatNumber") var seatNumber: Int? = null,
    @SerializedName("seatType") var seatType: String? = null,
    @SerializedName("price") var price: Int? = null,
    @SerializedName("ticketId") var ticketId: Int? = null,
    @SerializedName("coachName") var coachName: String? = null,
    @SerializedName("coachNum") var coachNum: String? = null,
    @SerializedName("coachLayout") var coachLayout: Int? = null,
    @SerializedName("fareKeys") var fareKeys: String? = null,
    @SerializedName("fare") var fare: Int? = null,
    @SerializedName("status") var status: Int? = null,
    @SerializedName("totalPrice") var totalPrice: Int? = null,
    @SerializedName("insurance") var insurance: Int? = null,
    @SerializedName("discount") var discount: Int? = null,
    @SerializedName("statusText") var statusText: String? = null,
    @SerializedName("trainId") var trainId: Int? = null,
    @SerializedName("selectKey") var selectKey: String? = null

) {


}