package com.hqt.data.model;

import java.util.Calendar;
import java.util.Date;

import com.hqt.datvemaybay.Common;

import org.json.JSONException;
import org.json.JSONObject;

public class Ticket {

    private String origin;
    private String destination;
    private String flightNumber;
    private String depatureDate;
    private String arrivalDate;
    private String duration = "";
    private Boolean isDepatureFlight;
    private Boolean isQuickDep = null;
    private int netPrices = 0;
    private int fullPrices;
    private int tax;
    private int airPortFee;
    private int adult = 0;
    private int child = 0;
    private int infant = 0;
    private int adultPrices = 0;
    private int childPrices = 0;
    private int infantPrices = 0;
    private Boolean isPromo;
    private String provider = "";
    private String seatClass = "";
    private String Logo = "";
    private String fareBasis = "";
    private Date strTime;
    private String stops = "";
    private JSONObject FlightDetail;

    public void setTicket(String SanBayDi, String SanBayToi, String SoHieu, String GioBay, String GioDen, String ThoiGianBay, String Gia, String GiaPhi, String ThuePhi, String ThueSanBay, String HangBay, String LoaiVe, String Logo, boolean isLuotDi, String diemDung, String fareBasis, Boolean promo) {
        this.origin = SanBayDi;
        this.destination = SanBayToi;
        this.flightNumber = SoHieu;
        this.depatureDate = GioBay;
        this.arrivalDate = GioDen;
        this.duration = ThoiGianBay;
        this.netPrices = ClearPrice(Gia);
        this.fullPrices = ClearPrice(GiaPhi);
        this.tax = ClearPrice(ThuePhi);
        this.airPortFee = ClearPrice(ThueSanBay);
        this.provider = HangBay;
        this.seatClass = LoaiVe;
        this.Logo = Logo;
        this.isDepatureFlight = isLuotDi;
        this.stops = diemDung;
        this.fareBasis = fareBasis;
        this.strTime = Common.getDateTimeFromFormat(getDepatureDate());
        this.isPromo = promo;
    }

    private int ClearPrice(String price) {
        try {
            String temp = price.replace(",", "").replace(".", "").replace(" VND", "").replace("đ", "");
            int p = Integer.valueOf(temp);
            return p;
        } catch (Exception e) {
            return 0;
        }

    }

    public Ticket paserJson(JSONObject flight) {
        try {
            String departureTime = flight.getString("departureTime");
            String arrivalTime = flight.getString("arrivalTime");

            int net = flight.getInt("net");
            int price = flight.getInt("price");
            String flightNumber = flight.getString("flightNumber");

            this.depatureDate = departureTime;
            this.arrivalDate = arrivalTime;
            this.netPrices = net;
            this.fullPrices = price;
            this.flightNumber = flightNumber;

            if (flightNumber.contains("BL")) {
                this.provider = "Pacific Airlines";
                this.Logo = "logo_bl";

            } else if (flightNumber.contains("VJ")) {
                this.provider = "Vietjet Air";
                this.Logo = "logo_vj";

            } else if (flightNumber.contains("VN")) {
                this.provider = "VietNam Airlines";
                this.Logo = "logo_vn";
            } else if (flightNumber.contains("QL")) {
                this.provider = "Bamboo Airways";
                this.Logo = "logo_ql";
            } else if (flightNumber.contains("VU")) {
                this.provider = "Vietravel Airlines";
                this.Logo = "logo_vu";
            }


        } catch (JSONException e) {

        }
        return this;
    }

    public Boolean isNextDay() {
        Calendar depDate = Common.stringToDate(this.getDepatureDate(), "HH:mm dd/MM/yyyy");
        Calendar arrDate = Common.stringToDate(this.getArrivalDate(), "HH:mm dd/MM/yyyy");
        if (depDate.get(Calendar.DAY_OF_MONTH) != arrDate.get(Calendar.DAY_OF_MONTH)) {
            return true;
        }
        return false;
    }

    public Ticket paserFlight(JSONObject flight) {
        try {
            String departureTime = flight.getString("departureDateTime");
            String arrivalTime = flight.getString("arriverDateTime");

            int net = flight.getInt("net");
            int price = flight.getInt("price");
            String flightNumber = flight.getString("flightNumber");
            String seatClass = flight.getString("class");
            String fareBasis = flight.getString("fareBasis");


            this.depatureDate = departureTime;
            this.arrivalDate = arrivalTime;
            this.seatClass = seatClass;
            this.fareBasis = fareBasis;

            this.netPrices = net;
            this.fullPrices = price;
            this.flightNumber = flightNumber;

            if (flightNumber.contains("BL")) {
                this.provider = "Pacific Airlines";
                this.Logo = "logo_bl";

            } else if (flightNumber.contains("VJ")) {
                this.provider = "Vietjet Air";
                this.Logo = "logo_vj";

            } else if (flightNumber.contains("VN")) {
                this.provider = "VietNam Airlines";
                this.Logo = "logo_vn";
            } else if (flightNumber.contains("QL")) {
                this.provider = "Bamboo Airways";
                this.Logo = "logo_ql";
            } else if (flightNumber.contains("VU")) {
                this.provider = "Vietravel Airlines";
                this.Logo = "logo_vu";
            }


        } catch (JSONException e) {

        }
        return this;
    }

    public void setGia(int GiaNgLon, int GiaTrEm, int GiaEmBe) {
        this.adultPrices = GiaNgLon;
        this.childPrices = GiaTrEm;
        this.infantPrices = GiaEmBe;
    }

    public void setSanBay(String from, String to) {
        this.origin = from;
        this.destination = to;

    }

    public Date getStrTime() {
        return this.strTime;
    }

    public String getOrigin() {
        return this.origin;
    }

    public String getDestination() {
        return this.destination;
    }

    public String getFlightNumber() {
        return this.flightNumber;
    }

    public void setHanhKhach(int nguoiLon, int treEm, int emBe) {
        this.adult = nguoiLon;
        this.child = treEm;
        this.infant = emBe;
    }

    public String getDepatureDate() {
        return this.depatureDate;
    }

    public String getArrivalDate() {
        return this.arrivalDate;
    }

    public String getDuration() {
        return this.duration;
    }

    public int getNetPrices() {
        return this.netPrices;
    }

    public int getFullPrices() {
        return this.fullPrices;
    }

    public int getTax() {
        return this.tax;
    }

    public int getAirPortFee() {
        return this.airPortFee;
    }

    public int getAdult() {
        return this.adult;
    }

    public int getChild() {
        return this.child;
    }

    public int getInfant() {
        return this.infant;
    }

    public String getProvider() {
        return this.provider;
    }

    public String getSeatClass() {
        return this.seatClass;
    }

    public String getLogo() {
        return this.Logo;
    }

    public String getStops() {
        return this.stops;
    }

    public String getFareBasis() {
        return this.fareBasis;
    }

    public boolean getIsChieuDi() {
        return this.isDepatureFlight;
    }

    public boolean getIsPromo() {
        return this.isPromo;
    }

    public boolean getIsQuickDepart() {
        if (this.isQuickDep == null) {
            Calendar departureDateTime = Common.stringToDate(this.depatureDate, "HH:mm dd/MM/yyyy");
            long diffHours = Common.hoursBetween(Calendar.getInstance(), departureDateTime);

            if (diffHours < 24.5) {
                this.isQuickDep = true;
                return true;
            }
            this.isQuickDep = false;
        } else {
            return this.isQuickDep;
        }
        return false;
    }

    public void setChildPrices(int childPrices) {
        this.childPrices = childPrices;
    }

    public int getChildPrices() {
        return this.childPrices;
    }

    public void setAdultPrices(int GiaNgLon) {
        this.adultPrices = GiaNgLon;
    }

    public int getAdultPrices() {
        return this.adultPrices;
    }

    public void setInfantPrices(int infantPrices) {
        this.infantPrices = infantPrices;
    }

    public int getInfantPrices() {
        return this.infantPrices;
    }

    public JSONObject setFlightDetail(JSONObject flightDetail) {
        this.FlightDetail = flightDetail;
        return this.FlightDetail;
    }

    public JSONObject getFlightDetail() {
        return this.FlightDetail;
    }


}
