package com.hqt.data.model;

import com.google.firebase.remoteconfig.FirebaseRemoteConfig;
import com.hqt.datvemaybay.Common;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;

public class Flight implements Serializable {
    private String originCode;
    private String destinationCode;
    private String departureDateTime;
    private String arriverDateTime;
    private String flightNumber;
    private String provider;
    private String seatClass;
    private Integer seatCount;
    private Boolean promo = false;
    private String uuid;
    private Integer infant;
    private Integer adult;
    private Integer child;
    private Integer netPrice;
    private Integer tax;
    private Integer airPortFee;
    private Integer stops;
    private Boolean isQuickDep = false;
    private String fareBasis;
    private String duration;
    private String flightKey;
    public String description = "";
    private Integer rewardPoint;

    public Integer getRewardPoint() {
        return rewardPoint;
    }

    public void setRewardPoint(Integer rewardPoint) {
        this.rewardPoint = rewardPoint;
    }

    public Integer getAirPortFee() {
        return airPortFee;
    }

    public void setAirPortFee(Integer airPortFee) {
        this.airPortFee = airPortFee;
    }

    public String getOriginCode() {
        return originCode;
    }

    public void setOriginCode(String originCode) {
        this.originCode = originCode;
    }

    public String getDestinationCode() {
        return destinationCode;
    }

    public void setDestinationCode(String destinationCode) {
        this.destinationCode = destinationCode;
    }

    public String getDepartureDateTime() {
        return departureDateTime;
    }

    public Date getDepartureDate() {

        return Common.getDateTimeFromFormat(this.departureDateTime);
    }

    public Date getArriverDate() {

        return Common.getDateTimeFromFormat(this.arriverDateTime);
    }

    public void setDepartureDateTime(String departureDateTime) {
        this.departureDateTime = departureDateTime;
    }

    public String getArriverDateTime() {
        return arriverDateTime;
    }

    public void setArriverDateTime(String arriverDateTime) {
        this.arriverDateTime = arriverDateTime;
    }

    public String getFlightNumber() {
        return flightNumber;
    }

    public void setFlightNumber(String flightNumber) {
        this.flightNumber = flightNumber;
    }

    public String getProvider() {
        return provider;
    }

    public String getProviderText() {

        String text = this.provider;
        if (this.provider.equals("3K") || this.provider.equals("GK")) {
            text = "Jetstar";
        } else if (this.provider.equals("BL")) {
            text = "Pacific Airlines";
        } else if (this.provider.equals("VJ")) {
            text = "Vietjet Air";
        } else if (this.provider.equals("VN")) {
            text = "Vietnam Airlines";
        } else if (this.provider.equals("QH")) {
            text = "Bamboo Airways";
        } else if (this.provider.equals("VU")) {
            text = "Vietravel Airlines";
        }

        return text;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    public String getSeatClass() {
        if (seatClass == null) return "";
        return seatClass;
    }

    public void setSeatClass(String seatClass) {
        this.seatClass = seatClass;
    }

    public Boolean getPromo() {
        return promo;
    }

    public void setPromo(Boolean promo) {
        this.promo = promo;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public Integer getInfant() {
        return infant;
    }

    public void setInfant(Integer infant) {
        this.infant = infant;
    }

    public Integer getAdult() {
        return adult;
    }

    public void setAdult(Integer adult) {
        this.adult = adult;
    }

    public Integer getChild() {
        return child;
    }

    public void setChild(Integer child) {
        this.child = child;
    }

    public Integer getNetPrice() {
        return this.netPrice;
    }

    public void setNetPrice(Integer netPrice) {
        this.netPrice = netPrice;
    }

    public Integer getTax() {
        return this.tax;
    }

    public void setTax(Integer tax) {
        this.tax = tax;
    }

    public Boolean getQuickDep() {
        if (this.isQuickDep == null) {
            Calendar departureDateTime = Common.stringToDate(this.departureDateTime, "HH:mm dd/MM/yyyy");
            long diffHours = Common.hoursBetween(Calendar.getInstance(), departureDateTime);

            if (diffHours < 24.5) {
                this.isQuickDep = true;
                return true;
            }
            this.isQuickDep = false;
        } else {
            return this.isQuickDep;
        }
        return isQuickDep;
    }

    public void setQuickDep(Boolean quickDep) {
        isQuickDep = quickDep;
    }

    public Integer getStops() {
        return stops;
    }

    public String getStopsText() {
        if (stops == 0) {
            return "Bay Thẳng";
        } else {
            return stops + " điểm dừng";
        }
    }

    public void setStops(Integer stops) {
        this.stops = stops;
    }

    public String getLogo() {

        return "logo_" + this.provider.toLowerCase();
    }

    public String getAirlinesLogo() {

        String logoLink = FirebaseRemoteConfig.getInstance().getString("AIRLINES_LOGO");

        return logoLink.replace("{provider}", this.provider);
        //return "https://blog.12bay.vn/airline_logo.php?code=" + this.provider.toUpperCase();
        // return "https://ssl.12bay.vn/images/airlines/" + this.provider.toLowerCase() + ".gif";
    }

    public String getDuration() {
        return duration;
    }

    public void setDuration(String duration) {
        this.duration = duration;
    }

    public String getFareBasis() {
        return fareBasis;
    }

    public void setFareBasis(String fareBasis) {
        this.fareBasis = fareBasis;
    }

    public String getFlightKey() {
        return flightKey;
    }

    public void getFlightKey(String flightKey) {
        this.flightKey = flightKey;
    }

    public Boolean isNextDay() {
        Calendar depDate = Common.stringToDate(this.getDepartureDateTime(), "HH:mm dd/MM/yyyy");
        Calendar arrDate = Common.stringToDate(this.getArriverDateTime(), "HH:mm dd/MM/yyyy");
        if (depDate.get(Calendar.DAY_OF_MONTH) != arrDate.get(Calendar.DAY_OF_MONTH)) {
            return true;
        }
        return false;
    }

    public Integer getSeatCount() {
        return seatCount;
    }

    public void setSeatCount(Integer seatCount) {
        this.seatCount = seatCount;
    }
}
