package com.hqt.data.model

import com.hqt.datvemaybay.Common
import com.hqt.view.ui.flightSearch.model.*
import org.json.JSONException
import org.json.JSONObject
import java.io.Serializable

class BookingFarexx : Booking(), Serializable {
    var fareData: FareData? = null


    override fun getGrandTotal(): Int {
        type = BookingType.INTER
        return total
    }

    override fun getTotalBagFee(): Int {
        var total = 0
        for (i in 0 until pax_info.adult.count()) {
            val pax = pax_info.adult.get(i)
            if (pax.baggage != null && pax.baggage!!.value > 0) {
                total += pax.baggage!!.price
            }
        }
        for (i in 0 until pax_info.child.count()) {
            val pax = pax_info.child.get(i)
            if (pax.baggage != null && pax.baggage!!.value > 0) {
                total += pax.baggage!!.price
            }
        }
        bag_fee = total
        return total
    }

}