package com.hqt.data.model

import com.google.gson.annotations.SerializedName
import com.hqt.datvemaybay.R
import java.io.Serializable
import java.text.DecimalFormat
import java.text.DecimalFormatSymbols
import java.util.*

class DatePrice : Serializable {
    var origin = ""
    var destination = ""
    var price = -1
    var date: Date? = null
    var code = ""
    var isCheap = false
    fun getShortPrice(): String {
        if (price >= 0) {

            val df = DecimalFormat()
            val symbols = DecimalFormatSymbols()
            symbols.decimalSeparator = ','
            symbols.groupingSeparator = '.'
            df.decimalFormatSymbols = symbols
            return df.format((price / 1000).toLong())

        }
        return ""
    }

    fun getPriceColor(): Int {
        if (price >= 0) {
            return if (isCheap) {
                R.drawable.price_board_cell_red
            } else {
                R.drawable.price_board_cell_default
            }
        }
        return R.drawable.transparent
    }
}

/**
 * Data model for weekly aggregated price data
 */
data class WeeklyPriceData(
    val weekStartDate: Date,
    val weekEndDate: Date,
    val weekLabel: String, // e.g., "2025-06-01"
    val cheapestPrice: Int,
    val origin: String,
    val destination: String,
    val isCheapestInRange: Boolean = false
) : Serializable {

    fun getFormattedPrice(): String {
        if (cheapestPrice >= 0) {
            val df = DecimalFormat()
            val symbols = DecimalFormatSymbols()
            symbols.decimalSeparator = ','
            symbols.groupingSeparator = '.'
            df.decimalFormatSymbols = symbols
            return df.format((cheapestPrice / 1000).toLong()) + "k"
        }
        return "N/A"
    }

    fun getPriceInThousands(): Float {
        return if (cheapestPrice >= 0) (cheapestPrice / 1000).toFloat() else 0f
    }
}