package com.hqt.data.model

import com.google.gson.annotations.SerializedName


data class Payment(@field:SerializedName("payment_type") val Type: String? = null,
    @field:SerializedName("payment_bank") val Bank: String? = null,
    @field:SerializedName("deep_link") val DeepLink: String? = null,
    @field:SerializedName("title") val Title: String? = null,
    @field:SerializedName("logo") val Logo: String? = null,
    @field:SerializedName("detail") val Detail: PaymentDetail? = null) {

}

data class PaymentDetail(@field:SerializedName("branch") val Branch: String? = null,
    @field:SerializedName("account_name") val AccountName: String? = null,
    @field:SerializedName("account_number") val AccountNumber: String? = null) {

}
