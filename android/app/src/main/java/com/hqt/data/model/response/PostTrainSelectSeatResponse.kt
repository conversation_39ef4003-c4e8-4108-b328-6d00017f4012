package com.hqt.data.model.response

import com.google.gson.annotations.SerializedName
import com.hqt.data.model.PaymentInfoGroup
import java.util.Date

class PostTrainSelectSeatResponse(
    @field:SerializedName("data") val data: TrainSelectSeatData? = null,
)

data class TrainSelectSeatData(

    @SerializedName("status") var status: Int? = null,
    @SerializedName("message") var message: String? = null,
    @SerializedName("duration") var duration: Int? = null,
    @SerializedName("time_limit") var timeLimit: String = "") {}