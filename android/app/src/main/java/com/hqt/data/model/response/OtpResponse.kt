package com.hqt.data.model.response

import com.google.gson.annotations.SerializedName

data class OtpResponse(
    @field:SerializedName("data") val data: OtpInfo? = null,
    @field:SerializedName("message") val message: String? = null,
)

data class OtpInfo(

    @SerializedName("status") var status: Boolean = false,
    @SerializedName("message") var message: String? = null,
    @SerializedName("token") var token: String? = null,
    @SerializedName("otp") var otp: String? = null


)