package com.hqt.data.model

import com.google.gson.annotations.SerializedName

data class OrderInfo(
    @field:SerializedName("id") val bookingId: String = "",
    @field:SerializedName("type") val type: String? = null,
    @field:SerializedName("title") val title: String? = null,
    @field:SerializedName("subtitle") val subtitle: String? = null,
    @field:SerializedName("status") val status: String? = null,
    @field:SerializedName("on_payment") val onPayment: Boolean? = null,
    @field:SerializedName("order_cash_amount") val orderCashAmount: Int = 0,
    @field:SerializedName("grand_total") val grandTotal: Int = 0,
    @field:SerializedName("session_id") val sessionId: String? = null,
    @field:SerializedName("timeLimit") val timeLimit: String? = null,
    @field:SerializedName("contact") val contact: Contact? = null,
) {


}

data class Contact(@field:SerializedName("contact_name") val contactName: String? = null,
    @field:SerializedName("contact_phone") val contactPhone: String? = null,
    @field:SerializedName("contact_email") val contactEmail: String? = null) {


}