package com.hqt.data.model.response

import com.google.gson.annotations.SerializedName
import com.hqt.view.ui.flightSearch.model.FareData


data class FlightSearchResponse(
    @field:SerializedName("data") val data: FareResponse? = null,
)

data class FareResponse(
    @field:SerializedName("apiKey") val apiKey: String? = null,
    @field:SerializedName("lastUpdateTime") val lastUpdateTime: String? = null,
    @field:SerializedName("cached") val cached: String? = null,
    @field:SerializedName("listFareData") val listFareData: ArrayList<FareData>? = null,
)