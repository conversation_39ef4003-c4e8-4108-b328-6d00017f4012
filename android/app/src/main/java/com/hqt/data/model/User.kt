package com.hqt.data.model

import androidx.databinding.BaseObservable
import com.google.gson.annotations.SerializedName
import com.hqt.util.payment.OrderResponse
import java.io.Serializable

data class User(@field:SerializedName("id") val id: Int = 0,
    @field:SerializedName("uid") val uid: String? = null,
    @field:SerializedName("name") var userName: String? = null,
    @field:SerializedName("email") var userEmail: String? = null,
    @field:SerializedName("provider") val provider: String? = null,
    @field:SerializedName("phone_number") var phoneNumber: String? = null,
    @field:SerializedName("avatar") var avatar: String? = "",
    @field:SerializedName("birthday") var birthDay: String? = "",
    @field:SerializedName("level") val level: Int = 0,
    @field:SerializedName("point") val point: Point = Point(),
    @field:SerializedName("waitting_booking") val waitingBooking: Int? = null,
    @field:SerializedName("success_booking") val successBooking: Int? = null,
    @field:SerializedName("unseen_notification") val unseenNotification: Int? = null,
    @field:SerializedName("created_at") val created_at: String = ""


) : BaseObservable(), Serializable {
    var payMeBalance: Int = 0
}

class Point : Serializable {
    var value: Int = 0
    var text: String = ""
}