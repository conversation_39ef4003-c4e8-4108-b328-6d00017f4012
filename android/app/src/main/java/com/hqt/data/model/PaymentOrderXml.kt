package com.hqt.data.model

import com.google.gson.annotations.SerializedName
import com.hqt.util.payment.OrderResponse

data class PaymentOrderXml(@field:SerializedName("booking_id") val bookingId: String = "",
    @field:SerializedName("session_id") val paymentMethod: String = "",
    @field:SerializedName("payment_fee") val paymentFee: String = "",
    @field:SerializedName("expired_date") val expiredDate: String = "",
    @field:SerializedName("billing_code") val billingCode: String = "",
    @field:SerializedName("grand_total") val grandTotal: Int = 0,
    @field:SerializedName("payment_type") val paymentType: String = "",
    @field:SerializedName("payment_bank") val paymentBank: String = "",
    @field:SerializedName("payment_note") val paymentNote: String = "",
    @field:SerializedName("status") val status: String = "",
    @field:SerializedName("form") val form: OrderResponse

) {
        
}