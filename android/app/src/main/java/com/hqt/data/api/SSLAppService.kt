package com.hqt.data.api

import com.hqt.data.model.User
import com.hqt.data.model.request.BusBookingRequest
import com.hqt.data.model.request.BusSearchRequest
import com.hqt.data.model.request.FlightSearchRequest
import com.hqt.data.model.request.GetAddOnRequest
import com.hqt.data.model.request.GetOrderXmlRequest
import com.hqt.data.model.request.GetSeatMap
import com.hqt.data.model.request.OtpSendRequest
import com.hqt.data.model.request.TrainsSelectSeatRequest
import com.hqt.data.model.response.BusBookingResponse
import com.hqt.data.model.response.BusSearchResponse
import com.hqt.data.model.response.BusSeatMapResponse
import com.hqt.data.model.response.FlightSearchResponse
import com.hqt.data.model.response.GetAddOnResponse
import com.hqt.data.model.response.GetAirportResponse
import com.hqt.data.model.response.GetListPaymentRespone
import com.hqt.data.model.response.GetOrderXmlRespone
import com.hqt.data.model.response.GetSeatMapResponse
import com.hqt.data.model.response.GetTrainSeatMapResponse
import com.hqt.data.model.response.GetUserResponse
import com.hqt.data.model.response.HomeFeatureResponse
import com.hqt.data.model.response.OtpResponse
import com.hqt.data.model.response.PostTrainSelectSeatResponse
import com.hqt.data.model.response.tour.GetTourDetailResponse
import com.hqt.data.model.response.tour.GetTourListResponse
import io.reactivex.Single
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path
import retrofit2.http.Query

interface SSLAppService {


    @GET("/api/v1/AirLines/Feature")
    fun getHomeFeature(): Single<HomeFeatureResponse>

    @GET("/api/v1/AirLines/MPayment/{id}")
    fun getPayment(@Path("id") id: String): Single<GetListPaymentRespone>

    @GET("/api/v1/users/{uid}")
    fun getUser(@Path("uid") uid: String): Single<GetUserResponse>

    @POST("/api/v1/users/{uid}")
    fun updateUser(@Path("uid") uid: String, @Body request: User): Single<GetUserResponse>

    @POST("/api/v1/AirLines/SeatMap/{air}")
    fun postSeatMap(
        @Path("air") air: String,
        @Body request: GetSeatMap
    ): Single<GetSeatMapResponse>

    @POST("/api/v1/AirLines/AddOn/{air}")
    fun postAddOn(
        @Path("air") air: String,
        @Body request: GetAddOnRequest
    ): Single<GetAddOnResponse>

    @POST("/api/v1/AirLines/Payments/Create")
    fun getOrderXml(@Body request: GetOrderXmlRequest): Single<GetOrderXmlRespone>

    @GET("/api/v1/AirLines/i/AirPort/")
    fun getAirport(@Query("q") search: String): Single<GetAirportResponse>


    @POST("/api/v1/1G/iSearch/{from}/{to}")
    fun postFlightSearch(
        @Path("from") originCode: String,
        @Path("to") destinationCode: String,
        @Body request: FlightSearchRequest
    ): Single<FlightSearchResponse>

    @GET("/api/v1/Tour/Home")
    fun getTourFeature(): Single<GetTourListResponse>

    @GET("/api/v1/Tour/Category")
    fun getTourByCategory(@Query("id") catId: String): Single<GetTourListResponse>

    @GET("/api/v1/Tour/Detail/{tourId}")
    fun getTourDetail(@Path("tourId") tourId: String): Single<GetTourDetailResponse>

    @POST("/api/v1/Bus/Route/{from}/{to}")
    fun getBusRoute(
        @Path("from") originCode: String,
        @Path("to") destinationCode: String,
        @Body request: BusSearchRequest
    ): Single<BusSearchResponse>

    @POST("/api/v1/Bus/SeatMap/{tripCode}")
    fun getBusSeatMap(
        @Path("tripCode") tripCode: String,
        @Body request: BusSearchRequest
    ): Single<BusSeatMapResponse>

    @POST("/api/v1/Bus/Booking")
    fun postBusBooking(@Body request: BusBookingRequest): Single<BusBookingResponse>

    @GET("/api/v1/Trains/GetSeatByTrain/{selectKey}")
    fun getSeatByTrain(@Path("selectKey") selectKey: String): Single<GetTrainSeatMapResponse>

    @POST("/api/v1/Trains/SelectSeat")
    fun postSelectSeatMap(@Body request: TrainsSelectSeatRequest): Single<PostTrainSelectSeatResponse>


    @POST("/api/v1/users/otp/request")
    fun postRequestOTP(@Body request: OtpSendRequest): Single<OtpResponse>

    @POST("/api/v1/users/otp/verify")
    fun postVerifyOTP(@Body request: OtpSendRequest): Single<OtpResponse>

}
