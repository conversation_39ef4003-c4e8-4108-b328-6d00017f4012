package com.hqt.data.model;

import java.util.List;

public class Snap {

    public static final String SNAP_TYPE_FULL = "SNAP_TYPE_FULL";
    public static final String SNAP_TYPE_TITLE = "SNAP_TYPE_TITLE";
    public static final String SNAP_TYPE_SQUARE = "SNAP_TYPE_SQUARE";
    public static final String SNAP_TYPE_IMAGE = "SNAP_TYPE_IMAGE";
    public static final String SNAP_TYPE_BANNER = "SNAP_TYPE_BANNER";

    private int mGravity;
    private String mText;
    private List<Post> mPosts;
    private String mSubTitle;
    private String mBg;
    private String mType;

    public Snap(int gravity, String text, String subTitle, String bg, List<Post> posts, String type) {
        mGravity = gravity;
        mText = text;
        mPosts = posts;
        mSubTitle = subTitle;
        mBg = bg;
        mType = type;
    }

    public String getText() {
        return mText;
    }

    public String getSubTitle() {
        return mSubTitle;
    }

    public String getBackground() {
        return mBg;
    }

    public String getType() {
        return mType;
    }

    public int getGravity() {
        return mGravity;
    }

    public List<Post> getApps() {
        return mPosts;
    }

}

