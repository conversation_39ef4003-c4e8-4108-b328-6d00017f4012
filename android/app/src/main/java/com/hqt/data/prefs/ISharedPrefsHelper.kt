package com.hqt.data.prefs

import com.hqt.data.model.User


/**
 * Created by <PERSON><PERSON><PERSON> on 10/6/2017.
 */
interface ISharedPrefsHelper {
    fun getCurrentUserId(): String?

    fun setCurrentUserId(userId: String?)

    fun getCurrentUserName(): String

    fun setCurrentUserName(userName: String)

    fun getCurrentUserEmail(): String

    fun setCurrentUserEmail(email: String)

    fun getCurrentUserProfilePicUrl(): String

    fun setCurrentUserProfilePicUrl(profilePicUrl: String)

    fun getAccessToken(): String?

    fun setAccessToken(accessToken: String)

    fun getPropertyId(): Int
    fun setPropertyId(propertyId: Int)
    fun getFcmToken(): String?

    fun setFcmToken(fcmToken: String)

    fun getRemember(): Boolean

    fun setRemember(isRemember: Boolean)

    fun getMessageToken(): String

    fun setMessageToken(token: String?)

    fun getLatitude(): Double
    fun setLatitude(latitude: Double)

    fun getLongitude(): Double
    fun setLongitude(longitude: Double)

    fun getBaseUrl(): String?
    fun setBaseUrl(url: String)


    fun getAvatar(): String?
    fun setAvatar(url: String)

    fun getPhone(): String?
    fun setPhone(url: String)


    fun saveUser(user: User)

    fun getUser(): User?


    fun getListAcceptEvent(): String?
    fun setListAcceptEvent(eventList: String)

    fun setOnDebug(boolean: Boolean)

    fun getOnDebug(): Boolean




    fun getLoginFirst(): Boolean

    fun setLoginFirst(first: Boolean)



    fun removeData(key: String)

    fun setData(key: String, data: Any?)
    fun <T> getData(key: String, classOfT: Class<T>): T?
}