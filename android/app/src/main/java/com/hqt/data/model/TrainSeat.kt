package com.hqt.data.model

import com.hqt.datvemaybay.Common
import java.io.Serializable
import java.util.*

class TrainSeat : Serializable {
    var id: Int? = null
    var trainVLId: Int? = null
    var seatTypeId: Int? = null
    var seatNumber: String = ""
    var seatType: String = ""
    var price: Int = 0
    var ticketId: Int = 0
    var coachName: String? = null
    var coachNum: String = ""
    var coachLayout: Int = 0
    var fare: Int = 0
    var status: Int = 0
    var statusText: String = ""
    var totalPrice: Int = 0
    var insurance: Int = 0
    var discount: Int = 0
    var trainId: Int = 0
    var selectKey: String = ""


}