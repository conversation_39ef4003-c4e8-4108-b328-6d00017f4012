package com.hqt.data.model

import android.graphics.Color
import com.hqt.datvemaybay.Common
import com.hqt.util.DateOnly
import com.mikepenz.iconics.typeface.library.googlematerial.GoogleMaterial

import com.mikepenz.iconics.typeface.IIcon
import java.io.Serializable
import java.time.Duration
import java.util.*
import kotlin.collections.ArrayList

class FlightWatches : Serializable {
    var id: Int? = null
    var adult: Int = 1
    var child: Int = 0
    var infant: Int = 0
    var type: String = "TREND"
    var is_round_trip: Boolean = false
    var origin_code: String = ""
    var destination_code: String = ""
    var departure_date: DateOnly = DateOnly()
    var departure_date_end: DateOnly = DateOnly()
    var return_date: DateOnly = DateOnly()
    var max_price: Int = 0
    var last_price: Int = 0
    var last_price_date: Date? = null
    var current_price: Int = 0
    var user_id: String = ""
    var uid: String = ""
    var updated_at: Date? = null
    var fare_trend: List<FareTrend>? = null
    var fare_trend_return: List<FareTrend>? = null
    var last_fare_in_day: List<Flight>? = null
    var last_fare_in_day_return: List<Flight>? = null
    var fare_in_range: List<FareRange> = ArrayList()

    fun isActive(): Boolean {
        if (departure_date.value == null) {
            return false;
        }
        var now = Calendar.getInstance()
        now.add(Calendar.DATE, -1);

        return departure_date.value!!.after(now.time)
    }

    fun isNew(): Boolean {
        if (current_price == 0) {
            return true
        }
        return false
    }

    fun getTripDetail(): String {
        if (is_round_trip) {
            return Common.getAirPortName(origin_code, true) + " ⇄ " + Common.getAirPortName(destination_code, true)
        } else {
            return Common.getAirPortName(origin_code, true) + " → " + Common.getAirPortName(destination_code, true)
        }
    }

    fun getTripDetail(isReturn: Boolean): String {
        if (isReturn) {
            return Common.getAirPortName(destination_code, true) + " → " + Common.getAirPortName(origin_code, true)
        } else {
            return Common.getAirPortName(origin_code, true) + " → " + Common.getAirPortName(destination_code, true)
        }
    }

    fun getPaxCount(): Int {
        return (adult + child + infant)
    }

    fun getPricesDiff(): String {
        var diff = current_price - last_price
        return if (diff == 0) {
            "không đổi"
        } else {
            Common.dinhDangTien(current_price - last_price)
        }
    }

    fun getPriceDiffIconColor(): Int {
        var diff = current_price - last_price
        if (diff == 0) {
            return Color.GRAY
        } else if (diff > 0) {
            return Color.RED
        } else {
            return Color.parseColor("#8BC34A")
        }

    }

    fun getDateDiffUpdate(): String {
        if (last_price_date == null) {
            return ""
        }
        var text = ""
        text = Common.getDurationBefore(last_price_date)
        return "(từ $text trước)"
    }

    fun getPricesDiffIcon(): IIcon {
        if (current_price - last_price >= 0) {
            return GoogleMaterial.Icon.gmd_trending_up
        } else {
            return GoogleMaterial.Icon.gmd_trending_down
        }
    }

    fun getDestinationImages(): String {
        return "https://ssl.12bay.vn/api/v1/AirLines/Image/$destination_code?size=m"
    }


}