package com.hqt.data.model.request

import com.google.gson.annotations.SerializedName

data class GetOrderXmlRequest(

        @field:SerializedName("session_id")
        var SessionId: String? = null,
        @field:SerializedName("contact_email")
        var ContactEmail: String? = null,
        @field:SerializedName("contact_name")
        var ContactName: String? = null,
        @field:SerializedName("contact_phone")
        var ContactPhone: String? = null,
        @field:SerializedName("payment_type")
        var PaymentType: String? = null,
        @field:SerializedName("payment_bank")
        var PaymentBank: String? = null

) : BaseModel()