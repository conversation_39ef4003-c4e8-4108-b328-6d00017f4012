package com.hqt.data.model

import com.hqt.datvemaybay.Common
import java.io.Serializable
import java.util.*

class TrainCoach : Serializable {
    var id: Int? = null
    var trainVLId: Int? = null
    var order: String? = null
    var coachNumberXP: Int? = null
    var coachName: String = ""
    var status: Int? = null
    var statusText: String = ""
    var layout: Int = 0
    var detail: String = ""
    var webGroup: String = ""
    var seatCount: Int = 0
    var selectKey: String = ""

    fun getCoachOrder(): String {
        return "Toa " + order + " "
    }

    fun getSeatRemain(): String {
        if (seatCount > 0) {
            return "Còn $seatCount ghế"
        }
        return "Hết ghế"
    }
}