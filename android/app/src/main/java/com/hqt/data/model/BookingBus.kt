package com.hqt.data.model

import java.io.Serializable


class BookingBus : Booking(), Serializable {
    var departure_f: BusSeatFare = BusSeatFare()
    var return_f: BusSeatFare = BusSeatFare()


    override fun getGrandTotal(): Int {
        type = BookingType.BUS
        return total
    }

    override fun getRewardPointTotal(): Int {
        var point = 0
        point += departure_f.getRewardPointTotal()
        return point
    }

}







