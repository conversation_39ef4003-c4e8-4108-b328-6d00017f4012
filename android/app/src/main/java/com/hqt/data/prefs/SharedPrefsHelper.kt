package com.hqt.data.prefs

import android.content.SharedPreferences
import com.google.gson.Gson
import com.hqt.data.model.User
import com.hqt.util.Log
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Created by <PERSON><PERSON><PERSON> on 10/5/2017.
 */
@Singleton
class SharedPrefsHelper @Inject constructor(private val mSharedPreferences: SharedPreferences) :
    ISharedPrefsHelper {

    companion object {
        private const val PREF_KEY_CURRENT_USER_ID = "PREF_KEY_CURRENT_USER_ID"
        private const val PREF_KEY_AUTH_TOKEN = "PREF_KEY_AUTH_TOKEN"
        private const val PREF_PROPERTY_ID = "PREF_PROPERTY_ID"
        private const val PHONE_ACCOUNT_OLD = "PHONE_ACCOUNT_OLD"
        private const val IS_SYNC_ACCOUNT = "IS_SYNC_ACCOUNT"
        private const val PREF_KEY_FCM_DEVICE_TOKEN = "FCM_DEVICE_TOKEN"
        private const val PREF_KEY_ACCESS_REMEMBER = "PREF_KEY_ACCESS_REMEMBER"
        private const val PREF_KEY_FIRST = "PREF_KEY_FIRST"
        private const val MESSAGE_AUTH = "pref_message_cc_auth"
        private const val LATITUDE = "pref_latitude"
        private const val LONGTITUDE = "pref_longitude"
        private const val BASE_URL = "pref_base_url"
        private const val AVATAR = "pref_avatar"
        private const val PHONE = "pref_phone"
        private const val PREF_USER_NAME = "pref_user_name"
        private const val PREF_USER = "pref_user"
        private const val PREF_SETTING = "pref_setting"
        private const val PREF_ACCEPT_EVENT = "pref_curent_accept_event"
        const val PREF_FAVORITE_MENU = "pref_favorite_menu"
    }


    private fun getString(key: String): String {
        var value = mSharedPreferences.getString(key, "")
        if (value.isNullOrEmpty()) {
            value = ""
        }
        return value
    }





    private fun getInt(key: String): Int {
        return mSharedPreferences.getInt(key, -1)
    }

    override fun getCurrentUserId() = mSharedPreferences.getString(PREF_KEY_CURRENT_USER_ID, null)

    override fun setCurrentUserId(userId: String?) {
        mSharedPreferences.edit().putString(PREF_KEY_CURRENT_USER_ID, userId).apply()
    }

    override fun getCurrentUserName(): String {
        return mSharedPreferences.getString(PREF_USER_NAME, "") ?: ""
    }

    override fun setCurrentUserName(userName: String) {
        mSharedPreferences.edit().putString(PREF_USER_NAME, userName).apply()
    }

    override fun getCurrentUserEmail(): String {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }

    override fun setCurrentUserEmail(email: String) {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }

    override fun getCurrentUserProfilePicUrl(): String {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }

    override fun setCurrentUserProfilePicUrl(profilePicUrl: String) {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }

    override fun getAccessToken(): String {
        return getString(PREF_KEY_AUTH_TOKEN)
    }

    override fun getPropertyId(): Int {
        return getInt(PREF_PROPERTY_ID)
    }

    override fun setPropertyId(propertyId: Int) {
        mSharedPreferences.edit().putInt(PREF_PROPERTY_ID, propertyId).apply()
    }

    override fun setAccessToken(accessToken: String) {
        mSharedPreferences.edit().putString(PREF_KEY_AUTH_TOKEN, accessToken).apply()
    }

    override fun getFcmToken(): String {
        return getString(PREF_KEY_FCM_DEVICE_TOKEN)
    }

    override fun setFcmToken(fcmToken: String) {
        mSharedPreferences.edit().putString(PREF_KEY_FCM_DEVICE_TOKEN, fcmToken).apply()
    }

    override fun getRemember() = mSharedPreferences.getBoolean(PREF_KEY_ACCESS_REMEMBER, false)

    override fun setRemember(isRemember: Boolean) {
        mSharedPreferences.edit().putBoolean(PREF_KEY_ACCESS_REMEMBER, isRemember).apply()
    }

    override fun getMessageToken() = getString(MESSAGE_AUTH)

    override fun setMessageToken(token: String?) {
        mSharedPreferences.edit().putString(MESSAGE_AUTH, token).apply()
    }

    override fun getLatitude(): Double {
        return mSharedPreferences.getFloat(LATITUDE, 0f).toDouble()
    }

    override fun setLatitude(latitude: Double) {
        mSharedPreferences.edit().putFloat(LATITUDE, latitude.toFloat()).apply()
    }

    override fun getLongitude(): Double {
        return mSharedPreferences.getFloat(LONGTITUDE, 0f).toDouble()
    }

    override fun setLongitude(longitude: Double) {
        mSharedPreferences.edit().putFloat(LONGTITUDE, longitude.toFloat()).apply()
    }

    override fun getBaseUrl(): String? {
        return mSharedPreferences.getString(BASE_URL, null)
    }

    override fun setBaseUrl(url: String) {
        mSharedPreferences.edit().putString(BASE_URL, url).apply()
    }

    override fun setOnDebug(boolean: Boolean) {
        mSharedPreferences.edit().putBoolean("OnDebug", boolean).apply()
    }

    override fun getOnDebug(): Boolean {
        return mSharedPreferences.getBoolean("OnDebug", false)
    }

    override fun getAvatar(): String? {
        return mSharedPreferences.getString(AVATAR, null)
    }

    override fun setAvatar(url: String) {
        mSharedPreferences.edit().putString(AVATAR, url).apply()
    }

    override fun getPhone(): String? {
        return mSharedPreferences.getString(PHONE, null)
    }

    override fun setPhone(url: String) {
        mSharedPreferences.edit().putString(PHONE, url).apply()
    }

    override fun saveUser(user: User) {
        mSharedPreferences.edit().putString(PREF_USER, Gson().toJson(user)).apply()
    }





    fun getPhoneAccountOld(): String {
        return getString(PHONE_ACCOUNT_OLD)
    }

    fun setPhoneAccountOld(phone: String) {
        mSharedPreferences.edit().putString(PHONE_ACCOUNT_OLD, phone).apply()
    }
    fun getAccountOld(): Boolean {
        return mSharedPreferences.getBoolean(IS_SYNC_ACCOUNT, false)
    }

    fun setAccountOld(isOld: Boolean) {
        mSharedPreferences.edit().putBoolean(IS_SYNC_ACCOUNT, isOld).apply()
    }

    override fun getUser(): User? {
        val result = mSharedPreferences.getString(PREF_USER, null)
        return if (result.isNullOrEmpty())
            User()
        else
            Gson().fromJson(result, User::class.java)
    }

    override fun getListAcceptEvent(): String? {
        return mSharedPreferences.getString("PREF_ACCEPT_EVENT", "")
    }

    override fun setListAcceptEvent(eventId: String) {
        mSharedPreferences.edit().putString("PREF_ACCEPT_EVENT", eventId).apply()
    }

    override fun getLoginFirst() = mSharedPreferences.getBoolean(PREF_KEY_FIRST, false)
    override fun setLoginFirst(first: Boolean) {
        mSharedPreferences.edit().putBoolean(PREF_KEY_FIRST, first).apply()
    }



    override fun removeData(key: String) {
        Log.d("mSharedPreferences", mSharedPreferences.edit().remove("PREF_ANY_$key").commit())
    }

    override fun setData(key: String, data: Any?) {
        val str = Gson().toJson(data).toString()
        mSharedPreferences.edit().putString("PREF_ANY_$key", str).apply()

    }


    override fun <T> getData(key: String, classOfT: Class<T>): T? {
        return try {
            Gson().fromJson(mSharedPreferences.getString("PREF_ANY_$key", null), classOfT)
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

}