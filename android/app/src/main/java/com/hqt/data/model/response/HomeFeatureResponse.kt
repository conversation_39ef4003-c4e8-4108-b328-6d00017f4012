package com.hqt.data.model.response

import com.google.gson.annotations.SerializedName

class HomeFeatureResponse(
    @field:SerializedName("data") val data: ArrayList<FeatureItem>? = null,
)


data class FeatureItem(
    @field:SerializedName("type") val type: String,
    @field:SerializedName("title") val title: String,
    @field:SerializedName("slug") val slug: String,
    @field:SerializedName("subtitle") val subtitle: String,
    @field:SerializedName("posts") val posts: ArrayList<MediaItem>? = null,
)

data class MediaItem(
    @field:SerializedName("url") val url: String,
    @field:SerializedName("title") val title: String,
    @field:SerializedName("subtitle") val subtitle: String,
    @field:SerializedName("image") val image: String,
)






