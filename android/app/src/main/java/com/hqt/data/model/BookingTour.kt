package com.hqt.data.model

import com.google.gson.annotations.SerializedName
import com.hqt.datvemaybay.Common
import com.hqt.view.ui.flightSearch.model.FareData
import com.hqt.view.ui.tour.TourBooking
import com.hqt.view.ui.tour.TourItem
import org.json.JSONException
import org.json.JSONObject
import java.io.Serializable

class BookingTour : Booking(), Serializable {
    var departure_f: TourBooking? = null
    var return_f: TourBooking? = null
    override var type = BookingType.TOUR

    @SerializedName("tourItem") var tourItem: TourItem? = null

    override fun getGrandTotal(): Int {
        return total
    }

    override fun getTotalBagFee(): Int {

        return total
    }

    override fun getTotalAddOn(): Int {
     
        return total
    }

}