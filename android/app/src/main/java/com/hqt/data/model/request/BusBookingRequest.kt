package com.hqt.data.model.request

import com.google.gson.annotations.SerializedName

data class BusBookingRequest(@SerializedName("trip_code") var tripCode: String? = null,
    @SerializedName("seats") var seats: String? = null,
    @SerializedName("customer_phone") var customerPhone: String? = null,
    @SerializedName("customer_name") var customerName: String? = null,
    @SerializedName("customer_email") var customerEmail: String? = null,
    @SerializedName("pickup") var pickup: String? = null,
    @SerializedName("pickup_id") var pickupId: Int? = null,
    @SerializedName("drop_off_info") var dropOffInfo: String? = null,
    @SerializedName("drop_off_point_id") var dropOffPointId: Int? = null,
    @SerializedName("voucher") var voucher: String? = null,
    @SerializedName("gcm") var gcm: String? = null

) : BaseModel()