package com.hqt.data.model

import com.hqt.datvemaybay.Common
import java.io.Serializable
import java.util.*

class BusSeatFare : Serializable {
    var id: Int? = null
    var originCode: String = ""
    var originName: String = ""
    var destinationCode: String = ""
    var destinationName: String = ""
    var seatClass: String = ""
    var seatClassName: String = ""
    var seatCount: Int = 0
    var trainNumber: String = ""
    var departureDateTime: Date? = null
    var arrivalDateTime: Date? = null
    var promo: Boolean = false
    var isQuickDep: Boolean = false
    var provider: String = ""
    var stops: Int = 0
    var rewardPoint: Int = 0
    var uuid: String = ""
    var adult: Int = 0
    var child: Int = 0
    var student: Int = 0
    var older: Int = 0
    var adultCount: Int = 0
    var childCount: Int = 0
    var studentCount: Int = 0
    var olderCount: Int = 0
    var netPrice: Int = 0
    var tax: Int = 0
    var pickupDate: Date? = null
    var fee: Int = 0
    var tripCode: String = ""
    var tripName: String = ""
    var pickUpId = -1
    var pickUpName = ""
    var dropOffId = -1
    var dropOffName = ""

    fun getTotalFare(): Int {
        return adult
    }

    fun getRewardPointTotal(): Int {
        var point = 0;
        point = rewardPoint * adultCount
        return point
    }

    fun setPaxCount(booking: BookingBus) {
        adultCount = booking.adult
    }
}