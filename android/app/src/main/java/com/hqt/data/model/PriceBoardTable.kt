package com.hqt.data.model

import com.hqt.datvemaybay.Common
import com.hqt.util.AppConfigs
import com.hqt.util.tableview.Cell
import com.hqt.util.tableview.ColumnHeader
import com.hqt.util.tableview.RowHeader
import org.json.JSONArray
import org.json.JSONException
import org.json.JSONObject
import java.util.*
import kotlin.collections.ArrayList

class PriceBoardTable {
    fun getPriceByKey(key: String, jsonArray: JSONArray): Int {
        try {
            for (i in 0 until jsonArray.length()) {
                val day = jsonArray.getJSONObject(i)

                if (!day.isNull("mPrice") && day.getString("mCode") == key) {
                    return day.getInt("mPrice")
                }
            }

        } catch (e: JSONException) {
            e.printStackTrace()
            AppConfigs.logException(e)
            return -1
        }
        return -1

    }

    fun getMinPrice(jsonArray: JSONArray): Int {
        try {
            var min = Int.MAX_VALUE
            for (i in 0 until jsonArray.length()) {
                val day = jsonArray.getJSONObject(i)

                if (!day.isNull("mPrice") && min > day.getInt("mPrice")) {

                    min = day.getInt("mPrice")
                }
            }
            return min
        } catch (e: JSONException) {
            e.printStackTrace()
            AppConfigs.logException(e)
            return -1
        }

    }
    fun getDayListMonth(route: JSONArray, routeName: String): ArrayList<DatePrice>{
        val list : ArrayList<DatePrice>   = java.util.ArrayList()
        var i = 0

            var headerText = routeName
            if (routeName.length == 6) {
                var originCode = routeName.subSequence(0, 3).toString()
                var destinationCode = routeName.subSequence(3, 6).toString()

                var minPrice = getMinPrice(route)
                for (j in 0 until route.length()) {
                    val p = route.getJSONObject(j)
                    val key = p.getString("mCode")
                    val date = Common.getDateTimeFromFormat(key.substring(7),"yyyyMMdd")

                    val datePrice = DatePrice()
                    datePrice.code = key
                    datePrice.origin = originCode
                    datePrice.destination = destinationCode
                    datePrice.date = date
                    datePrice.price =  p.getInt("mPrice")
                    datePrice.isCheap = datePrice.price == minPrice

                    list.add(datePrice)
                }
        }

        return list
    }
    fun getCellList(col: Int, json: JSONObject): List<List<Cell>>? {

        val list: ArrayList<List<Cell>> = java.util.ArrayList()
        var i = 0
        val keys: Iterator<*> = json.keys()
        while (keys.hasNext()) {
            val cellList: MutableList<Cell> = java.util.ArrayList()
            val routeName = keys.next() as String
            var headerText = routeName
            if (routeName.length == 6) {
                var originCode = routeName.subSequence(0, 3).toString()
                var destinationCode = routeName.subSequence(3, 6).toString()

                val route: JSONArray = json.getJSONArray(routeName)
                var minPrice = getMinPrice(route)
                for (j in 0 until col) {
                    val id = "$j-$i"
                    var date = Calendar.getInstance()
                    date.add(Calendar.MONTH, j)

                    var key = routeName + "_" + Common.dateToString(date.time, "yyyyMM00")
                    var datePrice = DatePrice()

                    datePrice.origin = originCode
                    datePrice.destination = destinationCode
                    datePrice.date = date.time
                    datePrice.price = getPriceByKey(key, route)
                    datePrice.isCheap = datePrice.price == minPrice

                    cellList.add(Cell(id, datePrice))
                }
                list.add(cellList)
                i++
            }
        }

        return list
    }

    fun getColumnHeaderList(): List<ColumnHeader> {
        val list: ArrayList<ColumnHeader> = java.util.ArrayList()
        var now = Calendar.getInstance()
        for (i in 0 until 8) {
            var title = now.get(Calendar.MONTH) + 1
            val header = ColumnHeader(i.toString(), "Th $title")
            list.add(header)
            now.add(Calendar.MONTH, 1)
        }
        return list
    }

    final fun getRowHeaderList(json: JSONObject): List<RowHeader> {
        val list: ArrayList<RowHeader> = ArrayList()

        var i = 0
        val keys: Iterator<*> = json.keys()
        while (keys.hasNext()) {

            val routeName = keys.next() as String
            val route: JSONArray = json.getJSONArray(routeName)
            //val desc = route.getString("desc")
            var headerText = routeName
            if (routeName.length == 6) {
                var originCode = routeName.subSequence(0, 3).toString()
                var destinationCode = routeName.subSequence(3, 6).toString()
                headerText = Common.getAirPortName(originCode, true) + "|" + Common.getAirPortName(destinationCode, true)
            }

            val header = RowHeader(i.toString(), headerText)
            list.add(header)
            i++
        }

        return list
    }

}