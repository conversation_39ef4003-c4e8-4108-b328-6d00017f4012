package com.hqt.view.ui.search.ui.adapter.airport.v2

import com.hqt.base.BaseAdapter
import com.hqt.view.ui.search.data.model.AirportInfo
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ListairportInfoItemBinding
import com.hqt.view.ui.search.ui.state.AirportInfoItemState


class AirportItemAdapterV2(private val listener: (AirportInfo) -> Unit) :
    BaseAdapter<AirportInfo, ListairportInfoItemBinding>(listener) {

    val RESULT_OK = -1



    override fun getLayoutRes(): Int {
        return R.layout.listairport_info_item
    }

    override fun bind(binding: ListairportInfoItemBinding, position: Int, model: AirportInfo) {
        binding.apply {

            itemViewState = AirportInfoItemState(model)



        }

    }


    override fun onItemClickListener(model: AirportInfo) {
        listener(model)
    }

}