package com.hqt.view.ui.bus


import android.app.Activity
import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.android.volley.VolleyError
import com.hqt.data.model.request.BusBookingRequest
import com.hqt.data.model.response.StationPoints
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.FragmentBusBookingBinding
import com.hqt.util.*
import com.hqt.view.ui.reward.ui.activity.RewardActivity
import com.hqt.view.ui.seatmap.SeatView
import com.hqt.viewmodel.BookingBusViewModel
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import org.json.JSONException
import org.json.JSONObject

class BusBookingFragment : Fragment() {
    private var _binding: FragmentBusBookingBinding? = null
    private val binding get() = _binding!! //    lateinit var emptyState: LinearLayout

    private var disposable: Disposable? = null
    private var seatViewList: ArrayList<SeatView> = ArrayList()
    private var routeInfo: BusRoute? = null
    private var bookingRequest: BusBookingRequest? = null
    private var seatSelected: ArrayList<SeatView> = ArrayList()
    private var pickUpPoint: StationPoints? = null
    private var dropOffPoint: StationPoints? = null
    var isInternetConnected = true
    var isUserSigned = false
    var seatListString = ""


    lateinit var viewModel: BookingBusViewModel


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        _binding = FragmentBusBookingBinding.inflate(inflater, container, false)


        viewModel = ViewModelProvider.AndroidViewModelFactory(requireActivity().application)
            .create(BookingBusViewModel::class.java)

        viewModel.isUserSigned.value = (requireActivity() as BusSelectActivity).isUserSigned
        getViewBindding().vm = viewModel

        getViewBindding().lifecycleOwner = this

        getViewBindding().btnBookVe.setOnClickListener {

            if (isValidatedContact()) {
                sendBooking()
            }
        }

        getViewBindding().btnGetVoucher.setOnClickListener {

            if (viewModel.mBooking.value!!.voucher.isNullOrEmpty()) {
                showSelectVoucherDialog()

            } else {
                useVoucher()
            }
        }
        getViewBindding().loginLayout.setOnClickListener {
            (requireActivity() as BusSelectActivity).signIn()
        }

        getViewBindding().toolbar.setNavigationIcon(R.drawable.ic_action_back_home)
        getViewBindding().toolbar.setNavigationOnClickListener {
            (requireActivity() as BusSelectActivity).backClick()
        }

        getViewBindding().toolbar.title = "Điền thông tin"

        return binding.root
    }

    fun initBusData(mRouteInfo: BusRoute,
        mSeatSelect: ArrayList<SeatView>,
        mPickUpPoint: StationPoints,
        mDropOffPoints: StationPoints) {
        routeInfo = mRouteInfo
        seatSelected = mSeatSelect
        pickUpPoint = mPickUpPoint
        dropOffPoint = mDropOffPoints

        viewModel.mBooking.value!!.total = seatSelected.filter { st -> st.isSelect }
            .sumOf { sv -> sv.seatOption!!.seatCharges }

        var fillerList = seatSelected.filter { st -> st.isSelect }
        var seatSummaryText = ""
        seatListString = ""


        fillerList.forEach { seat ->
            seatSummaryText += "Ghế " + seat.seatOption!!.text + ", "
            seatListString += seat.seatOption!!.selectionKey + ","
        }
        seatSummaryText = seatSummaryText.removeSuffix(", ")
        seatListString = seatListString.removeSuffix(",")

        getViewBindding().busRouteInfo.totalSeat.text = seatSelected.filter { st -> st.isSelect }.count().toString()
        getViewBindding().busRouteInfo.totalSeatCharges.text = Common.dinhDangTien(viewModel.mBooking.value!!.total)
        getViewBindding().busRouteInfo.txtSeatSelect.text = seatSummaryText

        getViewBindding().vm = viewModel
        getViewBindding().busRouteInfo.route = routeInfo
        getViewBindding().busRouteInfo.dropOff = dropOffPoint
        getViewBindding().busRouteInfo.pickUp = pickUpPoint

        onSystemSettingChange(AppConfigs.SystemSettingType.USER)


    }

    private fun getViewBindding(): FragmentBusBookingBinding {
        return _binding!!
    }

    private fun isValidatedContact(): Boolean {

        var contactName = Common.unAccent(getViewBindding().txtContactName.text.toString())

        getViewBindding().txtContactName.setText(contactName)

        if (getViewBindding().txtContactName.text.isEmpty()) {
            Toast.makeText(requireContext(), "Vui lòng điền tên liên hệ", Toast.LENGTH_SHORT).show()
            getViewBindding().txtContactName.requestFocus()
            return false
        } else if (Helper.validatorInput(contactName.toString(), Helper.ValidatorType.FULLNAME.text) != null) {
            Toast.makeText(requireContext(), "Vui lòng nhập đúng định dạng tên liên hệ", Toast.LENGTH_SHORT).show()
            getViewBindding().txtContactName.requestFocus()
            return false
        } else if (getViewBindding().txtContactPhone.text.isEmpty()) {
            Toast.makeText(requireContext(), "Vui lòng điền số điện thoại liên hệ", Toast.LENGTH_SHORT).show()
            getViewBindding().txtContactPhone.requestFocus()
            return false
        } else if (!Common.isEmailValid(getViewBindding().txtContactEmail.getText().toString())) {
            AlertDialog.Builder(requireContext()).setIcon(R.drawable.ic_bell_alert).setTitle("Chú ý")
                .setMessage(Common.convertHTML("Vui lòng nhập đúng email hoặc bỏ trống nếu không có"))
                .setPositiveButton("Nhập Email") { dialogInterface, i -> getViewBindding().txtContactEmail.requestFocus() }
                .setNegativeButton("Bỏ qua") { dialogInterface, i ->
                    getViewBindding().txtContactEmail.setText("<EMAIL>")
                    showConfirmBeforBook()

                }.show()
            return false
        }
        return true
    }

    private fun showConfirmBeforBook() {
        val alertDialog = AlertDialog.Builder(requireContext()).create()
        alertDialog.setTitle("Chú ý")
        alertDialog.setIcon(R.drawable.ic_bell_alert)
        alertDialog.setMessage(Common.convertHTML(Common.AppPopup))
        alertDialog.setButton(DialogInterface.BUTTON_NEGATIVE, "Đặt vé") { dialog, which -> sendBooking() }
        alertDialog.setButton(DialogInterface.BUTTON_POSITIVE, "Kiểm tra lại") { dialog, which -> }
        alertDialog.show()
    }

    fun sendBooking() {
        
        val busBookingRequest = BusBookingRequest()
        busBookingRequest.customerEmail = viewModel.mBooking.value!!.contact_email
        busBookingRequest.customerPhone = viewModel.mBooking.value!!.contact_phone
        busBookingRequest.customerName = viewModel.mBooking.value!!.contact_name
        busBookingRequest.tripCode = routeInfo!!.schedules!!.tripCode
        busBookingRequest.pickup = pickUpPoint!!.name
        busBookingRequest.pickupId = pickUpPoint!!.pointId
        busBookingRequest.dropOffInfo = dropOffPoint!!.name
        busBookingRequest.dropOffPointId = dropOffPoint!!.pointId
        busBookingRequest.seats = seatListString
        busBookingRequest.voucher = viewModel.mBooking.value!!.voucher
        busBookingRequest.gcm = Common.FCM_TOKEN

        val dialog = ProgressDialog.progressDialog(requireContext())
        dialog.show()


        AppConfigs.Log("BOOKING REQUEST", AppController.instance.gSon.toJson(busBookingRequest))
        disposable = AppController.instance.getService().postBusBooking(busBookingRequest).subscribeOn(Schedulers.io())
            .doOnSubscribe {


            }.observeOn(AndroidSchedulers.mainThread()).subscribe({ response ->
                dialog.dismiss()
                if (response.data != null) {
                    val `in` = Intent(requireContext(), BusBookingViewActivity::class.java)
                    `in`.putExtra("BookingToken", response.data.token)

                    requireContext().startActivity(`in`)
                    (requireContext() as Activity).finish()
                } else {
                    Toast.makeText(requireContext(), "Đặt chỗ không thành công " + response.message, Toast.LENGTH_SHORT)
                        .show()
                }

            }, { throwable ->

                Toast.makeText(requireContext(),
                    "Đặt chỗ không thành công. Liên hệ 19002642 để được hỗ trợ",
                    Toast.LENGTH_SHORT).show()
                throwable.printStackTrace()
            })


    }

    private fun useVoucher() {
        val dialog = ProgressDialog.progressDialog(requireContext())
        dialog.show()

        val postParam = JSONObject()
        postParam.put("voucher", viewModel.mBooking.value!!.voucher)
        postParam.put("booking", viewModel.mBooking.value!!.getBaseBookingSortInfo())
        postParam.put("checksum",
            Common.getMd5Hash(viewModel.mBooking.value!!.voucher + viewModel.mBooking.value!!.getBaseBookingSortInfo()))

        SSLSendRequest(requireContext()).POST(false,
            "AirLines/Voucher/Use",
            postParam,
            object : SSLSendRequest.CallBackInterface {
                override fun onSuccess(response: JSONObject, cached: Boolean) {
                    try {
                        if (!response.isNull("data")) {
                            val data = response.getJSONObject("data")
                            val rtCode = data.getString("voucher")
                            val rtDiscount = data.getInt("discount")
                            val rtresult = data.getString("text")

                            if (rtDiscount > 0) {
                                viewModel.mBooking.value!!.discount = rtDiscount
                                viewModel.mBooking.value!!.voucher = rtCode
                                getViewBindding().txtVoucherCode.clearFocus()
                                viewModel.onChange()

                            } else {
                                getViewBindding().txtVoucherCode.setText("")
                            }
                            Toast.makeText(requireContext(), rtresult, Toast.LENGTH_SHORT).show()

                        } else {
                            Common.showAlertDialog(requireContext(),
                                "Thật tiếc",
                                "Mã giảm giá không đúng!",
                                false,
                                false)
                        }
                        if (dialog.isShowing) dialog.dismiss()
                    } catch (e: JSONException) {
                        AppConfigs.logException(e)
                        e.printStackTrace()
                    }
                }

                override fun onFail(e: VolleyError) {
                    if (dialog.isShowing) dialog.dismiss()
                    Common.showAlertDialog(requireContext(),
                        "Thông báo !",
                        "Không tìm thấy voucher \nVui lòng liên lạc chúng tôi để được hỗ trợ",
                        false,
                        false)
                }
            })
    }

    fun showSelectVoucherDialog() {
        val alertDialog = AlertDialog.Builder(requireContext()).create()
        alertDialog.setTitle("Chú ý")
        alertDialog.setIcon(R.drawable.ic_bell_alert)

        alertDialog.setMessage(Common.convertHTML("Vui lòng nhập mã <b>Giảm Giá </b>. Nếu chưa có nhấn vào lấy mã để nhận mã giảm giá!"))

        alertDialog.setButton(DialogInterface.BUTTON_NEGATIVE, "Lấy mã") { dialog, which ->
            val `in` = Intent(requireContext(), RewardActivity::class.java)
            getResult.launch(`in`)
        }
        alertDialog.setButton(DialogInterface.BUTTON_POSITIVE,
            "Nhập lại") { dialog, which -> getViewBindding().txtVoucherCode.requestFocus() }
        alertDialog.show()
    }

    private val getResult = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
        if (it.resultCode == Activity.RESULT_OK) {
            if (it.data?.hasExtra("voucherCode") == true) {
                getViewBindding().txtVoucherCode.setText(it.data?.getStringExtra("voucherCode"))
                getViewBindding().btnGetVoucher.performClick()
            }
            val value = it.data?.getStringExtra("input")
        }
    }

    fun onSystemSettingChange(type: AppConfigs.SystemSettingType) {

        if (type == AppConfigs.SystemSettingType.INTERNET) {
            if (this::viewModel.isInitialized) {
                viewModel.updateInternetStatus(isInternetConnected)
            }
        }

        AppConfigs.Log("AppConfigs.SystemSettingType", type.toString())

        if (type == AppConfigs.SystemSettingType.USER) {
            if (this::viewModel.isInitialized) {
                isUserSigned = (requireActivity() as BusSelectActivity).isUserSigned
                viewModel.updateUserStatus(isUserSigned)
                getViewBindding().vm = viewModel
                if (isUserSigned && AppController.instance.user != null) {
                    getViewBindding().txtContactName.setText(AppController.instance.user?.userName)
                    getViewBindding().txtContactEmail.setText(AppController.instance.user?.userEmail)
                    getViewBindding().txtContactPhone.setText(AppController.instance.user?.phoneNumber)
                }
            }
        }
    }

    companion object {
        fun newInstance(): BusBookingFragment {
            val x = BusBookingFragment()

            return x
        }
    }
}