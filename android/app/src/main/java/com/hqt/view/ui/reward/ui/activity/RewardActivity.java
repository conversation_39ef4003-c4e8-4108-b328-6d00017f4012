package com.hqt.view.ui.reward.ui.activity;

import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.text.Html;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentStatePagerAdapter;
import androidx.fragment.app.FragmentTransaction;

import com.android.volley.VolleyError;
import com.github.florent37.materialviewpager.MaterialViewPager;
import com.github.florent37.materialviewpager.header.HeaderDesign;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.firebase.analytics.FirebaseAnalytics;
import com.google.firebase.auth.FirebaseUser;
import com.hqt.view.ui.reward.data.model.Promotion;
import com.hqt.view.ui.reward.data.model.Voucher;
import com.hqt.datvemaybay.Common;
import com.hqt.datvemaybay.R;
import com.hqt.util.AppConfigs;
import com.hqt.util.AppController;
import com.hqt.util.SSLSendRequest;
import com.hqt.view.ui.BaseActivity;
import com.hqt.view.ui.reward.ui.fragment.PromoListFragment;
import com.hqt.view.ui.reward.ui.fragment.VoucherListFragment;

import org.json.JSONException;
import org.json.JSONObject;


public class RewardActivity extends BaseActivity {

    private CoordinatorLayout coordinatorLayout;
    private MaterialViewPager mViewPager;
    AppCompatButton btnRedeemVoucher;
    Toolbar toolbar;
    BottomSheetDialog dialogPromotionView;
    String action = "nomal";

    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        coordinatorLayout = findViewById(R.id.coordinatorLayout);
        mViewPager = findViewById(R.id.materialViewPager);

        Intent in = getIntent();
        if (in.hasExtra("action")) {

            action = in.getStringExtra("action");
            String token = in.getStringExtra("token");
            if (action.equals("viewVoucher")) {
                getVoucher(token);
            } else if (action.equals("viewPromotion")) {

                getPromotion(token);
            }

        }

        viewPaperInit();
        getSupportActionBar().setTitle("Voucher ưu đãi");
        getProfile();


    }

    public void showPromotionView(Promotion promotion) {
        try {
            dialogPromotionView = new BottomSheetDialog(this);
            View viewPromotion = getLayoutInflater().inflate(R.layout.bottom_sheet_promotion_view, null);
            dialogPromotionView.setContentView(viewPromotion);


            ((TextView) viewPromotion.findViewById(R.id.txtName)).setText(promotion.getName());
            ((TextView) viewPromotion.findViewById(R.id.txtDescription)).setText(Html.fromHtml(promotion.getDescription()));
            ((TextView) viewPromotion.findViewById(R.id.txtPoint)).setText((promotion.getPoint() + " Điểm"));
            String exprired_time = Common.dateToString(promotion.getExpiredTime(), "dd/MM/yyyy");
            ((TextView) viewPromotion.findViewById(R.id.txtExpiredTime)).setText("Hạn sử dụng: " + exprired_time);

            btnRedeemVoucher = viewPromotion.findViewById(R.id.btnRedeemVoucher);

            Button btnLogin = viewPromotion.findViewById(R.id.btnLogin);

            btnLogin.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    signIn();
                }
            });

            if (isUserSigned()) {

                btnLogin.setVisibility(View.GONE);
                btnRedeemVoucher.setVisibility(View.VISIBLE);

            } else {
                btnRedeemVoucher.setVisibility(View.GONE);
                btnLogin.setVisibility(View.VISIBLE);
            }


            btnRedeemVoucher.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    // btnRedeemVoucher.startAnimation(() -> null);
                    redeemVoucher(promotion);
                }
            });

            if (!RewardActivity.this.isFinishing() && !dialogPromotionView.isShowing()) {
                dialogPromotionView.show();
                dialogPromotionView.getBehavior().setState(BottomSheetBehavior.STATE_EXPANDED);
            }

        } catch (Exception e) {
            AppConfigs.logException(e);
        }
    }


    @Override
    public void refreshLayout() {
        getProfile();
        viewPaperInit();
        if (dialogPromotionView != null && dialogPromotionView.isShowing()) {
            dialogPromotionView.dismiss();

        }
    }

    public void showVoucherView(Voucher voucher) {
        try {
            BottomSheetDialog dialog = new BottomSheetDialog(this);
            View viewUseVoucher = getLayoutInflater().inflate(R.layout.bottom_sheet_voucher_view, null);
            dialog.setContentView(viewUseVoucher);

            ((TextView) viewUseVoucher.findViewById(R.id.txtTitle)).setText(voucher.getPromotionDetail().getName());
            ((TextView) viewUseVoucher.findViewById(R.id.txtDescription)).setText(Html.fromHtml(voucher.getPromotionDetail().getDescription()));
            ((TextView) viewUseVoucher.findViewById(R.id.txtVoucherCode)).setText(voucher.getVoucher().toUpperCase());
            String exprired_time = Common.dateToString(voucher.getExpiredAt(), "dd/MM/yyyy");
            ((TextView) viewUseVoucher.findViewById(R.id.txtExpiredTime)).setText("Hạn sử dụng: " + exprired_time);

            Button btnCopy = viewUseVoucher.findViewById(R.id.copyVoucher);
            final ClipboardManager clipboard = (ClipboardManager) getApplication().getSystemService(Context.CLIPBOARD_SERVICE);

            btnCopy.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {

                    ClipData clip = ClipData.newPlainText("text", voucher.getVoucher());
                    assert clipboard != null;
                    clipboard.setPrimaryClip(clip);

                    if (getCallingActivity() != null) {
                        Intent in = new Intent();
                        in.putExtra("voucherCode", voucher.getVoucher());
                        setResult(RESULT_OK, in);

                        dialog.dismiss();
                        finish();
                    } else {
                        Toast toast = Toast.makeText(getApplicationContext(), "Đã sao chép mã " + voucher.getVoucher(), Toast.LENGTH_SHORT);
                        toast.show();
                    }

                    FirebaseAnalytics.getInstance(getApplicationContext()).logEvent("voucher_use", null);
                }
            });

            if (!RewardActivity.this.isFinishing() && !dialog.isShowing()) {
                dialog.show();
                dialogPromotionView.getBehavior().setState(BottomSheetBehavior.STATE_EXPANDED);
            }
        } catch (Exception e) {
            AppConfigs.logException(e);
        }

    }

    private void viewPaperInit() {
        mViewPager.getViewPager().setAdapter(new FragmentStatePagerAdapter(getSupportFragmentManager()) {

            @Override
            public Fragment getItem(int position) {
                switch (position % 2) {
                    case 0:
                        return PromoListFragment.newInstance();
                    case 1:
                        return VoucherListFragment.newInstance();
                    default:
                        return PromoListFragment.newInstance();
                }
            }

            @Override
            public int getCount() {
                return 2;
            }

            @Override
            public CharSequence getPageTitle(int position) {
                switch (position % 2) {
                    case 0:
                        return "Danh sách ưu đãi";
                    case 1:
                        return "Voucher của tôi";
                }
                return "";
            }
        });

        mViewPager.setMaterialViewPagerListener(new MaterialViewPager.Listener() {
                                                    @Override
                                                    public HeaderDesign getHeaderDesign(int page) {
                                                        switch (page) {
                                                            case 0: {

                                                                return HeaderDesign.fromColorResAndUrl(
                                                                        R.color.primary, "https://source.unsplash.com/800x600/?airplane");
                                                            }
                                                            case 1: {

                                                                return HeaderDesign.fromColorResAndUrl(
                                                                        R.color.google_blue, "https://source.unsplash.com/800x600/?landspace");
                                                            }
                                                        }
                                                        return null;
                                                    }
                                                }

        );

        mViewPager.getViewPager().setOffscreenPageLimit(mViewPager.getViewPager().getAdapter().getCount());
        mViewPager.getPagerTitleStrip().setViewPager(mViewPager.getViewPager());
    }

    public void changeViewPaper(int id) {
        mViewPager.getViewPager().setCurrentItem(id);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.activity_reward;
    }

    @Override
    public void onPointerCaptureChanged(boolean hasCapture) {

    }

    @Override
    public void setupToolbar() {
        mViewPager = findViewById(R.id.materialViewPager);

        toolbar = mViewPager.getToolbar();
        if (toolbar != null) {

            setSupportActionBar(toolbar);
            toolbar.inflateMenu(R.menu.main);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            toolbar.setNavigationIcon(R.drawable.ic_action_back_home);
            toolbar.setTitleTextColor(Color.WHITE);
            toolbar.setSubtitleTextColor(Color.WHITE);
            toolbar.setNavigationOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    finish();
                }
            });

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                getWindow().setFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS,
                        WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            }
        }
    }

    public void getVoucher(String voucherToken) {

        (new SSLSendRequest(this)).GET(false, "AirLines/Voucher/" + voucherToken, new JSONObject(), new SSLSendRequest.CallBackInterface() {
            @Override
            public void onSuccess(JSONObject response, boolean cached) {
                try {
                    mViewPager.getViewPager().setCurrentItem(1);
                    JSONObject jData = response.getJSONObject("data");
                    Voucher voucher = AppController.getInstance().getGSon().fromJson(jData.toString(), Voucher.class);
                    showVoucherView(voucher);

                } catch (JSONException e) {
                    e.printStackTrace();
                    AppConfigs.logException(e);
                }


            }

            @Override
            public void onFail(VolleyError error) {


            }
        });

    }

    public void getPromotion(String promotionId) {

        (new SSLSendRequest(this)).GET(false, "AirLines/Promotion/" + promotionId, new JSONObject(), new SSLSendRequest.CallBackInterface() {
            @Override
            public void onSuccess(JSONObject response, boolean cached) {
                try {
                    JSONObject jData = response.getJSONObject("data");
                    Promotion promotion = AppController.getInstance().getGSon().fromJson(jData.toString(), Promotion.class);
                    showPromotionView(promotion);

                } catch (JSONException e) {
                    e.printStackTrace();
                    AppConfigs.logException(e);
                }
            }

            @Override
            public void onFail(VolleyError error) {


            }
        });

    }

    public void redeemVoucher(Promotion promotion) {
        if (isUserSigned()) {
            FirebaseUser authUser = getFirebaseUser();
            JSONObject postParam = new JSONObject();
            try {
                postParam.put("promotion_id", promotion.getId());
                postParam.put("point", promotion.getPoint());
                postParam.put("uid", authUser.getUid());

            } catch (JSONException e) {

            }

            (new SSLSendRequest(this)).POST(false, "AirLines/Voucher/Redeem", postParam, new SSLSendRequest.CallBackInterface() {
                @Override
                public void onSuccess(JSONObject response, boolean cached) {
                    try {

                        JSONObject jData = response.getJSONObject("data");

                        String redeemText = jData.getString("text");
                        Toast.makeText(getApplicationContext(), redeemText, Toast.LENGTH_SHORT).show();

                        if (jData.getBoolean("success")) {
                            getProfile();
                            Common.isNewData = true;
                            FragmentManager manager = getSupportFragmentManager();
                            FragmentTransaction ft = getSupportFragmentManager().beginTransaction();

                            for (Fragment frag : manager.getFragments()) {
                                if (frag.getClass() == VoucherListFragment.class) {
                                    ((VoucherListFragment) frag).getVoucherList();
                                }
                            }
                            new Handler().postDelayed(new Runnable() {
                                @Override
                                public void run() {
                                    dialogPromotionView.dismiss();
                                }
                            }, 2000);
                        }

                    } catch (JSONException e) {
                        AppConfigs.logException(e);
                    }
                }

                @Override
                public void onFail(VolleyError error) {

                }
            });
        } else {

            btnRedeemVoucher.setText("Bạn vui lòng đăng nhập trước để đổi điểm nhé!");
        }

    }

    public void getProfile() {

        if (isUserSigned()) {
            FirebaseUser authUser = getFirebaseUser();
            (new SSLSendRequest(this)).GET(true, "users/" + authUser.getUid(), new JSONObject(), new SSLSendRequest.CallBackInterface() {
                @Override
                public void onSuccess(JSONObject response, boolean cached) {
                    try {
                        JSONObject jData = response.getJSONObject("data");
                        String pointText = jData.getJSONObject("point").getString("text");
                        toolbar.setSubtitle(pointText);
                        getSupportActionBar().setTitle(("Chào " + jData.getString("name")));

                    } catch (JSONException e) {
                        AppConfigs.logException(e);
                    }
                }

                @Override
                public void onFail(VolleyError error) {
                    error.printStackTrace();
                }

            });
        }
    }

}
