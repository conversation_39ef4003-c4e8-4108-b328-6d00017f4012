package com.hqt.view.adapter;

/**
 * Created by TN on 10/11/2015.
 */

import java.util.ArrayList;
import java.util.List;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;
import android.view.View.OnClickListener;

import com.hqt.datvemaybay.R;
import com.hqt.data.model.Airport;

public class ListViewAdapter extends BaseAdapter {

    // Declare Variables
    Context mContext;
    LayoutInflater inflater;
    private List<Airport> airPostList = null;
    private ArrayList<Airport> arraylist;
    final int RESULT_OK = -1;

    public ListViewAdapter(Context context,
                           List<Airport> Airportlist) {
        mContext = context;
        this.airPostList = Airportlist;
        inflater = LayoutInflater.from(mContext);
        this.arraylist = new ArrayList<Airport>();
        this.arraylist.addAll(Airportlist);
    }

    public class ViewHolder {
        TextView name;
        TextView nameEn;
        TextView code;
        TextView city;

    }

    @Override
    public int getCount() {
        return airPostList.size();
    }

    @Override
    public Airport getItem(int position) {
        return airPostList.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    public View getView(final int position, View view, ViewGroup parent) {
        final ViewHolder holder;
        if (view == null) {
            holder = new ViewHolder();
            view = inflater.inflate(R.layout.listairport_item, null);
            // Locate the TextViews in listview_item.xml
            holder.name = view.findViewById(R.id.airPortName);
            holder.code = view.findViewById(R.id.airPortCode);
            holder.city = view.findViewById(R.id.airPortCity);

            view.setTag(holder);
        } else {
            holder = (ViewHolder) view.getTag();
        }
        // Set the results into TextViews
        holder.name.setText(airPostList.get(position).getCity());
        holder.code.setText(airPostList.get(position).getCode());
        holder.city.setText(airPostList.get(position).getCity());

        view.setOnClickListener(new OnClickListener() {

            @Override
            public void onClick(View arg0) {
                // Send single item click data to SingleItemView Class

                Intent data = new Intent();
                data.putExtra("code", airPostList.get(position).getCode());
                data.putExtra("name", airPostList.get(position).getCity());
                ((Activity) mContext).setResult(RESULT_OK, data);
                ((Activity) mContext).finish();
            }
        });

        return view;
    }

    // Filter Class
    public void filter(String charText) {
        charText = charText.toLowerCase();
        airPostList.clear();
        if (charText.length() == 0) {
            airPostList.addAll(arraylist);
        } else {
            for (Airport wp : arraylist) {
                if (wp.getName().toLowerCase().contains(charText)) {
                    airPostList.add(wp);
                } else if (wp.getNameEn().toLowerCase().contains(charText)) {
                    airPostList.add(wp);
                } else if (wp.getCity().toLowerCase().contains(charText)) {
                    airPostList.add(wp);
                } else if (wp.getCityEn().toLowerCase().contains(charText)) {
                    airPostList.add(wp);
                } else {
                    if (wp.getCode().toLowerCase().contains(charText)) {
                        airPostList.add(wp);
                    }
                }

            }
        }
        notifyDataSetChanged();
    }


}
