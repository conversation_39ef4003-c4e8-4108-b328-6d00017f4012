package com.hqt.view.ui.flighthistory.ui.state

import com.hqt.view.ui.flighthistory.data.model.FlightHistory
import kotlin.math.roundToInt

data class FlightHistoryItemState(val item: FlightHistory){
    private val score = (item.meta?.score ?: 0f) * 100


    fun getScoreProgressText() = "" + score.roundToInt()
    fun getTxtMetaFlightNumberText() = item.meta?.flight
    fun getMetaGreenText() = item.meta?.flight
    fun getMetaRedText() = item.meta?.red?.total.toString()
    fun getMetaYellowText() = item.meta?.yellow?.total.toString()
    fun getMetaTotalFlightText() = item.meta?.yellow?.total.toString()
}
