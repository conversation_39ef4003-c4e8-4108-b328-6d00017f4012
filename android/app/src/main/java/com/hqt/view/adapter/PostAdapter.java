package com.hqt.view.adapter;

import static com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions.withCrossFade;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.Shader;
import android.net.Uri;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.MultiTransformation;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.engine.bitmap_recycle.BitmapPool;
import com.bumptech.glide.load.resource.bitmap.BitmapTransformation;

import com.bumptech.glide.request.transition.DrawableCrossFadeFactory;
import com.hqt.datvemaybay.Common;
import com.hqt.datvemaybay.R;
import com.hqt.data.model.Post;
import com.hqt.data.model.Snap;
import com.hqt.util.AppConfigs;

import java.security.MessageDigest;
import java.util.List;

public class PostAdapter extends RecyclerView.Adapter<PostAdapter.ViewHolder> {

    private List<Post> mPosts;
    private boolean mHorizontal;
    private boolean mPager;
    private Context mContext;
    private String mSnapType;

    public PostAdapter(Context context, boolean horizontal, boolean pager, List<Post> apps, String snapType) {
        mContext = context;
        mHorizontal = horizontal;
        mPosts = apps;
        mPager = pager;
        mSnapType = snapType;
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        if (mSnapType.equalsIgnoreCase(Snap.SNAP_TYPE_FULL)) {
            return new ViewHolder(LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.adapter_pager_full_title, parent, false));
        } else if (mSnapType.equalsIgnoreCase(Snap.SNAP_TYPE_TITLE)) {
            return mHorizontal ? new ViewHolder(LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.adapter_snap_title, parent, false)) :
                    new ViewHolder(LayoutInflater.from(parent.getContext())
                            .inflate(R.layout.adapter_vertical, parent, false));
        } else if (mSnapType.equalsIgnoreCase(Snap.SNAP_TYPE_SQUARE)) {

            return new ViewHolder(LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.adapter_snap_square, parent, false));
        } else if (mSnapType.equalsIgnoreCase(Snap.SNAP_TYPE_IMAGE)) {
            return new ViewHolder(LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.adapter_snap_image, parent, false));
        }
        if (mSnapType.equalsIgnoreCase(Snap.SNAP_TYPE_BANNER)) {

            return new ViewHolder(LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.adapter_snap_image, parent, false));
        } else {
            return new ViewHolder(LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.adapter_pager_full_title, parent, false));
        }
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, int position) {
        try {
            Post post = mPosts.get(position);
            DrawableCrossFadeFactory factory =
                    new DrawableCrossFadeFactory.Builder().setCrossFadeEnabled(true).build();

            if (mSnapType.equalsIgnoreCase(Snap.SNAP_TYPE_SQUARE) || mSnapType.equalsIgnoreCase(Snap.SNAP_TYPE_IMAGE) || mSnapType.equalsIgnoreCase(Snap.SNAP_TYPE_TITLE)) {

                Glide.with(mContext).load(post.getImage()).override(800, 400).skipMemoryCache(true).thumbnail(0.1f).transition(withCrossFade(factory))
                        .placeholder(R.drawable.placeholder_backgroud_gray).transform(new MultiTransformation(new BitmapTransformation() {
                            @Override
                            protected Bitmap transform(@NonNull BitmapPool pool, @NonNull Bitmap toTransform, int outWidth, int outHeight) {
                                int color = ContextCompat.getColor(mContext, R.color.snap_gradient);

                                if (mSnapType.equalsIgnoreCase(Snap.SNAP_TYPE_SQUARE)) {
                                    color = ContextCompat.getColor(mContext, R.color.snap_gradient);
                                } else {
                                    color = ContextCompat.getColor(mContext, R.color.snap_gradient);
                                }
                                return addGradient(toTransform, color);
                            }

                            @Override
                            public void updateDiskCacheKey(@NonNull MessageDigest messageDigest) {

                            }
                        })).into(holder.imageView);
            } else {
                Glide.with(mContext)
                        .load(post.getImage()).skipMemoryCache(true)
                        .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                        .override(400, 200)
                        .centerInside()
                        .transition(withCrossFade(factory))
                        .placeholder(R.drawable.placeholder_backgroud_gray)
                        .into(holder.imageView);
            }

            if (mSnapType.equalsIgnoreCase(Snap.SNAP_TYPE_BANNER)) {
                holder.nameTextView.setVisibility(View.GONE);
            }
            if (mSnapType.equalsIgnoreCase(Snap.SNAP_TYPE_SQUARE)) {
                long mPrices = Common.stringToLong(post.getSubTitle());
                if (mPrices > -1 && mPrices < 99) {
                    holder.subTitleTextView.setBackground(ContextCompat.getDrawable(mContext, R.drawable.top_subtitle_hot));
                }
            }

            holder.nameTextView.setText(post.getTitle());
            holder.subTitleTextView.setText(post.getSubTitle());
        } catch (Exception e) {

            AppConfigs.logException(e);
            e.printStackTrace();
        }
    }

    public Bitmap addGradient(Bitmap src, int color1) {
        int w = src.getWidth();
        int h = src.getHeight();
        Bitmap overlay = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(overlay);
        canvas.drawBitmap(src, 0, 0, null);

        Paint paint = new Paint();
        LinearGradient shader = new LinearGradient(0, (float) h / 2, 0, h, Color.TRANSPARENT, color1, Shader.TileMode.CLAMP);
        paint.setShader(shader);
        canvas.drawRect(0, 0, w, h, paint);

        return overlay;

    }

    @Override
    public int getItemViewType(int position) {
        return super.getItemViewType(position);
    }

    @Override
    public int getItemCount() {
        return mPosts.size();
    }

    public class ViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener {

        public ImageView imageView;
        public TextView nameTextView;
        public TextView subTitleTextView;

        public ViewHolder(View itemView) {
            super(itemView);
            itemView.setOnClickListener(this);
            imageView = itemView.findViewById(R.id.imageView);
            nameTextView = itemView.findViewById(R.id.nameTextView);
            subTitleTextView = itemView.findViewById(R.id.subtitleTextView);
        }

        @Override
        public void onClick(View v) {
            Intent i = Common.ConvertLinkAction(mContext, Uri.parse(mPosts.get(getAdapterPosition()).getUrl()));
            mContext.startActivity(i);
        }
    }

}