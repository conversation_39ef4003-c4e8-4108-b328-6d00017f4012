package com.hqt.view.ui.meal.ui.adapter

import com.hqt.base.BaseAdapter
import com.hqt.data.model.AddOnInfo
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ListMealItemBinding
import com.hqt.view.ui.search.data.model.AirportInfo


class MealItemAdapter(private val listener: (AddOnInfo) -> Unit) :
    BaseAdapter<AddOnInfo, ListMealItemBinding>(listener) {

    var onChoose : (AirportInfo) -> Unit = {}
    var updateSelected : () -> Unit = {}

    var totalPax = 1

    private var selectedAddOnList: ArrayList<AddOnInfo> = ArrayList()

    var selectedAddOnListSafe: ArrayList<AddOnInfo>
        get() = selectedAddOnList
        set(value) {
            selectedAddOnList = value
        }



    override fun getLayoutRes(): Int {
        return R.layout.list_meal_item
    }

    override fun bind(binding: ListMealItemBinding, position: Int, model: AddOnInfo) {
        binding.apply {


            meal = model
            total = selectedAddOnList.count { it.value == model.value }
            btnInAddOn.setOnClickListener {
                total = addToCart(model)
            }

            btnDeAddOn.setOnClickListener {
                total = removeToCart(model)
            }
        }

    }

    private fun addToCart(addOn: AddOnInfo): Int {

        val totalSelect = selectedAddOnList.count { it.value == addOn.value }
        if (totalSelect < totalPax * 4) {
            selectedAddOnList.add(addOn)
            updateSelected.invoke()
        }

        return selectedAddOnList.count { it.value == addOn.value }
    }

    private fun removeToCart(addOn: AddOnInfo): Int {
        val rm = selectedAddOnList.find { it.value == addOn.value }
        for (i in 0 until selectedAddOnList.size) {
            if (selectedAddOnList[i].value == addOn.value) {
                selectedAddOnList.removeAt(i)
                break;
            }
        }
        updateSelected.invoke()
        return selectedAddOnList.count { it.value == addOn.value }
    }


    override fun onItemClickListener(model: AddOnInfo) {
        listener(model)
    }

}