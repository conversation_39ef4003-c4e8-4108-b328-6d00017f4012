package com.hqt.view.ui.flighthistory.ui.state

import com.hqt.datvemaybay.Common
import com.hqt.view.ui.search.data.model.FlightV2

data class FlightDetailItemState(val item: FlightV2){

    private val depTimeStr = item.departureDateTime.toString()
    val arrTimeStr = item.arriverDateTime.toString()
    val dateStr = depTimeStr.substring(6, 16)


    fun getFlightTime() = depTimeStr.substring(0, 5)
    fun getTileOfTurn() = "${Common.getAirPortName(item.originCode, true)} → ${Common.getAirPortName(item.destinationCode, true)}"
    fun getFlightDate() = Common.getDayOfWeek(dateStr) ?: ""
    fun getFlightDateLunar() = Common.getLunarDate(Common.getDateFromString(dateStr).time) ?: ""
    fun getArrival() = arrTimeStr.substring(0, 5)


}
