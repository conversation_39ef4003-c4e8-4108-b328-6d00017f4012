package com.hqt.view.ui.search.ui

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.hqt.base.model.State
import com.hqt.data.prefs.SharedPrefsHelper
import com.hqt.view.ui.search.data.model.AirportGroup
import com.hqt.util.Log
import com.hqt.view.ui.search.data.api.SearchApiHelper
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import javax.inject.Inject


@HiltViewModel
class AirportSearchViewModel @Inject constructor(

    private val searchApiHelper: SearchApiHelper,
    private val sharedPrefsHelper: SharedPrefsHelper
) : ViewModel() {


    private val _airportLiveData: MutableLiveData<State<ArrayList<AirportGroup>>> =
        MutableLiveData()
    val airportLiveData: LiveData<State<ArrayList<AirportGroup>>> get() = _airportLiveData


    fun getAirport(q : String? = null){
        viewModelScope.launch(Dispatchers.IO) {

            try {

                _airportLiveData.postValue(State.Loading)
                val result = searchApiHelper.getAirport(q)


                Log.d("data", result.data)


                _airportLiveData.postValue(State.Success(result.data ?: arrayListOf()))



            }catch (ex : Exception){
                Log.logException(ex)
            }
        }
    }
    fun getAirportList(){
        viewModelScope.launch(Dispatchers.IO) {

            try {

                _airportLiveData.postValue(State.Loading)
                val result = searchApiHelper.getAirportList()

                sharedPrefsHelper.setData("AIRPORTLIST", result.data)



            }catch (ex : Exception){
                Log.logException(ex)
            }
        }
    }






}