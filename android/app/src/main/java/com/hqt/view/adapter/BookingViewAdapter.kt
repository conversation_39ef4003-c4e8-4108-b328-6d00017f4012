package com.hqt.view.adapter

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.Typeface
import android.os.CountDownTimer
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.hqt.data.model.*
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.PnrActivity
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.HistoryItemLayoutNewBinding
import com.hqt.datvemaybay.databinding.ListTrainSeatItemBinding
import com.hqt.util.AppConfigs
import com.hqt.util.AppController
import com.hqt.view.ui.bus.BusBookingViewActivity
import com.hqt.view.ui.train.TrainBookingViewActivity
import com.hqt.viewmodel.HistoryItemViewModel
import java.util.*

/**
 * Created by TN on 12/1/2016.
 */
class BookingViewAdapter(var mContext: Context, var contents: List<BookingJson>) :
    RecyclerView.Adapter<BookingViewAdapter.ViewHolder>() {
    class ViewHolder(var context: Context, val binding: HistoryItemLayoutNewBinding) :
        RecyclerView.ViewHolder(binding.root) {
        var timer: CountDownTimer? = null

        fun bind(bookingJson: BookingJson) {

            val viewModel = HistoryItemViewModel(context, bookingJson)
            binding.viewModel = viewModel
            binding.bookingview.setOnClickListener {
                if (bookingJson.type == Booking.BookingType.TRAIN) {
                    val bookingReturn = BookingTrain()
                    bookingReturn.token = bookingJson.token
                    bookingReturn.contact_email = bookingJson.contact_email
                    bookingReturn.id = ""
                    val inn = Intent(context, TrainBookingViewActivity::class.java)
                    inn.putExtra("BookingInfo", bookingReturn)
                    context.startActivity(inn)

                } else if (bookingJson.type == Booking.BookingType.BUS) {
                    val bookingReturn = BookingBus()
                    bookingReturn.token = bookingJson.token
                    bookingReturn.contact_email = bookingJson.contact_email
                    bookingReturn.id = ""
                    val inn = Intent(context, BusBookingViewActivity::class.java)
                    inn.putExtra("BookingInfo", bookingReturn)
                    context.startActivity(inn)

                } else {

                    val intent = Intent(context, PnrActivity::class.java)
                    intent.putExtra("bookingId", bookingJson.id)
                    intent.putExtra("token", bookingJson.token)
                    context.startActivity(intent)

                }
            }
            if (timer != null) {
                timer?.cancel()
            }
            val dateTimeLimit = Common.getDateTimeFromFormat(bookingJson.expired_date)
            if (bookingJson.status == "waiting_payment" && dateTimeLimit != null && dateTimeLimit.after(Calendar.getInstance()
                    .getTime())) {

                val endDate = dateTimeLimit.time
                val startTime = System.currentTimeMillis()
                val millisUntilFinished = endDate - startTime;
                timer = object : CountDownTimer(endDate, 1000) {
                    override fun onTick(millisUntilFinished: Long) {
                        binding.txtStatus.text = ("Đợi thanh toán • " + Common.getDateTextCountDown(millisUntilFinished,
                            startTime));
                    }

                    override fun onFinish() {
                        binding.txtStatus.text = ("Hết hạn thanh toán")
                        binding.statusBackground.background = (ContextCompat.getDrawable(context,
                            R.drawable.corner_full));
                    }
                }.start()
            } else {
                timer?.cancel()
            }
        }
    }

    override fun getItemCount(): Int {
        return contents.size
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val layoutInflater = LayoutInflater.from(parent.context)
        val binding: HistoryItemLayoutNewBinding = DataBindingUtil.inflate(layoutInflater,
            R.layout.history_item_layout_new,
            parent,
            false)
        return ViewHolder(mContext, binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        try {
            holder.bind(contents[position])
        } catch (e: Exception) {
            FirebaseCrashlytics.getInstance()
                .setCustomKey("data", AppController.instance.gSon.toJson(contents[position]))
            AppConfigs.logException(e)
        }
    }

    override fun getItemId(position: Int): Long {
        val booking: BookingJson = contents[position]
        return position.toLong()
    }

    override fun getItemViewType(position: Int): Int {
        return position
    }

    companion object {
        const val TYPE_HEADER = 0
        const val TYPE_CELL = 1
    }

}