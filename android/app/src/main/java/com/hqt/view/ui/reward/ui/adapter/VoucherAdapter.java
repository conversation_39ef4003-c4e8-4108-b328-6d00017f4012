package com.hqt.view.ui.reward.ui.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.hqt.datvemaybay.Common;
import com.hqt.datvemaybay.R;
import com.hqt.view.ui.reward.data.model.Voucher;
import com.hqt.view.ui.reward.ui.activity.RewardActivity;

import java.util.List;

public class VoucherAdapter extends RecyclerView.Adapter<VoucherAdapter.ViewHolder> {

    List<Voucher> contents;
    Context mContext;
    AppCompatActivity mActivity;
    static final int TYPE_HEADER = 0;
    static final int TYPE_CELL = 1;
    String selected_position = "";

    public class ViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener {

        public TextView txtTitle, txtExpiredTime;
        public ImageView imgLogo;

        public ViewHolder(View v) {
            super(v);
            txtTitle = v.findViewById(R.id.txtTitle);
            txtExpiredTime = v.findViewById(R.id.txtExpiredTime);
            imgLogo = v.findViewById(R.id.imgLogo);
            v.setOnClickListener(this);

        }

        @Override
        public void onClick(View v) {

            int viewPosition = getLayoutPosition();
            if (mContext instanceof RewardActivity) {

                ((RewardActivity) mContext).showVoucherView(contents.get(viewPosition));

            }
        }
    }


    public VoucherAdapter(Context context, List<Voucher> contents) {
        this.mContext = context;
        this.contents = contents;
    }

    @Override
    public int getItemViewType(int position) {
        switch (position) {
            case 0:
                return TYPE_HEADER;
            default:
                return TYPE_CELL;
        }
    }

    @Override
    public int getItemCount() {
        return contents.size();
    }

    @Override
    public VoucherAdapter.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = null;
        view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.adapter_voucher, parent, false);
        VoucherAdapter.ViewHolder v = new VoucherAdapter.ViewHolder(view);
        return v;
    }

    @Override
    public void onBindViewHolder(VoucherAdapter.ViewHolder holder, int position) {
        holder.txtTitle.setText(contents.get(position).getPromotionDetail().getName().toString());
        String exprired_time = Common.dateToString(contents.get(position).getExpiredAt(), "dd/MM/yyyy");
        holder.txtExpiredTime.setText("Hạn dùng: " + exprired_time);

        Glide.with(mContext)
                .load(contents.get(position).getPromotionDetail().getLogo()).skipMemoryCache(true)
                .fitCenter()
                .into(holder.imgLogo);

    }

    @Override
    public long getItemId(int position) {
        return position;
    }


}