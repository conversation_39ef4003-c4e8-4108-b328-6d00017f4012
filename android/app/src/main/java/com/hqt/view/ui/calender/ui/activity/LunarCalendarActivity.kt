package com.hqt.view.ui.calender.ui.activity

import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.view.View
import android.widget.Toast
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ActivityCalendarBinding
import com.hqt.util.AppConfigs
import com.hqt.util.amlich.EventDecorator
import com.hqt.util.amlich.OneDayDecorator
import com.hqt.util.amlich.TextDecorator
import com.hqt.view.ui.calender.ui.CalenderViewModel
import com.prolificinteractive.materialcalendarview.CalendarDay
import com.prolificinteractive.materialcalendarview.MaterialCalendarView
import com.prolificinteractive.materialcalendarview.OnDateSelectedListener
import com.prolificinteractive.materialcalendarview.OnMonthChangedListener
import com.prolificinteractive.materialcalendarview.format.ArrayWeekDayFormatter
import com.prolificinteractive.materialcalendarview.format.MonthArrayTitleFormatter
import dagger.hilt.android.AndroidEntryPoint
import org.json.JSONArray
import org.threeten.bp.LocalDate
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.concurrent.TimeUnit

/**
 * Created by NT on 4/17/2016.
 */

/**
 * Shows off the most basic usage
 */

@AndroidEntryPoint
class LunarCalendarActivity : com.hqt.base.BaseActivity<ActivityCalendarBinding>(),
    OnDateSelectedListener, OnMonthChangedListener {


    private val viewModel : CalenderViewModel by viewModels()
    var returnDate: Calendar? = null
    var birthDay: Calendar? = null
    var depDate: Calendar? = null
    var reDate: Calendar? = null
    var inId: Int = 0
    var onRangeSelect: Boolean = false
    var onChange: Boolean = false
    var listRangeDateSelect: List<CalendarDay> = ArrayList()
    var isReturn: Boolean = false
    var txtDestination: String? = null
    var txtOrigin: String? = null


    var isShowLunarDates: Boolean = true
    var loadedMonth: String = ""
    var act: String? = ""

    var dateFormat: SimpleDateFormat = SimpleDateFormat("dd/MM/yyyy", Locale.getDefault())

    override fun getLayoutRes(): Int {
        return R.layout.activity_calendar
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        getToolbar()?.title = "Chọn ngày khởi hành"

        getToolbar()?.setNavigationIcon(R.drawable.ic_action_back_home)




        observe()

        val intent = intent
        val txtdepDate = intent.getStringExtra("depDate")

        if (intent.hasExtra("origin"))
            txtOrigin = intent.getStringExtra("origin")
        if (intent.hasExtra("destination"))
            txtDestination = intent.getStringExtra("destination")

        val txtreDate = intent.getStringExtra("reDate")
        act = intent.getStringExtra("act")
        inId = intent.getIntExtra("id", 0)
        val isSearchTrain = intent.getBooleanExtra("isSearchTrain", false)
        binding.btnOk.setOnClickListener {
            onBackPressed()
        }

        if ((txtOrigin != null && txtOrigin!!.length == 3) && (txtDestination != null && txtDestination!!.length == 3) && !isSearchTrain) {
            binding.txtShowCheap.visibility = View.VISIBLE
            binding.cheapSearch.setOnCheckedChangeListener { compoundButton, b ->
                if (b) {
                    monthPrices
                } else {
                    isShowLunarDates = true
                    binding.calendarView.removeDecorators()
                    loadedMonth = ""
                    binding.calendarView.setShowLunarDates(isShowLunarDates)
                    binding.calendarView.state().edit().commit()
                }
            }
        } else {
            binding.txtShowCheap.visibility = View.GONE
        }


        if (act != null) {
            if (act == "RANGEDATE") {
                val txtdepDateEnd = intent.getStringExtra("depDateEnd")
                onRangeSelect = true
                val startDate = Common.getDateFromString(txtdepDate)
                val endDate = Common.getDateFromString(txtdepDateEnd)
                getToolbar()?.title = "Chọn khoảng thời gian đi"
                genCalendarRangeSelect(startDate, endDate)
            } else {
                birthDay = Common.stringToDate(txtdepDate, "yyyy-MM-dd")
                binding.txtShowCheap.setVisibility(View.GONE)
                val minDate = Common.stringToDate(intent.getStringExtra("minDate"), "yyyy-MM-dd")
                genCalendarBirthDate(minDate, birthDay)
                getToolbar()?.title = "Chọn ngày sinh em bé"
            }
        } else if (txtreDate == null) {
            depDate = Common.getDateFromString(txtdepDate)
            genCalendar(depDate)
            getToolbar()?.title = "Chọn ngày đi"
        } else {
            depDate = Common.getDateFromString(txtdepDate)
            reDate = Common.getDateFromString(txtreDate)

            getToolbar()?.title = "Chọn ngày về"
            genCalendar(depDate, reDate, depDate)
        }
    }

    val monthPrices: Unit
        get() {
            val currentMonth = binding.calendarView.currentDate
            val month = Common.dateToString(convertToDate(currentMonth.date).time, "yyyyMM")
            if (loadedMonth.contains(month)) {
                AppConfigs.Log("loaded", loadedMonth)
            } else {
                loadedMonth = "$loadedMonth-$month"

                viewModel.getPricesBoard(binding.calendarView.currentDate, txtOrigin + txtDestination)


            }
        }


    private fun observe(){

        viewModel.listDecorLiveData.observe(this){
            if (isShowLunarDates) {
                binding.calendarView.setShowLunarDates(!isShowLunarDates)
                isShowLunarDates = false
                binding.calendarView.state().edit().commit()
            }


            binding.calendarView.addDecorators(it)

        }
        viewModel.pricesBoardLiveData.observe(this){

        }
    }



    private fun genCalendar(selectedDate: Calendar?) {
        returnDate = selectedDate
        binding.calendarView.setOnDateChangedListener(this)
        binding.calendarView.showOtherDates = MaterialCalendarView.SHOW_OUT_OF_RANGE

        val maxDate = Calendar.getInstance()
        maxDate.add(Calendar.MONTH, 18)
        maxDate[maxDate[Calendar.YEAR], maxDate[Calendar.MONTH]] = maxDate[Calendar.DATE]

        binding.calendarView.selectedDate = CalendarDay.from(
            selectedDate!![Calendar.YEAR],
            selectedDate[Calendar.MONTH] + 1,
            selectedDate[Calendar.DATE]
        )


        binding.calendarView.setWeekDayFormatter(ArrayWeekDayFormatter(resources.getTextArray(R.array.custom_weekdays)))
        binding.calendarView.setTitleFormatter(MonthArrayTitleFormatter(resources.getTextArray(R.array.custom_months)))
        binding.calendarView.selectionMode = MaterialCalendarView.SELECTION_MODE_SINGLE
        binding.calendarView.setDateTextAppearance(R.style.CustomDayTextAppearance)
        binding.calendarView.currentDate = convertToCalendarDay(selectedDate)
        binding.calendarView.setOnMonthChangedListener(this)
        binding.calendarView.setShowLunarDates(true)
        binding.calendarView.state().edit().isCacheCalendarPositionEnabled(true)
            .setMinimumDate(LocalDate.now()).setMaximumDate(convertToCalendarDay(maxDate)).commit()
        val cal = Calendar.getInstance()
        cal[Calendar.DATE] = 1

        //addMonthPrcesDecord(selectedDate);
    }

    fun convertToCalendarDay(date: Calendar?): CalendarDay {
        return CalendarDay.from(
            LocalDate.of(
                date!![Calendar.YEAR], date[Calendar.MONTH] + 1, date[Calendar.DATE]
            )
        )
    }

    private fun genCalendarBirthDate(minDate: Calendar, birthDay: Calendar?) {
        binding.calendarView.setOnDateChangedListener(this)
        binding.calendarView.showOtherDates = MaterialCalendarView.SHOW_OUT_OF_RANGE
        val maxDate = Calendar.getInstance()
        maxDate[minDate[Calendar.YEAR] + 2, minDate[Calendar.MONTH]] = minDate[Calendar.DATE]
        if (birthDay != null) {
            binding.calendarView.selectedDate = convertToCalendarDay(birthDay)
        }

        binding.calendarView.setWeekDayFormatter(ArrayWeekDayFormatter(resources.getTextArray(R.array.custom_weekdays)))
        binding.calendarView.setTitleFormatter(MonthArrayTitleFormatter(resources.getTextArray(R.array.custom_months)))
        binding.calendarView.selectionMode = MaterialCalendarView.SELECTION_MODE_SINGLE
        binding.calendarView.setDateTextAppearance(R.style.CustomDayTextAppearance)
        binding.calendarView.setOnMonthChangedListener(this)
        binding.calendarView.currentDate = convertToCalendarDay(birthDay)
        binding.calendarView.setShowLunarDates(true)
        binding.calendarView.state().edit().isCacheCalendarPositionEnabled(true)
            .setMinimumDate(convertToCalendarDay(minDate))
            .setMaximumDate(convertToCalendarDay(maxDate)).commit()

        val cal = Calendar.getInstance()
        cal[Calendar.DATE] = 1
    }

    private fun genCalendar(minDate: Calendar?, selectedDate: Calendar?, departDate: Calendar?) {
        returnDate = selectedDate

//        val ca = LocalDate.now()
//        val min = LocalDate.of(ca.year, Month.JANUARY, 1)
//        val max = LocalDate.of(ca.year + 1, Month.DECEMBER, 31)

        binding.calendarView.setOnDateChangedListener(this)
        binding.calendarView.showOtherDates = MaterialCalendarView.SHOW_OUT_OF_RANGE
        val maxDate = Calendar.getInstance()
        maxDate.add(Calendar.MONTH, 18)
        maxDate[maxDate[Calendar.YEAR], maxDate[Calendar.MONTH]] = maxDate[Calendar.DATE]

        binding.calendarView.setWeekDayFormatter(ArrayWeekDayFormatter(resources.getTextArray(R.array.custom_weekdays)))
        binding.calendarView.setTitleFormatter(MonthArrayTitleFormatter(resources.getTextArray(R.array.custom_months)))
        binding.calendarView.setDateTextAppearance(R.style.CustomDayTextAppearance)
        binding.calendarView.currentDate = convertToCalendarDay(selectedDate)
        binding.calendarView.selectionMode = MaterialCalendarView.SELECTION_MODE_RANGE
        binding.calendarView.setOnMonthChangedListener(this)
        binding.calendarView.selectedDate = convertToCalendarDay(selectedDate)
        binding.calendarView.setShowLunarDates(true)
        binding.calendarView.selectionColor = ContextCompat.getColor(this, R.color.primary)
        binding.calendarView.selectRange(
            convertToCalendarDay(departDate), convertToCalendarDay(selectedDate)
        )
        binding.calendarView.state().edit().isCacheCalendarPositionEnabled(true).setMinimumDate(
            LocalDate.of(
                minDate!![Calendar.YEAR], minDate[Calendar.MONTH] + 1, minDate[Calendar.DATE]
            )
        ).setMaximumDate(
            LocalDate.of(
                maxDate[Calendar.YEAR], maxDate[Calendar.MONTH] + 1, maxDate[Calendar.DATE]
            )
        ).commit()


        val oneDayDecorator = OneDayDecorator(this)
        oneDayDecorator.setDate(Calendar.getInstance().time)
        binding.calendarView.addDecorator(
            EventDecorator(
                ContextCompat.getDrawable(this,R.drawable.max_select_date_background),
                convertToCalendarDay(selectedDate)
            )
        )
        binding.calendarView.addDecorator(
            EventDecorator(
                ContextCompat.getDrawable(this,R.drawable.min_select_date_background),
                convertToCalendarDay(departDate)
            )
        )
    }

    private fun genCalendarRangeSelect(startDate: Calendar?, endDate: Calendar?) {
//        val ca = LocalDate.now()
//        val min = LocalDate.of(ca.year, Month.JANUARY, 1)
//        val max = LocalDate.of(ca.year + 1, Month.DECEMBER, 31)
        val now = Calendar.getInstance()
        val maxDate = Calendar.getInstance()
        maxDate.add(Calendar.MONTH, 18)
        maxDate[maxDate[Calendar.YEAR], maxDate[Calendar.MONTH]] = maxDate[Calendar.DATE]

        binding.calendarView.setOnRangeSelectedListener { widget, dates ->
            if (dates.size <= 15) {
                listRangeDateSelect = dates
                onChange = true
            } else {
                Toast.makeText(
                    applicationContext,
                    "Khoảng thời gian tối đa không quá 15 ngày",
                    Toast.LENGTH_SHORT
                ).show()
            }
        }
        binding.calendarView.setOnDateChangedListener { widget, date, selected -> }

        binding.calendarView.showOtherDates = MaterialCalendarView.SHOW_OUT_OF_RANGE
        binding.calendarView.setWeekDayFormatter(ArrayWeekDayFormatter(resources.getTextArray(R.array.custom_weekdays)))
        binding.calendarView.setTitleFormatter(MonthArrayTitleFormatter(resources.getTextArray(R.array.custom_months)))
        binding.calendarView.setDateTextAppearance(R.style.CustomDayTextAppearance)
        binding.calendarView.currentDate = convertToCalendarDay(now)
        binding.calendarView.selectionMode = MaterialCalendarView.SELECTION_MODE_RANGE
        binding.calendarView.setOnMonthChangedListener(this)

        binding.calendarView.setShowLunarDates(true)
        binding.calendarView.selectionColor = ContextCompat.getColor(this, R.color.primary)
        binding.calendarView.state().edit().isCacheCalendarPositionEnabled(true).setMinimumDate(
            LocalDate.of(
                now[Calendar.YEAR], now[Calendar.MONTH] + 1, now[Calendar.DATE]
            )
        ).setMaximumDate(
            LocalDate.of(
                maxDate[Calendar.YEAR], maxDate[Calendar.MONTH] + 1, maxDate[Calendar.DATE]
            )
        ).commit()

        binding.calendarView.selectRange(
            convertToCalendarDay(startDate), convertToCalendarDay(endDate)
        )
        binding.calendarView.addDecorator(
            EventDecorator(
                ContextCompat.getDrawable(this, R.drawable.min_select_date_background),
                convertToCalendarDay(startDate)
            )
        )
        binding.calendarView.addDecorator(
            EventDecorator(
                ContextCompat.getDrawable(this, R.drawable.max_select_date_background),
                convertToCalendarDay(endDate)
            )
        )

        Toast.makeText(
            this, "Chọn khoảng thời gian bạn có thể bay\nTối đa 15 ngày", Toast.LENGTH_SHORT
        ).show()
    }

    fun addRangeSelectDate(startDate: Date, endDate: Date) {
        val days = getUnitBetweenDates(startDate, endDate, TimeUnit.DAYS).toInt()
        val oneDayDecorator = OneDayDecorator(this)

        for (i in 0 until days) {
            val c = Calendar.getInstance()
            c.time = startDate
            c.add(Calendar.DATE, i)
            oneDayDecorator.setDate(c.time)
            if (c.time.before(endDate)) {
                binding.calendarView.addDecorator(oneDayDecorator)
            }
        }
    }

    fun addMonthPricesDecord(pricesData: JSONArray) {
        if (isShowLunarDates) {
            binding.calendarView.setShowLunarDates(!isShowLunarDates)
            isShowLunarDates = false
            binding.calendarView.state().edit().commit()
        }
        val listDecor = ArrayList<TextDecorator>()
        try {
            for (i in 0 until pricesData.length()) {
                val day = pricesData.getJSONObject(i)


                val mCode = day.getString("mCode")
                val price = day.getInt("mPrice") / 1000

                val calDay = CalendarDay.from(
                    mCode.substring(7, 11).toInt(),
                    mCode.substring(11, 13).toInt(),
                    mCode.substring(13, 15).toInt()
                )
                if (CalendarDay.today().isBefore(calDay)) {
                    if (price < 200) {
                        listDecor.add(
                            (TextDecorator(
                                Color.parseColor("#2980b9"), calDay, price.toString() + "k"
                            ))
                        )
                    } else {
                        listDecor.add(
                            (TextDecorator(
                                Color.parseColor("#848383"), calDay, price.toString() + "k"
                            ))
                        )
                    }
                }
            }
            binding.calendarView.addDecorators(listDecor)
        } catch (e: Exception) {
            AppConfigs.logException(e)
        }
    }

    private fun convertToDate(date: LocalDate): Calendar {
        val d = Calendar.getInstance()
        d[date.year, date.monthValue - 1] = date.dayOfMonth
        return d
    }

    override fun onDateSelected(
        widget: MaterialCalendarView, date: CalendarDay, selected: Boolean
    ) {
        onChange = true
        if (reDate != null) {
            widget.selectRange(convertToCalendarDay(depDate), date)
        }

        returnDate = convertToDate(date.date)
        binding.btnOk.text = "Chọn"
    }


    override fun onBackPressed() {
        super.onBackPressed()
        try {
            if (!onChange) {
                finish()
            } else {
                if (onRangeSelect) {
                    if (listRangeDateSelect.isNotEmpty()) {
                        val data = Intent()
                        data.putExtra(
                            "date",
                            dateFormat.format(convertToDate(listRangeDateSelect[0].date).time)
                        )
                        data.putExtra(
                            "dateEnd",
                            dateFormat.format(convertToDate(listRangeDateSelect[listRangeDateSelect.size - 1].date).time)
                        )
                        setResult(RESULT_OK, data)
                    }
                    finish()
                } else if (returnDate != null) {
                    val data = Intent()
                    data.putExtra("date", dateFormat.format(returnDate!!.time))
                    setResult(RESULT_OK, data)
                    finish()
                } else {
                    Toast.makeText(this, "Vui lòng chọn ngày sinh em bé", Toast.LENGTH_LONG).show()
                }
            }
        } catch (e: Exception) {
            AppConfigs.logException(e)
        }
    }

    override fun onMonthChanged(widget: MaterialCalendarView, date: CalendarDay) {
        val cal = Calendar.getInstance()
        cal[date.year, date.month] = 1
        widget.currentDate = date
        if (binding.cheapSearch.isChecked) {
            Handler().postDelayed({ monthPrices }, 1000)
        }
    } //    @Override
    //    protected void attachBaseContext(Context newBase) {
    //        super.attachBaseContext(IconicsContextWrapper.wrap(newBase));
    //    }

    companion object {
        private fun getUnitBetweenDates(startDate: Date, endDate: Date, timeUnit: TimeUnit): Long {
            val timeDiff = endDate.time - startDate.time
            return timeUnit.convert(timeDiff, TimeUnit.MILLISECONDS)
        }
    }
}
/**
 * Simulate an API call to show how to add decorators
 */

