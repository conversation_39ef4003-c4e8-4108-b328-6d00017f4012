package com.hqt.view.ui.tour

import android.content.Context
import android.net.Uri
import android.view.Gravity
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.github.rubensousa.gravitysnaphelper.GravitySnapHelper
import com.github.rubensousa.gravitysnaphelper.OrientationAwareRecyclerView
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.TourListItemLayoutNewBinding
import com.hqt.util.AppConfigs
import com.hqt.util.AppController
import com.hqt.util.Widget

/**
 * Created by TN on 12/1/2016.
 */
class TourListViewAdapter(var mContext: Context, var contents: List<TourList>) :
    RecyclerView.Adapter<TourListViewAdapter.ViewHolder>() {
    class ViewHolder(var context: Context, val binding: TourListItemLayoutNewBinding) :
        RecyclerView.ViewHolder(binding.root) {
        var recyclerView: OrientationAwareRecyclerView? = null

        fun bind(tourListItem: TourList) { //            tourListItem.style = "VERTICAL" //            val viewModel = HistoryItemViewModel(context, bookingJson)
            //            binding.viewModel = viewModel

            binding.title.text = tourListItem.title

            binding.titleView.setOnClickListener {
                val notificationIntent = Common.ConvertLinkAction(context, Uri.parse(tourListItem.link))
                notificationIntent.putExtra("title", tourListItem.title)
                context.startActivity(notificationIntent)
            }
            binding.subCategory.removeAllViews()
            if (tourListItem.sub != null) {
                tourListItem.sub?.forEach {
                    Widget.createTourSubTitleItem(context, it, binding.subCategory)
                }
            }

            if (tourListItem.style == "VERTICAL") {
                binding.recyclerView.layoutManager = GridLayoutManager(binding.recyclerView.context, 2)
            } else {
                binding.recyclerView.layoutManager = LinearLayoutManager(binding.recyclerView.context,
                    LinearLayoutManager.HORIZONTAL,
                    false)
            }
            val mAdapter = TourListItemAdapter(context, tourListItem.tours, tourListItem.style!!)
            binding.recyclerView.adapter = mAdapter
        }
    }

    override fun getItemCount(): Int {
        return contents.size
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val layoutInflater = LayoutInflater.from(parent.context)
        val binding: TourListItemLayoutNewBinding = DataBindingUtil.inflate(layoutInflater,
            R.layout.tour_list_item_layout_new,
            parent,
            false)
        return ViewHolder(mContext, binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        try {
            holder.bind(contents[position])
        } catch (e: Exception) {
            FirebaseCrashlytics.getInstance()
                .setCustomKey("data", AppController.instance.gSon.toJson(contents[position]))
            AppConfigs.logException(e)
        }
    }

    override fun getItemId(position: Int): Long {
        val booking: TourList = contents[position]
        return position.toLong()
    }

    override fun getItemViewType(position: Int): Int {
        return position
    }

    companion object {
        const val TYPE_HEADER = 0
        const val TYPE_CELL = 1
    }

}