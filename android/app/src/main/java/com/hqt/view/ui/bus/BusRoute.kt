package com.hqt.view.ui.bus

import com.google.gson.annotations.SerializedName
import com.hqt.util.AppConfigs
import java.util.*
import java.util.concurrent.TimeUnit

data class BusRoute(@SerializedName("idIndex") var idIndex: String? = null,
    @SerializedName("departure_place") var departurePlace: Int? = null,
    @SerializedName("name") var name: String? = null,
    @SerializedName("from") var from: BusStation? = BusStation(),
    @SerializedName("to") var to: BusStation? = BusStation(),
    @SerializedName("duration") var duration: Int? = null,
    @SerializedName("distance") var distance: Int? = null,
    @SerializedName("departure_date") var departureDate: Date? = null,
    @SerializedName("departure_time") var departureTime: String? = null,
    @SerializedName("company") var company: Company? = Company(),
    @SerializedName("note") var note: BusNote? = null,
    @SerializedName("schedules") var schedules: Schedules? = Schedules()) {
    fun getDurationText(): String {
        try {
            val diffInHours = duration!!.div(60)
            val diffInMin = duration!!.mod(60)
            return diffInHours.toString() + "h" + diffInMin + ""
        } catch (e: Exception) {
            return ""
        }
    }
}

data class BusStation(

    @SerializedName("id") var id: Int? = null,
    @SerializedName("name") var name: String? = null,
    @SerializedName("address") var address: String? = null,
    @SerializedName("city_id") var cityId: Int? = null,
    @SerializedName("city_name") var cityName: String? = null,
    @SerializedName("district_id") var districtId: Int? = null,
    @SerializedName("district_name") var districtName: String? = null

)

data class Ratings(

    @SerializedName("overall") var overall: Double? = null,
    @SerializedName("comments") var comments: Int? = null,


    ) {
    fun text(): String {
        return "" + overall + " (" + comments + ")"
    }
}

data class Company(

    @SerializedName("id") var id: Int? = null,
    @SerializedName("name") var name: String? = null,
    @SerializedName("image") var image: String? = null,
    @SerializedName("ratings") var ratings: Ratings? = Ratings()

)

data class BusNote(

    @SerializedName("category") var id: Int? = null,
    @SerializedName("label") var name: String? = null,
    @SerializedName("content") var image: String? = null,

    )

data class Schedules(

    @SerializedName("trip_code") var tripCode: String? = null,
    @SerializedName("pickup_date") var pickupDate: Date? = null,
    @SerializedName("arrival_time") var arrivalTime: Date? = null,
    @SerializedName("fare") var fare: Int? = null,
    @SerializedName("available_seats") var availableSeats: Int? = null,
    @SerializedName("total_seats") var totalSeats: Int? = null,
    @SerializedName("vehicle_type") var vehicleType: String? = null,
    @SerializedName("time_limit") var timeLimit: Int? = null

)