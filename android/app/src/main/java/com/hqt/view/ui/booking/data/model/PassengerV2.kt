package com.hqt.view.ui.booking.data.model

import android.text.Spanned
import androidx.databinding.BaseObservable
import com.hqt.data.model.AddOnInfo
import com.hqt.data.model.AddOnType
import com.hqt.datvemaybay.Common
import com.mikepenz.iconics.typeface.IIcon
import com.mikepenz.iconics.typeface.library.fontawesome.FontAwesome
import com.mikepenz.iconics.typeface.library.googlematerial.GoogleMaterial
import java.io.Serializable
import java.util.Calendar
import java.util.regex.Matcher
import java.util.regex.Pattern

class PassengerV2 : BaseObservable(), Serializable {

    var type: PassengerType? = null
    var title: PassengerTitle? = null
    var baggage: Baggage? = null
    var returnBaggage: Baggage? = null
    var birthday: String? = ""
    var idNumber: String? = ""

    var addOn: ArrayList<AddOnInfo> = ArrayList()
    var addOnReturn: ArrayList<AddOnInfo> = ArrayList()


    @Transient
    var order: Int = 0
    var firstName: String = ""
    var lastName: String = ""

    @Transient
    var isValidated: Boolean = false
    var fullName: String = ""
    var fullname: String = ""

    fun startInput(paxType: PassengerType, paxOrder: Int) {
        order = paxOrder
        type = paxType
    }

    fun updateAddOn(type: AddOnType, addOnList: ArrayList<AddOnInfo>, isReturn: Boolean) {
        if (isReturn) {
            val currentAddOn = addOnReturn.filter { it -> it.type != type }.toList()
            addOnReturn = ArrayList(currentAddOn)
            addOnReturn.addAll(addOnList)

        } else {
            val currentAddOn = addOn.filter { it -> it.type != type }.toList()
            addOn = ArrayList(currentAddOn)
            addOn.addAll(addOnList)
        }
    }

    fun getPaxFullName(): String {

        if (fullName == "" && fullname != "") {
            return fullname
        }
        return fullName;
    }

    fun getSeatInfo(): String {

        var text = ""
        var textRt = ""
        var firstReturn = true
        addOnReturn.filter { addOnInfo -> addOnInfo.type == AddOnType.SEAT }.forEach {
            if (firstReturn) {
                textRt += "\nVề ghế " + it.text
            } else {
                textRt += " " + it.text
            }
            firstReturn = false

        }
        addOn.filter { addOnInfo -> addOnInfo.type == AddOnType.SEAT }.forEach {
            if (firstReturn) {
                text += "Ghế " + it.text
            } else {
                text += "Đi ghế " + it.text
            }

        }


        if (text.length >= 3) {
            return text + textRt
        }
        return ""
    }

    fun getFullNameDetail(): String { //fullName = "$lastName $firstName"
        if (fullName == "") {
            when (type) {
                PassengerType.ADULT -> return "Người lớn $order"
                PassengerType.CHILD -> return "Trẻ em $order"
                PassengerType.STUDENT -> return "Học sinh $order"
                PassengerType.INFANT -> return "Em bé $order"
                PassengerType.OLDER -> return "Người cao tuổi $order"
                else -> return "Người lớn"

            }
        }


        val p: Pattern = Pattern.compile("([0-9])")
        val m: Matcher = p.matcher(fullName)
        if (m.find()) {
            return fullName;
        }

        if (title == null) return fullName

        return (title as PassengerTitle).text + " , $fullName"
    }

    fun splitPaxName() {

        if (!fullName.isEmpty()) {
            val nameParts = fullName.split(" ".toRegex()).toTypedArray()
            lastName = nameParts[0]
            nameParts[0] = ""
            firstName = Common.join(" ", nameParts)

        }

    }

    fun saveFullName() {
        fullName = "$lastName $firstName"
    }

    fun getAddonDetail(): Spanned {
        var text = ""
        if ((baggage != null && (baggage!!.value?.toIntOrNull()
                ?: 0) > 0) || (returnBaggage != null && (returnBaggage!!.value?.toIntOrNull()
                ?: 0) > 0)
        ) {
            text = "- Hành lý"
            if (baggage != null && (baggage!!.value?.toIntOrNull() ?: 0) > 0) {
                text += " lượt đi <b>${baggage!!.value}kg</b> "
            }
            if (returnBaggage != null && (returnBaggage!!.value?.toIntOrNull() ?: 0) > 0) {
                text += " lượt về <b>${returnBaggage!!.value}kg</b>"
            }
            text += "<br/>"
        }
        if (!birthday.isNullOrEmpty()) {
            text = if (text == "") {
                "- Ngày sinh: <b>$birthday </b>"
            } else {
                "$text<br/>- Ngày sinh: <b>$birthday </b>"
            }

        }
        if (!idNumber.isNullOrEmpty()) {
            text = if (text == "") {
                "- Số giấy tờ: <b>$idNumber </b>"
            } else {
                "$text- Số giấy tờ: <b>$idNumber </b>"
            }
        }
        var totalMeal = addOn.count { it.type == AddOnType.MEAL }
        totalMeal += addOnReturn.count { it.type == AddOnType.MEAL }
        if (totalMeal > 0) {
            text = "$text- Món ăn nóng x <b>$totalMeal </b>"
        }

        return Common.convertHTML(text)

    }

    fun getIsShowAddon(): Boolean {
        if ((baggage != null && (baggage!!.value?.toIntOrNull()
                ?: 0) > 0) || (returnBaggage != null && (returnBaggage!!.value?.toIntOrNull()
                ?: 0) > 0)
        ) {
            return true
        }
        if (addOn.count { it.type == AddOnType.MEAL } + addOnReturn.count { it.type == AddOnType.MEAL } > 0) {
            return true
        }
        if (!birthday.isNullOrEmpty()) {
            return true
        }
        return false
    }

    fun getMinBirthDate(isTrain: Boolean): Calendar {
        var now = Calendar.getInstance()
        if (!Common.MAX_DEPATURE_DATE.isNullOrEmpty()) {
            now = Common.stringToDate(Common.MAX_DEPATURE_DATE, "yyyy-MM-dd")
        }
        when (type) {
            PassengerType.ADULT -> now.add(Calendar.YEAR, -100)
            PassengerType.CHILD -> if (isTrain) now.add(
                Calendar.YEAR,
                -10
            ) else now.add(Calendar.YEAR, -12)

            PassengerType.STUDENT -> now.add(Calendar.YEAR, -100)
            PassengerType.INFANT -> now.add(Calendar.YEAR, -2)
            PassengerType.OLDER -> now.add(Calendar.YEAR, -100)
            else -> now.add(Calendar.YEAR, -100)
        }
        return now
    }

    fun getMaxBirthDate(isTrain: Boolean): Calendar {
        var now = Calendar.getInstance()
        when (type) {
            PassengerType.ADULT -> if (isTrain) now.add(
                Calendar.YEAR,
                -10
            ) else now.add(Calendar.YEAR, -12)

            PassengerType.CHILD -> if (isTrain) now.add(
                Calendar.YEAR,
                -6
            ) else now.add(Calendar.YEAR, -2)

            PassengerType.STUDENT -> now.add(Calendar.YEAR, -10)
            PassengerType.OLDER -> now.add(Calendar.YEAR, -60)
            PassengerType.INFANT -> now
            else -> now.add(Calendar.YEAR, -12)
        }
        return now
    }

    fun getPaxIcon(): IIcon {
        when (type) {
            PassengerType.ADULT -> return FontAwesome.Icon.faw_male
            PassengerType.CHILD -> return FontAwesome.Icon.faw_child
            PassengerType.STUDENT -> return FontAwesome.Icon.faw_user_graduate
            PassengerType.OLDER -> return FontAwesome.Icon.faw_blind
            PassengerType.INFANT -> return GoogleMaterial.Icon.gmd_child_care
            else -> FontAwesome.Icon.faw_male
        }
        return FontAwesome.Icon.faw_male
    }


    //    @Bindable
    //    var fullName: String = String()
    //        @Bindable get() = field
    //        set(value) {
    //            field = value
    //            notifyPropertyChanged(BR.fullName)
    //        }


}




