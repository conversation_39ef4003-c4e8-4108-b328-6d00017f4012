package com.hqt.view.ui.reward.ui.fragment

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Button
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.github.florent37.materialviewpager.MaterialViewPagerHelper
import com.github.florent37.materialviewpager.header.MaterialViewPagerHeaderDecorator
import com.google.firebase.analytics.FirebaseAnalytics
import com.hqt.base.BaseFragment
import com.hqt.base.model.State
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.WebViewActivity
import com.hqt.datvemaybay.databinding.FragmentRecyclerviewRewardBinding
import com.hqt.util.ViewUtil
import com.hqt.view.ui.reward.data.model.Promotion
import com.hqt.view.ui.reward.ui.RewardViewModel
import com.hqt.view.ui.reward.ui.adapter.PromotionAdapterV2
import com.hqt.view.ui.reward.ui.dialog.PromotionViewDialog
import com.hqt.view.ui.search.ui.helper.MaterialViewPagerHeaderDecoratorV2
import dagger.hilt.android.AndroidEntryPoint


@AndroidEntryPoint
class PromoListFragmentV2 : BaseFragment<FragmentRecyclerviewRewardBinding>() {
    
    private val viewModel : RewardViewModel by activityViewModels()

    private var emptyStateLayout: LinearLayout? = null


    private val promotionAdapter by lazy {
        PromotionAdapterV2{

            viewModel.promotion = it
            val dialog = PromotionViewDialog()
            dialog.onSignIn = {
                signIn()
            }
            dialog.redeemVoucher = {

            }
            dialog.show(childFragmentManager, "")


        }
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_recyclerview_reward
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        MaterialViewPagerHelper.registerRecyclerView(activity, binding.recyclerView)
        emptyStateLayout = view.findViewById(R.id.emptyStateLayout)
        initEmptyState(view)
        initRcv()
//        binding.shimmerViewContainer.startShimmer()
//        binding.shimmerViewContainer.visibility = View.VISIBLE
        FirebaseAnalytics.getInstance(requireActivity()).logEvent("promotion_view", null)
    }


    private fun initRcv(){
        binding.recyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext())
            setHasFixedSize(true)
            adapter = promotionAdapter
            addItemDecoration(MaterialViewPagerHeaderDecoratorV2(viewModel.offset))
        }
        observe()

    }
    
    private fun observe(){
        viewModel.promotionListLiveData.observe(viewLifecycleOwner){
            when(it){
                is State.Error -> {}
                State.Loading -> {
                    binding.shimmerViewContainer.startShimmer()
                    binding.shimmerViewContainer.visibility = View.VISIBLE
                }
                is State.Success -> {
                    promotionAdapter.setData(it.data)
                    binding.shimmerViewContainer.stopShimmer()
                    binding.shimmerViewContainer.visibility = View.GONE

                    if (it.data.isEmpty()) {
                        emptyStateLayout!!.visibility = View.VISIBLE
                    }
                }
            }
            
        }
        viewModel.getPromotion()

    }



    @SuppressLint("NotifyDataSetChanged")
    fun addFlight(listFlight: List<Promotion>?, sortBy: String?) {
        promotionAdapter.clearData()
        promotionAdapter.addData(listFlight ?: arrayListOf())

        //        mAdapter = new RecyclerViewMaterialAdapter(new FlightViewAdapter(getActivity(),mContentItems));
//        binding.recyclerView.setAdapter(mAdapter);
    }

    @SuppressLint("NotifyDataSetChanged")
    fun clearSelect() {
        promotionAdapter.notifyDataSetChanged()
    }

    fun initEmptyState(view: View?) {
        ViewUtil.initEmptyState(view,
            context,
            "Chương trình đã hết. Bạn vui lòng quay lại sau nhé !",
            ContextCompat.getDrawable(requireContext(), R.drawable.ic_empty_state_waiting),
            -1,
            object : ViewUtil.EmptyStateCallBackInterface {
                override fun positiveButton(button: Button) {
                    button.text = "TRỞ VỀ"
                    button.setOnClickListener {
                        activity?.finish()
                    }
                }

                override fun negativeButton(button: Button) {
                    button.text = "SĂN VÉ"
                    button.setOnClickListener {
                        val intent = Intent(requireContext(), WebViewActivity::class.java)
                        startActivity(intent)
                    }
                }
            })
    }

    companion object {
        private const val ITEM_COUNT = 5
        fun newInstance(): PromoListFragmentV2 {
            return PromoListFragmentV2()
        }
    }
}
