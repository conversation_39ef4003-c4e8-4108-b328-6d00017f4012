package com.hqt.view.ui.meal.data.api

import com.hqt.base.model.HttpData
import com.hqt.data.model.AddOnInfo
import com.hqt.data.model.request.GetAddOnRequest
import retrofit2.http.Body
import retrofit2.http.POST
import retrofit2.http.Path

interface MealService {

    @POST("/api/v1/AirLines/AddOn/{air}")
    suspend fun getMeal(
        @Path("air") air: String,
        @Body request: GetAddOnRequest?
    ): HttpData<ArrayList<AddOnInfo>>
}