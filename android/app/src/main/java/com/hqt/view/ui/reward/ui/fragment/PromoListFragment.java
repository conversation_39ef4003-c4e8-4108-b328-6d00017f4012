package com.hqt.view.ui.reward.ui.fragment;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.android.volley.VolleyError;
import com.facebook.shimmer.ShimmerFrameLayout;
import com.github.florent37.materialviewpager.header.MaterialViewPagerHeaderDecorator;
import com.google.firebase.analytics.FirebaseAnalytics;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.hqt.util.AppController;
import com.hqt.util.SSLSendRequest;
import com.hqt.util.ViewUtil;
import com.hqt.view.ui.reward.ui.adapter.PromotionAdapter;
import com.hqt.datvemaybay.R;
import com.hqt.datvemaybay.WebViewActivity;
import com.hqt.view.ui.reward.data.model.Promotion;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

public class PromoListFragment extends Fragment {

    private RecyclerView mRecyclerView;
    private RecyclerView.Adapter mAdapter;

    private static final int ITEM_COUNT = 5;
    private List<Promotion> mContentItems = new ArrayList<>();
    private ShimmerFrameLayout mShimmerViewContainer;
    private LinearLayout emptyStateLayout;

    public static PromoListFragment newInstance() {
        return new PromoListFragment();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_recyclerview_reward, container, false);
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);


        emptyStateLayout = view.findViewById(R.id.emptyStateLayout);
        initEmptyState(view);

        mRecyclerView = view.findViewById(R.id.recyclerView);
        RecyclerView.LayoutManager layoutManager = new LinearLayoutManager(getActivity());
        mRecyclerView.setLayoutManager(layoutManager);
        mRecyclerView.setHasFixedSize(true);

        mAdapter = new PromotionAdapter(getActivity(), mContentItems);

        mRecyclerView.addItemDecoration(new MaterialViewPagerHeaderDecorator());
        mRecyclerView.setAdapter(mAdapter);

        mShimmerViewContainer = view.findViewById(R.id.shimmer_view_container);
        mShimmerViewContainer.startShimmer();
        mShimmerViewContainer.setVisibility(View.VISIBLE);
        
        FirebaseAnalytics.getInstance(getActivity()).logEvent("promotion_view", null);

        getPromotionList();

    }

    public void getPromotionList() {
        mContentItems.clear();

        new SSLSendRequest(getActivity()).GET(false, "AirLines/Promotion", new JSONObject(), new SSLSendRequest.CallBackInterface() {
            @Override
            public void onSuccess(JSONObject response, boolean cached) {
                try {

                    Gson gsons = AppController.getInstance().getGSon();
                    Type listType = new TypeToken<ArrayList<Promotion>>() {
                    }.getType();
                    List<Promotion> parser = gsons.fromJson(response.getJSONArray("data").toString(), listType);

                    mContentItems.addAll(parser);
                    mAdapter.notifyDataSetChanged();
                    mShimmerViewContainer.stopShimmer();
                    mShimmerViewContainer.setVisibility(View.GONE);

                    if (parser.isEmpty()) {
                        emptyStateLayout.setVisibility(View.VISIBLE);
                    }


                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onFail(VolleyError error) {

            }
        });
    }

    public void addFlight(List<Promotion> listFlight, String sortBy) {

        mContentItems.clear();
        mContentItems.addAll(listFlight);
        mAdapter.notifyDataSetChanged();
//        mAdapter = new RecyclerViewMaterialAdapter(new FlightViewAdapter(getActivity(),mContentItems));
//        mRecyclerView.setAdapter(mAdapter);

    }

    public void clearSelect() {

        mAdapter.notifyDataSetChanged();
    }

    public void initEmptyState(View view) {

        ViewUtil.initEmptyState(view, getContext(), "Chương trình đã hết. Bạn vui lòng quay lại sau nhé !", getActivity().getResources().getDrawable(R.drawable.ic_empty_state_waiting), -1, new ViewUtil.EmptyStateCallBackInterface() {
            @Override
            public void positiveButton(Button button) {
                button.setText("TRỞ VỀ");
                button.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        getActivity().finish();
                    }
                });
            }

            @Override
            public void negativeButton(Button button) {
                button.setText("SĂN VÉ");
                button.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        Intent in = new Intent(getContext(), WebViewActivity.class);
                        startActivity(in);
                    }
                });
            }
        });
    }

}
