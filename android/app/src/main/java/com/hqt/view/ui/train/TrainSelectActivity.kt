package com.hqt.view.ui.train

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.widget.Toast
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.android.volley.VolleyError
import com.google.gson.reflect.TypeToken
import com.hqt.data.model.AddOnInfo
import com.hqt.data.model.AddOnType
import com.hqt.data.model.BookingTrain
import com.hqt.data.model.Passenger
import com.hqt.data.model.PassengerType
import com.hqt.data.model.Train
import com.hqt.data.model.TrainSeatFare
import com.hqt.data.model.response.TrainSeatDetail
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ActivityTrainSearchBinding
import com.hqt.util.AppConfigs
import com.hqt.util.AppController
import com.hqt.util.SSLSendRequest
import com.hqt.util.helper.NonSwipeableViewPager
import com.hqt.view.adapter.ViewPagerAdapter
import com.hqt.view.ui.BaseActivityKt
import org.json.JSONException
import org.json.JSONObject


class TrainSelectActivity : BaseActivityKt<ActivityTrainSearchBinding>() {
    private lateinit var viewPager: NonSwipeableViewPager
    lateinit var adapter: ViewPagerAdapter
    override val layoutId: Int = R.layout.activity_train_select_acvitity

    private var tripDetail: JSONObject = JSONObject()
    private var departureData: List<Train> = ArrayList()
    private var returnData: List<Train> = ArrayList()
    lateinit var mContext: Context
    var booking: BookingTrain = BookingTrain()


    override fun onCreate(savedInstanceState: Bundle?) {
        try {
            super.onCreate(savedInstanceState)
            viewPager = findViewById(R.id.viewpager)

            settingUpBooking()
            mContext = this

            firebaseAnalytics.setCurrentScreen(this, "train_booking_result", null)

            viewPager.setOnClickListener {
                Toast.makeText(this, "viewPager Select", Toast.LENGTH_SHORT).show()
            }
            setupViewPager(viewPager)
            getTrainTask()

            LocalBroadcastManager.getInstance(this)
                .registerReceiver(mMessageReceiver, IntentFilter("bookingInsert"))

        } catch (e: Exception) {
            AppConfigs.logException(e)
            finish()
        }

    }

    private fun setupViewPager(viewPager: NonSwipeableViewPager) {

        adapter = ViewPagerAdapter(supportFragmentManager)


        adapter.addFragment(TrainListFragment(false), "SelectTrainFragment")
        if (AppConfigs.DSVNAPI) {
            adapter.addFragment(TrainCoachSelectFragment(false), "SeatClassFragment")
        } else {
            adapter.addFragment(SeatClassListFragment(false), "SeatClassFragment")
        }


        adapter.addFragment(TrainListFragment(true), "SelectTrainFragment")
        if (AppConfigs.DSVNAPI) {
            adapter.addFragment(TrainCoachSelectFragment(true), "SeatClassFragment")
        } else {
            adapter.addFragment(SeatClassListFragment(true), "SeatClassFragment")
        }

        viewPager.adapter = adapter
        viewPager.offscreenPageLimit = 4
    }

    private fun getTrainTask() {

        tripDetail = JSONObject()
        try {
            tripDetail.put("departureDate", booking.departure_date)
            tripDetail.put("returnDate", booking.return_date)
            tripDetail.put("adultCount", booking.adult)
            tripDetail.put("childCount", booking.child)
            tripDetail.put("studentCount", booking.student)
            tripDetail.put("olderCount", booking.older)
            tripDetail.put("isRoundTrip", if (booking.is_round_trip) "1" else "0")

        } catch (e: JSONException) {
            e.printStackTrace()
            AppConfigs.logException(e)
        }
        var baseRequest = "Trains/SearchTrainWeb/"
        if (AppConfigs.DSVNAPI) {
            baseRequest = "Trains/SearchTrain/"
        }

        SSLSendRequest(this).POST(false,
            "${baseRequest}${booking.origin_code}/${booking.destination_code}",
            tripDetail,
            object : SSLSendRequest.CallBackInterface {
                override fun onSuccess(response: JSONObject, cached: Boolean) {

                    if (!response.isNull("data")) {
                        processTrainSearchData(response)
                    }
                }

                override fun onFail(er: VolleyError) {
                    Common.showAlertDialog(
                        mContext,
                        "Thông báo",
                        "Không tìm thấy chuyến đi phù hợp! Vui lòng chọn lại chuyến đi khác",
                        true,
                        false
                    )
                }
            })

    }

    private fun settingUpBooking() {

        booking.origin_code = intent.getStringExtra("originCode").toString()
        booking.destination_code = intent.getStringExtra("destinationCode").toString()
        booking.departure_date = intent.getStringExtra("departureTime").toString()
        booking.return_date = intent.getStringExtra("returnTime").toString()
        booking.adult = intent.getIntExtra("adult", 1)
        booking.child = intent.getIntExtra("child", 0)
        booking.student = intent.getIntExtra("student", 0)
        booking.older = intent.getIntExtra("older", 0)
        booking.is_round_trip = intent.getBooleanExtra("isRoundTrip", false)

        for (i in 1..booking.adult) {
            val passenger = Passenger()
            passenger.type = PassengerType.ADULT
            booking.pax_info.adult.add(passenger)
        }
        for (i in 1..booking.child) {
            val passenger = Passenger()
            passenger.type = PassengerType.CHILD
            booking.pax_info.child.add(passenger)
        }
        for (i in 1..booking.student) {
            val passenger = Passenger()
            passenger.type = PassengerType.STUDENT
            booking.pax_info.student.add(passenger)
        }
        for (i in 1..booking.older) {
            val passenger = Passenger()
            passenger.type = PassengerType.OLDER
            booking.pax_info.older.add(passenger)
        }


    }

    fun processTrainSearchData(json: JSONObject) {
        val listType = object : TypeToken<ArrayList<Train>>() {}.type

        departureData = AppController.instance.gSon.fromJson(
            json.getJSONObject("data").getJSONArray("departure")
                .toString(), listType
        )
        returnData = AppController.instance.gSon.fromJson(
            json.getJSONObject("data").getJSONArray("return").toString(),
            listType
        )

        if (departureData.isEmpty()) {
            Common.showAlertDialog(
                this,
                "Thông báo",
                "Không tìm thấy chuyến đi phù hợp! Vui lòng chọn lại chuyến đi khác",
                true,
                false
            )
        }
        selectTrainDeparture()

    }

    private fun selectTrainDeparture() {
        val frag1 = viewPager.adapter!!.instantiateItem(viewPager, 0) as TrainListFragment
        frag1.genListTrain(departureData)
    }

    fun selectTrainReturn(seat: TrainSeatFare) {

        seat.setPaxCount(booking)

        if (viewPager.currentItem == 1) booking.departure_f = seat
        if (viewPager.currentItem == 3) booking.return_f = seat

        if (viewPager.currentItem == 1 && booking.is_round_trip) {
            viewPager.currentItem = 2
            val frag2 = viewPager.adapter!!.instantiateItem(viewPager, 2) as TrainListFragment
            frag2.genListTrain(returnData)

        }

        if (viewPager.currentItem == 3 || !booking.is_round_trip) {

            //Toast.makeText(this, "Chọn xong  " + booking.is_round_trip.toString() + booking.departure_f.trainNumber + " - " + booking.return_f.trainNumber, Toast.LENGTH_SHORT).show()

            val i = Intent(applicationContext, TrainBookingActivity::class.java)
            i.putExtra("trainBookingInfo", booking)

            startActivity(i)

        }

    }

    fun selectTrainReturn2() {

        if (viewPager.currentItem == 1 && booking.is_round_trip) {
            viewPager.currentItem = 2
            val frag2 = viewPager.adapter!!.instantiateItem(viewPager, 2) as TrainListFragment
            frag2.genListTrain(returnData)

        }

        if (viewPager.currentItem == 3 || !booking.is_round_trip) {


            val i = Intent(applicationContext, TrainBookingActivity::class.java)
            i.putExtra("trainBookingInfo", booking)
            startActivity(i)

        }

    }


    fun selectTrain(train: Train) {

        if (train.fareOptions.isNotEmpty()) {

            if (viewPager.currentItem == 0) {
                booking.departure_f = train.getTrainSeatFare()
            } else if (viewPager.currentItem == 2) {
                booking.return_f = train.getTrainSeatFare()
            }

            viewPager.currentItem = viewPager.currentItem + 1
            val selectSeatFragment = viewPager.adapter!!.instantiateItem(
                viewPager,
                viewPager.currentItem
            ) as SeatClassListFragment
            selectSeatFragment.genListTrainSeat(train)

        } else {

            if (viewPager.currentItem == 0) {
                booking.departure_f = train.getTrainSeatFare()
            } else if (viewPager.currentItem == 2) {
                booking.return_f = train.getTrainSeatFare()
            }


            viewPager.currentItem = viewPager.currentItem + 1
            val selectSeatFragment = viewPager.adapter!!.instantiateItem(
                viewPager,
                viewPager.currentItem
            ) as TrainCoachSelectFragment
            selectSeatFragment.genListCoach(train)

            val trainSeatFare = train.getTrainSeatFare()
            trainSeatFare.adultCount = booking.adult
            trainSeatFare.childCount = booking.child
            trainSeatFare.studentCount = booking.student
            trainSeatFare.olderCount = booking.older


        }


    }

    fun selectSeat(seatDetail: TrainSeatDetail, selected: Boolean): Boolean {

        val selectSeatFragment = viewPager.adapter!!.instantiateItem(
            viewPager,
            viewPager.currentItem
        ) as TrainCoachSelectFragment
        return selectSeatFragment.selectSeat(seatDetail, selected)

    }

    fun genPaxInfo(arraySelectedSeat: ArrayList<TrainSeatDetail>, isRoundTrip: Boolean) {
        try {
            var i = 0
            booking.pax_info.adult.forEach { passenger ->
                val seatDetail = AddOnInfo()
                seatDetail.type = AddOnType.SEAT
                seatDetail.text =
                    "" + arraySelectedSeat[i].seatNumber!! + " Toa " + arraySelectedSeat[i].coachNum!!
                seatDetail.value = arraySelectedSeat[i].selectKey!!
                seatDetail.price = arraySelectedSeat[i].totalPrice!!

                if (isRoundTrip) {
                    passenger.addOnReturn.clear()
                    passenger.addOnReturn.add(seatDetail)
                } else {
                    passenger.addOn.clear()
                    passenger.addOn.add(seatDetail)
                }
                i += 1
            }
            booking.pax_info.child.forEach { passenger ->
                val seatDetail = AddOnInfo()
                seatDetail.type = AddOnType.SEAT
                seatDetail.text =
                    "" + arraySelectedSeat[i].seatNumber!! + " Toa " + arraySelectedSeat[i].coachNum!!
                seatDetail.value = arraySelectedSeat[i].selectKey!!
                seatDetail.price = arraySelectedSeat[i].totalPrice!!

                if (isRoundTrip) {
                    passenger.addOnReturn.clear()
                    passenger.addOnReturn.add(seatDetail)
                } else {
                    passenger.addOn.clear()
                    passenger.addOn.add(seatDetail)
                }
                i += 1
            }
            booking.pax_info.older.forEach { passenger ->
                val seatDetail = AddOnInfo()
                seatDetail.type = AddOnType.SEAT
                seatDetail.text =
                    "" + arraySelectedSeat[i].seatNumber!! + " Toa " + arraySelectedSeat[i].coachNum!!
                seatDetail.value = arraySelectedSeat[i].selectKey!!
                seatDetail.price = arraySelectedSeat[i].totalPrice!!

                if (isRoundTrip) {
                    passenger.addOnReturn.clear()
                    passenger.addOnReturn.add(seatDetail)
                } else {
                    passenger.addOn.clear()
                    passenger.addOn.add(seatDetail)
                }
                i += 1
            }
            booking.pax_info.student.forEach { passenger ->
                val seatDetail = AddOnInfo()
                seatDetail.type = AddOnType.SEAT
                seatDetail.text =
                    "" + arraySelectedSeat[i].seatNumber!! + " Toa " + arraySelectedSeat[i].coachNum!!
                seatDetail.value = arraySelectedSeat[i].selectKey!!
                seatDetail.price = arraySelectedSeat[i].totalPrice!!

                if (isRoundTrip) {
                    passenger.addOnReturn.clear()
                    passenger.addOnReturn.add(seatDetail)
                } else {
                    passenger.addOn.clear()
                    passenger.addOn.add(seatDetail)
                }
                i += 1
            }

        } catch (e: Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }
    }

    fun doneClick(arraySelectedSeat: ArrayList<TrainSeatDetail>, isRoundTrip: Boolean) {
        try {
            if (arraySelectedSeat.size != booking.getTotalPax()) {
                Toast.makeText(this, "Vui lòng chọn đủ số lượng ghế", Toast.LENGTH_SHORT).show()
            } else {
                genPaxInfo(arraySelectedSeat, isRoundTrip)
                if (viewPager.currentItem == 1) {
                    selectTrainReturn2()
                } else {
                    val i = Intent(applicationContext, TrainBookingActivity::class.java)
                    i.putExtra("trainBookingInfo", booking)
                    startActivity(i)
                }
            }
        } catch (e: Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }

    }

    fun backClick() {

    }

    override fun onBackPressed() {
        if (viewPager.currentItem == 0) {
            super.onBackPressed()
        } else {
            viewPager.currentItem = viewPager.currentItem - 1
        }
    }

    private val mMessageReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            finish()
        }
    }

}
