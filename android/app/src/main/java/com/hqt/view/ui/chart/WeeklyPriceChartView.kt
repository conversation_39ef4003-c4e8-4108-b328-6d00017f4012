package com.hqt.view.ui.chart

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.github.mikephil.charting.charts.BarChart
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.BarData
import com.github.mikephil.charting.data.BarDataSet
import com.github.mikephil.charting.data.BarEntry
import com.github.mikephil.charting.formatter.IndexAxisValueFormatter
import com.github.mikephil.charting.formatter.ValueFormatter
import com.github.mikephil.charting.highlight.Highlight
import com.github.mikephil.charting.listener.OnChartValueSelectedListener
import com.hqt.data.model.WeeklyPriceData
import com.hqt.datvemaybay.R
import com.hqt.util.WeeklyPriceProcessor

/**
 * Custom chart view for displaying weekly cheapest ticket prices
 * Uses MPAndroidChart library for rendering interactive bar chart
 */
class WeeklyPriceChartView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr), OnChartValueSelectedListener {

    private lateinit var chartView: BarChart
    private lateinit var titleTextView: TextView
    private lateinit var subtitleTextView: TextView
    private lateinit var loadingTextView: TextView
    private lateinit var errorTextView: TextView

    private var weeklyData: List<WeeklyPriceData> = emptyList()
    private var onBarClickListener: ((WeeklyPriceData) -> Unit)? = null

    init {
        initView()
    }

    private fun initView() {
        orientation = VERTICAL
        LayoutInflater.from(context).inflate(R.layout.view_weekly_price_chart, this, true)

        chartView = findViewById(R.id.weeklyPriceChart)
        titleTextView = findViewById(R.id.chartTitle)
        subtitleTextView = findViewById(R.id.chartSubtitle)
        loadingTextView = findViewById(R.id.loadingText)
        errorTextView = findViewById(R.id.errorText)

        setupChart()
    }

    private fun setupChart() {
        chartView.apply {
            // Chart description
            description.isEnabled = false

            // Chart interaction
            setTouchEnabled(true)
            setDragEnabled(true)
            setScaleEnabled(true)
            setScaleXEnabled(true)
            setScaleYEnabled(false)
            setPinchZoom(true)
            setDrawBarShadow(false)
            setDrawValueAboveBar(true)
            setHighlightFullBarEnabled(true)

            // Chart styling
            setDrawGridBackground(false)
            setOnChartValueSelectedListener(this@WeeklyPriceChartView)

            // X-Axis styling
            xAxis.apply {
                position = XAxis.XAxisPosition.BOTTOM
                setDrawGridLines(false)
                granularity = 1f
                textColor = Color.GRAY
                textSize = 10f
            }

            // Y-Axis styling
            axisLeft.apply {
                setDrawGridLines(true)
                gridColor = Color.LTGRAY
                textColor = Color.GRAY
                textSize = 10f
                axisMinimum = 0f
                valueFormatter = object : ValueFormatter() {
                    override fun getFormattedValue(value: Float): String {
                        return "${value.toInt()}k"
                    }
                }
            }

            // Disable right Y-axis
            axisRight.isEnabled = false

            // Legend
            legend.isEnabled = false

            // Animation
            animateY(800)
        }
    }

    /**
     * Sets the weekly price data and updates the chart
     */
    fun setWeeklyData(
        data: List<WeeklyPriceData>,
        origin: String,
        destination: String
    ) {
        this.weeklyData = data
        updateChart()
        updateTitle(origin, destination)
        setupMarkerView()
        showChart()
    }

    /**
     * Sets up the marker view for detailed tooltips
     */
    private fun setupMarkerView() {
        if (weeklyData.isNotEmpty()) {
            val markerView = WeeklyPriceMarkerView(context, weeklyData)
            markerView.chartView = chartView
            chartView.marker = markerView
        }
    }

    /**
     * Shows loading state
     */
    fun showLoading() {
        chartView.visibility = GONE
        titleTextView.visibility = GONE
        subtitleTextView.visibility = GONE
        errorTextView.visibility = GONE
        loadingTextView.visibility = VISIBLE
        loadingTextView.text = "Đang tải dữ liệu giá vé..."
    }

    /**
     * Shows error state
     */
    fun showError(message: String = "Không thể tải dữ liệu giá vé") {
        chartView.visibility = GONE
        titleTextView.visibility = GONE
        subtitleTextView.visibility = GONE
        loadingTextView.visibility = GONE
        errorTextView.visibility = VISIBLE
        errorTextView.text = message
    }

    /**
     * Shows the chart with data
     */
    private fun showChart() {
        loadingTextView.visibility = GONE
        errorTextView.visibility = GONE
        chartView.visibility = VISIBLE
        titleTextView.visibility = VISIBLE
        subtitleTextView.visibility = VISIBLE
    }

    /**
     * Updates the chart with current weekly data
     */
    private fun updateChart() {
        if (weeklyData.isEmpty()) {
            showError("Không có dữ liệu giá vé")
            return
        }

        val (labels, values) = WeeklyPriceProcessor.convertToChartData(weeklyData)

        // Create bar entries
        val barEntries = mutableListOf<BarEntry>()
        values.forEachIndexed { index, value ->
            barEntries.add(BarEntry(index.toFloat(), value))
        }

        // Create bar data set
        val barDataSet = BarDataSet(barEntries, "Giá vé theo tuần").apply {
            // Color bars - highlight cheapest week
            val colors = mutableListOf<Int>()
            weeklyData.forEachIndexed { index, weekData ->
                if (weekData.isCheapestInRange) {
                    colors.add(ContextCompat.getColor(context, R.color.chart_cheapest_color))
                } else {
                    colors.add(ContextCompat.getColor(context, R.color.colorPrimary))
                }
            }
            setColors(colors)

            // Bar styling
            setDrawValues(true)
            valueTextColor = Color.BLACK
            valueTextSize = 9f
            valueFormatter = object : ValueFormatter() {
                override fun getFormattedValue(value: Float): String {
                    return "${value.toInt()}k"
                }
            }

            // Highlight styling
            highLightColor = Color.TRANSPARENT
        }

        // Create bar data
        val barData = BarData(barDataSet).apply {
            barWidth = 0.8f
        }

        // Set data to chart
        chartView.data = barData

        // Set X-axis labels
        chartView.xAxis.valueFormatter = IndexAxisValueFormatter(labels)
        chartView.xAxis.labelCount = labels.size
        chartView.xAxis.setLabelCount(labels.size, false)

        // Adjust viewport to show all data nicely
        chartView.setVisibleXRangeMaximum(8f) // Show max 8 weeks at once
        chartView.moveViewToX(0f) // Start from the beginning

        // Refresh chart
        chartView.invalidate()
    }

    /**
     * Updates chart title and subtitle
     */
    private fun updateTitle(origin: String, destination: String) {
        titleTextView.text = "Giá vé rẻ nhất theo tuần"
        subtitleTextView.text = "$origin → $destination"
    }

    /**
     * Shows tooltip with price information
     */
    private fun showTooltip(weekData: WeeklyPriceData) {
        // Create and show a simple tooltip or update subtitle
        val tooltipText = "Tuần ${weekData.weekLabel}: ${weekData.getFormattedPrice()}"
        subtitleTextView.text = tooltipText

        // Reset subtitle after 3 seconds
        postDelayed({
            if (weeklyData.isNotEmpty()) {
                val origin = weeklyData.first().origin
                val destination = weeklyData.first().destination
                updateTitle(origin, destination)
            }
        }, 3000)
    }

    /**
     * Sets click listener for chart bars
     */
    fun setOnBarClickListener(listener: (WeeklyPriceData) -> Unit) {
        this.onBarClickListener = listener
    }

    /**
     * Gets the current weekly data
     */
    fun getWeeklyData(): List<WeeklyPriceData> = weeklyData

    /**
     * Refreshes the chart with current data
     */
    fun refresh() {
        if (weeklyData.isNotEmpty()) {
            updateChart()
        }
    }

    // OnChartValueSelectedListener implementation
    override fun onValueSelected(e: com.github.mikephil.charting.data.Entry?, h: Highlight?) {
        e?.let { entry ->
            val index = entry.x.toInt()
            if (index < weeklyData.size) {
                onBarClickListener?.invoke(weeklyData[index])
                showTooltip(weeklyData[index])
            }
        }
    }

    override fun onNothingSelected() {
        // Reset subtitle after selection is cleared
        if (weeklyData.isNotEmpty()) {
            val origin = weeklyData.first().origin
            val destination = weeklyData.first().destination
            updateTitle(origin, destination)
        }
    }
}
