package com.hqt.view.ui.chart

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.db.williamchart.data.BarSet
import com.db.williamchart.view.BarChartView
import com.hqt.data.model.WeeklyPriceData
import com.hqt.datvemaybay.R
import com.hqt.util.WeeklyPriceProcessor

/**
 * Custom chart view for displaying weekly cheapest ticket prices
 * Uses williamchart library for rendering interactive bar chart
 */
class WeeklyPriceChartView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    private lateinit var chartView: BarChartView
    private lateinit var titleTextView: TextView
    private lateinit var subtitleTextView: TextView
    private lateinit var loadingTextView: TextView
    private lateinit var errorTextView: TextView
    
    private var weeklyData: List<WeeklyPriceData> = emptyList()
    private var onBarClickListener: ((WeeklyPriceData) -> Unit)? = null

    init {
        initView()
    }

    private fun initView() {
        orientation = VERTICAL
        LayoutInflater.from(context).inflate(R.layout.view_weekly_price_chart, this, true)
        
        chartView = findViewById(R.id.weeklyPriceChart)
        titleTextView = findViewById(R.id.chartTitle)
        subtitleTextView = findViewById(R.id.chartSubtitle)
        loadingTextView = findViewById(R.id.loadingText)
        errorTextView = findViewById(R.id.errorText)
        
        setupChart()
    }

    private fun setupChart() {
        chartView.apply {
            // Chart styling
            setBarSpacing(8f)
            setRoundCorners(4f)
            setBarsColor(ContextCompat.getColor(context, R.color.colorPrimary))
            
            // Grid and axis styling
            setXAxis(true)
            setYAxis(true)
            setAxisColor(Color.LTGRAY)
            setLabelsColor(Color.GRAY)
            setAxisBorderValues(0f, 100f, 1)
            
            // Animation
            animate()
        }
    }

    /**
     * Sets the weekly price data and updates the chart
     */
    fun setWeeklyData(
        data: List<WeeklyPriceData>,
        origin: String,
        destination: String
    ) {
        this.weeklyData = data
        updateChart()
        updateTitle(origin, destination)
        showChart()
    }

    /**
     * Shows loading state
     */
    fun showLoading() {
        chartView.visibility = GONE
        titleTextView.visibility = GONE
        subtitleTextView.visibility = GONE
        errorTextView.visibility = GONE
        loadingTextView.visibility = VISIBLE
        loadingTextView.text = "Đang tải dữ liệu giá vé..."
    }

    /**
     * Shows error state
     */
    fun showError(message: String = "Không thể tải dữ liệu giá vé") {
        chartView.visibility = GONE
        titleTextView.visibility = GONE
        subtitleTextView.visibility = GONE
        loadingTextView.visibility = GONE
        errorTextView.visibility = VISIBLE
        errorTextView.text = message
    }

    /**
     * Shows the chart with data
     */
    private fun showChart() {
        loadingTextView.visibility = GONE
        errorTextView.visibility = GONE
        chartView.visibility = VISIBLE
        titleTextView.visibility = VISIBLE
        subtitleTextView.visibility = VISIBLE
    }

    /**
     * Updates the chart with current weekly data
     */
    private fun updateChart() {
        if (weeklyData.isEmpty()) {
            showError("Không có dữ liệu giá vé")
            return
        }

        val (labels, values) = WeeklyPriceProcessor.convertToChartData(weeklyData)
        
        // Create bar set
        val barSet = BarSet(labels.toTypedArray(), values.toFloatArray())
        
        // Color bars - highlight cheapest week
        val colors = IntArray(weeklyData.size) { index ->
            if (weeklyData[index].isCheapestInRange) {
                ContextCompat.getColor(context, R.color.chart_cheapest_color)
            } else {
                ContextCompat.getColor(context, R.color.colorPrimary)
            }
        }
        barSet.setColor(*colors)

        // Set data to chart
        chartView.addData(barSet)
        
        // Set click listener
        chartView.setOnEntryClickListener { set, entryIndex, _ ->
            if (entryIndex < weeklyData.size) {
                onBarClickListener?.invoke(weeklyData[entryIndex])
                showTooltip(weeklyData[entryIndex])
            }
        }

        // Show chart
        chartView.show()
    }

    /**
     * Updates chart title and subtitle
     */
    private fun updateTitle(origin: String, destination: String) {
        titleTextView.text = "Giá vé rẻ nhất theo tuần"
        subtitleTextView.text = "$origin → $destination"
    }

    /**
     * Shows tooltip with price information
     */
    private fun showTooltip(weekData: WeeklyPriceData) {
        // Create and show a simple tooltip or update subtitle
        val tooltipText = "Tuần ${weekData.weekLabel}: ${weekData.getFormattedPrice()}"
        subtitleTextView.text = tooltipText
        
        // Reset subtitle after 3 seconds
        postDelayed({
            if (weeklyData.isNotEmpty()) {
                val origin = weeklyData.first().origin
                val destination = weeklyData.first().destination
                updateTitle(origin, destination)
            }
        }, 3000)
    }

    /**
     * Sets click listener for chart bars
     */
    fun setOnBarClickListener(listener: (WeeklyPriceData) -> Unit) {
        this.onBarClickListener = listener
    }

    /**
     * Gets the current weekly data
     */
    fun getWeeklyData(): List<WeeklyPriceData> = weeklyData

    /**
     * Refreshes the chart with current data
     */
    fun refresh() {
        if (weeklyData.isNotEmpty()) {
            updateChart()
        }
    }
}
