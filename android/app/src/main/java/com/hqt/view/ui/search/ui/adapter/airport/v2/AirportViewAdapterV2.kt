package com.hqt.view.ui.search.ui.adapter.airport.v2

import androidx.recyclerview.widget.LinearLayoutManager
import com.hqt.base.BaseAdapter
import com.hqt.view.ui.search.data.model.AirportGroup
import com.hqt.view.ui.search.data.model.AirportInfo
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ListAirportGroupItemBinding


class AirportViewAdapterV2(private val listener: (AirportGroup) -> Unit) :
    BaseAdapter<AirportGroup, ListAirportGroupItemBinding>(listener) {





    var onChoose : (AirportInfo) -> Unit = {}
    override fun getLayoutRes(): Int {
        return R.layout.list_airport_group_item
    }

    override fun bind(binding: ListAirportGroupItemBinding, position: Int, model: AirportGroup) {
        binding.apply {
            groupName.text = model.title



            val airportItemAdapter by lazy {
                AirportItemAdapterV2{ info ->
                    onChoose.invoke(info)

                }
            }

            listAirport.apply {
                layoutManager = LinearLayoutManager(context)
                adapter = airportItemAdapter
            }
            airportItemAdapter.setData(model.airPorts)



        }

    }


    override fun onItemClickListener(model: AirportGroup) {
        listener(model)
    }

}