package com.hqt.view.ui.booking.data.model

import com.google.gson.annotations.SerializedName
import com.hqt.data.model.Booking
import com.hqt.datvemaybay.Common
import com.hqt.view.ui.flightSearch.model.FareData
import com.hqt.view.ui.search.data.model.FlightV2
import org.json.JSONException
import org.json.JSONObject
import java.io.Serializable

class BookingV3 : BaseBooking(), Serializable {
    var departure_f: FlightV2? = null
    var return_f: FlightV2? = null
    var contact: ContactBooking? = ContactBooking()



    @SerializedName("fare_data") var fareData: FareData? = null

    val listPaxString: String
        get() {
            var paxList = ""
            try {
                for (i in 0 until pax_info.adult.count()) {
                    val pax = pax_info.adult.get(i)
                    val paxName = pax.getPaxFullName()
                    val paxTitle = pax.title!!.name
                    paxList = if (i == 0) {
                        "$paxTitle.$paxName"
                    } else {
                        "$paxList\n$paxTitle.$paxName"
                    }
                }
                for (i in 0 until pax_info.child.count()) {
                    val pax = pax_info.child.get(i)
                    val paxName = pax.getPaxFullName()
                    val paxTitle = pax.title!!.name
                    paxList = "$paxList\n$paxTitle.$paxName"
                }
                for (i in 0 until pax_info.infant.count()) {
                    val pax = pax_info.infant.get(i)
                    val paxName = pax.getPaxFullName()
                    val paxTitle = pax.title!!.name
                    val birthDate = pax.birthday
                    paxList = "$paxList\n$paxTitle.$paxName"
                }
            } catch (e: JSONException) {
                return ""
            }
            return paxList
        }

    val smsMessage: String
        get() {
            var mes = ""
            mes = if (!is_round_trip) {
                "#" + id + "\n" + Common.getAirPortCode(origin_code) + "-" + Common.getAirPortCode(destination_code) + "\n" + departure_f!!.departureDateTime + " " + departure_f!!.flightNumber + " " + departure_f!!.seatClass + " " + Common.rutGonTien(
                    departure_f!!.adult ?: 0) + " - " + listPaxString + (if (bag_fee == 0) "" else " Hly " + Common.rutGonTien(
                    bag_fee)) + "\nTC:" + Common.rutGonTien(total + bag_fee - discount)
            } else {
                "#" + id + "\n" + Common.getAirPortCode(origin_code) + "-" + Common.getAirPortCode(destination_code) + "\n" + departure_f!!.departureDateTime + " " + departure_f!!.flightNumber + " " + departure_f!!.seatClass + " " + Common.rutGonTien(
                    departure_f!!.adult ?: 0) + "\n" + Common.getAirPortCode(destination_code) + "-" + Common.getAirPortCode(
                    origin_code) + "\n" + return_f!!.departureDateTime + " " + return_f!!.flightNumber + " " + return_f!!.seatClass + " " + Common.rutGonTien(
                    return_f!!.adult ?: 0) + listPaxString + (if (bag_fee == 0) "" else " Hly " + Common.rutGonTien(bag_fee)) + "\nTC:" + Common.rutGonTien(
                    total + bag_fee - discount)
            }
            return mes
        }

    override fun getGrandTotal(): Int {
        return total
    }


    override fun getTotalAddOn(): Int {
        var total = 0
        for (i in 0 until pax_info.adult.count()) {
            val pax = pax_info.adult.get(i)

            pax.addOn.forEach {
                total += it.price
            }

            pax.addOnReturn.forEach {
                total += it.price
            }
        }
        for (i in 0 until pax_info.child.count()) {
            val pax = pax_info.child.get(i)
            pax.addOn.forEach {
                total += it.price
            }

            pax.addOnReturn.forEach {
                total += it.price
            }
        }
        addon_fee = total
        return total
    }

    override fun getRewardPointTotal(): Int {
        if (departure_f == null) return 0
        else return departure_f!!.rewardPoint ?: 0
    }
}