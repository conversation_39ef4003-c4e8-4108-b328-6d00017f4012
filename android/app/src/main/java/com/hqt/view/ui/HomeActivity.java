package com.hqt.view.ui;

import android.content.DialogInterface;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.view.MenuItem;
import android.view.View;

import androidx.appcompat.app.AlertDialog;
import androidx.core.view.OnApplyWindowInsetsListener;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.viewpager.widget.ViewPager;

import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.firebase.inappmessaging.FirebaseInAppMessaging;
import com.hqt.datvemaybay.Common;
import com.hqt.datvemaybay.R;
import com.hqt.util.AppConfigs;
import com.hqt.view.adapter.ViewPagerAdapter;
import com.hqt.view.ui.priceboard.PricesBoardFragment;

import org.json.JSONArray;

import q.rorbin.badgeview.Badge;
import q.rorbin.badgeview.QBadgeView;

public class HomeActivity extends BaseActivity {
    com.google.android.material.bottomnavigation.BottomNavigationView navigation;
    private ViewPager viewPager;
    public ViewPagerAdapter adapter;
    public Badge wattingBadge, unseenBadge;
    final int REQUEST_AIRPORT = 2;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_home_acvitity;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        navigation = findViewById(R.id.bottomNav);


        viewPager = findViewById(R.id.viewpager);
        setupViewPager(viewPager);
        ViewCompat.setOnApplyWindowInsetsListener(viewPager,
                new OnApplyWindowInsetsListener() {
                    @Override
                    public WindowInsetsCompat onApplyWindowInsets(View v, WindowInsetsCompat insets) {
                        insets = ViewCompat.onApplyWindowInsets(v, insets);
                        if (insets.isConsumed()) {
                            return insets;
                        }

                        boolean consumed = false;
                        for (int i = 0, count = viewPager.getChildCount(); i < count; i++) {
                            ViewCompat.dispatchApplyWindowInsets(viewPager.getChildAt(i), insets);
                            if (insets.isConsumed()) {
                                consumed = true;
                            }
                        }
                        return consumed ? insets.consumeSystemWindowInsets() : insets;
                    }
                });
        navigation.setOnNavigationItemSelectedListener(mOnNavigationItemSelectedListener);

        viewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                switch (position) {
                    case 0:
                        navigation.getMenu().findItem(R.id.navigation_home).setChecked(true);
                        break;
                    case 1:
                        navigation.getMenu().findItem(R.id.navigation_history).setChecked(true);
                        break;
                    case 2:
                        navigation.getMenu().findItem(R.id.navigation_cheap).setChecked(true);
                        break;
                    case 3:
                        navigation.getMenu().findItem(R.id.navigation_help).setChecked(true);
                        break;
                    case 4:
                        navigation.getMenu().findItem(R.id.navigation_account).setChecked(true);
                        break;

                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
        wattingBadge = addBadgeAt(1, 0);
        unseenBadge = addBadgeAt(3, 0);


        getFirebaseAnalytics().setCurrentScreen(this, "home_view_screen", null);
        getFirebaseAnalytics().logEvent("home_screen_view", null);

        FirebaseInAppMessaging.getInstance().triggerEvent("home_screen_view");

        new Handler().postDelayed(
                new Runnable() {
                    @Override
                    public void run() {
                        initPlayUtil();
                    }
                }, 2000);
    }

    private BottomNavigationView.OnNavigationItemSelectedListener mOnNavigationItemSelectedListener = new BottomNavigationView.OnNavigationItemSelectedListener() {

        @Override
        public boolean onNavigationItemSelected(MenuItem item) {
            Fragment fragment;
            int itemId = item.getItemId();
            if (itemId == R.id.navigation_home) {
                viewPager.setCurrentItem(0);
            } else if (itemId == R.id.navigation_history) {
                viewPager.setCurrentItem(1);
            } else if (itemId == R.id.navigation_cheap) {
                viewPager.setCurrentItem(2);
            } else if (itemId == R.id.navigation_help) {
                viewPager.setCurrentItem(3);
            } else if (itemId == R.id.navigation_account) {
                viewPager.setCurrentItem(4);
            }

            return false;
        }
    };

    public void initPlayUtil() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
//                new AppRating.Builder(this)
//                        .setMinimumLaunchTimes(5)
//                        .setMinimumDays(7)
//                        .setMinimumLaunchTimesToShowAgain(5)
//                        .setMinimumDaysToShowAgain(10)
//                        .useGoogleInAppReview()
//                        .showIfMeetsConditions();

//                InAppUpdateManager inAppUpdateManager = InAppUpdateManager.Builder(this, 11)
//                        .resumeUpdates(true)
//                        .mode(Constants.UpdateMode.FLEXIBLE)
//                        .snackBarMessage("Bản cập nhật đã tải xong.")
//                        .snackBarAction("Cập nhật ngay");
//
//                inAppUpdateManager.checkForAppUpdate();
            }
        } catch (Exception e) {
            AppConfigs.logException(e);
        }
    }

    public void clickNavigation(int position) {
        navigation.getMenu().getItem(position).setChecked(true);
        viewPager.setCurrentItem(position);
    }

    private boolean loadFragment(Fragment fragment) {
        if (fragment != null) {
            return true;
        }
        return false;
    }

    @Override
    public void onBackPressed() {
        if (navigation.getMenu().getItem(0).isChecked() == false) {
            clickNavigation(0);
        } else {
            new AlertDialog.Builder(this)
                    .setIcon(R.drawable.log_out)
                    .setTitle("Thoát")
                    .setMessage("Bạn có chắc chắn muốn thoát?")
                    .setPositiveButton("Có", new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            moveTaskToBack(true);
                            finish();
                            System.exit(0);
                        }
                    })
                    .setNegativeButton("Không", null)
                    .show();
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (resultCode == RESULT_OK && requestCode == REQUEST_AIRPORT) {
            try {

                FragmentManager manager = getSupportFragmentManager();
                for (Fragment frag : manager.getFragments()) {
                    if (frag.getClass() == PricesBoardFragment.class) {
                        if (data.hasExtra("SELECTALL")) {
                            ((PricesBoardFragment) frag).setRoute(true);
                        } else {
                            ((PricesBoardFragment) frag).setRoute(false);
                        }

                    }
                }

            } catch (Exception e) {
                AppConfigs.logException(e);
                e.printStackTrace();
            }
        }
        super.onActivityResult(requestCode, resultCode, data);

    }

    @Override
    public void onPause() {
        Common.isAppRunning = false;
        super.onPause();
    }

    private void onNotificationControl(Intent in) {
        if (in == null) {
            in = getIntent();
        }
        AppConfigs.Log("onNotificationControl", "onNotificationControl");

        if (in.hasExtra("PRICEBOARD")) {
            AppConfigs.Log("onNotificationControl", "PRICEBOARD");
            try {
                if (in.hasExtra("destination") && in.hasExtra("origin")) {
                    String origin = in.getStringExtra("origin");
                    String destination = in.getStringExtra("destination");
                    JSONArray routes = new JSONArray();
                    routes.put(origin + destination);
                    routes.put(destination + origin);
                    if (origin.length() == 3 && destination.length() == 3)
                        ((PricesBoardFragment) adapter.getItem(2)).setDefaultRoute(routes);
                }

            } catch (Exception e) {
                AppConfigs.logException(e);
                e.printStackTrace();
            }
            viewPager.setCurrentItem(2);

        }
    }

    @Override
    public void onNewIntent(Intent intent) {
        onNotificationControl(intent);
        super.onNewIntent(intent);
    }

    @Override
    public void onResume() {

        Common.isAppRunning = true;
        onNotificationControl(getIntent());
        try {
            FragmentManager manager = getSupportFragmentManager();
            FragmentTransaction ft = getSupportFragmentManager().beginTransaction();

            for (Fragment frag : manager.getFragments()) {
                if (frag.getClass() == AccountFragment.class || frag.getClass() == HistoryFragment.class || frag.getClass() == NotificationFragment.class) {
                    if (Common.isNewData) {
                        ft.detach(frag).commitNow();
                        ft.attach(frag).commitNow();
                    }
                }
            }
            ft.commit();
            Common.isNewData = false;
        } catch (Exception e) {
            AppConfigs.logException(e);
            e.printStackTrace();
        }
        super.onResume();
    }

    @Override
    public void refreshLayout() {
        try {
            FragmentManager manager = getSupportFragmentManager();
            FragmentTransaction ft = getSupportFragmentManager().beginTransaction();

            for (Fragment frag : manager.getFragments()) {
                if (frag.getClass() == AccountFragment.class || frag.getClass() == HistoryFragment.class || frag.getClass() == NotificationFragment.class) {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                        ft.detach(frag).commitNow();
                        ft.attach(frag).commitNow();
                    } else {
                        ft.detach(frag).commit();
                        ft.attach(frag).commit();
                    }
                }
            }
            ft.commit();
        } catch (Exception e) {
            AppConfigs.logException(e);
            e.printStackTrace();
        }
    }

    @Override
    public void onStart() {
        Common.isAppRunning = true;
        super.onStart();

    }

    private void setupViewPager(ViewPager viewPager) {

        adapter = new ViewPagerAdapter(getSupportFragmentManager());
        adapter.addFragment(new HomeFragment(), "HomeFragment");
        adapter.addFragment(new HistoryFragment(), "HistoryFragment");
        adapter.addFragment(new PricesBoardFragment(), "PricesBoardFragment");
        adapter.addFragment(new NotificationFragment(), "NotificationFragment");
        adapter.addFragment(new AccountFragment(), "AccountFragment");
        viewPager.setAdapter(adapter);
        viewPager.setOffscreenPageLimit(5);

        //navigation.setupWithViewPager(viewPager);

    }

    public Badge addBadgeAt(int position, int number) {
        // add badge
        var id = R.id.navigation_home;
        if (position == 0) {
            id = R.id.navigation_home;
        } else if (position == 1) {
            id = R.id.navigation_history;
        } else if (position == 2) {
            id = R.id.navigation_cheap;
        } else if (position == 3) {
            id = R.id.navigation_help;
        } else if (position == 4) {
            id = R.id.navigation_account;
        }

        return new QBadgeView(this)
                .setGravityOffset(12, 2, true)
                .bindTarget(navigation.findViewById(id))
                .setOnDragStateChangedListener(new Badge.OnDragStateChangedListener() {
                    @Override
                    public void onDragStateChanged(int dragState, Badge badge, View targetView) {
                        if (Badge.OnDragStateChangedListener.STATE_SUCCEED == dragState) {

                        }

                    }
                });
    }


}
