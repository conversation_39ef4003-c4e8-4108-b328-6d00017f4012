package com.hqt.view.ui.meal.data.api

import com.hqt.base.model.HttpData
import com.hqt.data.model.AddOnInfo
import com.hqt.data.model.request.GetAddOnRequest
import com.hqt.data.prefs.SharedPrefsHelper
import javax.inject.Inject
import javax.inject.Singleton


@Singleton
class MealApiHelper @Inject constructor(
    private val api : MealService,
    private val sharedPrefsHelper: SharedPrefsHelper
) {

    suspend fun getMeal(provider : String?, request : GetAddOnRequest?) : HttpData<ArrayList<AddOnInfo>>{
        return api.getMeal(provider ?: "", request)
    }


}