package com.hqt.view.ui.train

import android.annotation.SuppressLint
import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Color
import android.os.Bundle
import android.view.View
import android.view.animation.AnimationSet
import android.view.animation.AnimationUtils
import android.view.animation.DecelerateInterpolator
import android.view.animation.RotateAnimation
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.widget.AppCompatButton
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.localbroadcastmanager.content.LocalBroadcastManager


import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.snackbar.Snackbar
import com.hqt.datvemaybay.AirportSearch
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.PnrActivity
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ActivityTrainSearchBinding
import com.hqt.util.AppConfigs
import com.hqt.util.amlich.AmLich
import com.hqt.view.ui.BaseActivityKt
import com.mikepenz.iconics.context.IconicsContextWrapper
import java.text.SimpleDateFormat
import java.util.*

class TrainSearchActivity : BaseActivityKt<ActivityTrainSearchBinding>() {

    private lateinit var depDate: Calendar
    private lateinit var retDate: Calendar
    override val layoutId: Int = R.layout.activity_train_search
    lateinit var dialog: BottomSheetDialog
    lateinit var coordinatorLayout: CoordinatorLayout
    private lateinit var selectPassengerView: View
    var adultCount = 1
    var childCount = 0
    var studentCount = 0
    var olderCount = 0
    var isRoundTrip = false;
    private val REQUEST_CODE_FROM = 0
    private val REQUEST_CODE_TO = 1
    private val REQUEST_CODE_DEP_DATE = 2
    private val REQUEST_CODE_RE_DATE = 3
    val dF = SimpleDateFormat("dd/MM/yyyy", Locale.US)

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        coordinatorLayout = findViewById(R.id.coordinatorLayout)

        selectPassengerView = layoutInflater.inflate(R.layout.select_passenger_train_layout, null)

        firebaseAnalytics.setCurrentScreen(this, "train_booking_search", null)

        depDate = Calendar.getInstance()
        retDate = Calendar.getInstance()

        depDate.add(Calendar.DAY_OF_MONTH, 3)
        retDate.add(Calendar.DAY_OF_MONTH, 5)

        getToolbar().title = "Đặt vé tàu hỏa"
        getToolbar().setNavigationIcon(R.drawable.ic_action_back_home)
        supportActionBar!!.setDisplayShowHomeEnabled(true)
        initClickAction()
        selectPassger()

    }

    private fun initClickAction() {
        getViewBindding().selectPaxButton.setOnClickListener {
            if (!dialog.isShowing) {
                dialog.show()
            }
        }
        val i = Intent(applicationContext, AirportSearch::class.java)
        i.putExtra("isSearchTrain", true)

        getViewBindding().selectOrigin.setOnClickListener {
            startActivityForResult(i, REQUEST_CODE_FROM)
        }
        getViewBindding().selectDestination.setOnClickListener {
            startActivityForResult(i, REQUEST_CODE_TO)
        }

        setDateToView(depDate.time, false)

        getViewBindding().checkOneWay.setOnClickListener { // TODO Auto-generated method stub

            isRoundTrip = false
            setDateToView(null, true)

            getViewBindding().checkOneWay.background = (resources.getDrawable(R.drawable.button_one_way))
            getViewBindding().checkOneWay.setTextColor(Color.parseColor("#FFFFFF"))

            getViewBindding().checkRoundtrip.setBackgroundColor(resources.getColor(R.color.fui_transparent))
            getViewBindding().checkRoundtrip.setTextColor(Color.parseColor("#00a2e3"))
        }

        getViewBindding().checkRoundtrip.setOnClickListener {
            if (retDate.after(depDate) && retDate.get(Calendar.DATE) != depDate.get(Calendar.DATE)) {
                setDateToView(retDate.time, true)
            } else {
                retDate.time = depDate.time
                retDate.add(Calendar.DATE, 2)
                setDateToView(retDate.time, true)
            }
            isRoundTrip = true

            getViewBindding().checkRoundtrip.background = resources.getDrawable(R.drawable.button_return)
            getViewBindding().checkRoundtrip.setTextColor(Color.parseColor("#FFFFFF"))

            getViewBindding().checkOneWay.setBackgroundColor(resources.getColor(R.color.fui_transparent))
            getViewBindding().checkOneWay.setTextColor(Color.parseColor("#00a2e3"))
        }

        getViewBindding().selectDepDate.setOnClickListener {
            try {
                val i = Intent(applicationContext, AmLich::class.java)
                i.putExtra("isSearchTrain", true)
                i.putExtra("depDate", dF.format(depDate.time))
                i.putExtra("origin", getViewBindding().originCode.text)
                i.putExtra("destination", getViewBindding().destinationCode.text)
                startActivityForResult(i, REQUEST_CODE_DEP_DATE)
                AppConfigs.Log("depDate", dF.format(depDate.time))

            } catch (except: Exception) {

            }
        }

        getViewBindding().selectRetDate.setOnClickListener {
            try {
                getViewBindding().checkRoundtrip.performClick()

                if (retDate.after(depDate)) {

                } else {
                    retDate.time = depDate.time
                    retDate.add(Calendar.DATE, 2)

                }

                val i = Intent(applicationContext, AmLich::class.java)
                i.putExtra("depDate", dF.format(depDate.time))
                i.putExtra("reDate", dF.format(retDate.time))
                i.putExtra("origin", getViewBindding().originCode.text)
                i.putExtra("destination", getViewBindding().destinationCode.text)
                startActivityForResult(i, REQUEST_CODE_RE_DATE)

                setDateToView(retDate.time, true)

            } catch (except: Exception) {

            }
        }

        getViewBindding().swapRouteLayout.setOnClickListener { getViewBindding().swapRoute.performClick() }

        getViewBindding().swapRoute.setOnClickListener {
            val tem = getViewBindding().originCode.text
            val temName = getViewBindding().originName.text

            getViewBindding().originName.text = getViewBindding().destinationName.text
            getViewBindding().originCode.text = getViewBindding().destinationCode.text

            getViewBindding().destinationCode.text = (tem)
            getViewBindding().destinationName.text = (temName)

            val animSet = AnimationSet(true)
            animSet.interpolator = DecelerateInterpolator()
            animSet.fillAfter = true
            animSet.isFillEnabled = true

            val animRotate = RotateAnimation(0.0f,
                360.0f,
                RotateAnimation.RELATIVE_TO_SELF,
                0.5f,
                RotateAnimation.RELATIVE_TO_SELF,
                0.5f)

            animRotate.duration = 200
            animRotate.fillAfter = true
            animSet.addAnimation(animRotate)
            getViewBindding().swapRoute.startAnimation(animSet)
        }

        getViewBindding().search.setOnClickListener {


            if (isInternetConnected) {

                if (isRoundTrip && depDate.timeInMillis > retDate.timeInMillis) {
                    Toast.makeText(this, "Ngày chuyến bay lượt về phải sau ngày lượt đi !", Toast.LENGTH_SHORT).show()
                } else if (getViewBindding().originCode.text.length != 3) {
                    Toast.makeText(this, "Vui lòng chọn điểm đi !", Toast.LENGTH_SHORT).show()

                } else if (getViewBindding().destinationCode.text.length != 3) {
                    Toast.makeText(this, "Vui lòng chọn điểm đến !", Toast.LENGTH_SHORT).show()

                } else if (getViewBindding().originCode.text.trim() == getViewBindding().destinationCode.text.trim()) {
                    Toast.makeText(this, "Vui lòng chọn điểm đến và điểm đi khác nhau !", Toast.LENGTH_SHORT).show()

                } else {

                    val dF = SimpleDateFormat("yyyy-MM-dd")
                    val departureTime = dF.format(depDate.time)
                    val returnTime = dF.format(retDate.time)

                    //NEW LA CHON XEM VE RE
                    var intentSearch = Intent(this, TrainSelectActivity::class.java)

                    intentSearch.putExtra("originCode", getViewBindding().originCode.text.toString())
                    intentSearch.putExtra("destinationCode", getViewBindding().destinationCode.text.toString())
                    intentSearch.putExtra("departureTime", departureTime)
                    intentSearch.putExtra("returnTime", returnTime)
                    intentSearch.putExtra("adult", adultCount)
                    intentSearch.putExtra("child", childCount)
                    intentSearch.putExtra("student", studentCount)
                    intentSearch.putExtra("older", olderCount)
                    intentSearch.putExtra("isRoundTrip", isRoundTrip)

                    startActivity(intentSearch)
                    overridePendingTransition(R.anim.enter, R.anim.exit)


                }
            } else { //Nếu không có kết nối với intenet thì
                Common.showAlertDialog(this,
                    "Không có Internet",
                    "Xin vui lòng kiểm tra lại Wifi/3G để tiếp tục",
                    false,
                    true)
            }
        }

    }

    //    override fun attachBaseContext(newBase: Context) {
    //        super.attachBaseContext(IconicsContextWrapper.wrap(newBase))
    //    }


    private fun selectPassger() {
        try {
            dialog = BottomSheetDialog(this)
            dialog.setContentView(selectPassengerView)

            val txtAdult = selectPassengerView.findViewById<TextView>(R.id.sheet_adult_number)
            val txtChild = selectPassengerView.findViewById<TextView>(R.id.sheet_child_number)
            val txtStudent = selectPassengerView.findViewById<TextView>(R.id.sheet_student_number)
            val txtOlder = selectPassengerView.findViewById<TextView>(R.id.sheet_older_number)

            selectPassengerView.findViewById<LinearLayout>(R.id.select_older).visibility = (View.VISIBLE)
            selectPassengerView.findViewById<LinearLayout>(R.id.select_student).visibility = (View.VISIBLE)
            selectPassengerView.findViewById<LinearLayout>(R.id.select_infant).visibility = (View.GONE)

            val inAdult = selectPassengerView.findViewById<LinearLayout>(R.id.btn_in_adult)
            val deAdult = selectPassengerView.findViewById<LinearLayout>(R.id.btn_de_adult)

            val inChild = selectPassengerView.findViewById<LinearLayout>(R.id.btn_in_child)
            val deChild = selectPassengerView.findViewById<LinearLayout>(R.id.btn_de_child)

            val inStudent = selectPassengerView.findViewById<LinearLayout>(R.id.btn_in_student)
            val deStudent = selectPassengerView.findViewById<LinearLayout>(R.id.btn_de_student)

            val inOlder = selectPassengerView.findViewById<LinearLayout>(R.id.btn_in_older)
            val deOlder = selectPassengerView.findViewById<LinearLayout>(R.id.btn_de_older)

            val choicePassengerBtn = selectPassengerView.findViewById<AppCompatButton>(R.id.select_passenger_button)
            choicePassengerBtn.setOnClickListener { if (dialog.isShowing()) dialog.dismiss() }


            val inAnim = AnimationUtils.loadAnimation(this, android.R.anim.fade_in)
            inAnim.duration = 250


            inAdult.setOnClickListener {

                if (olderCount + studentCount + adultCount + childCount < 4) {
                    adultCount++
                    txtAdult.text = adultCount.toString()
                    getViewBindding().adultCount.text = adultCount.toString()
                    txtAdult.startAnimation(inAnim)

                } else {
                    Toast.makeText(applicationContext, "Tối đa 4 hành khách", Toast.LENGTH_SHORT).show()
                }
            }
            deAdult.setOnClickListener {
                if (adultCount > 0 && olderCount + studentCount + adultCount + childCount <= 4) {
                    adultCount--
                    txtAdult.text = adultCount.toString()
                    getViewBindding().adultCount.text = adultCount.toString()
                    txtAdult.startAnimation(inAnim)

                }
            }
            inChild.setOnClickListener {
                if (olderCount + studentCount + adultCount + childCount < 4) {
                    childCount++
                    txtChild.text = childCount.toString()
                    getViewBindding().childCount.text = childCount.toString()
                    txtChild.startAnimation(inAnim)
                } else {
                    Toast.makeText(applicationContext, "Tối đa 4 hành khách ", Toast.LENGTH_SHORT).show()
                }
            }
            deChild.setOnClickListener {
                if (childCount > 0 && olderCount + studentCount + adultCount + childCount <= 4) {
                    childCount--
                    txtChild.text = childCount.toString()
                    getViewBindding().childCount.text = childCount.toString()
                    txtChild.startAnimation(inAnim)
                }
            }
            inStudent.setOnClickListener {
                if (olderCount + studentCount + adultCount + childCount < 4) {
                    studentCount++
                    getViewBindding().studentCount.text = studentCount.toString()
                    txtStudent.text = (studentCount).toString()
                    txtStudent.startAnimation(inAnim)
                } else {
                    Toast.makeText(applicationContext, "Tối đa 4 hành khách ", Toast.LENGTH_SHORT).show()
                }
            }
            deStudent.setOnClickListener {
                if (studentCount > 0 && olderCount + studentCount + adultCount + childCount <= 4) {
                    studentCount--
                    txtStudent.text = studentCount.toString()
                    getViewBindding().studentCount.text = studentCount.toString()
                    txtStudent.startAnimation(inAnim)
                }
            }
            inOlder.setOnClickListener {
                if (olderCount + studentCount + adultCount + childCount < 4) {
                    olderCount++
                    getViewBindding().olderCount.text = olderCount.toString()
                    txtOlder.text = olderCount.toString()
                    txtOlder.startAnimation(inAnim)
                } else {
                    Toast.makeText(applicationContext, "Tối đa 4 hành khách ", Toast.LENGTH_SHORT).show()
                }
            }
            deOlder.setOnClickListener {
                if (olderCount > 0 && olderCount + studentCount + adultCount + childCount <= 4) {
                    olderCount--
                    txtOlder.text = olderCount.toString()
                    getViewBindding().olderCount.text = olderCount.toString()
                    txtOlder.startAnimation(inAnim)
                }
            }


        } catch (E: Exception) {
            AppConfigs.logException(E)
        }

    }

    @SuppressLint("MissingSuperCall") override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {


        if (resultCode == Activity.RESULT_OK && requestCode == REQUEST_CODE_FROM) {
            if (data!!.hasExtra("code")) {
                getViewBindding().originName.text = (data.extras!!.getString("name"))
                getViewBindding().originCode.text = (data.extras!!.getString("code"))
            }
        } else if (resultCode == Activity.RESULT_OK && requestCode == REQUEST_CODE_TO) {
            if (data!!.hasExtra("code")) {

                getViewBindding().destinationName.text = (data.extras!!.getString("name"))
                getViewBindding().destinationCode.text = (data.extras!!.getString("code"))
            }
        } else if (resultCode == Activity.RESULT_OK && requestCode == REQUEST_CODE_DEP_DATE) {
            if (data!!.hasExtra("date")) { //txtDepDate.setText(data.getExtras().getString("date"));
                depDate.time = Common.getDateFromString(data.extras!!.getString("date")).time
                setDateToView(depDate.time, false)
                if (depDate.after(retDate) && isRoundTrip) {
                    getViewBindding().checkRoundtrip.performClick()
                }
            }
        } else if (resultCode == Activity.RESULT_OK && requestCode == REQUEST_CODE_RE_DATE) {
            if (data!!.hasExtra("date")) {

                retDate.time = Common.getDateFromString(data.extras!!.getString("date")).time
                setDateToView(retDate.time, true)
            }
        }


    }

    private fun setDateToView(date: Date?, isReturn: Boolean?) {
        try {
            if (date == null) {
                getViewBindding().txtRetDate.text = "";
                getViewBindding().txtRetDayOfWeek.text = ""
                getViewBindding().txtRetYear.text = ""
                getViewBindding().txtRetLunarDate.text = ""
                getViewBindding().txtRetCaret.visibility = View.GONE
            } else {

                if (isReturn!!) {
                    getViewBindding().txtRetCaret.visibility = View.VISIBLE
                    getViewBindding().txtRetDate.text = (Common.dateToString(date, "dd/MM"))
                    getViewBindding().txtRetDayOfWeek.text = Common.getDayOfWeek(date)
                    getViewBindding().txtRetYear.text = Common.dateToString(date, "yyyy")
                    getViewBindding().txtRetLunarDate.text = "Âm lịch " + Common.getLunarFromDate(date)
                } else {
                    getViewBindding().txtDepDate.text = (Common.dateToString(date, "dd/MM"))
                    getViewBindding().txtDepDayofweek.text = Common.getDayOfWeek(date)
                    getViewBindding().txtDepYear.text = Common.dateToString(date, "yyyy")
                    getViewBindding().txtDepLunarDate.text = "Âm lịch " + Common.getLunarFromDate(date)
                }

            }
        } catch (e: Exception) {
            AppConfigs.Log("setDepDateString ", e.toString())
        }

    }

    private val mMessageReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) { // Get extra data included in the Intent

            val message = intent.getStringExtra("message").toString()
            val snackbar = Snackbar.make(coordinatorLayout, message, 10000).setAction("XEM CHI TIẾT") {
                val i = Intent(applicationContext, PnrActivity::class.java)
                i.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                startActivity(i)
            }
            snackbar.show()

        }
    }

    override fun onStart() {
        super.onStart()
        LocalBroadcastManager.getInstance(this).registerReceiver(mMessageReceiver, IntentFilter("bookingupdate"))

    }

}

