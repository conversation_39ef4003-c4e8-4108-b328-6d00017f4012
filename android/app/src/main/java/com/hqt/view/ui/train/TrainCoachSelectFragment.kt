package com.hqt.view.ui.train

import android.annotation.SuppressLint
import android.graphics.Color
import android.graphics.Typeface
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelProviders
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.flexbox.FlexboxLayout
import com.google.android.material.behavior.HideBottomViewOnScrollBehavior
import com.hqt.data.model.BookingTrain
import com.hqt.data.model.Train
import com.hqt.data.model.TrainCoach
import com.hqt.data.model.request.TrainsSelectSeatRequest
import com.hqt.data.model.response.TrainSeatDetail
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.FragmentTrainCoachSelectListBinding
import com.hqt.datvemaybay.databinding.TrainSeatTitleView6LayoutBinding
import com.hqt.datvemaybay.databinding.TrainSeatTitleViewLayoutBinding
import com.hqt.datvemaybay.databinding.TrainSeatViewItemBinding
import com.hqt.datvemaybay.databinding.TrainSeatViewLayoutBinding
import com.hqt.util.AppConfigs
import com.hqt.util.AppController
import com.hqt.view.adapter.TrainCoachAdapter
import com.hqt.view.ui.BaseActivityKt
import com.hqt.view.ui.seatmap.SelectSeatActivity
import com.hqt.viewmodel.TrainCoachSelectViewModel
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import java.util.*
import kotlin.collections.ArrayList


class TrainCoachSelectFragment(var isRoundTrip: Boolean) : Fragment() {
    lateinit var recyclerView: RecyclerView
    private lateinit var mAdapter: TrainCoachAdapter
    private var arraylistCoach: ArrayList<TrainCoach> = ArrayList()
    private var arraylistSelectSeat: ArrayList<TrainSeatDetail> = ArrayList()
    private var arraylistSelectSeatView: ArrayList<TrainSeatView> = ArrayList()
    private var listRawCoach: List<TrainCoach> = ArrayList()
    lateinit var toolbar: Toolbar
    lateinit var binding: FragmentTrainCoachSelectListBinding
    private var disposable: Disposable? = null
    lateinit var viewModel: TrainCoachSelectViewModel
    var currentBooking = BookingTrain()

    @SuppressLint("CheckResult") override fun onCreateView(inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_train_coach_select_list, container, false)

        var rootView = binding.root
        toolbar = binding.toolbar
        toolbar.inflateMenu(R.menu.main)

        if (isRoundTrip) toolbar.title = "Chọn ghế lượt về" else toolbar.title = "Chọn ghế lượt đi"

        toolbar.setNavigationIcon(R.drawable.ic_action_back_home)
        toolbar.bringToFront()


        (activity as AppCompatActivity).setSupportActionBar(toolbar)
        (activity as AppCompatActivity).supportActionBar!!.setDisplayShowHomeEnabled(true)

        toolbar.setNavigationOnClickListener {
            (activity as TrainSelectActivity).onBackPressed()
        }
        iniToolbar()
        viewModel = ViewModelProvider(this)[TrainCoachSelectViewModel::class.java]

        binding.lifecycleOwner = this
        binding.viewModel = viewModel


        initCoachSelectListen()
        setSubTitleInfo()
        initAnalytics()



        recyclerView = binding.recyclerView
        recyclerView.setHasFixedSize(true)

        recyclerView.layoutManager = LinearLayoutManager(activity, LinearLayoutManager.HORIZONTAL, false)
        mAdapter = TrainCoachAdapter(arraylistCoach)
        recyclerView.adapter = mAdapter

        mAdapter.itemClickStream.subscribe { // Toast.makeText(requireContext(), it.detail, Toast.LENGTH_SHORT).show()

            binding.coach = it
            genListSeat(it)

        }

        binding.bottomSheet.findViewById<Button>(R.id.btnNext).setOnClickListener {
            (activity as TrainSelectActivity).doneClick(arraylistSelectSeat, isRoundTrip)
        }
        binding.bottomSheet.findViewById<Button>(R.id.btnBack).setOnClickListener {
            (activity as TrainSelectActivity).backClick()
        }



        return rootView
    }

    fun initCoachSelectListen() {

        viewModel.onSort.observe(viewLifecycleOwner, androidx.lifecycle.Observer {

            //sortListTrain(viewModel.sortKey)
        })

    }

    fun iniToolbar() {
        try {
            val view = toolbar.getChildAt(1)
            if (view is TextView) {
                view.textSize = 18f
                view.ellipsize = TextUtils.TruncateAt.MARQUEE;
                view.marqueeRepeatLimit = -1;
                view.isSingleLine = true;
                view.setSelected(true);
            }
            activity?.window?.statusBarColor = Color.TRANSPARENT
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun genListSeat(coach: TrainCoach) {
        disposable = AppController.instance.getService().getSeatByTrain(coach.selectKey).subscribeOn(Schedulers.io())
            .doOnSubscribe {

                binding.shimmerViewContainer.startShimmer()
                binding.shimmerViewContainer.visibility = View.VISIBLE

                //                seatViewList.clear()
                //                arraylistSeatGroup.clear()

                binding.flexBoxContainer.removeAllViews()
                binding.roomBoxContainer.removeAllViews()
                arraylistSelectSeatView.clear()

            }.observeOn(AndroidSchedulers.mainThread()).subscribe({ response ->


                if (response.data != null) {

                    binding.shimmerViewContainer.stopShimmer()
                    binding.shimmerViewContainer.visibility = View.GONE
                    binding.bottomSheet.visibility = View.VISIBLE
                    genSeatLayout(coach, response.data)

                } else {

                    binding.shimmerViewContainer.stopShimmer()
                    binding.shimmerViewContainer.visibility = View.GONE
                }

            }, { throwable ->

                binding.shimmerViewContainer.stopShimmer()
                binding.shimmerViewContainer.visibility = View.GONE

                throwable.printStackTrace()
            })

    }

    fun genSeatLayout(coach: TrainCoach, listSeat: List<TrainSeatDetail>) {
        try {


            var startRow = 1

            listSeat.forEach { seat ->
                arraylistSelectSeat.forEach {
                    if (it.id == seat.id) {
                        seat.status = 6
                    }
                }
            }

            if (listSeat.size in 51..99) { // ghế mềm ghế cứng

                for (i in 1 until (listSeat.size / 4) + 1) {
                    for (j in 0 until 5) {
                        if (j == 2) {
                            val seatView = TrainSeatView(requireContext(), TrainSeatView.TrainSeatStyle.WAY_SPACE)
                            binding.flexBoxContainer.addView(seatView)
                        } else {

                            var seatViewX = TrainSeatView(requireContext(), TrainSeatView.TrainSeatStyle.SEAT)

                            if (startRow > listSeat.size / 2) {
                                seatViewX = TrainSeatView(requireContext(), TrainSeatView.TrainSeatStyle.REVERT_SEAT)
                            }

                            seatViewX.setSeat(listSeat[startRow - 1])
                            arraylistSelectSeatView.add(seatViewX)

                            binding.flexBoxContainer.addView(seatViewX)

                            if (startRow == listSeat.size / 2) {
                                val centerSpace = TrainSeatView(requireContext(),
                                    TrainSeatView.TrainSeatStyle.CENTER_SPACE)
                                binding.flexBoxContainer.addView(centerSpace)
                            }
                            startRow++
                        }
                    }
                }
            }
            if (listSeat.size == 108) { // ghế 108 2 tang
                var index = 1
                for (i in 1 until 29) {
                    for (j in 0 until 5) {
                        if (j == 2) {
                            val seatView = TrainSeatView(requireContext(), TrainSeatView.TrainSeatStyle.WAY_SPACE)
                            binding.flexBoxContainer.addView(seatView)
                        } else {

                            var seatViewX = TrainSeatView(requireContext(), TrainSeatView.TrainSeatStyle.SEAT)

                            if ((startRow <= 28 || startRow in 55..82) && index != 55 && index != 56) {
                                seatViewX = TrainSeatView(requireContext(), TrainSeatView.TrainSeatStyle.SEAT)
                                seatViewX.setSeat(listSeat[startRow - 1])
                                arraylistSelectSeatView.add(seatViewX)
                                binding.flexBoxContainer.addView(seatViewX)

                            }

                            if ((startRow in (29..54) || startRow > 82) && index != 55 && index != 56) {
                                seatViewX = TrainSeatView(requireContext(), TrainSeatView.TrainSeatStyle.REVERT_SEAT)
                                if (startRow < 109) {
                                    seatViewX.setSeat(listSeat[startRow - 1])
                                    arraylistSelectSeatView.add(seatViewX)
                                    binding.flexBoxContainer.addView(seatViewX)
                                }

                            }

                            if (index == 55 || index == 56 || index == 110 || index == 111) {
                                val centerSpace = TrainSeatView(requireContext(),
                                    TrainSeatView.TrainSeatStyle.HIDDEN_SEAT)
                                binding.flexBoxContainer.addView(centerSpace)
                            }

                            if (startRow == 28 || startRow == 82) {
                                val centerSpace = TrainSeatView(requireContext(),
                                    TrainSeatView.TrainSeatStyle.CENTER_SPACE)
                                binding.flexBoxContainer.addView(centerSpace)
                            }
                            if (index == 56) {
                                val centerSpace = TrainSeatView(requireContext(),
                                    TrainSeatView.TrainSeatStyle.CENTER_SPACE)
                                binding.flexBoxContainer.addView(centerSpace)
                            }
                            if (startRow == 82) {
                                val centerSpace = TrainSeatView(requireContext(),
                                    TrainSeatView.TrainSeatStyle.CENTER_SPACE)
                                binding.flexBoxContainer.addView(centerSpace)
                            }

                            if (index != 55 && index != 56) {
                                startRow++

                            }
                            index++
                        }
                    }
                }
            }

            if (coach.layout == 5 || coach.layout == 27) { //Giường nằm khoang 4 điều hòa //
                var roomTitle = 1;

                TrainSeatTitleViewLayoutBinding.inflate(LayoutInflater.from(context), binding.roomBoxContainer, true)

                for (i in 1 until (listSeat.size / 4) + 1) {

                    val roomLayout = TrainSeatViewLayoutBinding.inflate(LayoutInflater.from(context),
                        binding.roomBoxContainer,
                        true)
                    roomLayout.txtTitle.text = "Khoang $roomTitle"

                    // for (j in 0 until 4) { // 4 seat in room

                    if (startRow < listSeat.size + 1) {
                        val seatView1 = TrainSeatView(requireContext(), TrainSeatView.TrainSeatStyle.SEAT_ROOM_4)
                        seatView1.setSeat(listSeat[startRow - 1])
                        arraylistSelectSeatView.add(seatView1)
                        roomLayout.flexBoxContainer.addView(seatView1)

                        if (coach.layout == 27 && (roomTitle == 3 || roomTitle == 4)) {
                            val seatView2 = TrainSeatView(requireContext(), TrainSeatView.TrainSeatStyle.HIDDEN_SEAT)
                            roomLayout.flexBoxContainer.addView(seatView2)
                        } else {
                            val seatView2 = TrainSeatView(requireContext(), TrainSeatView.TrainSeatStyle.SEAT_ROOM_4)
                            seatView2.setSeat(listSeat[startRow - 1 + 2])
                            arraylistSelectSeatView.add(seatView2)

                            roomLayout.flexBoxContainer.addView(seatView2)
                        }

                        if (coach.layout == 27 && (roomTitle == 3 || roomTitle == 4)) {
                            val seatView2 = TrainSeatView(requireContext(), TrainSeatView.TrainSeatStyle.HIDDEN_SEAT)
                            roomLayout.flexBoxContainer.addView(seatView2)
                        } else {
                            val seatView3 = TrainSeatView(requireContext(), TrainSeatView.TrainSeatStyle.SEAT_ROOM_4)
                            seatView3.setSeat(listSeat[startRow - 1 + 1])
                            arraylistSelectSeatView.add(seatView3)

                            roomLayout.flexBoxContainer.addView(seatView3)
                        }


                        val seatView4 = TrainSeatView(requireContext(), TrainSeatView.TrainSeatStyle.SEAT_ROOM_4)
                        seatView4.setSeat(listSeat[startRow - 1 + 3])
                        arraylistSelectSeatView.add(seatView4)

                        roomLayout.flexBoxContainer.addView(seatView4)
                    }


                    if (coach.layout == 27 && (roomTitle == 3 || roomTitle == 4)) {
                        startRow += 2
                    } else {
                        startRow += 4
                    }


                    //}
                    roomTitle++
                }
            }
            if (coach.layout == 7) { //Giường nằm khoang 6 điều hòa
                var roomTitle = 1;

                TrainSeatTitleView6LayoutBinding.inflate(LayoutInflater.from(context), binding.roomBoxContainer, true)

                for (i in 1 until (listSeat.size / 6) + 1) {

                    val roomLayout = TrainSeatViewLayoutBinding.inflate(LayoutInflater.from(context),
                        binding.roomBoxContainer,
                        true)
                    roomLayout.txtTitle.text = "Khoang $roomTitle"
                    roomTitle++

                    //  for (j in 0 until 6) { // 4 seat in room
                    if (startRow < listSeat.size + 1) {
                        val seatView1 = TrainSeatView(requireContext(), TrainSeatView.TrainSeatStyle.SEAT_ROOM_6)
                        seatView1.setSeat(listSeat[startRow - 1])
                        arraylistSelectSeatView.add(seatView1)
                        roomLayout.flexBoxContainer.addView(seatView1)

                        val seatView3 = TrainSeatView(requireContext(), TrainSeatView.TrainSeatStyle.SEAT_ROOM_6)
                        seatView3.setSeat(listSeat[startRow - 1 + 2])
                        arraylistSelectSeatView.add(seatView3)

                        roomLayout.flexBoxContainer.addView(seatView3)

                        val seatView5 = TrainSeatView(requireContext(), TrainSeatView.TrainSeatStyle.SEAT_ROOM_6)
                        seatView5.setSeat(listSeat[startRow - 1 + 4])
                        arraylistSelectSeatView.add(seatView5)

                        roomLayout.flexBoxContainer.addView(seatView5)

                        val seatView2 = TrainSeatView(requireContext(), TrainSeatView.TrainSeatStyle.SEAT_ROOM_6)
                        seatView2.setSeat(listSeat[startRow - 1 + 1])
                        arraylistSelectSeatView.add(seatView2)
                        roomLayout.flexBoxContainer.addView(seatView2)

                        val seatView4 = TrainSeatView(requireContext(), TrainSeatView.TrainSeatStyle.SEAT_ROOM_6)
                        seatView4.setSeat(listSeat[startRow - 1 + 3])
                        arraylistSelectSeatView.add(seatView4)
                        roomLayout.flexBoxContainer.addView(seatView4)

                        val seatView6 = TrainSeatView(requireContext(), TrainSeatView.TrainSeatStyle.SEAT_ROOM_6)
                        seatView6.setSeat(listSeat[startRow - 1 + 5])
                        arraylistSelectSeatView.add(seatView6)
                        roomLayout.flexBoxContainer.addView(seatView6)


                    }
                    startRow += 6 // }
                }
            }
        } catch (e: Exception) {
            AppConfigs.logException(e)
        }
    }

    fun genListCoach(trains: Train) {
        listRawCoach = trains.coachs

        arraylistCoach.clear()
        arraylistCoach.addAll(listRawCoach) //.filter { train -> train.getRemainSeat() > 0 }
        if (arraylistCoach.isEmpty()) {
            arraylistCoach.addAll(listRawCoach)
        }

        mAdapter.notifyDataSetChanged() //        binding.filter.btnOne.performClick()
        binding.shimmerViewContainer.stopShimmer()
        binding.shimmerViewContainer.visibility = View.GONE //       binding.filter.root.visibility = View.VISIBLE
        setSubTitleInfo()
        binding.coach = listRawCoach[0]
        genListSeat(listRawCoach[0])

    }

    private fun setSubTitleInfo() {
        currentBooking = (activity as TrainSelectActivity).booking

        var trainNumber = currentBooking.departure_f.trainNumber + " " + (activity as TrainSelectActivity).booking.origin_code + "-" + (activity as TrainSelectActivity).booking.destination_code
        if (isRoundTrip) {
            trainNumber = currentBooking.return_f.trainNumber + " " + (activity as TrainSelectActivity).booking.destination_code + "-" + (activity as TrainSelectActivity).booking.origin_code
        }

        var subtitle = trainNumber + " " + currentBooking.departure_date + " - "

        if ((activity as TrainSelectActivity).booking.adult > 0) {
            subtitle = subtitle + " " + currentBooking.adult + getString(R.string.fa_male)
        }
        if ((activity as TrainSelectActivity).booking.child > 0) {
            subtitle = subtitle + " " + currentBooking.child + getString(R.string.fa_child)
        }
        if ((activity as TrainSelectActivity).booking.student > 0) {
            subtitle = subtitle + " " + currentBooking.student + getString(R.string.fa_user_graduate)
        }
        if ((activity as TrainSelectActivity).booking.older > 0) {
            subtitle = subtitle + " " + currentBooking.older + getString(R.string.fa_blind)
        }
        toolbar.subtitle = subtitle
        val view = toolbar.getChildAt(3)
        if (view is TextView) {
            view.textSize = 16f
            view.typeface = Typeface.createFromAsset((activity as TrainSelectActivity).assets,
                "fonts/fontawesome-webfont.ttf")
        }
    }

    private fun initAnalytics() {

        (activity as TrainSelectActivity).firebaseAnalytics.setCurrentScreen((activity as TrainSelectActivity),
            "train_select_train",
            null)
    }

    fun selectSeat(seatDetail: TrainSeatDetail, selected: Boolean): Boolean {
        if (arraylistSelectSeat.size < currentBooking.getTotalPax() || !selected) {
            if (seatDetail.status == 3 || seatDetail.status == 6) {
                sendSelectSeat(seatDetail, selected)
                return true
            }
        } else {
            Toast.makeText(requireContext(), "Bạn đã chọn quá số lượng ghế cho phép", Toast.LENGTH_SHORT).show()
        }

        return false // Toast.makeText(requireContext(), arraylistSelectSeat.size.toString(), Toast.LENGTH_SHORT).show()


    }

    fun sendSelectSeat(seatDetail: TrainSeatDetail, selected: Boolean) {

        val request = TrainsSelectSeatRequest()
        request.select = selected
        request.selectKey = seatDetail.selectKey!!
        request.isReturn = false
        request.bookingCode = "xxxxx"


        disposable = AppController.instance.getService().postSelectSeatMap(request).subscribeOn(Schedulers.io())
            .doOnSubscribe {
                (activity as TrainSelectActivity).showProgressDialog("Đang chọn ghế...", false, false)
            }.observeOn(AndroidSchedulers.mainThread()).subscribe({ response ->


                if (response.data != null) {
                    Toast.makeText(requireContext(), response.data.message, Toast.LENGTH_SHORT).show()

                    if (response.data.status == 1 && selected) {
                        arraylistSelectSeat.add(seatDetail)
                        arraylistSelectSeatView.forEach { seatView ->
                            if (seatView.seatDetail!!.id == seatDetail.id) {
                                seatDetail.status = 6
                                seatView.setSeat(seatDetail)
                            }
                        }
                    } else if (response.data.status == 1 && !selected) {
                        arraylistSelectSeat.removeIf { it.id == seatDetail.id }
                        arraylistSelectSeatView.forEach { seatView ->
                            if (seatView.seatDetail!!.id == seatDetail.id) {
                                seatDetail.status = 3
                                seatView.setSeat(seatDetail)
                            }
                        }

                    } else {
                        arraylistSelectSeat.removeIf { it.id == seatDetail.id }
                        arraylistSelectSeatView.forEach { seatView ->
                            if (seatView.seatDetail!!.id == seatDetail.id) {
                                seatDetail.status = 4
                                seatView.setSeat(seatDetail)
                            }
                        }
                    }
                }
                updateSeatSelected()

                (activity as TrainSelectActivity).dismissProgressDialog()

            }, { throwable ->

                throwable.printStackTrace()
                (activity as TrainSelectActivity).dismissProgressDialog()
            })
    }

    private fun updateSeatSelected() {
        try {
            var totalPrice = 0
            var seatSummaryText = ""

            arraylistSelectSeat.forEach { seat ->
                totalPrice += seat.totalPrice!!
                seatSummaryText += "ghế " + seat.seatNumber + " toa " + seat.coachNum + ", "
            }
            seatSummaryText = seatSummaryText.removeSuffix(", ")


            if (seatSummaryText != "") {
                binding.bottomSheet.findViewById<TextView>(R.id.txtGrandTotalPrice).text = Common.dinhDangTien(
                    totalPrice)
                binding.bottomSheet.findViewById<TextView>(R.id.txt_seat_select).text = seatSummaryText
            } else {
                binding.bottomSheet.findViewById<TextView>(R.id.txtGrandTotalPrice).text = Common.dinhDangTien(0)
                binding.bottomSheet.findViewById<TextView>(R.id.txt_seat_select).text = "Vui lòng chọn"

            }


            val params = binding.bottomLayout.layoutParams as CoordinatorLayout.LayoutParams
            (params.behavior as HideBottomViewOnScrollBehavior).slideUp(binding.bottomLayout)


        } catch (e: Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }

    }
}
