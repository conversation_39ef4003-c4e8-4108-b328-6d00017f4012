package com.hqt.view.ui.meal.ui

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.hqt.base.model.State
import com.hqt.data.model.AddOnInfo
import com.hqt.data.model.request.GetAddOnRequest
import com.hqt.util.Log
import com.hqt.view.ui.meal.data.api.MealApiHelper
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import javax.inject.Inject


@HiltViewModel
class MealViewModel @Inject constructor(
    private val seatApiHelper: MealApiHelper
) : ViewModel() {




    var flightK = ""

    private val _mealListLiveData: MutableLiveData<State<ArrayList<AddOnInfo>>> = MutableLiveData()
    val mealListLiveData: LiveData<State<ArrayList<AddOnInfo>>> get() = _mealListLiveData


    fun getMeal(provider: String?) {
        viewModelScope.launch(Dispatchers.IO) {

            try {

                _mealListLiveData.postValue(State.Loading)
                val request = GetAddOnRequest(flightKey = flightK)
                val result = seatApiHelper.getMeal(provider, request)

                if (result.status){
                    _mealListLiveData.postValue(State.Success(result.data ?: arrayListOf()))
                }else{
                    _mealListLiveData.postValue(State.Error(Throwable(result.message)))
                }

            }catch (ex : Exception){
                _mealListLiveData.postValue(State.Error(ex))
                Log.logException(ex)
            }



        }


    }




}