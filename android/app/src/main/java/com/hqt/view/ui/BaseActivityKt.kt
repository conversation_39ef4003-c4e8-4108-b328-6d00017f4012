package com.hqt.view.ui

import android.app.Activity
import android.app.ProgressDialog
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Color
import android.net.ConnectivityManager
import android.os.*
import android.text.TextUtils
import android.view.*
import android.view.inputmethod.InputMethodManager
import android.widget.*
import androidx.annotation.RawRes
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.content.res.AppCompatResources
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.airbnb.lottie.LottieAnimationView
import com.bumptech.glide.Glide
import com.firebase.ui.auth.AuthUI
import com.firebase.ui.auth.ErrorCodes
import com.firebase.ui.auth.FirebaseAuthUIActivityResultContract
import com.firebase.ui.auth.data.model.FirebaseAuthUIAuthenticationResult
import com.google.android.material.snackbar.Snackbar
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import com.hqt.data.api.SSLAppService
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.ThanhToan
import com.hqt.util.AppConfigs
import com.hqt.util.AppController
import com.hqt.util.AppController.Companion.instance
import com.hqt.util.Log
import com.hqt.util.TypefaceUtil
import com.hqt.view.ui.account.LoginActivity
import com.hqt.view.ui.search.ui.activity.SearchActivityV2
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.*

/**
 *
 *
 * BaseActivity contains some modifications to the native AppCompatActivity.
 * Mainly, it use ButterKnife for view binding and it automatically check if
 * toolbar exists.
 *
 */
abstract class BaseActivityKt<B : ViewDataBinding> : AppCompatActivity() {


    private lateinit var toolbar: Toolbar
    lateinit var firebaseAnalytics: FirebaseAnalytics
    var isInternetConnected = true
    private var snackbar: Snackbar? = null
    private var mProgressDialog: ProgressDialog? = null
    private var auth: FirebaseAuth? = null
    private var mAuthListener: FirebaseAuth.AuthStateListener? = null
    private lateinit var bindding: B
    private val signInLauncher = registerForActivityResult(
        FirebaseAuthUIActivityResultContract(),
    ) { res ->
        this.onSignInResult(res)
    }


    val sslService: SSLAppService
        get() = AppController.instance.getService()
    private var disposable: Disposable? = null

    /**
     * Check Firebase auth user [ ][Boolean.getFirebaseAuth]
     */
    val isUserSigned: Boolean
        get() = if (auth!!.currentUser == null) false else true

    /**
     * Get Firebase auth user [ ][FirebaseUser.getFirebaseAuth]
     */
    val firebaseUser: FirebaseUser?
        get() = auth!!.currentUser

    /**
     * @return The layout id that's gonna be the activity view.
     */
    protected abstract val layoutId: Int

    /**
     * Runtime Broadcast receiver inner class to capture internet connectivity events
     */
    private var broadcastReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val status = getConnectivityStatusString(context)
            setSnackbarMessage(status, false)

        }
    }
    private val mMessageReceiver = object : BroadcastReceiver() {
        override fun onReceive(
            context: Context,
            intent: Intent
        ) { // Get extra data included in the Intent
            setmMessageReceiverAction()
            onSystemSettingChange(AppConfigs.SystemSettingType.BOOKING)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        try {

            super.onCreate(savedInstanceState)
            Log.d("className", this.javaClass.simpleName)
            TypefaceUtil.overrideFont(applicationContext, "SERIF", "fonts/Roboto-Regular.ttf")
            bindding = DataBindingUtil.setContentView(this, layoutId)

            // transparentStatus()
            setupToolbar()
            checkInternetConnection(this)
            firebaseAuthInit()
        } catch (e: java.lang.Exception) {
            AppConfigs.logException(e)
            finish()
        }
    }

    fun getUpTrackingClick() {
        try {
            if (intent.hasExtra("track")) {
                val trackingCode = intent.getStringExtra("track")!!
                val formatter = DateTimeFormatter.ofPattern("yyyyMMdd")
                val current = LocalDateTime.now().format(formatter)

                AppConfigs.setStringLocalCache(this, "track_code_$current", trackingCode)
                instance.trackingCode = trackingCode
            }
        } catch (e: Exception) {
            AppConfigs.logException(e)
        }
    }

    fun getToolbar(): Toolbar {

        return toolbar
    }

    fun iniToolbar() {
        try {
            val view = getToolbar().getChildAt(0)
            if (view is TextView) {
                view.textSize = 18f
                view.ellipsize = TextUtils.TruncateAt.MARQUEE;
                view.marqueeRepeatLimit = -1;
                view.isSingleLine = true;
                view.setSelected(true);
            }
            window.statusBarColor = Color.TRANSPARENT
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


    fun getViewBindding(): B {
        return bindding;
    }

    private fun firebaseAuthInit() {
        try {
            firebaseAnalytics = FirebaseAnalytics.getInstance(this)
            auth = FirebaseAuth.getInstance()
            auth!!.firebaseAuthSettings.setAppVerificationDisabledForTesting(true)


            mAuthListener = FirebaseAuth.AuthStateListener { firebaseAuth ->
                val user = firebaseAuth.currentUser
                if (user != null) {
                    Common.commonSave(
                        applicationContext,
                        user.displayName,
                        user.email,
                        user.uid,
                        if (user.photoUrl == null) "" else user.photoUrl!!.toString(),
                        ""
                    )
                    firebaseAnalytics.setUserId(user.uid)
                } else { // User is signed out
                    AppConfigs.Log("onAuthStateChanged", "onAuthStateChanged:signed_out")
                }
            }
        } catch (e: Exception) {
            AppConfigs.logException(e)
        }

    }

    /**
     * Its common use a toolbar within activity, if it exists in the
     * layout this will be configured
     */
    open fun setupToolbar() {
        try {
            toolbar = findViewById(R.id.toolbar)
            toolbar.title = "12BAY.VN"
            setSupportActionBar(toolbar)
            supportActionBar!!.setDisplayShowHomeEnabled(true)
            toolbar.bringToFront()
            toolbar.inflateMenu(R.menu.main)
            toolbar.setNavigationOnClickListener {
                if (isTaskRoot) {
                    val intent = Intent(applicationContext, HomeActivity::class.java)
                    startActivity(intent)
                    finish()
                } else {
                    onBackPressed()
                }
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                window.setFlags(
                    WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS,
                    WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS
                )

            }
            iniToolbar()
        } catch (e: Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }

    }

    fun initEmptyState(
        textString: String,
        background: Int,
        @RawRes animationResId: Int,
        callBackInterface: EmptyStateCallBackInterface
    ): LinearLayout {
        val emptyStateLayout = findViewById<LinearLayout>(R.id.emptyStateLayout)
        try {
            val animationView = findViewById<LottieAnimationView>(R.id.animation_view)
            val emptyStateBackground = findViewById<ImageView>(R.id.emptyStateBackground)
            if (animationResId != -1) {

                animationView.setAnimation(animationResId)
                animationView.loop(true)
                animationView.playAnimation()
                animationView.visibility = View.VISIBLE
                emptyStateBackground.visibility = View.GONE
            } else {
                Glide.with(this).load(background).into(emptyStateBackground)
                emptyStateBackground.visibility = View.VISIBLE
                animationView.visibility = View.GONE
            }


            emptyStateLayout.visibility = View.GONE
            val emptyStateTitle = findViewById<TextView>(R.id.emptyStateTitle)
            emptyStateTitle.text = Common.convertHTML(textString)


            val btnNegative = findViewById<Button>(R.id.btnNegative)
            val btnPositive = findViewById<Button>(R.id.btnPositive)

            callBackInterface.negativeButton(btnNegative)
            callBackInterface.positiveButton(btnPositive)
        } catch (e: Exception) {
            AppConfigs.logException(e)
        }

        return emptyStateLayout

    }

    fun transparentStatus() { //make full transparent statusBar
        window.statusBarColor = (Color.TRANSPARENT)

        if (Build.VERSION.SDK_INT in 19..20) {
            setWindowFlag(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS, true)
        }

        setWindowFlag(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS, false)
        window.statusBarColor = Color.TRANSPARENT
    }

    fun setWindowFlag(bits: Int, on: Boolean) {
        val win = window
        val winParams = win.attributes
        if (on) {
            winParams.flags = winParams.flags or bits
        } else {
            winParams.flags = winParams.flags and bits.inv()
        }
        win.attributes = winParams
    }

    /**
     * Every object annotated with [butterknife.Bind] its gonna injected trough butterknife
     */
    private fun bindViews() {
    }

    override fun onResume() {
        super.onResume()
        registerInternetCheckReceiver()
    }

    override fun onPause() {
        super.onPause()
        unregisterReceiver(broadcastReceiver)
    }

    /**
     * Method to register runtime broadcast receiver to show snackbar alert for internet connection..
     */
    private fun registerInternetCheckReceiver() {
        val internetFilter = IntentFilter()
        internetFilter.addAction("android.net.wifi.STATE_CHANGE")
        internetFilter.addAction("android.net.conn.CONNECTIVITY_CHANGE")
        registerReceiver(broadcastReceiver, internetFilter)
    }

    private fun checkInternetConnection(context: Context) {
        val status = getConnectivityStatusString(context)
        setSnackbarMessage(status, false)
    }

    fun showSnackbarMessage(text: String, bgColor: Int, duration: Int, align: Int) {
        if (findViewById<View>(R.id.viewSnack) != null) {
            snackbar = Snackbar.make(findViewById(R.id.viewSnack), text, Snackbar.LENGTH_LONG)
        } else {
            snackbar = Snackbar.make(findViewById(android.R.id.content), text, Snackbar.LENGTH_LONG)
        }
        snackbar!!.setActionTextColor(Color.WHITE)
        val sbView = snackbar!!.view
        val textView = sbView.findViewById<TextView>(com.google.android.material.R.id.snackbar_text)
        textView.setTextColor(Color.WHITE)
        textView.textAlignment = align
        textView.gravity = Gravity.CENTER_HORIZONTAL
        val snackBarView = snackbar!!.view as FrameLayout
        val params = snackBarView.getChildAt(0).layoutParams as FrameLayout.LayoutParams
        params.setMargins(params.leftMargin, -24, params.rightMargin, -24)
        snackBarView.getChildAt(0).layoutParams = params
        sbView.background = ContextCompat.getDrawable(this, bgColor)
        snackbar!!.duration = duration
        snackbar!!.show()

    }

    fun showSnackBarMessage(string: String, color: Int) {
        try {
            if (findViewById<View>(R.id.viewSnack) != null) {
                snackbar = Snackbar.make(findViewById(R.id.viewSnack), string, Snackbar.LENGTH_LONG)
            } else {
                snackbar =
                    Snackbar.make(findViewById(android.R.id.content), string, Snackbar.LENGTH_LONG)
            }
            snackbar!!.setActionTextColor(Color.WHITE)
            val snackBarView = snackbar!!.view as FrameLayout
            val params = snackBarView.getChildAt(0).layoutParams as FrameLayout.LayoutParams

            params.setMargins(params.leftMargin, -24, params.rightMargin, -24)
            val sbTextView =
                snackbar!!.view.findViewById<View>(com.google.android.material.R.id.snackbar_text) as TextView

            sbTextView.textAlignment = View.TEXT_ALIGNMENT_CENTER

            snackBarView.getChildAt(0).layoutParams = params
            snackbar!!.duration = 1000
            snackbar!!.show()


            snackbar!!.view.background = AppCompatResources.getDrawable(this, color)


        } catch (e: Exception) {
            Toast.makeText(this, string, Toast.LENGTH_SHORT).show()
        }
    }

    fun setSnackbarMessage(status: String?, showBar: Boolean) {
        var internetStatus = ""
        if (status!!.equals("Wifi enabled", ignoreCase = true) || status.equals(
                "Mobile data enabled",
                ignoreCase = true
            )
        ) {
            internetStatus = "Đã kết nối internet"
        } else {
            internetStatus = "Mất kết nối internet"
        } //android.R.id.content
        if (findViewById<View>(R.id.viewSnack) != null) {
            snackbar =
                Snackbar.make(findViewById(R.id.viewSnack), internetStatus, Snackbar.LENGTH_LONG)
        } else {
            snackbar = Snackbar.make(
                findViewById(android.R.id.content),
                internetStatus,
                Snackbar.LENGTH_LONG
            )
        }

        snackbar!!.setActionTextColor(Color.WHITE)
        val sbView = snackbar!!.view
        val textView = sbView.findViewById<TextView>(com.google.android.material.R.id.snackbar_text)
        textView.setTextColor(Color.WHITE)
        textView.textAlignment = View.TEXT_ALIGNMENT_CENTER
        textView.gravity = Gravity.CENTER_HORIZONTAL

        val snackBarView = snackbar!!.view as FrameLayout
        val params = snackBarView.getChildAt(0).layoutParams as FrameLayout.LayoutParams

        params.setMargins(params.leftMargin, -24, params.rightMargin, -24)
        snackBarView.getChildAt(0).layoutParams = params

        if (internetStatus.equals("Mất kết nối internet", ignoreCase = true)) {
            if (isInternetConnected) {
                snackbar!!.duration = 10000
                snackbar!!.show()
                isInternetConnected = false
                onSystemSettingChange(AppConfigs.SystemSettingType.INTERNET)

            }
        } else {
            if (!isInternetConnected) {
                if (snackbar!!.isShown) snackbar!!.dismiss()
                sbView.background = AppCompatResources.getDrawable(this, R.color.stt_green)
                isInternetConnected = true
                onSystemSettingChange(AppConfigs.SystemSettingType.INTERNET)
                snackbar!!.duration = 2000
                snackbar!!.show()
                Handler(Looper.getMainLooper()).postDelayed({ //RELOAD ACTIVITY WHEN INTERNET BACK
                    refreshLayout() // recreate();
                }, 1100)

            }
        }

    }


    fun showProgressDialog(
        cancelable: Boolean,
        canceledOnTouchOutside: Boolean,
        title: String,
        isShow: Boolean
    ) {
        if (mProgressDialog != null) {
            mProgressDialog!!.setCanceledOnTouchOutside(canceledOnTouchOutside)
            mProgressDialog!!.setCancelable(cancelable)


        } else {

            showProgressDialog(title, false, false)
        }
    }

    fun showProgressDialog(text: String, cancelable: Boolean, canceledOnTouchOutside: Boolean) {
        if (isFinishing) {
            return
        }
        if (mProgressDialog == null) {
            mProgressDialog = ProgressDialog(this)
            mProgressDialog!!.setMessage(text)
            mProgressDialog!!.isIndeterminate = false
            mProgressDialog!!.max = 100
            mProgressDialog!!.setCanceledOnTouchOutside(canceledOnTouchOutside)
            mProgressDialog!!.setCancelable(cancelable)
            mProgressDialog!!.setProgressStyle(ProgressDialog.STYLE_SPINNER)
            mProgressDialog!!.show()
        } else {
            mProgressDialog!!.setMessage(text)
            mProgressDialog!!.show()
        }
    }

    fun dismissProgressDialog() {
        if (mProgressDialog != null && mProgressDialog!!.isShowing) {
            mProgressDialog!!.dismiss()
        }
    }

    /**
     * Open Login auth user [  ][.signIn]
     */
    fun signIn() {

        if (auth!!.currentUser == null) {
            val login = Intent(this, LoginActivity::class.java)
            login.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            startActivity(login)
        } else {
            Toast.makeText(this, "Bạn đã đăng nhập rồi", Toast.LENGTH_SHORT).show()
        }
    }

    fun signOut() {
        if (auth!!.currentUser != null) {
            AuthUI.getInstance().signOut(this).addOnCompleteListener {
                Toast.makeText(applicationContext, "Đăng xuất thành công", Toast.LENGTH_SHORT)
                    .show()
                refreshLayout()
            }
            FirebaseAuth.getInstance().signOut()
        }
    }

    open fun refreshLayout() {}
    public override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        try {

        } catch (e: java.lang.Exception) {
            AppConfigs.logException(e)
        }

    }

    private fun onSignInResult(result: FirebaseAuthUIAuthenticationResult) {
        val response = result.idpResponse

        try {
            if (result.resultCode == Activity.RESULT_OK) {
                val user = FirebaseAuth.getInstance().currentUser

                Toast.makeText(
                    applicationContext,
                    "Chào  " + user!!.displayName!!,
                    Toast.LENGTH_SHORT
                ).show()
                getUserProfile(user.uid)
                onSystemSettingChange(AppConfigs.SystemSettingType.USER)
            } else {
                if (response!!.error!!.errorCode == ErrorCodes.ANONYMOUS_UPGRADE_MERGE_CONFLICT) {
                    Toast.makeText(
                        applicationContext,
                        "Tài khoản đã tạo. Vui lòng chọn phương thức khác để đăng nhập lại",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }


        } catch (e: java.lang.Exception) {
            AppConfigs.logException(e)
        }
    }

    /**
     * @return The layout id that's gonna be the activity view.
     */

    private fun getUserProfile(uid: String) {
        disposable = sslService.getUser(uid).subscribeOn(Schedulers.io()).doOnSubscribe {

        }.observeOn(AndroidSchedulers.mainThread()).subscribe({ response ->
            AppController.instance.user = response.data
            refreshLayout()
        }, { throwable ->
            AppConfigs.logException(throwable)
        })


    }

    public override fun onStart() {
        super.onStart()
        LocalBroadcastManager.getInstance(this)
            .registerReceiver(mMessageReceiver, IntentFilter("bookingupdate"))

    }

    public override fun onStop() {
        super.onStop()
        LocalBroadcastManager.getInstance(this).unregisterReceiver(mMessageReceiver)

    }

    open fun setmMessageReceiverAction() {

    }

    open fun onSystemSettingChange(type: AppConfigs.SystemSettingType) {

    }

    interface EmptyStateCallBackInterface {
        fun negativeButton(button: Button)
        fun positiveButton(button: Button)
    }

    fun hideKeyBoard(view: View) {
        val imm = getSystemService(Activity.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(view.windowToken, 0)

    }

    fun shakeItBaby() {
        val mVibratePattern = longArrayOf(0, 30, 100, 20, 100, 10, 100, 5)
        if (Build.VERSION.SDK_INT >= 26) {
            (getSystemService(Context.VIBRATOR_SERVICE) as Vibrator).vibrate(
                VibrationEffect.createWaveform(
                    mVibratePattern,
                    VibrationEffect.DEFAULT_AMPLITUDE
                )
            )

        } else {
            (getSystemService(Context.VIBRATOR_SERVICE) as Vibrator).vibrate(mVibratePattern, -1)
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        val inflater = menuInflater
        inflater.inflate(R.menu.main, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean { // Take appropriate action for each action item click
        when (item.itemId) {
            android.R.id.home -> return true
            R.id.action_search -> {
//                val intent = Intent(this, SearchActivity::class.java)
                val intent = Intent(this, SearchActivityV2::class.java)
                startActivity(intent)
                return true
            }

            R.id.thanhToan -> {
                val in2 = Intent(this, ThanhToan::class.java)
                startActivity(in2)
                return true
            }

            else -> {
            }
        }
        return false
    }

    companion object {
        private var TYPE_WIFI = 1
        private var TYPE_MOBILE = 2
        private var TYPE_NOT_CONNECTED = 0
        private const val RC_SIGN_IN_UI = 123
        const val LOCATION_PERMISSION_REQUEST_CODE = 1

        private fun getConnectivityStatus(context: Context): Int {
            val cm = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val activeNetwork = cm.activeNetworkInfo
            if (null != activeNetwork) {
                if (activeNetwork.type == ConnectivityManager.TYPE_WIFI) return TYPE_WIFI
                if (activeNetwork.type == ConnectivityManager.TYPE_ETHERNET) return TYPE_WIFI
                if (activeNetwork.type == ConnectivityManager.TYPE_MOBILE) return TYPE_MOBILE
            }
            return TYPE_NOT_CONNECTED
        }

        fun getConnectivityStatusString(context: Context): String? {
            val conn = getConnectivityStatus(context)
            var status: String? = null
            if (conn == TYPE_WIFI) {
                status = "Wifi enabled"
            } else if (conn == TYPE_MOBILE) {
                status = "Mobile data enabled"
            } else if (conn == TYPE_NOT_CONNECTED) {
                status = "Not connected to Internet"
            }
            return status
        }
    }
}