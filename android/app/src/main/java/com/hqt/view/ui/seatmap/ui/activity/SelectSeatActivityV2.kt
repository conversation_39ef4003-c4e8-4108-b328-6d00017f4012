package com.hqt.view.ui.seatmap.ui.activity


import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.Menu
import android.view.MenuItem
import android.view.View
import androidx.activity.viewModels
import androidx.fragment.app.Fragment
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.gson.Gson
import com.hqt.base.BaseActivity
import com.hqt.base.PagerAdapter
import com.hqt.data.model.AddOnType
import com.hqt.data.model.BookingV2
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ActivitySelectSeatLayoutV2Binding
import com.hqt.util.Log
import com.hqt.view.ui.booking.data.model.BaseBooking
import com.hqt.view.ui.booking.data.model.BookingV3
import com.hqt.view.ui.reward.ui.activity.RewardActivityV2
import com.hqt.view.ui.seatmap.ui.SeatManagerViewModel
import com.hqt.view.ui.seatmap.ui.fragment.SeatSelectFragmentV2
import dagger.hilt.android.AndroidEntryPoint
import q.rorbin.badgeview.QBadgeView


@AndroidEntryPoint
class SelectSeatActivityV2 : BaseActivity<ActivitySelectSeatLayoutV2Binding>() {

    private val viewModel : SeatManagerViewModel by viewModels()

    override fun getLayoutRes(): Int {
        return R.layout.activity_select_seat_layout_v2
    }
//    lateinit var viewModel: BookingViewModel

//    private val viewModel : SeatViewModel by viewModels()


    companion object{



        var doneClick : () -> Unit = {}
        


    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        getToolbar()?.title = "Chọn ghế ngồi"

        viewModel.booking = Gson().fromJson(intent.getStringExtra("bookingDetail"), BookingV3::class.java)

        if (viewModel.booking.type == BaseBooking.BookingType.FLIGHT) {
            viewModel.totalPax = viewModel.booking.adult + viewModel.booking.child
        }
        binding.lifecycleOwner = this

        initTab()
        observe()
        getPaxSeat()

//        setWindowFlag(this, WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS, false)
//        window.statusBarColor = ContextCompat.getColor(this, R.color.primary_dark)




    }


    private fun initTab(){

        var fragments: List<Fragment> = listOf(
            SeatSelectFragmentV2.newInstance(
                viewModel.booking.departure_f?.provider ?: "",
                viewModel.booking.departure_f?.flightKey ?: "",
                false
            )
        )
        if (viewModel.booking.is_round_trip){
            fragments = listOf(
                SeatSelectFragmentV2.newInstance(
                    viewModel.booking.departure_f?.provider ?: "",
                    viewModel.booking.departure_f?.flightKey ?: "",
                    false
                ),
                SeatSelectFragmentV2.newInstance(
                    viewModel.booking.return_f?.provider ?: "",
                    viewModel.booking.return_f?.flightKey ?: "",
                    true
                )
            )
        }
        val adapter = PagerAdapter(this, fragments)
        binding.viewPager.adapter = adapter
        binding.viewPager.offscreenPageLimit = 2
        binding.viewPager.setCurrentItem(0, false)
        binding.viewPager.isUserInputEnabled = false
        TabLayoutMediator(
            binding.tabLayout, binding.viewPager
        ) { tab, position ->
            when (position) {
                0 -> tab.text = "Lượt đi"
                1 -> tab.text = "Lượt về"

            }
        }.attach()
        binding.tabLayout.tabMode = TabLayout.MODE_FIXED




    }


    private fun observe(){

        doneClick = {
            doneClick()
        }


    }



    private fun getPaxSeat() {

        viewModel.booking.pax_info.adult.forEach { passenger ->
            passenger.addOn.filter { addOnInfo -> addOnInfo.type == AddOnType.SEAT }.forEach {
                viewModel.paxSeatString += "-" + it.text
            }


            passenger.addOnReturn.filter { addOnInfo -> addOnInfo.type == AddOnType.SEAT }.forEach {
                viewModel.paxSeatReturnString += "-" + it.text
            }
        }
        viewModel.booking.pax_info.child.forEach { passenger ->
            passenger.addOn.filter { addOnInfo -> addOnInfo.type == AddOnType.SEAT }.forEach {
                viewModel.paxSeatString += "-" + it.text
            }

            passenger.addOnReturn.filter { addOnInfo -> addOnInfo.type == AddOnType.SEAT }.forEach {
                viewModel.paxSeatReturnString += "-" + it.text
            }
        }
    }


    private fun backToBooking() {
        val data = Intent()
        data.putExtra("bookingDetail", Gson().toJson(viewModel.booking.pax_info))
        setResult(RESULT_OK, data)
        finish()
    }




    private fun initAnalytics(booking: BookingV2, event: String) {
        try {

            val params = Bundle()
            params.putString(FirebaseAnalytics.Param.ORIGIN, booking.departure_f?.originCode)
            params.putString(FirebaseAnalytics.Param.DESTINATION, booking.departure_f?.destinationCode)
            params.putString(FirebaseAnalytics.Param.START_DATE, booking.departure_f?.departureDateTime.toString())
            params.putString(FirebaseAnalytics.Param.END_DATE, booking.departure_f?.arriverDateTime.toString())
            params.putString(FirebaseAnalytics.Param.FLIGHT_NUMBER, booking.departure_f?.flightNumber)
            params.putString(FirebaseAnalytics.Param.ITEM_NAME,
                booking.departure_f?.originCode + booking.departure_f?.destinationCode + booking.departure_f?.flightNumber)
            params.putString(FirebaseAnalytics.Param.NUMBER_OF_PASSENGERS,
                ((booking.departure_f?.adult ?: 0) + (booking.departure_f?.child ?: 0) + (booking.departure_f?.infant ?: 0)).toString() + "")
            FirebaseAnalytics.getInstance(this).logEvent(event, params)
            FirebaseAnalytics.getInstance(this).setCurrentScreen(this, "booking_input", null)

        } catch (e: Exception) {

        }
    }

    override fun onResume() {
        super.onResume()
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean { // Inflate the menu; this adds items to the action bar if it is present.
        menuInflater.inflate(R.menu.main_reward, menu)
        try {
            Handler(Looper.getMainLooper()).postDelayed({
                val rewardButton: View = findViewById(R.id.action_reward)
                QBadgeView(applicationContext).setGravityOffset(0f, 0f, true).bindTarget(rewardButton).setBadgeText("!")
                    .setOnDragStateChangedListener { dragState, badge, targetView ->

                    }
            }, 1000)
        } catch (e: java.lang.Exception) {
            Log.logException(e)
        }
        return true
    }

    fun doneClick(): Boolean {

        //        if (booking.is_round_trip && seatViewListReturn.size == 0 && booking.return_f?.provider == "VJ") {
        //            Toast.makeText(this, "Vui lòng chọn ghế lượt về", Toast.LENGTH_SHORT).show()
        //            binding.viewPager.currentItem = 1
        //            return false
        //        }
        //        if (!booking.is_round_trip && seatViewList.size == 0 && booking.departure_f?.provider == "VJ") {
        //            Toast.makeText(this, "Vui lòng chọn ghế lượt đi", Toast.LENGTH_SHORT).show()
        //            binding.viewPager.currentItem = 0
        //            return false
        //        }

        backToBooking()
        return true
    }

    fun backClick() {
        finish()
    }

    

    override fun onOptionsItemSelected(item: MenuItem): Boolean { // Take appropriate action for each action item click
        when (item.itemId) {
            android.R.id.home -> {

                finish()
            }

            R.id.action_reward -> {
//                val intent = Intent(this, RewardActivity::class.java)
                val intent = Intent(this, RewardActivityV2::class.java)

                startActivity(intent)
                return true
            }

            else -> {
                finish()
            }
        }
        return false
    }

//    override fun onSystemSettingChange(type: AppConfigs.SystemSettingType) {
//
//        if (type == AppConfigs.SystemSettingType.INTERNET) {
//            if (this::viewModel.isInitialized) {
//                viewModel.updateInternetStatus(isInternetConnected)
//            }
//        }
//
//
//    }




}

//class SeatFragmentAdapter(fm: FragmentManager, booking: BookingV3) : FragmentPagerAdapter(fm) {
//    var bookingx = booking
//
//    override fun getItem(position: Int): SeatSelectFragment {
//
//        AppConfigs.Log("book", AppController.instance.gSon.toJson(bookingx))
//        if (bookingx.is_round_trip) {
//            return when (position) {
//                0 -> SeatSelectFragment.newInstance(bookingx.departure_f?.provider ?: "",
//                    bookingx.departure_f?.flightKey ?: "",
//                    false)
//                1 -> SeatSelectFragment.newInstance(bookingx.return_f?.provider ?: "", bookingx.return_f?.flightKey ?: "", true)
//                else -> SeatSelectFragment.newInstance("", "", false)
//            }
//        } else {
//            return SeatSelectFragment.newInstance(bookingx.departure_f?.provider ?: "",
//                bookingx.departure_f?.flightKey ?: "",
//                false)
//        }
//
//    }
//
//    override fun getPageTitle(position: Int): CharSequence { // if (bookingx.is_round_trip) {
//        when (position) {
//            0 -> return "Lượt đi"
//            1 -> return "Lượt về"
//            else -> return ""
//        } //}
//    }
//
//
//    override fun getCount(): Int {
//        if (!bookingx.is_round_trip) {
//            return 1
//        }
//        return 2
//    }
//
//}
