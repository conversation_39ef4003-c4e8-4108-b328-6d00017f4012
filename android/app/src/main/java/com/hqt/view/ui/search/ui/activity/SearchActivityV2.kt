package com.hqt.view.ui.search.ui.activity


import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Color
import android.os.Bundle
import android.view.View
import android.view.animation.AnimationSet
import android.view.animation.DecelerateInterpolator
import android.view.animation.RotateAnimation
import android.widget.Toast
import androidx.activity.viewModels
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.content.ContextCompat
import androidx.core.view.isInvisible
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.snackbar.Snackbar
import com.google.firebase.analytics.FirebaseAnalytics
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.PnrActivity
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.SearchResult
import com.hqt.datvemaybay.databinding.ActivitySearchBinding
import com.hqt.util.AppConfigs
import com.hqt.util.Helper.clickWithDebounce
import com.hqt.util.Log
import com.hqt.util.base.BaseActivity
import com.hqt.view.ui.HomeActivity
import com.hqt.view.ui.booking.FlightWeekViewActivity
import com.hqt.view.ui.calender.ui.activity.LunarCalendarActivity
import com.hqt.view.ui.flightSearch.FlightSearchActivity
import com.hqt.view.ui.search.data.model.AirportInfo
import com.hqt.view.ui.search.ui.SearchViewModel
import com.hqt.view.ui.search.ui.dialog.SelectPassengerDialog
import dagger.hilt.android.AndroidEntryPoint
import uk.co.deanwild.materialshowcaseview.MaterialShowcaseSequence
import uk.co.deanwild.materialshowcaseview.MaterialShowcaseView
import uk.co.deanwild.materialshowcaseview.ShowcaseConfig
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale


@AndroidEntryPoint
class SearchActivityV2 : BaseActivity<ActivitySearchBinding>() {
    private val viewModel: SearchViewModel by viewModels()



    var isInternetConnected = true
    private var isRoundTrip = true
    private var isDomestic = true
    var coordinatorLayout: CoordinatorLayout? = null
    private var page: String? = null



    var showCase: Boolean = true

    val REQUEST_CODE_FROM: Int = 0
    val REQUEST_CODE_TO: Int = 1
    val REQUEST_CODE_DEP_DATE: Int = 2
    val REQUEST_CODE_RE_DATE: Int = 3
    var dateFormat: SimpleDateFormat = SimpleDateFormat("dd/MM/yyyy", Locale.getDefault())


    //private AdView adView;
    var sheetBehavior: BottomSheetBehavior<*>? = null
    var sequence: MaterialShowcaseSequence? = null
    var showcaseView: MaterialShowcaseView? = null
    override fun getLayoutRes(): Int {
        return R.layout.activity_search
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        observe()
        coordinatorLayout = findViewById(R.id.coordinatorLayout)


        Common.SHOWFULLPRICE = false
        val config = ShowcaseConfig()
        config.delay = 500 // half second between each showcase view

        val sequence = MaterialShowcaseSequence(this, "SHOWCASE_SEARCH") //
        sequence.setConfig(config)
        sequence.setOnItemShownListener { itemView, position ->
            showcaseView = itemView
            if (showCase) {
                Toast.makeText(this, "Nhấn OK ĐÃ HIỂU để tiếp tục", Toast.LENGTH_SHORT).show()
                showCase = false
            }
        }


        //ADD new date
        getToolbar()?.title = "Tìm chuyến bay"
        getToolbar()?.setNavigationIcon(R.drawable.ic_action_back_home)
        isDomestic = viewModel.sharedPrefsHelper.getData("isDomestic", Boolean::class.java) ?: true


        sequence.addSequenceItem(
            binding.checkOneWay,
            "Hướng dẫn cơ bản", "Nhấn chọn bay một chiều hoặc khứ hồi", "OK Đã hiểu"
        )


        sequence.addSequenceItem(
            binding.originCode,
            "Nhấn chọn sân bay đi", "OK Đã hiểu"
        )
        sequence.addSequenceItem(
            binding.destinationCode,
            "Nhấn chọn sân bay đến", "OK Đã hiểu"
        )



        sequence.addSequenceItem(
            binding.selectDepDate,
            "Nhấn chọn ngày đi", "OK Đã hiểu"
        )

        sequence.addSequenceItem(
            binding.selectDepDate,
            "Nhấn chọn ngày về", "OK Đã hiểu"
        )


        sequence.addSequenceItem(
            binding.showPaxSelect,
            "Chọn số lượng hành khách", "OK Đã hiểu"
        )
        sequence.addSequenceItem(
            binding.search,
            "Nhấn tìm chuyến bay", "OK. Đã hiểu. Bắt đầu đặt vé"
        )


        //setCurrentDate();
        addButtonListener()
        if (AppConfigs.getInstance().config.getBoolean("on_roundtrip")) {
            binding.checkRoundtrip.performClick()
        } else {
            binding.checkOneWay.performClick()
        }

        // ATTENTION: This was auto-generated to handle app links.
        val appLinkIntent = intent
        val appLinkAction = appLinkIntent.action
        val appLinkData = appLinkIntent.data

        if (AppConfigs.getInstance().config.getBoolean("show_tip")) {
            sequence.start()
        }
        searchRequest()
    }


    private fun observe() {

        viewModel.originCode.observe(this) {
            binding.originCode.text = it
        }
        viewModel.originName.observe(this) {
            binding.originName.text = it
        }
        viewModel.destinationCode.observe(this) {
            binding.destinationCode.text = it
        }
        viewModel.destinationName.observe(this) {
            binding.destinationName.text = it
        }

        viewModel.depDate.observe(this){
            binding.txtDepDate.text = Common.dateToString(it.time, "dd/MM")
            binding.txtDepDayofweek.text = Common.getDayOfWeek(it.time)
            binding.txtDepYear.text = Common.dateToString(it.time, "yyyy")
            binding.txtDepLunarDate.text = "Âm lịch ${Common.getLunarFromDate(it.time)}"
        }
        viewModel.retDate.observe(this){
            binding.txtRetCaret.visibility = View.VISIBLE
            binding.txtRetDate.text = Common.dateToString(it.time, "dd/MM")
            binding.txtRetDayOfWeek.text = Common.getDayOfWeek(it.time)
            binding.txtRetYear.text = Common.dateToString(it.time, "yyyy")
            binding.txtRetLunarDate.text = "Âm lịch ${Common.getLunarFromDate(it.time)}"
        }


        viewModel.adultCount.observe(this) {
            binding.adultCount.text = it.toString()
        }
        viewModel.childCount.observe(this) {
            binding.childCount.text = it.toString()
        }
        viewModel.infantCount.observe(this) {
            binding.infantCount.text = it.toString()
        }

    }

    private fun searchRequest() {
        val intent = intent
        if (intent.hasExtra("originCode")) {
            binding.originCode.text = intent.getStringExtra("originCode")
            binding.originName.text = Common.getAirPortName(
                intent.getStringExtra("originCode"),
                false
            )
        }
        if (intent.hasExtra("destinationCode")) {
            binding.destinationCode.text = intent.getStringExtra("destinationCode")
            binding.destinationName.text = Common.getAirPortName(
                intent.getStringExtra("destinationCode"),
                false
            )
        }
        if (intent.hasExtra("departureDate")) {
            viewModel.depDate.value = Common.stringToDate(intent.getStringExtra("departureDate"), "yyyy-MM-dd")
        }
        if (intent.hasExtra("returnDate")) {
            binding.checkRoundtrip.performClick()
            viewModel.retDate.value = Common.stringToDate(intent.getStringExtra("returnDate"), "yyyy-MM-dd")
        }
        if (intent.hasExtra("adultCount")) {
            viewModel.adultCount.value = intent.getIntExtra("adultCount", 1)
        }
        if (intent.hasExtra("childCount")) {
            viewModel.childCount.value = intent.getIntExtra("childCount", 0)
        }
        if (intent.hasExtra("infantCount")) {
            viewModel.infantCount.value = intent.getIntExtra("infantCount", 0)
        }
    }


    private fun addButtonListener() {



        binding.selectOrigin.clickWithDebounce(500) { //
            val i = Intent(
                applicationContext,
                AirportSearchActivityV2::class.java
            )
            startActivityForResult(i, REQUEST_CODE_FROM)
        }
        binding.selectDestination.clickWithDebounce(500) { //
            val intent = Intent(
                applicationContext,
                AirportSearchActivityV2::class.java
            )
            intent.putExtra("dep", binding.originCode.text.toString())
            startActivityForResult(intent, REQUEST_CODE_TO)
        }
        binding.swapRouteLayout.clickWithDebounce(500) {
            binding.swapRoute.performClick()
        }
        binding.swapRoute.clickWithDebounce(100) {

            viewModel.swapLocations()

            val animSet = AnimationSet(true)
            animSet.interpolator = DecelerateInterpolator()
            animSet.fillAfter = true
            animSet.isFillEnabled = true

            val animRotate = RotateAnimation(
                0.0f, 360.0f,
                RotateAnimation.RELATIVE_TO_SELF, 0.5f,
                RotateAnimation.RELATIVE_TO_SELF, 0.5f
            )

            animRotate.duration = 200
            animRotate.fillAfter = true
            animSet.addAnimation(animRotate)
            binding.swapRoute.startAnimation(animSet)
        }



        binding.selectDepDate.clickWithDebounce(500) {
            try {
//                val i = Intent(this, AmLich::class.java)
                val i = Intent(this, LunarCalendarActivity::class.java)

                i.putExtra("depDate", dateFormat.format(viewModel.depDate.value?.time ?: ""))
                i.putExtra("origin", binding.originCode.text.toString())
                i.putExtra("destination", binding.destinationCode.text.toString())
                startActivityForResult(i, REQUEST_CODE_DEP_DATE)
            } catch (except: Exception) {
                Log.logException(except)
            }
        }


        binding.selectRetDate.clickWithDebounce(500) {
            try {
                binding.checkRoundtrip.performClick()

                viewModel.depDate.value?.let { dep ->
                    val ret = viewModel.retDate.value
                    if (ret == null || !ret.after(dep)) {
                        viewModel.retDate.value = (dep.clone() as Calendar).apply {
                            add(Calendar.DATE, 2)
                        }
                    }
                }


//                val i = Intent(applicationContext, AmLich::class.java)
                val i = Intent(this, LunarCalendarActivity::class.java)

                i.putExtra("depDate", dateFormat.format(viewModel.depDate.value?.time ?: ""))
                i.putExtra("reDate", dateFormat.format(viewModel.retDate.value?.time ?: ""))
                i.putExtra("origin", binding.originCode.text.toString())
                i.putExtra("destination", binding.destinationCode.text.toString())
                startActivityForResult(i, REQUEST_CODE_RE_DATE)


                viewModel.retDate.value = viewModel.retDate.value
            } catch (except: Exception) {
                Log.logException(except)
            }
        }


        binding.selectPaxButton.clickWithDebounce(500) {

            val dialog = SelectPassengerDialog()
            dialog.onActionDone = {

            }
            dialog.show(supportFragmentManager, "")

        }
        binding.checkOneWay.clickWithDebounce(500) {

            isRoundTrip = false


            binding.llRetDate?.isInvisible = true

            binding.checkOneWay.background = ContextCompat.getDrawable(this, R.drawable.button_one_way)
            binding.checkOneWay.setTextColor(Color.parseColor("#FFFFFF"))

            binding.checkRoundtrip.setBackgroundColor(ContextCompat.getColor(this, R.color.fui_transparent))
            binding.checkRoundtrip.setTextColor(Color.parseColor("#00a2e3"))
        }


        binding.checkRoundtrip.clickWithDebounce(500) {
            if (viewModel.retDate.value!!.after(viewModel.depDate.value) && viewModel.retDate.value!![Calendar.DATE] != viewModel.depDate.value!![Calendar.DATE]) {

                viewModel.retDate.value = viewModel.retDate.value

            } else {

                val original = viewModel.depDate.value
                val date = original?.clone() as? Calendar
                date?.add(Calendar.DATE, 2)
                viewModel.retDate.value = date


            }
            isRoundTrip = true

            binding.checkRoundtrip.background = ContextCompat.getDrawable(this, R.drawable.button_return)
            binding.checkRoundtrip.setTextColor(Color.parseColor("#FFFFFF"))

            binding.checkOneWay.setBackgroundColor(ContextCompat.getColor(this, R.color.fui_transparent)
            )
            binding.checkOneWay.setTextColor(Color.parseColor("#00a2e3"))

            binding.llRetDate?.isInvisible = false

        }


        binding.search.clickWithDebounce(500) {
            if (isInternetConnected) {
                if (isRoundTrip && (viewModel.depDate.value!!.timeInMillis > viewModel.retDate.value!!.timeInMillis)) {
                    Toast.makeText(this@SearchActivityV2, "Ngày chuyến bay lượt về phải sau ngày lượt đi !", Toast.LENGTH_SHORT).show()
                } else if (binding.originCode.text.toString() == binding.destinationCode.text.toString()
                ) {
                    Toast.makeText(this@SearchActivityV2, "Vui lòng chọn điểm đến và điểm đi khác nhau !", Toast.LENGTH_SHORT).show()
                } else if (binding.originCode.text.length != 3) {
                    Toast.makeText(this@SearchActivityV2, "Vui lòng chọn điểm đi !", Toast.LENGTH_SHORT).show()
                } else if (binding.destinationCode.text.length != 3) {
                    Toast.makeText(this@SearchActivityV2, "Vui lòng chọn điểm đến !", Toast.LENGTH_SHORT).show()
                } else {
                    viewModel.sharedPrefsHelper.setData("from", binding.originCode.text.toString())
                    viewModel.sharedPrefsHelper.setData("to", binding.destinationCode.text.toString())
                    viewModel.sharedPrefsHelper.setData("isDomestic", isDomestic)


                    page = "1"
                    viewModel.originCode.value = binding.originCode.text.toString()
                    viewModel.destinationCode.value = binding.destinationCode.text.toString()


                    val dF = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
                    val departureTime = dF.format(viewModel.depDate.value?.time ?: "")
                    val returnTime = dF.format(viewModel.retDate.value?.time ?: "")


                    if (binding.originCode.text.toString().trim().isEmpty()) {
                        Toast.makeText(this@SearchActivityV2, "Chưa chọn điểm đi ", Toast.LENGTH_SHORT).show()
                    } else if (binding.destinationCode.text.toString().trim()
                            .isEmpty()
                    ) {
                        Toast.makeText(this@SearchActivityV2, "Chưa chọn điểm đến", Toast.LENGTH_SHORT).show()
                        binding.destinationCode.requestFocus()
                    }

                    try {
                        val params = Bundle()
                        params.putString(FirebaseAnalytics.Param.ORIGIN, Common.getAirPortCode(viewModel.originCode.value))
                        params.putString(FirebaseAnalytics.Param.DESTINATION, Common.getAirPortCode(viewModel.destinationCode.value))
                        params.putString(FirebaseAnalytics.Param.START_DATE, departureTime)
                        params.putString(FirebaseAnalytics.Param.END_DATE, returnTime)
                        params.putInt(FirebaseAnalytics.Param.NUMBER_OF_PASSENGERS, viewModel.getNumberOfPassenger())
                        firebaseAnalytics.logEvent(FirebaseAnalytics.Event.SEARCH, params)
                    } catch (e: Exception) {
                        AppConfigs.logException(e)
                    }


                    //NEW LA CHON XEM VE RE
                    val intent: Intent = if (binding.cheapSearch.isChecked && isDomestic) {
                        Intent(applicationContext, FlightWeekViewActivity::class.java)
                    } else {
                        if (isDomestic) {
//                            Intent(this, SearchResult::class.java)
                            Intent(this, SearchResultActivityV2::class.java)
                        } else {
                            Intent(this, FlightSearchActivity::class.java)
                        }
                    }


                    AppConfigs.Log("departureTime", departureTime)
                    intent.putExtra("originCode", binding.originCode.text.toString())
                    intent.putExtra("destinationCode", binding.destinationCode.text.toString())
                    intent.putExtra("departureTime", departureTime)
                    intent.putExtra("returnTime", returnTime)
                    intent.putExtra("adult", viewModel.adultCount.value)
                    intent.putExtra("child", viewModel.childCount.value)
                    intent.putExtra("infant", viewModel.infantCount.value)
                    intent.putExtra("isRoundTrip", isRoundTrip)

                    startActivity(intent)
                    overridePendingTransition(R.anim.enter, R.anim.exit)
                }
            } else {
                Common.showAlertDialog(
                    this@SearchActivityV2,
                    "Không có Internet",
                    "Xin vui lòng kiểm tra lại Wifi/3G để tiếp tục",
                    false,
                    true
                )
            }
        }
    }




    override fun onStart() {
        super.onStart()
        LocalBroadcastManager.getInstance(this)
            .registerReceiver(mMessageReceiver, IntentFilter("bookingupdate"))
    }

    override fun onBackPressed() {
        if (isTaskRoot) {
            val intent = Intent(this, HomeActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            startActivity(intent)
            finish()
            super.onBackPressed()
        } else {
            super.onBackPressed()
        }
    }

    override fun onStop() {
        super.onStop()
        LocalBroadcastManager.getInstance(this).unregisterReceiver(mMessageReceiver)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)


        if (resultCode != Activity.RESULT_OK || data == null) return

        when (requestCode) {
            REQUEST_CODE_FROM -> {

                viewModel.setOrigin(data.getSerializableExtra("airportInfo") as? AirportInfo)


                isDomestic = (viewModel.isOriginDomestic.value ?: true) && (viewModel.isDestinationDomestic.value ?: true)
                viewModel.sharedPrefsHelper.setData("isDomestic", isDomestic)
            }
            REQUEST_CODE_TO -> {

                viewModel.setDestination(data.getSerializableExtra("airportInfo") as? AirportInfo)

                isDomestic = (viewModel.isOriginDomestic.value ?: true) && (viewModel.isDestinationDomestic.value ?: true)
                viewModel.sharedPrefsHelper.setData("isDomestic", isDomestic)

            }
            REQUEST_CODE_DEP_DATE -> data.getStringExtra("date")?.let { dateStr ->
                viewModel.depDate.value = Common.getDateFromString(dateStr)
                if (isRoundTrip && viewModel.depDate.value?.after(viewModel.retDate.value) == true) {
                    binding.checkRoundtrip.performClick()
                }
            }
            REQUEST_CODE_RE_DATE -> data.getStringExtra("date")?.let { dateStr ->
                viewModel.retDate.value = Common.getDateFromString(dateStr)
            }
        }
    }




    private val mMessageReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            // Get extra data included in the Intent

            val message = intent.getStringExtra("message")
            val snackbar: Snackbar = Snackbar
                .make(binding.coordinatorLayout, message.toString(), 10000)
                .setAction("XEM CHI TIẾT") {
                    val pnrIntent = Intent(
                        applicationContext,
                        PnrActivity::class.java
                    )
                    pnrIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                    startActivity(pnrIntent)
                }
            snackbar.show()
            AppConfigs.Log("Message rc", "SEARCH")
        }
    }


}
