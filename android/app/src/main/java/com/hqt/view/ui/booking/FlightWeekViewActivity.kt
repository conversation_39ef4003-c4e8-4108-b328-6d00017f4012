package com.hqt.view.ui.booking

import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.view.View
import androidx.coordinatorlayout.widget.CoordinatorLayout
import com.android.volley.AuthFailureError
import com.android.volley.Response
import com.android.volley.toolbox.JsonObjectRequest
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.hqt.data.model.WeeklyPriceData
import com.hqt.datvemaybay.BuildConfig
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.Common.convertToDate
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ActivityFlightWeekViewBinding
import com.hqt.util.AppConfigs
import com.hqt.util.AppController.Companion.instance
import com.hqt.util.Helper
import com.hqt.util.WeeklyPriceProcessor
import com.hqt.util.amlich.TextDecorator
import com.hqt.view.ui.BaseActivityKt
import com.hqt.view.ui.chart.WeeklyPriceChartView
import com.prolificinteractive.materialcalendarview.CalendarDay
import com.prolificinteractive.materialcalendarview.MaterialCalendarView
import com.prolificinteractive.materialcalendarview.OnDateSelectedListener
import com.prolificinteractive.materialcalendarview.OnMonthChangedListener
import com.prolificinteractive.materialcalendarview.format.ArrayWeekDayFormatter
import com.prolificinteractive.materialcalendarview.format.MonthArrayTitleFormatter
import org.json.JSONArray
import org.json.JSONException
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale

class FlightWeekViewActivity : BaseActivityKt<ActivityFlightWeekViewBinding>(),
    OnDateSelectedListener, OnMonthChangedListener {

    private lateinit var depDate: Calendar
    private lateinit var retDate: Calendar
    override val layoutId: Int = R.layout.activity_flight_week_view
    lateinit var dialog: BottomSheetDialog
    lateinit var coordinatorLayout: CoordinatorLayout
    private lateinit var selectPassengerView: View

    private var fromId = -1
    private var toId = -1

    private val REQUEST_CODE_FROM = 0
    private val REQUEST_CODE_TO = 1
    private val REQUEST_CODE_DEP_DATE = 2
    private val REQUEST_CODE_RE_DATE = 3
    private var txtOrigin = ""
    private var txtDestination = ""
    val dF = SimpleDateFormat("dd/MM/yyyy", Locale.US)
    var loadedMonth: String = ""

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)


        val `in` = intent
        var act = `in`.getStringExtra("act")
        txtOrigin = `in`.getStringExtra("originCode") ?: ""
        txtDestination = `in`.getStringExtra("destinationCode") ?: ""
        var txtdepartureTime = `in`.getStringExtra("departureTime")
        var txtreturnTime = `in`.getStringExtra("returnTime")
        var adult = `in`.getIntExtra("adult", 1)
        var child = `in`.getIntExtra("child", 0)
        var infant = `in`.getIntExtra("infant", 0)
        var isRoundTrip = `in`.getBooleanExtra("isRoundTrip", false)




        depDate = Common.stringToDate(txtdepartureTime, "yyyy-MM-dd")
        retDate = Common.stringToDate(txtreturnTime, "yyyy-MM-dd")

        depDate.add(Calendar.DAY_OF_MONTH, 3)
        retDate.add(Calendar.DAY_OF_MONTH, 5)

        //binding.originName.text = "xxxxxx";
        getToolbar().title = "Gia an can"
        getToolbar().setNavigationIcon(R.drawable.ic_action_back_home)
        supportActionBar!!.setDisplayShowHomeEnabled(true)
        initClickAction()
        initAnalytics()

    }

    private fun initClickAction() {

        getToolbar().title = "Chọn khoảng thời gian đi"
        genCalendar(getViewBindding().calendarView, Calendar.getInstance(), depDate)

        getMonthPrices(getViewBindding().calendarView)
        genCalendar(getViewBindding().calendarViewReturn, depDate, retDate)


    }


    fun genCalendar(widget: MaterialCalendarView, minDate: Calendar?, selectedDate: Calendar) {
        //  returnDate = selectedDate
        widget.setOnDateChangedListener(this)
        widget.showOtherDates = MaterialCalendarView.SHOW_OUT_OF_RANGE

        val maxDate = Calendar.getInstance()
        maxDate.add(Calendar.MONTH, 18)
        maxDate[maxDate[Calendar.YEAR], maxDate[Calendar.MONTH]] = maxDate[Calendar.DATE]

        widget.setSelectedDate(
            CalendarDay.from(
                selectedDate[Calendar.YEAR], selectedDate[Calendar.MONTH] + 1,
                selectedDate[Calendar.DATE]
            )
        )


        widget.setWeekDayFormatter(
            ArrayWeekDayFormatter(
                resources.getTextArray(
                    R.array.custom_weekdays
                )
            )
        )
        widget.setTitleFormatter(
            MonthArrayTitleFormatter(
                resources.getTextArray(
                    R.array.custom_months
                )
            )
        )
        widget.setSelectionMode(MaterialCalendarView.SELECTION_MODE_SINGLE)
        widget.setDateTextAppearance(R.style.CustomDayTextAppearance)
        widget.currentDate = Helper.convertToCalendarDay(selectedDate)
        widget.setOnMonthChangedListener(this)
        widget.setShowLunarDates(false)
        widget.state().edit().isCacheCalendarPositionEnabled(true)
            .setMinimumDate(Helper.convertToCalendarDay(minDate!!))
            .setMaximumDate(Helper.convertToCalendarDay(maxDate))
            .commit()
        val cal = Calendar.getInstance()
        cal[Calendar.DATE] = 1
    }

    @SuppressLint("MissingSuperCall")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {


    }


    override fun onStart() {
        super.onStart()

    }


    private fun initAnalytics() {

        val params = Bundle()
        firebaseAnalytics.logEvent("bus_search_view", params)
        firebaseAnalytics.setCurrentScreen(this, "bus_search_view", null)
    }


    fun getMonthPrices(widget: MaterialCalendarView) {
        val curentMonth: CalendarDay = widget.getCurrentDate()
        val month = Common.dateToString(convertToDate(curentMonth.date).getTime(), "yyyyMM")
        if (loadedMonth.contains(month)) {
            AppConfigs.Log("loaded", loadedMonth)
        } else {
            loadedMonth = loadedMonth + "-" + month
            val tag_json_obj = "json_month_prices"
            val url =
                AppConfigs.getInstance().config.getString("root_api") + "/api/v1/AirLines/PricesBoard"

            val postParam = JSONObject()
            val route = JSONArray()
            route.put(txtOrigin + txtDestination)

            try {
                postParam.put("startDate", month + "01")
                postParam.put("endDate", month + "31")
                postParam.put("maxPrice", "0")
                postParam.put("minPrice", "10000")
                postParam.put("routes", route)
                postParam.put("source", "ANDROID")
                postParam.put("type", "date")
                postParam.put("key", Common.getKeyHash())
                postParam.put("ver", BuildConfig.VERSION_CODE.toString() + "")
            } catch (e: JSONException) {
            }

            AppConfigs.Log("postjson ", postParam.toString())
            val jsonObjReq: JsonObjectRequest = object : JsonObjectRequest(
                Method.POST,
                url, postParam,
                Response.Listener<JSONObject> { response ->
                    try {
                        if (!response.isNull("data")) {
                            val data = response.getJSONObject("data")
                            val fullData = data.getJSONObject("full")
                            val route: String = txtOrigin + txtDestination

                            if (!fullData.isNull(route) && fullData.length() > 0) {
                                addMonthPricesToCalendar(fullData.getJSONArray(route), widget)
                            }
                        }
                    } catch (e: JSONException) {
                        AppConfigs.logException(e)
                        e.printStackTrace()
                    }
                }, Response.ErrorListener { e ->
                    AppConfigs.logException(e)
                }) {

                @Throws(AuthFailureError::class)
                override fun getHeaders(): HashMap<String, String> {
                    return getHeaderRequest()
                }
            }

            // Adding request to request queue
            instance.addToRequestQueue(jsonObjReq, tag_json_obj)
        }
    }

    fun getHeaderRequest(): HashMap<String, String> {
        val headers: HashMap<String, String> = HashMap<String, String>()
        headers["Content-Type"] = "application/json"
        headers["X-Auth-ID"] = "9B1B13952BD9FF446AB569BBB49B3"
        headers["Origin"] = "https://12bay.vn"
        headers["Referer"] = "https://12bay.vn"
        headers["X-Requested-With"] = "XMLHttpRequest"

        return headers
    }

    fun addMonthPricesToCalendar(pricesData: JSONArray, widget: MaterialCalendarView) {
        widget.setShowLunarDates(false)
        widget.state().edit().commit()

        val listDecor = ArrayList<TextDecorator>()
        try {
            for (i in 0 until pricesData.length()) {
                val day = pricesData.getJSONObject(i)


                val mCode = day.getString("mCode")
                val price = day.getInt("mPrice") / 1000

                val calDay = CalendarDay.from(
                    mCode.substring(7, 11).toInt(),
                    mCode.substring(11, 13).toInt(),
                    mCode.substring(13, 15).toInt()
                )
                if (CalendarDay.today().isBefore(calDay)) {
                    if (price < 500) {
                        listDecor.add(
                            (TextDecorator(
                                Color.parseColor("#2980b9"),
                                calDay,
                                price.toString() + "k"
                            ))
                        )
                    } else {
                        listDecor.add(
                            (TextDecorator(
                                Color.parseColor("#848383"),
                                calDay,
                                price.toString() + "k"
                            ))
                        )
                    }
                }
            }
            widget.addDecorators(listDecor)
        } catch (e: Exception) {
            AppConfigs.logException(e)
        }
    }


    override fun onDateSelected(
        widget: MaterialCalendarView,
        date: CalendarDay,
        selected: Boolean
    ) {
        // TODO("Not yet implemented")
    }

    override fun onMonthChanged(widget: MaterialCalendarView?, date: CalendarDay?) {
        // TODO("Not yet implemented")
    }


}

