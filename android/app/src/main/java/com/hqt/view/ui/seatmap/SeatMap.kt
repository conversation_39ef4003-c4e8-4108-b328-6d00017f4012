package com.hqt.view.ui.seatmap

import androidx.databinding.BaseObservable
import com.google.gson.annotations.SerializedName
import java.io.Serializable

data class SeatMap(

    @field:SerializedName("airCraft") var airCraft: String = "",
    @field:SerializedName("seatIdentifierTitles") var seatIdentifierTitles: ArrayList<SeatIdentifierTitle> = ArrayList(),
    @field:SerializedName("seatGroups") var seatGroups: ArrayList<SeatGroup> = ArrayList(),
    @field:SerializedName("seatType") val seatType: ArrayList<SeatType> = ArrayList(),
) : BaseObservable(), Serializable {

}

class SeatType : Serializable {
    @field:SerializedName("color")
    var color: String = "A9A9A9"
    @field:SerializedName("text")
    var text: String = ""
}

class SeatIdentifierTitle : Serializable {
    @field:SerializedName("index")
    var index: Int = 1
    @field:SerializedName("value")
    var value: String? = null
}

class SeatGroup : Serializable {
    @field:SerializedName("title")
    var title: String? = null
    @field:SerializedName("seatOptions")
    var seatOptions: ArrayList<SeatOption> = ArrayList()
}

class SeatOption : Serializable {
    @field:SerializedName("available")
    var available: Boolean = false
    @field:SerializedName("rowIdentifier")
    var rowIdentifier: String? = null
    @field:SerializedName("rowIndex")
    var rowIndex: Int = 0
    @field:SerializedName("seatIdentifier")
    var seatIdentifier: String = ""
    @field:SerializedName("columnIndex")
    var columnIndex: Int = 1
    @field:SerializedName("selectionKey")
    var selectionKey: String = ""
    @field:SerializedName("seatCharges")
    var seatCharges: Int = 0
    @field:SerializedName("color")
    var color: String? = null
    @field:SerializedName("text")
    var text: String? = null
    @field:SerializedName("seatNumber")
    var seatNumber: String? = null
}