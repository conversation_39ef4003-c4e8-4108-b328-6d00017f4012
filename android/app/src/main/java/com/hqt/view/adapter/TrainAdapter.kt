package com.hqt.view.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.hqt.datvemaybay.databinding.ListTrainItemBinding
import com.hqt.data.model.Train
import androidx.databinding.DataBindingUtil
import androidx.databinding.ObservableField
import com.hqt.datvemaybay.R
import com.hqt.view.ui.train.TrainSelectHanlder


class TrainAdapter(var mContext: Context, internal var contents: List<Train>) :
    RecyclerView.Adapter<TrainAdapter.ViewHolder>() {
    class ViewHolder(var context: Context, val binding: ListTrainItemBinding) : RecyclerView.ViewHolder(binding.root) {
        val divisionName = ObservableField<String>("")
        fun bind(train: Train) {
            binding.train = train
            if (binding.viewHolder == null) {
                binding.viewHolder = this
            }

            binding.train = train
            if (binding.handler == null) {
                binding.handler = TrainSelectHanlder(context)
            }
            divisionName.set(train.originName)
            binding.executePendingBindings()
        }

    }

    override fun getItemCount(): Int {
        return contents.size
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {

        val layoutInflater = LayoutInflater.from(parent.context)
        val binding: ListTrainItemBinding = DataBindingUtil.inflate(layoutInflater,
            R.layout.list_train_item,
            parent,
            false)

        return ViewHolder(mContext, binding)
    }


    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(contents[position])
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getItemViewType(position: Int): Int {
        return position
    }


}