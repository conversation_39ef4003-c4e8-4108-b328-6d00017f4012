package com.hqt.view.ui.calender.data.api

import com.hqt.base.model.HttpData
import com.hqt.data.prefs.SharedPrefsHelper
import com.hqt.util.ApiUtil
import javax.inject.Inject
import javax.inject.Singleton


@Singleton
class CalenderApiHelper @Inject constructor(
    private val api : CalenderService,
    private val sharedPrefsHelper: SharedPrefsHelper
) {

    suspend fun getPricesBoard(request : Any?) : Any{

        val header = ApiUtil.header(sharedPrefsHelper)
        return api.getPricesBoard(header, request)
    }

}