package com.hqt.view.ui.flighthistory.ui.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import android.view.inputmethod.InputMethodManager
import androidx.activity.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.google.firebase.analytics.FirebaseAnalytics
import com.hqt.base.BaseActivity
import com.hqt.base.model.State
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ActivityFlightHistoryBinding
import com.hqt.util.AppConfigs
import com.hqt.util.Log
import com.hqt.util.Widget
import com.hqt.util.Widget.isAvailable
import com.hqt.util.common.ConnectionState
import com.hqt.view.ui.HomeActivity
import com.hqt.view.ui.booking.ui.adapter.FlightHistoryItemAdapterV2
import com.hqt.view.ui.flighthistory.data.model.FlightHistory
import com.hqt.view.ui.flighthistory.ui.FlightHistoryViewModel
import com.hqt.view.ui.flighthistory.ui.dialog.FlightHistoryDetailDialog
import com.hqt.view.ui.search.ui.activity.SearchActivityV2
import dagger.hilt.android.AndroidEntryPoint
import java.util.Locale
import kotlin.math.roundToInt


@AndroidEntryPoint
class FlightHistoryActivityV2 : BaseActivity<ActivityFlightHistoryBinding>() {

    val viewModel: FlightHistoryViewModel by viewModels()


    private val mAdapter by lazy {
        FlightHistoryItemAdapterV2 {
            if (it.status.live) {
                val intent = Intent(this, MapViewActivity::class.java)
                intent.putExtra("flightId", it.id)
                intent.putExtra("onMoveCamera", true)
                if (!it.status.live) intent.putExtra("isHistory", true)
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                startActivity(intent)
                overridePendingTransition(R.anim.enter, R.anim.exit)

            } else {
//                Widget.showFlightHistory(this, it, false)
                FlightHistoryDetailDialog(it, false).show(supportFragmentManager, "")
            }
        }
    }

    override fun getLayoutRes(): Int {
        return R.layout.activity_flight_history
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)


        getToolbar()?.title = "Theo dõi chuyến bay"
        supportActionBar!!.setDisplayShowHomeEnabled(true)

        binding.viewModel = viewModel
        binding.lifecycleOwner = this


        initRecycleView()
        initBindingClick()

        if (intent.hasExtra("flightNumber") || intent.hasExtra("link")) {
            try {


                var getFnumber = "";
                if (intent.hasExtra("flightNumber")) getFnumber =
                    intent.getStringExtra("flightNumber")!!

                if (intent.hasExtra("link")) { //https://12bay.vn/lich-bay-vietnam-airlines-dong-hoi-di-ho-chi-minh-city-vn1405
                    val link = intent.getStringExtra("link")!!.replace("https://12bay.vn/", "")
                    val linkPart = link.split("/")
                    if (linkPart.isNotEmpty()) {
                        val fpart = linkPart[0].split("-");
                        getFnumber = fpart[fpart.size - 1]
                    }

                }


                binding.txtFlightNumber.setText(getFnumber.uppercase(Locale.getDefault()))
                Handler(Looper.getMainLooper()).postDelayed({
                    binding.btnFind.performClick()
                }, 100)
            } catch (e: Exception) {
                AppConfigs.logException(e)
            }
        } else {
            binding.txtFlightNumber.requestFocus()
        }
    }

    private fun initRecycleView() {
        binding.recyclerView.apply {
            layoutManager = LinearLayoutManager(this@FlightHistoryActivityV2)
            setHasFixedSize(true)
            adapter = mAdapter
        }
        observe()
    }

    private fun initBindingClick() {

        binding.btnSearchFlight.setOnClickListener {
//            var intent = Intent(this, SearchActivity::class.java)
            val intent = Intent(this, SearchActivityV2::class.java)
            startActivity(intent)

        }
        binding.btnFind.setOnClickListener {

            if (binding.txtFlightNumber.text.isNullOrEmpty()) {
//                showSnackbarMessage("Vui lòng nhập mã chuyến bay (vd: VN110)",
//                    R.color.red,
//                    2000,
//                    View.TEXT_ALIGNMENT_TEXT_START)
                binding.txtFlightNumber.requestFocus()
            } else {
                getList()
                val view = this.currentFocus
                if (view != null) {
                    val imm: InputMethodManager =
                        getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                    imm.hideSoftInputFromWindow(view.windowToken, 0)
                }
                initAnalytics(binding.txtFlightNumber.text.toString())

            }
        }
        binding.scoreProgress.setOnClickListener {

        }
        binding.btnAddNew.setOnClickListener {
            binding.txtFlightNumber.requestFocus()
        }

    }

//    private fun getListTask() {
//        binding.metaLayout.visibility = View.GONE
//        binding.shimmerViewContainer.visibility = View.VISIBLE
//        binding.shimmerViewContainer.startShimmer()
//        binding.notfound.visibility = View.GONE
//
//
//        var days = binding.inputDays.selectedItem.toString().replace(" ngày", "")
//        if (days.isEmpty()) days = "14"
//
//        val request = JSONObject()
//        try {
//
//            request.put("flightNumber", binding.txtFlightNumber.text.toString())
//            request.put("days", days)
//
//        } catch (e: JSONException) {
//            AppConfigs.logException(e)
//        }
//
//        SSLSendRequest(this).GET(false, "Flight/History", request, object : SSLSendRequest.CallBackInterface {
//            override fun onSuccess(response: JSONObject, cached: Boolean) {
//
//                if (!response.isNull("data") && response.getJSONObject("data").length() > 0) {
//                    processListData(response)
//                } else {
//                    binding.recyclerView.visibility = View.GONE
//                    binding.shimmerViewContainer.visibility = View.GONE
//                    binding.notfound.visibility = View.VISIBLE
//
////                    showSnackbarMessage("Không tìm thấy thông tin chuyến bay.",
////                        R.color.google_yellow,
////                        2000,
////                        View.TEXT_ALIGNMENT_TEXT_START)
//                }
//            }
//
//            override fun onFail(er: VolleyError) {
//
//                binding.recyclerView.visibility = View.GONE
//                binding.shimmerViewContainer.visibility = View.GONE
//                binding.notfound.visibility = View.VISIBLE
//
//
//            }
//        })
//    }

    fun observe() {

        viewModel.flightHistoryLiveData.observe(this) {
            when (it) {
                is State.Error -> {
                    binding.recyclerView.visibility = View.GONE
                    binding.shimmerViewContainer.visibility = View.GONE
                    binding.notfound.visibility = View.VISIBLE

                    //                showSnackbarMessage("Không tìm thấy thông tin lịch sử chuyến bay.",
//                    R.color.google_yellow,
//                    2000,
//                    View.TEXT_ALIGNMENT_TEXT_START)
                }

                State.Loading -> {
                    binding.metaLayout.visibility = View.GONE
                    binding.shimmerViewContainer.visibility = View.VISIBLE
                    binding.shimmerViewContainer.startShimmer()
                    binding.notfound.visibility = View.GONE
                }

                is State.Success -> {
                    if (it.data.history.isNotEmpty() && it.data.meta != null) {
                        processListData(it.data)
                    } else {
                        binding.recyclerView.visibility = View.GONE
                        binding.shimmerViewContainer.visibility = View.GONE
                        binding.notfound.visibility = View.VISIBLE
                    }

                }
            }
        }


    }


    private fun processListData(flightHistory: FlightHistory) {
        try {

            viewModel.flightHistory.postValue(flightHistory)
            if (flightHistory.meta != null) {
                try {
                    val score = flightHistory.meta!!.score * 100
                    binding.scoreProgress.progress = score
                    binding.scoreProgress.text = "" + score.roundToInt()
                    binding.metaLayout.visibility = View.VISIBLE
                    if (this.isAvailable()) {
                        Glide.with(this).load(flightHistory.meta!!.logo)
                            .apply(RequestOptions().centerInside()).skipMemoryCache(true)
                            .placeholder(R.drawable.logo_gray).into(binding.airLogo)
                    }
                    binding.metaLayout.visibility = View.VISIBLE
                } catch (e: Exception) {
                    binding.metaLayout.visibility = View.GONE
                    AppConfigs.logException(e)
                    AppConfigs.Log("E", e.message);
                }
            }

            binding.recyclerView.visibility = View.VISIBLE
            val listFlight = flightHistory.history

            mAdapter.setData(ArrayList(listFlight))

            binding.shimmerViewContainer.stopShimmer()
            binding.shimmerViewContainer.visibility = View.GONE
            binding.notfound.visibility = View.GONE

            listFlight.forEach { flightHistoryItem ->

                if (flightHistoryItem.status.live) {
                    val intent = Intent(this, MapViewActivity::class.java)
                    intent.putExtra("flightId", flightHistoryItem.id)
                    intent.putExtra("onMoveCamera", true)
                    if (!flightHistoryItem.status.live) intent.putExtra("isHistory", true)
                    intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    startActivity(intent)
                    overridePendingTransition(R.anim.enter, R.anim.exit)

                }

            }


        } catch (e: java.lang.Exception) {
            AppConfigs.logException(e)
            binding.shimmerViewContainer.stopShimmer()
            binding.shimmerViewContainer.visibility = View.GONE
            binding.notfound.visibility = View.VISIBLE
        }

    }

    override fun onBackPressed() {
        if (this.isTaskRoot) {
            val `in` = Intent(this, HomeActivity::class.java)
            startActivity(`in`)
            finish()
        } else {
            super.onBackPressed()
        }
    }

    override fun onRestart() {

        super.onRestart()
    }

    fun onSystemSettingChange(type: AppConfigs.SystemSettingType) {

        if (type == AppConfigs.SystemSettingType.INTERNET) {
            getList()
        }
    }


    override fun onConnectionChange(it: ConnectionState) {
        super.onConnectionChange(it)
        try {

            if (it != ConnectionState.CONNECTED) {
                getList()
            }


        } catch (e: Exception) {
            Log.logException(e)
        }

    }

    private fun initAnalytics(fnumber: String) {

        val params = Bundle()
        params.putString(FirebaseAnalytics.Param.FLIGHT_NUMBER, fnumber)
        FirebaseAnalytics.getInstance(this).logEvent("flight_history_view", params)
        FirebaseAnalytics.getInstance(this).setCurrentScreen(this, "flight_history_list", null)
    }


    private fun getList(){
        var days = binding.inputDays.selectedItem.toString().replace(" ngày", "")
        if (days.isEmpty()) days = "14"

        viewModel.getFlightHistoryList(binding.txtFlightNumber.text.toString(), days)
    }


}
