package com.hqt.view.ui.train

import android.content.Context
import android.view.View
import android.widget.Toast
import com.hqt.data.model.Train
import com.hqt.data.model.TrainSeatFare


class TrainSelectHanlder(val context: Context) {

    fun onLongClickFriend(view: View): Boolean {
        Toast.makeText(context, "On Long Click Listener", Toast.LENGTH_SHORT).show()
        return true
    }

    fun onSelectTrain(train: Train) {
        (context as TrainSelectActivity).selectTrain(train)
    }

    fun onSelectSeat(seat: TrainSeatFare) {
        if (seat.seatCount <= 0) {
            Toast.makeText(context, "Vé " + seat.seatClassName + " đã hết chỗ !", Toast.LENGTH_SHORT).show()
        } else {
            (context as TrainSelectActivity).selectTrainReturn(seat)
        }


    }
}