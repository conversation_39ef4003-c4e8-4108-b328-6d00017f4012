package com.hqt.view.ui.seatmap.data.api

import com.hqt.base.model.HttpData
import com.hqt.data.model.request.GetSeatMap
import com.hqt.view.ui.seatmap.SeatMap
import retrofit2.http.Body
import retrofit2.http.POST
import retrofit2.http.Path

interface SeatService {

    @POST("/api/v1/AirLines/SeatMap/{air}")
    suspend fun postSeatMap(
        @Path("air") air: String?,
        @Body request: GetSeatMap?
    ): HttpData<SeatMap>
}