package com.hqt.view.ui.flightwaches

import android.annotation.SuppressLint
import android.app.Activity
import android.app.ProgressDialog
import android.content.*
import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import android.view.animation.AnimationSet
import android.view.animation.AnimationUtils
import android.view.animation.DecelerateInterpolator
import android.view.animation.RotateAnimation
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.content.res.AppCompatResources
import androidx.appcompat.widget.AppCompatButton
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.lifecycle.ViewModelProviders
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.android.volley.VolleyError
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.snackbar.Snackbar
import com.google.firebase.analytics.FirebaseAnalytics
import com.hqt.data.model.FlightWatches
import com.hqt.data.model.PaymeConfig
import com.hqt.datvemaybay.AirportSearch
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.PnrActivity
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ActivityFlightWatchesAddBinding
import com.hqt.util.AppConfigs
import com.hqt.util.AppController
import com.hqt.util.SSLSendRequest
import com.hqt.util.amlich.AmLich
import com.hqt.view.ui.BaseActivityKt
import com.hqt.viewmodel.FlightWatchesViewModel
import com.mikepenz.iconics.context.IconicsContextWrapper
import org.json.JSONException
import org.json.JSONObject

import java.text.SimpleDateFormat
import java.util.*

class NewFlightWatchesActivity : BaseActivityKt<ActivityFlightWatchesAddBinding>() {

    override val layoutId: Int = R.layout.activity_flight_watches_add
    lateinit var dialog: BottomSheetDialog
    lateinit var coordinatorLayout: CoordinatorLayout
    private lateinit var selectPassengerView: View
    lateinit var viewModel: FlightWatchesViewModel
    var flightWatches = FlightWatches()
    var adultCount = 1
    var childCount = 0
    var infantCount = 0

    var isRoundTrip = false;
    private val REQUEST_CODE_FROM = 0
    private val REQUEST_CODE_TO = 1
    private val REQUEST_CODE_DEP_DATE = 2
    private val REQUEST_CODE_RE_DATE = 3
    private val REQUEST_CODE_RANGE_DATE = 4

    val dF = SimpleDateFormat("dd/MM/yyyy", Locale.US)

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        coordinatorLayout = findViewById(R.id.coordinatorLayout)
        selectPassengerView = layoutInflater.inflate(R.layout.select_passenger_layout, null)
        viewModel = ViewModelProviders.of(this).get(FlightWatchesViewModel::class.java)


        getViewBindding().viewModel = viewModel
        getViewBindding().lifecycleOwner = this


        firebaseAnalytics.setCurrentScreen(this, "flight_watches_add", null)

        var depDate = Calendar.getInstance()
        var retDate = Calendar.getInstance()

        depDate.add(Calendar.DAY_OF_MONTH, 3)
        retDate.add(Calendar.DAY_OF_MONTH, 5)
        if (intent.hasExtra("origin")) {


            getViewBindding().originCode.text = intent.getStringExtra("origin")
            getViewBindding().destinationCode.text = intent.getStringExtra("destination")
            if(intent.hasExtra("departureDate")) {
                flightWatches.departure_date.value = Common.stringToDate(
                    intent.getStringExtra("departureDate"),
                    "yyyy-MM-dd"
                ).time
            }
            adultCount = intent.getIntExtra("adult", 1)
            getViewBindding().adultCount.text = adultCount.toString()
            childCount = intent.getIntExtra("child", 0)
            getViewBindding().childCount.text = childCount.toString()
            infantCount = intent.getIntExtra("infant", 0)
            getViewBindding().infantCount.text = infantCount.toString()

            if (intent.hasExtra("autoSaved")) {
                saveClick()
            }

        } else {
            flightWatches.departure_date.value = depDate.time
            flightWatches.return_date.value = retDate.time
        }
        viewModel.flightWatches.postValue(flightWatches)

        getToolbar().title = "Tạo thông báo giá vé"
        getToolbar().setNavigationIcon(R.drawable.ic_action_back_home)
        supportActionBar!!.setDisplayShowHomeEnabled(true)
        initClickAction()
        selectPassger()

        if (!isUserSigned) {
            showSnackbarMessage("Vui lòng đăng nhập để tiếp tục", R.color.green, 3000, View.TEXT_ALIGNMENT_CENTER)
            signIn()
        }

    }


    private fun initClickAction() {
        getViewBindding().selectPaxButton.setOnClickListener {
            if (!dialog.isShowing) {
                dialog.show()
            }
        }
        val i = Intent(applicationContext, AirportSearch::class.java)

        getViewBindding().selectOrigin.setOnClickListener {
            startActivityForResult(i, REQUEST_CODE_FROM)
        }
        getViewBindding().selectDestination.setOnClickListener {
            startActivityForResult(i, REQUEST_CODE_TO)
        }

        setDateToView(false)
        getViewBindding().checkOneWay.setOnClickListener { // TODO Auto-generated method stub
            isRoundTrip = false
            setDateToView(false)

            getViewBindding().checkOneWay.background = (resources.getDrawable(R.drawable.button_one_way))
            getViewBindding().checkOneWay.setTextColor(Color.parseColor("#FFFFFF"))

            getViewBindding().checkRoundtrip.setBackgroundColor(resources.getColor(R.color.fui_transparent))
            getViewBindding().checkRoundtrip.setTextColor(Color.parseColor("#00a2e3"))

        }

        getViewBindding().sendZalo.setOnClickListener {
            Toast.makeText(this, "Quan Tâm Zalo Page để nhận thông báo", Toast.LENGTH_SHORT).show()
            if (getViewBindding().sendZalo.isChecked) {
                val uri = Uri.parse("https://zalo.me/4377741633721725644")
                val intent = Intent(Intent.ACTION_VIEW, uri)
                startActivity(intent)
            }
        }
        getViewBindding().swRangeDate.setOnClickListener {
            getViewBindding().checkOneWay.performClick()
        }
        getViewBindding().checkRoundtrip.setOnClickListener {

            getViewBindding().swRangeDate.isChecked = false

            if (flightWatches.return_date.value == null) flightWatches.return_date.value = Common.addDay(flightWatches.departure_date.value,
                3)

            if (flightWatches.return_date.value != null && flightWatches.return_date.value!!.after(flightWatches.departure_date.value)) {
                setDateToView(true)
            } else {

                flightWatches.return_date.value = Common.addDay(flightWatches.departure_date.value, 3)
                setDateToView(true)
            }

            isRoundTrip = true

            getViewBindding().checkRoundtrip.background = AppCompatResources.getDrawable(this, R.drawable.button_return)
            getViewBindding().checkRoundtrip.setTextColor(Color.parseColor("#FFFFFF"))

            getViewBindding().checkOneWay.setBackgroundColor(resources.getColor(R.color.fui_transparent))
            getViewBindding().checkOneWay.setTextColor(Color.parseColor("#00a2e3"))
        }

        getViewBindding().selectDepDate.setOnClickListener {
            try {
                AppConfigs.Log("flightWatches.departure_date.value", flightWatches.departure_date.value.toString())
                val i = Intent(applicationContext, AmLich::class.java)
                i.putExtra("isSearchTrain", true)
                i.putExtra("depDate", dF.format(flightWatches.departure_date.value!!.time))
                i.putExtra("origin", getViewBindding().originCode.text)
                i.putExtra("destination", getViewBindding().destinationCode.text)

                if (getViewBindding().swRangeDate.isChecked) {
                    i.putExtra("act", "RANGEDATE")
                    i.putExtra("depDateEnd", dF.format(flightWatches.departure_date_end.value!!.time))
                    startActivityForResult(i, REQUEST_CODE_RANGE_DATE)
                } else startActivityForResult(i, REQUEST_CODE_DEP_DATE)

            } catch (except: Exception) {

            }
        }

        getViewBindding().selectRetDate.setOnClickListener {
            try {
                getViewBindding().checkRoundtrip.performClick()

                val i = Intent(applicationContext, AmLich::class.java)
                i.putExtra("depDate", dF.format(flightWatches.departure_date.value!!.time))
                i.putExtra("reDate", dF.format(flightWatches.return_date.value!!.time))
                i.putExtra("origin", getViewBindding().originCode.text)
                i.putExtra("destination", getViewBindding().destinationCode.text)

                startActivityForResult(i, REQUEST_CODE_RE_DATE)
                setDateToView(true)

            } catch (except: Exception) {
                except.printStackTrace()
            }
        }

        getViewBindding().swapRouteLayout.setOnClickListener { getViewBindding().swapRoute.performClick() }

        getViewBindding().swapRoute.setOnClickListener {
            val tem = getViewBindding().originCode.text
            val temName = getViewBindding().originName.text

            getViewBindding().originName.text = getViewBindding().destinationName.text
            getViewBindding().originCode.text = getViewBindding().destinationCode.text

            getViewBindding().destinationCode.text = (tem)
            getViewBindding().destinationName.text = (temName)

            val animSet = AnimationSet(true)
            animSet.interpolator = DecelerateInterpolator()
            animSet.fillAfter = true
            animSet.isFillEnabled = true

            val animRotate = RotateAnimation(0.0f,
                360.0f,
                RotateAnimation.RELATIVE_TO_SELF,
                0.5f,
                RotateAnimation.RELATIVE_TO_SELF,
                0.5f)

            animRotate.duration = 200
            animRotate.fillAfter = true
            animSet.addAnimation(animRotate)
            getViewBindding().swapRoute.startAnimation(animSet)
        }

        getViewBindding().search.setOnClickListener {
            saveClick()

        }

    }

    private fun saveClick() {
        if (isInternetConnected) {

            if (isRoundTrip && flightWatches.departure_date.value!!.after(flightWatches.return_date.value)) {
                Toast.makeText(this, "Ngày chuyến bay lượt về phải sau ngày lượt đi !", Toast.LENGTH_SHORT).show()
            } else if (getViewBindding().originCode.text.length != 3) {
                Toast.makeText(this, "Vui lòng chọn điểm đi !", Toast.LENGTH_SHORT).show()

            } else if (getViewBindding().destinationCode.text.length != 3) {
                Toast.makeText(this, "Vui lòng chọn điểm đến !", Toast.LENGTH_SHORT).show()

            } else if (getViewBindding().originCode.text.trim() == getViewBindding().destinationCode.text.trim()) {
                Toast.makeText(this, "Vui lòng chọn điểm đến và điểm đi khác nhau !", Toast.LENGTH_SHORT).show()

            } else if (firebaseUser == null) {
                Toast.makeText(this, "Vui đăng nhập để tiếp tục !", Toast.LENGTH_SHORT).show()

            } else {
                if (!getViewBindding().swRangeDate.isChecked) {
                    flightWatches.departure_date_end.value = null
                }

                flightWatches.origin_code = getViewBindding().originCode.text.toString()
                flightWatches.destination_code = getViewBindding().destinationCode.text.toString()
                flightWatches.adult = adultCount
                flightWatches.child = childCount
                flightWatches.infant = infantCount
                flightWatches.is_round_trip = isRoundTrip
                flightWatches.uid = firebaseUser!!.uid

                if (getViewBindding().txtMaxPrice.text?.trim().isNullOrEmpty()) {
                    flightWatches.max_price = 0
                } else flightWatches.max_price = Common.parserInt(getViewBindding().txtMaxPrice.text.toString())

                storeFlightWatches()
                overridePendingTransition(R.anim.enter, R.anim.exit)
            }
        } else {
            Common.showAlertDialog(this,
                "Không có Internet",
                "Xin vui lòng kiểm tra lại Wifi/3G để tiếp tục",
                false,
                true)
        }
    }

    private fun storeFlightWatches() {

        val dialog = ProgressDialog(this)
        dialog.setMessage("Đang tạo thông báo giá vé ...\nVui lòng đợi giây lát!")
        dialog.isIndeterminate = false
        dialog.max = 100
        dialog.setCanceledOnTouchOutside(false)
        dialog.setCancelable(false)
        dialog.setProgressStyle(ProgressDialog.STYLE_SPINNER)
        dialog.show()

        var postParam = JSONObject()
        try {
            postParam = JSONObject(AppController.instance.gSon.toJson(flightWatches, FlightWatches::class.java))
            postParam.put("gcm", Common.FCM_TOKEN)

        } catch (e: JSONException) {
            e.printStackTrace()
        }
        val params = Bundle()
        params.putString(FirebaseAnalytics.Param.CURRENCY, "VND")
        params.putInt(FirebaseAnalytics.Param.PRICE, flightWatches.max_price)
        params.putInt(FirebaseAnalytics.Param.PRICE, flightWatches.max_price)
        params.putString(FirebaseAnalytics.Param.ORIGIN, flightWatches.origin_code)
        params.putString(FirebaseAnalytics.Param.DESTINATION, flightWatches.destination_code)
        params.putString(FirebaseAnalytics.Param.ITEM_NAME, flightWatches.origin_code + flightWatches.destination_code)
        params.putInt(FirebaseAnalytics.Param.NUMBER_OF_PASSENGERS, flightWatches.getPaxCount())

        firebaseAnalytics.logEvent(FirebaseAnalytics.Event.ADD_TO_WISHLIST, params)
        SSLSendRequest(this).POST(false, "FlightWatches/Store", postParam, object : SSLSendRequest.CallBackInterface {
            override fun onSuccess(response: JSONObject, cached: Boolean) {
                try {
                    if (response.has("data")) {
                        val json = response.getJSONObject("data")
                        var flightWatches = AppController.instance.gSon.fromJson(json.toString(),
                            FlightWatches::class.java)
                        dialog.dismiss()
                        showSnackbarMessage("Tạo theo dõi giá thành công",
                            R.color.green,
                            1000,
                            View.TEXT_ALIGNMENT_CENTER)
                        Handler(Looper.getMainLooper()).postDelayed({
                            finish()
                        }, 1000)

                    }

                } catch (e: JSONException) {
                    dialog.dismiss()
                    Toast.makeText(applicationContext,
                        "Thật xin lỗi :( \nCó lỗi xảy ra khi kết nối máy chủ đặt chỗ \n Vui lòng thử lại hoặc gọi " + AppConfigs.getInstance().config.getString(
                            "hotline") + " để được hỗ trợ!",
                        Toast.LENGTH_SHORT).show()
                    AppConfigs.logException(e)

                }
            }

            override fun onFail(e: VolleyError) {
                dialog.dismiss()
                Toast.makeText(applicationContext,
                    "Thật xin lỗi :( \nCó lỗi xảy ra khi kết nối máy chủ \n Vui lòng thử lại hoặc gọi " + AppConfigs.getInstance().config.getString(
                        "hotline") + " để được hỗ trợ!",
                    Toast.LENGTH_SHORT).show()
                AppConfigs.logException(e)
                e.printStackTrace()
            }
        })
    }

    //    override fun attachBaseContext(newBase: Context) {
    //        super.attachBaseContext(IconicsContextWrapper.wrap(newBase))
    //    }


    private fun selectPassger() {
        try {
            dialog = BottomSheetDialog(this)
            dialog.setContentView(selectPassengerView)

            val txtAdult = selectPassengerView.findViewById<TextView>(R.id.sheet_adult_number)
            val txtChild = selectPassengerView.findViewById<TextView>(R.id.sheet_child_number)
            val txtInfant = selectPassengerView.findViewById<TextView>(R.id.sheet_infant_number)

            selectPassengerView.findViewById<LinearLayout>(R.id.select_older).visibility = (View.GONE)
            selectPassengerView.findViewById<LinearLayout>(R.id.select_student).visibility = (View.GONE)
            selectPassengerView.findViewById<LinearLayout>(R.id.select_infant).visibility = (View.VISIBLE)


            val inAdult = selectPassengerView.findViewById<LinearLayout>(R.id.btn_in_adult)
            val deAdult = selectPassengerView.findViewById<LinearLayout>(R.id.btn_de_adult)

            val inChild = selectPassengerView.findViewById<LinearLayout>(R.id.btn_in_child)
            val deChild = selectPassengerView.findViewById<LinearLayout>(R.id.btn_de_child)

            val inInfant = selectPassengerView.findViewById<LinearLayout>(R.id.btn_in_infant)
            val deInfant = selectPassengerView.findViewById<LinearLayout>(R.id.btn_de_infant)

            val choicePassengerBtn = selectPassengerView.findViewById<AppCompatButton>(R.id.select_passenger_button)
            choicePassengerBtn.setOnClickListener { if (dialog.isShowing()) dialog.dismiss() }


            val inAnim = AnimationUtils.loadAnimation(this, android.R.anim.fade_in)
            inAnim.duration = 250

            inAdult.setOnClickListener {
                if (infantCount + adultCount + childCount <= 9) {
                    adultCount++
                    txtAdult.text = adultCount.toString()
                    getViewBindding().adultCount.text = adultCount.toString()
                    txtAdult.startAnimation(inAnim)

                } else {
                    Toast.makeText(applicationContext, "Tối đa 4 hành khách", Toast.LENGTH_SHORT).show()
                }
            }
            deAdult.setOnClickListener {
                if (adultCount > 0 && infantCount + adultCount + childCount <= 9) {
                    adultCount--
                    txtAdult.text = adultCount.toString()
                    getViewBindding().adultCount.text = adultCount.toString()
                    txtAdult.startAnimation(inAnim)

                }
            }
            inChild.setOnClickListener {
                if (infantCount + adultCount + childCount < 9) {
                    childCount++
                    txtChild.text = childCount.toString()
                    getViewBindding().childCount.text = childCount.toString()
                    txtChild.startAnimation(inAnim)
                } else {
                    Toast.makeText(applicationContext, "Tối đa 9 hành khách ", Toast.LENGTH_SHORT).show()
                }
            }
            deChild.setOnClickListener {
                if (childCount > 0 && infantCount + adultCount + childCount <= 9) {
                    childCount--
                    txtChild.text = childCount.toString()
                    getViewBindding().childCount.text = childCount.toString()
                    txtChild.startAnimation(inAnim)
                }
            }

            inInfant.setOnClickListener {
                if (infantCount + adultCount + childCount < 9 && infantCount < adultCount) {
                    infantCount++
                    getViewBindding().infantCount.text = infantCount.toString()
                    txtInfant.text = infantCount.toString()
                    txtInfant.startAnimation(inAnim)
                }
            }
            deInfant.setOnClickListener {
                if (infantCount > 0 && infantCount + adultCount + childCount < 9) {
                    infantCount--
                    txtInfant.text = infantCount.toString()
                    getViewBindding().infantCount.text = infantCount.toString()
                    txtInfant.startAnimation(inAnim)
                }
            }


        } catch (E: Exception) {
            AppConfigs.logException(E)
        }

    }

    @SuppressLint("MissingSuperCall") override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {


        if (resultCode == Activity.RESULT_OK && requestCode == REQUEST_CODE_FROM) {
            if (data!!.hasExtra("code")) {
                getViewBindding().originName.text = (data.extras!!.getString("name"))
                getViewBindding().originCode.text = (data.extras!!.getString("code"))
            }
        } else if (resultCode == Activity.RESULT_OK && requestCode == REQUEST_CODE_TO) {
            if (data!!.hasExtra("code")) {

                getViewBindding().destinationName.text = (data.extras!!.getString("name"))
                getViewBindding().destinationCode.text = (data.extras!!.getString("code"))
            }
        } else if (resultCode == Activity.RESULT_OK && requestCode == REQUEST_CODE_DEP_DATE) {
            if (data!!.hasExtra("date")) {
                flightWatches.departure_date.value = Common.getDateFromString(data.extras!!.getString("date")).time
                setDateToView(false)

                if (flightWatches.return_date.value!!.after(flightWatches.departure_date.value!!) && isRoundTrip) {
                    getViewBindding().checkRoundtrip.performClick()
                }
            }
        } else if (resultCode == Activity.RESULT_OK && requestCode == REQUEST_CODE_RE_DATE) {
            if (data!!.hasExtra("date")) {

                flightWatches.return_date.value = Common.getDateFromString(data.extras!!.getString("date")).time
                setDateToView(true)
            }
        } else if (resultCode == Activity.RESULT_OK && requestCode == REQUEST_CODE_RANGE_DATE) {
            if (data!!.hasExtra("date")) {
                flightWatches.departure_date.value = Common.getDateFromString(data.extras!!.getString("date")).time
                flightWatches.departure_date_end.value = Common.getDateFromString(data.extras!!.getString("dateEnd")).time

                setDateToView(false)
            }
        }
    }

    private fun setDateToView(isReturn: Boolean) {
        try {
            if (getViewBindding().swRangeDate.isChecked) {
                flightWatches.type = "RANGE"
                getViewBindding().selectRetDate.visibility = View.GONE
                if (flightWatches.departure_date_end.value == null || flightWatches.departure_date.value!!.after(
                        flightWatches.departure_date_end.value)) {
                    flightWatches.departure_date_end.value = Common.addDay(flightWatches.departure_date.value, 3)
                }
                getViewBindding().txtDepDate.text = (Common.dateToString(flightWatches.departure_date.value,
                    "DOW, dd/MM/yy") + " - " + Common.dateToString(flightWatches.departure_date_end.value,
                    "DOW, dd/MM/yy"))

            } else {
                flightWatches.type = "TREND"
                if (isReturn) {
                    getViewBindding().txtRetDate.text = (Common.dateToString(flightWatches.return_date.value,
                        "DOW, dd/MM/yy"))
                } else {
                    getViewBindding().txtDepDate.text = (Common.dateToString(flightWatches.departure_date.value,
                        "DOW, dd/MM/yy"))
                    if (!flightWatches.is_round_trip) {
                        getViewBindding().txtRetDate.text = ""
                    }
                }
                getViewBindding().selectRetDate.visibility = View.VISIBLE
            }


        } catch (e: Exception) {
            AppConfigs.Log("setDepDateString ", e.toString())
        }

    }

    private val mMessageReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) { // Get extra data included in the Intent

            val message = intent.getStringExtra("message").toString()
            val snackbar = Snackbar.make(coordinatorLayout, message, 10000).setAction("XEM CHI TIẾT") {
                val i = Intent(applicationContext, PnrActivity::class.java)
                i.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                startActivity(i)
            }
            snackbar.show()

        }
    }

    override fun onBackPressed() {
        super.onBackPressed() //overridePendingTransition(R.anim.left_to_right, R.anim.right_to_left)
    }

    override fun onStart() {
        super.onStart()
        LocalBroadcastManager.getInstance(this).registerReceiver(mMessageReceiver, IntentFilter("bookingupdate"))

    }

}