package com.hqt.view.ui.bus


import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.tabs.TabLayout
import com.hqt.data.model.response.BusSeatMapInfo
import com.hqt.data.model.response.StationPoints
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.FragmentBusPointSelectListBinding


class BusPointSelectFragment : Fragment() {
    lateinit var recyclerView: RecyclerView

    private var _binding: FragmentBusPointSelectListBinding? = null
    private val binding get() = _binding!!
    private var seatMapInfo: BusSeatMapInfo? = null
    private lateinit var mAdapter: BusStationPointAdapter

    private var listPoints: ArrayList<StationPoints> = ArrayList()
    private var isPickUp = true

    private var pickUpPoint: StationPoints? = null
    private var dropOffPoint: StationPoints? = null


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentBusPointSelectListBinding.inflate(inflater, container, false)

        getViewBindding().bottomSheet.findViewById<Button>(R.id.btnNext).setOnClickListener {
            if (dropOffPoint == null) {
                getViewBindding().tabLayout.getTabAt(1)?.select()
                Toast.makeText(requireContext(), "Vui lòng chọn điểm xuống xe", Toast.LENGTH_SHORT)
                    .show()
            } else if (pickUpPoint == null) {
                getViewBindding().tabLayout.getTabAt(0)?.select()
                Toast.makeText(requireContext(), "Vui lòng chọn điểm đón", Toast.LENGTH_SHORT)
                    .show()
            } else (activity as BusSelectActivity).nextBookingInput(pickUpPoint!!, dropOffPoint!!)

        }

        getViewBindding().bottomSheet.findViewById<Button>(R.id.btnBack).setOnClickListener {
            (activity as BusSelectActivity).backClick()
        }

        getViewBindding().btnBack.setOnClickListener {
            (activity as BusSelectActivity).backClick()
        }

        getViewBindding().tabLayout.addTab(getViewBindding().tabLayout.newTab().setText("Điểm đón"))
        getViewBindding().tabLayout.addTab(getViewBindding().tabLayout.newTab().setText("Điểm trả"))

        getViewBindding().tabLayout.addOnTabSelectedListener(object :
            TabLayout.OnTabSelectedListener {
            override fun onTabReselected(p0: TabLayout.Tab?) {
            }

            override fun onTabUnselected(p0: TabLayout.Tab?) {

            }

            override fun onTabSelected(p0: TabLayout.Tab?) {

                if (getViewBindding().tabLayout.selectedTabPosition == 0) {
                    initStationList(seatMapInfo, true)
                } else {
                    initStationList(seatMapInfo, false)
                }

            }


        })

        getViewBindding().toolbar.setNavigationIcon(R.drawable.ic_action_back_home)
        getViewBindding().toolbar.setNavigationOnClickListener {
            getViewBindding().btnBack.performClick()
        }
        getViewBindding().toolbar.title = "Chọn điểm lên xuống xe"

        recyclerView = binding.recyclerView
        recyclerView.setHasFixedSize(true)
        recyclerView.layoutManager = LinearLayoutManager(activity)
        mAdapter = BusStationPointAdapter((activity as AppCompatActivity), listPoints)
        recyclerView.adapter = mAdapter


        return binding.root
    }

    fun clearSelectPoint(stationPoints: StationPoints) {
        listPoints.forEach {
            if (it != stationPoints || !stationPoints.selected) {
                it.selected = false
            }
            if (it == stationPoints && stationPoints.selected) {
                if (isPickUp) {
                    pickUpPoint = stationPoints
                    getViewBindding().busSelectStation.txtPickUp.text =
                        stationPoints.time + " " + stationPoints.name
                } else {
                    dropOffPoint = stationPoints
                    getViewBindding().busSelectStation.txtDropoffSelect.text =
                        stationPoints.time + " " + stationPoints.name
                }
            }

        }
        mAdapter.notifyDataSetChanged()
    }

    private fun getViewBindding(): FragmentBusPointSelectListBinding {
        return _binding!!
    }

    fun reinit(seatData: BusSeatMapInfo?, isPu: Boolean) {

        seatMapInfo = null
        pickUpPoint = null
        dropOffPoint = null
        listPoints.clear()

        getViewBindding().tabLayout.getTabAt(0)?.select()

        getViewBindding().busSelectStation.txtPickUp.text = "Vui lòng chọn"
        getViewBindding().busSelectStation.txtDropoffSelect.text = "Vui lòng chọn"

        initStationList(seatData, isPu)
    }

    fun initStationList(seatData: BusSeatMapInfo?, isPu: Boolean) {
        isPickUp = isPu


        if (seatData != null) {

            listPoints.clear()
            if (isPickUp) {
                listPoints.addAll(seatData.pickupPoints)

            } else {
                listPoints.addAll(seatData.dropOffPoints)
            }
            mAdapter.notifyDataSetChanged()

            if (listPoints.size > 0) {
                seatMapInfo = seatData
                getViewBindding().shimmerViewContainer.stopShimmer()
                getViewBindding().shimmerViewContainer.visibility = View.GONE

                getViewBindding().emptyState.visibility = View.GONE
                getViewBindding().bottomSheet.visibility = View.VISIBLE
            } else {
                getViewBindding().emptyState.visibility = View.VISIBLE
                getViewBindding().shimmerViewContainer.stopShimmer()
                getViewBindding().shimmerViewContainer.visibility = View.GONE
            }
        }
    }

    companion object {
        fun newInstance(): BusPointSelectFragment {
            val x = BusPointSelectFragment()
            Handler(Looper.getMainLooper()).postDelayed({
                x.initStationList(null, true)
            }, 1000)

            return x
        }
    }
}