package com.hqt.view.ui.seatmap.ui

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.hqt.base.model.State
import com.hqt.data.model.AddOnInfo
import com.hqt.data.model.AddOnType
import com.hqt.data.model.PaxInfoList
import com.hqt.data.model.request.GetSeatMap
import com.hqt.util.AppConfigs
import com.hqt.util.Log
import com.hqt.view.ui.booking.data.model.BookingV3
import com.hqt.view.ui.seatmap.SeatMap
import com.hqt.view.ui.seatmap.data.api.SeatApiHelper
import com.hqt.view.ui.seatmap.ui.view.SeatViewV2
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import javax.inject.Inject


@HiltViewModel
class SeatViewModel @Inject constructor(
    private val seatApiHelper: SeatApiHelper
) : ViewModel() {




    var flightK = ""



    var doneClick = MutableLiveData<Boolean>()




    private val _seatListLiveData: MutableLiveData<State<SeatMap>> = MutableLiveData()
    val seatListLiveData: LiveData<State<SeatMap>> get() = _seatListLiveData








//    fun getAllowSelect(): Boolean {
//        val totalSelected = seatViewList.count { seatView -> seatView.isSelect }
//
//        Log.d("getAllowSelect", totalSelected < totalPax || totalPax == 1)
//        return totalSelected < totalPax || totalPax == 1
//    }


    fun postSeatMap(provider: String?) {
        viewModelScope.launch(Dispatchers.IO) {

            try {

                _seatListLiveData.postValue(State.Loading)

                val request = GetSeatMap(
                    flightKey = flightK
                )

                val result = seatApiHelper.postSeatMap(provider, request)

                if (result.status){
                    _seatListLiveData.postValue(State.Success(result.data ?: SeatMap()))
                }else{
                    _seatListLiveData.postValue(State.Error(Throwable(result.message)))
                }

            }catch (ex : Exception){
                _seatListLiveData.postValue(State.Error(ex))
                Log.logException(ex)
            }



        }


    }




}