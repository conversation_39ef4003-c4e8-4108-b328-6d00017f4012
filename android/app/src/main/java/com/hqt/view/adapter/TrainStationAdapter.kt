package com.hqt.view.adapter


import android.app.Activity
import android.content.Context
import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView

import androidx.recyclerview.widget.RecyclerView

import com.hqt.datvemaybay.R
import com.hqt.data.model.TrainStation

/**
 * Created by TN on 12/1/2016.
 */
class TrainStationAdapter(internal var mContext: Context, internal var contents: List<TrainStation>) : RecyclerView.Adapter<TrainStationAdapter.ViewHolder>() {
    internal val RESULT_OK = -1

    inner class ViewHolder(v: View) : RecyclerView.ViewHolder(v), View.OnClickListener {
        // each data item is just a string in this case
        var txtAirportName: TextView = v.findViewById(R.id.stationName)
        var txtAirportCode: TextView = v.findViewById(R.id.stationCode)
        var imgBanner: ImageView = v.findViewById(R.id.image)

        init {

            v.setOnClickListener(this)
        }

        override fun onClick(v: View) {

            val data = Intent()
            data.putExtra("code", contents[adapterPosition].code)
            data.putExtra("name", contents[adapterPosition].name)
            (mContext as Activity).setResult(RESULT_OK, data)
            (mContext as Activity).finish()
        }
    }

    override fun getItemCount(): Int {
        return contents.size
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        var view: View? = null

        view = LayoutInflater.from(parent.context)
                .inflate(R.layout.list_trainstation_item, parent, false)

        return ViewHolder(view)
    }


    override fun onBindViewHolder(holder: ViewHolder, position: Int) {

        holder.txtAirportName.text = contents[position].name
        holder.txtAirportCode.text = contents[position].code
        holder.imgBanner.setImageResource(R.drawable.ic_transportation)

    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getItemViewType(position: Int): Int {
        return position
    }

    companion object {
        internal val TYPE_HEADER = 0
        internal val TYPE_CELL = 1
    }


}