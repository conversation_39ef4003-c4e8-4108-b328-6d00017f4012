package com.hqt.view.ui.tour


import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.view.WindowManager
import android.webkit.WebView
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import com.hqt.data.model.BookingTour
import com.hqt.data.model.SliderItem
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.util.AppConfigs
import com.hqt.util.AppController
import com.hqt.util.Widget
import com.hqt.view.adapter.SliderAdapterCustom
import com.hqt.view.ui.BaseActivityKt
import com.hqt.view.ui.reward.ui.activity.RewardActivity
import com.hqt.view.ui.booking.BookingActivity
import com.hqt.viewmodel.BookingViewModel
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import q.rorbin.badgeview.QBadgeView


class TourDetailActivity() : BaseActivityKt<com.hqt.datvemaybay.databinding.ActivityTourDetailLayoutBinding>() {


    override val layoutId: Int = R.layout.activity_tour_detail_layout
    lateinit var viewModel: BookingViewModel
    var tourList: ArrayList<TourList> = ArrayList()
    private var disposable: Disposable? = null
    lateinit var sliderAdapter: SliderAdapterCustom
    var tourItem: TourItem? = null
    var tourDeparture: DepartureDates? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        getToolbar().title = "Bạn muốn đi đâu"
        getToolbar().setNavigationIcon(R.drawable.ic_action_back_home)
        supportActionBar!!.setDisplayShowHomeEnabled(true)

        getViewBindding().lifecycleOwner = this


        setWindowFlag(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS, false)
        window.statusBarColor = ContextCompat.getColor(this, R.color.primary_dark)
        sliderAdapter = SliderAdapterCustom(this, false)
        getViewBindding().slider.setSliderAdapter(sliderAdapter)
        if (intent.hasExtra("tourItemExtra")) {
            tourItem = intent.getSerializableExtra("tourItemExtra") as TourItem
            getTourDetail(tourItem!!.id.toString())
            getToolbar().title = tourItem!!.title
            getToolbar().subtitle = tourItem!!.extra
        } else {
            if (intent.hasExtra("link")) {
                val link = intent.getStringExtra("link")
                val catId = link!!.replace("https://12bay.vn/tour/detail/", "")
                getTourDetail(tourItem!!.id.toString())
            }
        }

        iniToolbar()

        getViewBindding().selectSchedule.btnNext.setOnClickListener {

            AlertDialog.Builder(this).setIcon(R.drawable.ic_tour).setTitle("Bạn cần hỗ trợ đặt tour du lịch")
                .setMessage("Liên hệ với 12bay để được hỗ trợ tư vấn tốt nhất nhé !.")
                .setPositiveButton("Gọi điện thoại") { dialog, which ->
                    var p = ""
                    val i = Intent(Intent.ACTION_DIAL)

                    p = "tel:" + AppConfigs.getInstance().config.getString("hotline")

                    i.data = Uri.parse(p)
                    startActivity(i)
                }.setNegativeButton("Nhắn Zalo") { dialog, which ->

                    val uri = Uri.parse("https://zalo.me/4377741633721725644")
                    val intent = Intent(Intent.ACTION_VIEW, uri)
                    intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    startActivity(intent)
                }.setNeutralButton("Gửi Tin Nhắn") { dialog, which ->

                    Common.sendSms(applicationContext,
                        Common.unAccent("Hi 12bay. Minh can ho tro dat tour: " + tourItem?.title),
                        AppConfigs.getInstance().config.getString("sms"))

                }.show()
        }

        initAnalytics()
    }

    fun sendBooking(tour: TourDetail) {

        val bookingTour = BookingTour()
        val tourDetail = TourBooking()

        tourDetail.item = tourItem
        tourDetail.departureDate = tourDeparture
        tourDetail.tourInfo = tour.tourInfo
        tourDetail.toLocations = tour.toLocations
        tourDetail.startLocations = tour.startLocations


        bookingTour.departure_f = tourDetail
        bookingTour.is_round_trip = false
        bookingTour.departure_date = tourDeparture?.fromDate!!
        bookingTour.origin_code = tour.startLocations[0].identify!!
        bookingTour.destination_code = tour.toLocations[0].identify!!
        bookingTour.total = tourDeparture!!.estimatePriceVat!!

        val vi = Intent(applicationContext, BookingActivity::class.java)
        vi.putExtra("bookingDetail", bookingTour)
        vi.putExtra("BookingTour", true)
        startActivity(vi)
        overridePendingTransition(R.anim.enter, R.anim.exit)
    }


    fun getTourDetail(tourId: String) {

        disposable = AppController.instance.getService().getTourDetail(tourId).subscribeOn(Schedulers.io())
            .doOnSubscribe {

            }.observeOn(AndroidSchedulers.mainThread()).subscribe({ response ->

                if (response.data != null) {

                    genTourDetail(response.data)
                    getViewBindding().shimmerViewContainer.stopShimmer()
                    getViewBindding().shimmerViewContainer.visibility = View.GONE
                    getViewBindding().sliderLayout.visibility = View.VISIBLE
                    getViewBindding().container.visibility = View.VISIBLE


                } else { // getViewBindding().emptyState.visibility = View.VISIBLE
                    getViewBindding().shimmerViewContainer.stopShimmer()
                    getViewBindding().shimmerViewContainer.visibility = View.GONE
                    getViewBindding().container.visibility = View.GONE
                }

            }, { throwable ->

                getViewBindding().shimmerViewContainer.stopShimmer()
                getViewBindding().shimmerViewContainer.visibility = View.GONE
                getViewBindding().container.visibility = View.GONE

                throwable.printStackTrace()
            })
    }

    private fun genTourDetail(tour: TourDetail) {
        try {
            val listSlider: ArrayList<SliderItem> = ArrayList()

            getToolbar().title = tour.title


            tour.photos.forEach {
                listSlider.add(SliderItem(it.path, "", it.path))
            }
            sliderAdapter.renewItems(listSlider)

            tour.plans.forEach {

                Widget.createTourDetailPlan(this, it, getViewBindding().plansContainer)
            }
            getViewBindding().tourTitle.text = tourItem!!.title

            getViewBindding().tourInfo.tourInfo = tour.tourInfo
            val webView = WebView(this)
            webView.loadUrl(tour.note!!);
            getViewBindding().htmlText.addView(webView)


            tour.departureDates.forEach {
                Widget.createTourDetailSchedule(this, it, getViewBindding().scheduleView.scheduleTable)
            }

            if (tour.departureDates.size > 0) {
                getViewBindding().scheduleLayout.visibility = View.VISIBLE
                getViewBindding().selectSchedule.schedule = tour.departureDates[0]
            } else {
                getViewBindding().scheduleLayout.visibility = View.GONE
            }


        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun selectTourSchedule(departureDate: DepartureDates) {
        tourDeparture = departureDate
        getViewBindding().selectSchedule.schedule = departureDate
    }

    private fun initAnalytics() {
        try {

            val params = Bundle()

            firebaseAnalytics.logEvent("activity_view", params)
            firebaseAnalytics.setCurrentScreen(this, "tour_detail_view", null)

        } catch (e: Exception) {

        }
    }

    override fun onResume() {
        super.onResume()
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean { // Inflate the menu; this adds items to the action bar if it is present.
        menuInflater.inflate(R.menu.main_reward, menu)
        try {
            Handler(Looper.getMainLooper()).postDelayed({
                val rewardButton = findViewById(R.id.action_reward) as View
                QBadgeView(applicationContext).setGravityOffset(0f, 0f, true).bindTarget(rewardButton).setBadgeText("!")
                    .setOnDragStateChangedListener { dragState, badge, targetView ->
                    }
            }, 1000)
        } catch (e: java.lang.Exception) {
        }
        return true
    }


    override fun onOptionsItemSelected(item: MenuItem): Boolean { // Take appropriate action for each action item click
        when (item.itemId) {
            android.R.id.home -> {

                finish()
            }

            R.id.action_reward -> {
                val `in` = Intent(this, RewardActivity::class.java)

                startActivity(`in`)
                return true
            }

            else -> {
                finish()
            }
        }
        return false
    }

    override fun onSystemSettingChange(type: AppConfigs.SystemSettingType) {

        if (type == AppConfigs.SystemSettingType.INTERNET) {
            if (this::viewModel.isInitialized) {
                viewModel.updateInternetStatus(isInternetConnected)
            }
        }


    }


}
