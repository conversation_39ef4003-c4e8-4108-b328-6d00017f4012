package com.hqt.view.ui.flightwaches

import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.LinearLayout
import androidx.lifecycle.ViewModelProviders
import androidx.recyclerview.widget.RecyclerView
import com.android.volley.VolleyError
import com.bayvn.charts.BarData
import com.bayvn.charts.androidcharts.LineView
import com.hqt.data.model.FareRange
import com.hqt.data.model.FlightWatches
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.SearchResult
import com.hqt.datvemaybay.databinding.ActivityFlightWatchesViewBinding
import com.hqt.util.AppConfigs
import com.hqt.util.AppController
import com.hqt.util.HeightWrappingViewPager
import com.hqt.util.SSLSendRequest
import com.hqt.view.adapter.ViewPagerAdapter
import com.hqt.view.ui.BaseActivityKt
import com.hqt.view.ui.HomeActivity
import com.hqt.viewmodel.FlightWatchesViewModel
import org.json.JSONException
import org.json.JSONObject
import kotlin.collections.ArrayList


class FlightWachesViewActivity : BaseActivityKt<ActivityFlightWatchesViewBinding>() {

    override val layoutId: Int = R.layout.activity_flight_watches_view
    lateinit var recyclerView: RecyclerView
    lateinit var viewModel: FlightWatchesViewModel
    lateinit var flightWatches: FlightWatches
    private var showBaseFare = false
    lateinit var viewAdapter: ViewPagerAdapter
    var currentPos = -1


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        firebaseAnalytics.setCurrentScreen(this, "flight_watches_view", null)
        getToolbar().title = "Theo dõi giá vé"
        getToolbar().setNavigationIcon(R.drawable.ic_action_back_home)
        supportActionBar!!.setDisplayShowHomeEnabled(true)
        viewAdapter = ViewPagerAdapter(supportFragmentManager)

        viewModel = ViewModelProviders.of(this).get(FlightWatchesViewModel::class.java)
        getViewBindding().viewModel = viewModel
        getViewBindding().lifecycleOwner = this
        initFromIntent()

        Common.SHOWFULLPRICE = !showBaseFare

        getViewBindding().swShowBaseFare.setOnCheckedChangeListener { v, isChecked ->
            showBaseFare = !isChecked

            if (flightWatches.type == "RANGE") {
                genFareRangeChart(flightWatches)
            } else {
                showLineChart(flightWatches)
            }

            Common.SHOWFULLPRICE = !showBaseFare
            notifyDataChange()

        }
        getViewBindding().search.setOnClickListener { //NEW LA CHON XEM VE RE
            if (flightWatches.isActive()) {
                var intent = Intent(applicationContext, SearchResult::class.java)

                intent.putExtra("originCode", flightWatches.origin_code)
                intent.putExtra("destinationCode", flightWatches.destination_code)
                intent.putExtra("departureTime", Common.dateToString(flightWatches.departure_date.value, "yyyy-MM-dd"))
                if (flightWatches.return_date != null) intent.putExtra("returnTime",
                    Common.dateToString(flightWatches.return_date.value, "yyyy-MM-dd"))
                else intent.putExtra("returnTime",
                    Common.dateToString(flightWatches.departure_date.value, "yyyy-MM-dd"))
                intent.putExtra("adult", flightWatches.adult)
                intent.putExtra("child", flightWatches.child)
                intent.putExtra("infant", flightWatches.infant)
                intent.putExtra("isRoundTrip", flightWatches.is_round_trip)

                startActivity(intent)
                overridePendingTransition(R.anim.enter, R.anim.exit)
            }
        }
        setupViewPager(getViewBindding().viewpager)
        getListTask()

    }

    private fun initFromIntent() {
        flightWatches = FlightWatches()

        if (intent.getBooleanExtra("fromNoti", false)) {
            flightWatches.id = intent.getIntExtra("flightWatchId", -1)

        } else if (intent.getBooleanExtra("fromList", false)) {

            flightWatches = intent.getSerializableExtra("flightWatch") as FlightWatches

            viewModel.flightWatches.postValue(flightWatches)

            getToolbar().title = flightWatches.getTripDetail()
            var subTitle = Common.dateToString(flightWatches.departure_date.value, "DOW, dd/MM/yy")
            if (flightWatches.type == "RANGE") {
                subTitle = subTitle + " - " + Common.dateToString(flightWatches.departure_date_end.value,
                    "DOW, dd/MM/yy")
            }
            if (flightWatches.is_round_trip) subTitle = subTitle + " ⇄ " + Common.dateToString(flightWatches.return_date.value,
                "DOW, dd/MM/yy")
            getToolbar().subtitle = subTitle


            if (!flightWatches.isActive()) {
                Handler(Looper.getMainLooper()).postDelayed({
                    showSnackbarMessage("Chuyến bay đã qua ngày", R.color.stt_gray, 10000, View.TEXT_ALIGNMENT_CENTER)
                }, 1000)
            }


        } else {
            var link = intent.extras?.getString("action_link", null)
            if (!link.isNullOrEmpty()) {
                var idF = link.replace("https://12bay.vn/theo-doi-gia/", "")
                flightWatches.id = idF.toInt()
            }
        }
    }

    private fun setupViewPager(viewPager: HeightWrappingViewPager) {

        viewAdapter.addFragment(FlightListFragment(), "LƯỢT ĐI")
        viewAdapter.addFragment(FlightListFragment(), "LƯỢT VỀ")

        viewPager.adapter = viewAdapter
        viewPager.measure(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);

        getViewBindding().tabs.setupWithViewPager(viewPager)
    }

    private fun getListTask() {

        var request = JSONObject()
        try {
            var id = flightWatches.id
            request.put("uid", firebaseUser?.uid)
            request.put("email", firebaseUser?.email)
            request.put("id", id)

        } catch (e: JSONException) {
            e.printStackTrace()
            AppConfigs.logException(e)
        }

        SSLSendRequest(this).POST(false, "FlightWatches/Show", request, object : SSLSendRequest.CallBackInterface {
            override fun onSuccess(response: JSONObject, cached: Boolean) {
                if (!response.isNull("data")) {
                    processListData(response.getJSONObject("data"))
                } else {
                    Common.showAlertDialog(this@FlightWachesViewActivity,
                        "Thông báo",
                        "Thông báo giá đã bị xóa hoặc hết hạn.\n" + "Vui lòng chọn thông báo khác",
                        true,
                        false)
                }
            }

            override fun onFail(er: VolleyError) {
                Common.showAlertDialog(this@FlightWachesViewActivity,
                    "Thông báo",
                    "Thông báo giá đã bị xóa hoặc hết hạn.\nVui lòng chọn thông báo khác",
                    true,
                    false)
            }
        })
    }

    private fun deleteItemTask() {

        var request = JSONObject()
        try {
            var id = flightWatches.id
            request.put("uid", firebaseUser!!.uid)
            request.put("email", firebaseUser!!.email)
            request.put("id", id)

        } catch (e: JSONException) {
            e.printStackTrace()
            AppConfigs.logException(e)
        }

        showSnackbarMessage("Đang xóa thông báo giá", R.color.red, 1000, View.TEXT_ALIGNMENT_CENTER)

        SSLSendRequest(this).POST(false, "FlightWatches/Delete", request, object : SSLSendRequest.CallBackInterface {
            override fun onSuccess(response: JSONObject, cached: Boolean) {
                onBackPressed()
            }

            override fun onFail(er: VolleyError) {
                Common.showAlertDialog(this@FlightWachesViewActivity,
                    "Thông báo",
                    "Không tìm thấy dữ liệu! Vui lòng thực hiện lại",
                    true,
                    false)
            }
        })
    }

    private fun genFareTrendChart(lineChart: LineView, data: ArrayList<ArrayList<BarData>>) {

        lineChart.setShowGrid(false)
        lineChart.setDrawDotLine(true)
        lineChart.setShowPopup(LineView.SHOW_POPUPS_LASTEST)
        lineChart.setShowBackgroundLine(true)
        lineChart.setColorArray(intArrayOf(Color.parseColor("#03a1e4"), Color.GREEN, Color.GRAY, Color.CYAN))
        lineChart.setDataList(data)
    }

    private fun genFareRangeChart(flightWatches: FlightWatches) {
        try {
            val dataList = ArrayList<BarData>()
            var max = Int.MIN_VALUE
            var min = Int.MAX_VALUE
            var minPos = 0
            var i = 1
            for (fare in flightWatches.fare_in_range) {
                var price: Int = if (showBaseFare) fare.min_price else fare.min_adult_price

                val bar = BarData(Common.dateToString(fare.date!!.value, "dd/MM"),
                    price + 0.0f,
                    Common.dinhDangTien(price))
                dataList.add(bar)
                if (price > max) {
                    max = price
                }
                if (price < min) {
                    min = price
                    minPos = i
                }
                i++
            }
            val flightListFragment = viewAdapter.getItem(0) as FlightListFragment

            getViewBindding().ChartProgressBar.setDataList(dataList)
            getViewBindding().ChartProgressBar.setMaxValue(max + 0f)
            getViewBindding().ChartProgressBar.setIshowDashLine(true)
            getViewBindding().ChartProgressBar.setOnBarClickedListener { index ->

                if (flightWatches.fare_in_range.count() > 0) {
                    currentPos = index
                    flightListFragment.addFlightData(flightWatches.fare_in_range[index].flight_detail)
                    updateCurrentSelectDate(flightWatches.fare_in_range[index])
                    getViewBindding().shimmerViewContainer.stopShimmer()
                    getViewBindding().shimmerViewContainer.visibility = View.GONE
                }
            }

            getViewBindding().ChartProgressBar.build()
            Handler(Looper.getMainLooper()).postDelayed({
                if (currentPos == -1) {
                    getViewBindding().ChartProgressBar.selectBar(minPos)
                } else {
                    getViewBindding().ChartProgressBar.selectBar(currentPos)
                }
            }, 500)

        } catch (e: Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }
    }

    private fun updateCurrentSelectDate(day: FareRange) {
        flightWatches.departure_date = day.date!!
        flightWatches.return_date = day.date!!
        viewModel.flightWatches.postValue(flightWatches)
    }

    private fun showLineChart(flightWatches: FlightWatches) {
        var data = ArrayList<ArrayList<BarData>>()
        data.add(Common.convertTrendListToBardata(flightWatches.fare_trend, showBaseFare))

        genFareTrendChart(getViewBindding().fareTrendChart, data)
        if (flightWatches.is_round_trip) {
            var dataRt = ArrayList<ArrayList<BarData>>()
            dataRt.add(Common.convertTrendListToBardata(flightWatches.fare_trend_return, showBaseFare))
            genFareTrendChart(getViewBindding().fareTrendChartRt, dataRt)
        }
    }

    private fun notifyDataChange() {
        val flightListFragment = viewAdapter.getItem(0) as FlightListFragment
        flightListFragment.mAdapter.notifyDataSetChanged()

        if (flightWatches.is_round_trip) {
            val flightListFragment2 = viewAdapter.getItem(1) as FlightListFragment
            flightListFragment2.mAdapter.notifyDataSetChanged()
        }
    }

    fun processListData(json: JSONObject) {
        try {

            flightWatches = AppController.instance.gSon.fromJson(json.toString(), FlightWatches::class.java)

            getToolbar().title = flightWatches.getTripDetail()
            var subTitle = Common.dateToString(flightWatches.departure_date.value, "DOW, dd/MM/yy")
            if (flightWatches.type == "RANGE") {
                subTitle = subTitle + " - " + Common.dateToString(flightWatches.departure_date_end.value,
                    "DOW, dd/MM/yy")
            }
            if (flightWatches.is_round_trip) subTitle = subTitle + " ⇄ " + Common.dateToString(flightWatches.return_date.value,
                "DOW, dd/MM/yy")
            getToolbar().subtitle = subTitle

            if (!flightWatches.is_round_trip) {
                getViewBindding().tabs.removeTabAt(1)
                getViewBindding().viewpager.swipeLocked = true
                getViewBindding().tabs.getTabAt(0)?.text = "Chuyến bay"
            }
            Handler(Looper.getMainLooper()).postDelayed({
                if (flightWatches.type == "TREND") {
                    val flightListFragment = viewAdapter.getItem(0) as FlightListFragment

                    flightListFragment.addFlightData(flightWatches.last_fare_in_day)

                    if (flightWatches.is_round_trip) {
                        val flightListFragment2 = viewAdapter.getItem(1) as FlightListFragment
                        flightListFragment2.addFlightData(flightWatches.last_fare_in_day_return)
                    }


                    showLineChart(flightWatches)

                    viewModel.onLoading.postValue(false)
                    viewModel.flightWatches.postValue(flightWatches)

                    getViewBindding().shimmerViewContainer.stopShimmer()
                    getViewBindding().shimmerViewContainer.visibility = View.GONE

                } else {
                    viewModel.onLoading.postValue(false)
                    viewModel.flightWatches.postValue(flightWatches)
                    genFareRangeChart(flightWatches)

                }
            }, 500)


        } catch (e: Exception) {
            e.printStackTrace()
        }

    }

    override fun onBackPressed() {
        if (this.isTaskRoot) {
            val `in` = Intent(this, HomeActivity::class.java)
            startActivity(`in`)
            finish()
        } else {
            try {
                super.onBackPressed()
                overridePendingTransition(R.anim.left_to_right, R.anim.right_to_left)
            } catch (e: Exception) {
                val `in` = Intent(this, HomeActivity::class.java)
                startActivity(`in`)
                finish()
            }
        }
    }

    override fun onResume() {
        super.onResume() // getListTask()
    }

    fun clickNext() {
        getViewBindding().search.performClick()
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        val inflater = menuInflater
        inflater.inflate(R.menu.flightwatches, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean { // Take appropriate action for each action item click
        when (item.itemId) {
            android.R.id.home -> return true
            R.id.action_delete -> {
                deleteItemTask()
                return true
            }
            else -> {
            }
        }
        return false
    }

}
