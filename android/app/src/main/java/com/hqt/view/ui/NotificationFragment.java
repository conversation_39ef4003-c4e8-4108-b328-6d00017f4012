package com.hqt.view.ui;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.android.volley.VolleyError;
import com.facebook.shimmer.ShimmerFrameLayout;
import com.google.firebase.auth.FirebaseUser;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.hqt.data.model.Notification;
import com.hqt.datvemaybay.R;
import com.hqt.util.AppConfigs;
import com.hqt.util.AppController;
import com.hqt.util.SSLSendRequest;
import com.hqt.view.adapter.NotificationViewAdapter;
import com.hqt.view.ui.search.ui.activity.SearchActivityV2;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

public class NotificationFragment extends Fragment {
    Toolbar toolbar;
    HomeActivity activity;
    RecyclerView mRecyclerView;
    SwipeRefreshLayout swipeRefreshLayout;
    SSLSendRequest APISSL;
    LinearLayout notfound;
    View rootView;

    int page = 1;
    int unRead = 0;
    Boolean loadMore = false, refresh = false, onRefresh = false, onLoadMore = false;
    private ShimmerFrameLayout mShimmerViewContainer;
    NotificationViewAdapter mAdapter;
    private List<Notification> listNotification = new ArrayList<>();

    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {

        rootView = inflater.inflate(R.layout.fragment_notification, null);

        activity = (HomeActivity) getActivity();
        toolbar = rootView.findViewById(R.id.toolbar);
        toolbar.inflateMenu(R.menu.main);
        toolbar.setTitle("Thông báo");

        APISSL = new SSLSendRequest(activity);

        activity.setSupportActionBar(toolbar);
        activity.getSupportActionBar().setDisplayShowHomeEnabled(true);
        toolbar.setNavigationIcon(R.drawable.ic_action_back_home);
        toolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                ((HomeActivity) getActivity()).clickNavigation(0);
            }
        });

        mShimmerViewContainer = rootView.findViewById(R.id.shimmer_view_container);
        notfound = rootView.findViewById(R.id.notfound);
        mRecyclerView = rootView.findViewById(R.id.recyclerView);
        mRecyclerView.setLayoutManager(new LinearLayoutManager(activity));
        mRecyclerView.setHasFixedSize(true);


        mAdapter = new NotificationViewAdapter(activity, listNotification, this);
        mRecyclerView.setAdapter(mAdapter);

        mRecyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (!recyclerView.canScrollVertically(1) && newState == RecyclerView.SCROLL_STATE_IDLE) {
                    if (loadMore && activity.isInternetConnected()) getNotificationList();
                }
            }
        });


        swipeRefreshLayout = rootView.findViewById(R.id.swipe_refresh_layout);
        swipeRefreshLayout.setColorSchemeResources(R.color.green, R.color.red, R.color.google_yellow);
        swipeRefreshLayout.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                if (!onLoadMore) {
                    onRefresh = true;

                    new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            page = 1;
                            getNotificationList();
                        }
                    }, 900);
                }
            }
        });

        if (((HomeActivity) getActivity()).isUserSigned()) {
            new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                @Override
                public void run() {
                    page = 1;
                    listNotification.clear();
                    getNotificationList();
                }
            }, 3000);
        } else {
            notfound.setVisibility(View.VISIBLE);
            swipeRefreshLayout.setEnabled(false);
            mShimmerViewContainer.setVisibility(View.GONE);
            mRecyclerView.setVisibility(View.GONE);

        }
        addBtnListener();
        return rootView;
    }

    public void getNotificationList() {
        try {
            loadMore = false;
            FirebaseUser authUser = ((HomeActivity) getActivity()).getFirebaseUser();
            JSONObject request = new JSONObject();
            try {
                request.put("page", page);
            } catch (JSONException e) {
                e.printStackTrace();
            }
            if (authUser != null) {
                APISSL.GET(true, "notification/u/" + authUser.getUid(), request, new SSLSendRequest.CallBackInterface() {
                    @Override
                    public void onSuccess(JSONObject response, boolean cached) {
                        mRecyclerView.setVisibility(View.VISIBLE);
                        processJsonResponse(response);
                    }

                    @Override
                    public void onFail(VolleyError error) {

                    }
                });
            }
        } catch (Exception e) {
            AppConfigs.logException(e);
        }
    }

    public void seenNofitication(int position, String notification_id) {
        FirebaseUser authUser = ((HomeActivity) getActivity()).getFirebaseUser();
        if (authUser != null) {
            AppConfigs.Log("seenNofitication", notification_id);
            JSONObject postData = new JSONObject();
            try {
                postData.put("uid", authUser.getUid());
                postData.put("id", notification_id);

            } catch (JSONException e) {
                e.printStackTrace();
                AppConfigs.logException(e);
            }
            APISSL.POST(false, "notification/seen", postData, new SSLSendRequest.CallBackInterface() {
                @Override
                public void onSuccess(JSONObject response, boolean cached) {
                    if (listNotification.size() > position) {
                        listNotification.get(position).setRead(true);
                    }

                    mAdapter.notifyDataSetChanged();
                }

                @Override
                public void onFail(VolleyError error) {

                }
            });
            activity.unseenBadge.setBadgeNumber(activity.unseenBadge.getBadgeNumber() - 1);
        }
    }

    public void addBtnListener() {
        rootView.findViewById(R.id.btnSearch).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (((HomeActivity) getActivity()).isInternetConnected()) {
//                    Intent in = new Intent(getContext(), SearchActivity.class);
                    Intent in = new Intent(getContext(), SearchActivityV2.class);
                    startActivity(in);
                }
            }
        });
        rootView.findViewById(R.id.btnCheap).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (((HomeActivity) getActivity()).isInternetConnected()) {
                    ((HomeActivity) getActivity()).clickNavigation(2);
                }
            }
        });
    }

    private void processJsonResponse(JSONObject response) {
        try {
            unRead = 0;


            int currentPage = response.getJSONObject("meta").getJSONObject("pagination").getInt("current_page");
            int totalPage = response.getJSONObject("meta").getJSONObject("pagination").getInt("total_pages");
            if (currentPage < totalPage) {
                loadMore = true;
                page = currentPage + 1;
            }

            Type listType = new TypeToken<ArrayList<Notification>>() {
            }.getType();
            JSONArray listNotificationJS = response.getJSONArray("data");
            Gson gson = new Gson();

            List<Notification> listNoti = AppController.getInstance().getGSon().fromJson(listNotificationJS.toString(), listType);
            for (int i = 0; i < listNotificationJS.length(); i++) {
                Notification noti = gson.fromJson(listNotificationJS.get(i).toString(), Notification.class);
                if (!noti.getRead()) {
                    unRead++;
                }
            }


            if (onRefresh && listNotification.size() > 0) {
                onRefresh = false;
                listNotification.clear();
                listNotification.addAll(listNoti);
                mAdapter.notifyDataSetChanged();
                activity.shakeItBaby();
            } else {
                listNotification.addAll(listNoti);
                mAdapter.notifyDataSetChanged();
            }

            if (listNotification.isEmpty() && page == 1) {
                mRecyclerView.setVisibility(View.GONE);
                notfound.setVisibility(View.VISIBLE);
            } else {
                mRecyclerView.setVisibility(View.VISIBLE);
                notfound.setVisibility(View.GONE);
            }


            swipeRefreshLayout.setRefreshing(false);
            mShimmerViewContainer.setVisibility(View.GONE);


        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
}
