package com.hqt.view.ui.booking.data.api

import com.hqt.base.model.HttpData
import com.hqt.data.model.AddOnInfo
import com.hqt.data.model.request.GetAddOnRequest
import com.hqt.data.prefs.SharedPrefsHelper
import com.hqt.view.ui.booking.data.model.Baggage
import com.hqt.view.ui.booking.data.model.BaseBooking
import javax.inject.Inject
import javax.inject.Singleton


@Singleton
class BookingHelper @Inject constructor(
    private val api : BookingService,
    private val sharedPrefsHelper: SharedPrefsHelper
) {

    suspend fun getBagFeeApi(provider : String?, fareBasis : String?, stops : String?, flightKey : String?) : HttpData<ArrayList<Baggage>>{
        return api.getBagFeeApi(provider ?: "", fareBasis, stops, flightKey)
    }
    suspend fun sendBooking(request : Any?) : HttpData<BaseBooking>{
        return api.sendBooking(request )
    }


}