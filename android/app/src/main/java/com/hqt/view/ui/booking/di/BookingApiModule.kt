package  com.hqt.view.ui.booking.di

import com.hqt.view.ui.booking.data.api.BookingService
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
class BookingApiModule {

    @Provides
    @Singleton
    fun provideBookingService(retrofit: Retrofit): BookingService {
        return retrofit.create(BookingService::class.java)
    }



}