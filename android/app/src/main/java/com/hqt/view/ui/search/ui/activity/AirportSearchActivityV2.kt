package com.hqt.view.ui.search.ui.activity

import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import androidx.activity.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.hqt.base.BaseActivity
import com.hqt.base.model.State
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ActivityAirportSearchBinding
import com.hqt.util.AppConfigs
import com.hqt.view.ui.search.ui.AirportSearchViewModel
import com.hqt.view.ui.search.ui.adapter.airport.v2.AirportViewAdapterV2
import dagger.hilt.android.AndroidEntryPoint


@AndroidEntryPoint
class AirportSearchActivityV2 : BaseActivity<ActivityAirportSearchBinding>() {

    private val viewModel: AirportSearchViewModel by viewModels()
    

    private val mAdapter by lazy {
        AirportViewAdapterV2{

        }
    }
    override fun getLayoutRes(): Int {
        return R.layout.activity_airport_search
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        getToolbar()?.title = "Tìm sân bay"
        getToolbar()?.setNavigationIcon(R.drawable.ic_action_back_home)


        initRecycleView()

        binding.editSearch.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                val query = binding.editSearch.text.toString()
                if (query.length >= 2) {
                    viewModel.getAirport(query)
                }
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        })

        viewModel.getAirport("")

        //binding.durationView.drawDuration(arrayListOf(100, 50, 50, 10))
        viewModel.getAirportList()

    }


    private fun initRecycleView(){
        binding.myRecyclerView.apply {
            layoutManager = LinearLayoutManager(this@AirportSearchActivityV2)
            adapter = mAdapter
            setHasFixedSize(true)

        }
        mAdapter.onChoose = {
            val data = Intent()
            data.putExtra("code", it.code)
            data.putExtra("name", it.city)
            data.putExtra("isDomestic", it.isDomestic)
            data.putExtra("airportInfo", it)

            setResult(RESULT_OK, data)
            finish()
        }

        observe()

    }




    private fun observe(){
        viewModel.airportLiveData.observe(this){
            when(it){
                is State.Error -> {

                }
                State.Loading -> {

                }
                is State.Success -> {
                    AppConfigs.Log("getAirport", it.data.size.toString())

                    mAdapter.setData( ArrayList(it.data))

                    binding.shimmerViewContainer.stopShimmer()
                    binding.shimmerViewContainer.visibility = View.GONE
                    binding.myRecyclerView.visibility = View.VISIBLE


                }
            }
        }
    }

}