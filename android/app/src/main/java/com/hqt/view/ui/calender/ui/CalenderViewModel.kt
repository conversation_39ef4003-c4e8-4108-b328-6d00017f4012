package com.hqt.view.ui.calender.ui

import android.graphics.Color
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.google.gson.JsonParser
import com.hqt.base.model.State
import com.hqt.data.prefs.SharedPrefsHelper
import com.hqt.datvemaybay.BuildConfig
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.Common.convertToDate
import com.hqt.util.AppConfigs
import com.hqt.util.Log
import com.hqt.util.amlich.TextDecorator
import com.hqt.view.ui.calender.data.api.CalenderApiHelper
import com.hqt.view.ui.calender.data.model.PricesBoardRequest
import com.hqt.view.ui.search.data.model.AirportGroup
import com.prolificinteractive.materialcalendarview.CalendarDay
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONObject
import javax.inject.Inject


@HiltViewModel
class CalenderViewModel @Inject constructor(
    private val calenderApiHelper: CalenderApiHelper,
    private val sharedPrefsHelper: SharedPrefsHelper
) : ViewModel() {


    private val _pricesBoardLiveData: MutableLiveData<Any> = MutableLiveData()
    val pricesBoardLiveData: LiveData<Any> get() = _pricesBoardLiveData



    private val _listDecorLiveData: MutableLiveData<ArrayList<TextDecorator>> = MutableLiveData()
    val listDecorLiveData: LiveData<ArrayList<TextDecorator>> get() = _listDecorLiveData



    fun getPricesBoard(currentMonth : CalendarDay, route : String?){
        viewModelScope.launch(Dispatchers.IO) {

            try {
                val month = Common.dateToString(convertToDate(currentMonth.date).time, "yyyyMM")

                val request = PricesBoardRequest(
                    startDate = month + "01",
                    endDate = month + "31",
                    maxPrice = "0",
                    minPrice = "10000",
                    routes = arrayListOf(route ?: ""),
                    source = "ANDROID",
                    type = "date",
                    key = "Common.getKeyHash()",
                    ver = BuildConfig.VERSION_CODE.toString()
                )

                val result = calenderApiHelper.getPricesBoard(request)
                _pricesBoardLiveData.postValue(result)


                val fullData = getFullData()
                if (!fullData.isNull(route) && fullData.length() > 0) {
                    addMonthPricesDecor()
                }



            }catch (ex : Exception){
                Log.logException(ex)
            }
        }
    }

    private fun getFullData() : JSONObject {
        val jsonString = Gson().toJson(_listDecorLiveData.value)   // convert Any -> String JSON
        val jsonObject = JSONObject(jsonString)
        val data = jsonObject.getJSONObject("data")
        val full = data.getJSONObject("full")
        return full
    }

    private fun addMonthPricesDecor(){
        val listDecor = ArrayList<TextDecorator>()
        try {


            val full = getFullData()

            if (getFullData().has("HANSGN")) {
                val HANSGNArray = full.getJSONArray("HANSGN")
                for (i in 0 until HANSGNArray.length()) {
                    val item = HANSGNArray.getJSONObject(i)


                    val mCode = item.getString("mCode")
                    val price = item.getInt("mPrice") / 1000

                    val calDay = CalendarDay.from(
                        mCode.substring(7, 11).toInt(),
                        mCode.substring(11, 13).toInt(),
                        mCode.substring(13, 15).toInt()
                    )
                    if (CalendarDay.today().isBefore(calDay)) {
                        if (price < 200) {
                            listDecor.add(
                                (TextDecorator(
                                    Color.parseColor("#2980b9"), calDay, price.toString() + "k"
                                ))
                            )
                        } else {
                            listDecor.add(
                                (TextDecorator(
                                    Color.parseColor("#848383"), calDay, price.toString() + "k"
                                ))
                            )
                        }
                    }
                }
            } else {
                Log.d("HANSGN", "Không có key HANSGN trong full")
            }

            _listDecorLiveData.postValue(listDecor)
        } catch (e: Exception) {
            AppConfigs.logException(e)
        }
    }



}