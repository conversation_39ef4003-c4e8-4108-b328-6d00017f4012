package com.hqt.view.ui.reward.ui.fragment;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.android.volley.VolleyError;
import com.facebook.shimmer.ShimmerFrameLayout;
import com.github.florent37.materialviewpager.header.MaterialViewPagerHeaderDecorator;
import com.google.firebase.analytics.FirebaseAnalytics;
import com.google.firebase.auth.FirebaseUser;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.hqt.view.ui.reward.data.model.Voucher;
import com.hqt.datvemaybay.R;
import com.hqt.util.AppController;
import com.hqt.util.SSLSendRequest;
import com.hqt.util.ViewUtil;
import com.hqt.view.ui.reward.ui.adapter.VoucherAdapter;
import com.hqt.view.ui.BaseActivity;
import com.hqt.view.ui.reward.ui.activity.RewardActivity;
import com.hqt.view.ui.search.ui.activity.SearchActivityV2;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

public class VoucherListFragment extends Fragment {

    private RecyclerView mRecyclerView;
    private RecyclerView.Adapter mAdapter;

    private LinearLayout emptyStateLayout;
    private ShimmerFrameLayout mShimmerViewContainer;

    private static final int ITEM_COUNT = 5;
    private List<Voucher> mContentItems = new ArrayList<>();

    public static VoucherListFragment newInstance() {
        return new VoucherListFragment();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_recyclerview_reward, container, false);
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        emptyStateLayout = view.findViewById(R.id.emptyStateLayout);
        initEmptyState(view);

        mRecyclerView = view.findViewById(R.id.recyclerView);
        RecyclerView.LayoutManager layoutManager = new LinearLayoutManager(getActivity());
        mRecyclerView.setLayoutManager(layoutManager);
        mRecyclerView.setHasFixedSize(true);

        mAdapter = new VoucherAdapter(getActivity(), mContentItems);
        mRecyclerView.addItemDecoration(new MaterialViewPagerHeaderDecorator());

        mRecyclerView.setAdapter(mAdapter);

        mShimmerViewContainer = view.findViewById(R.id.shimmer_view_container);
        mShimmerViewContainer.startShimmer();
        mShimmerViewContainer.setVisibility(View.VISIBLE);

        FirebaseAnalytics.getInstance(getActivity()).logEvent("voucher_view", null);


        getVoucherList();
    }

    public void getVoucherList() {
        FirebaseUser fUser = ((BaseActivity) getActivity()).getFirebaseUser();

        if (((BaseActivity) getActivity()).isUserSigned()) {
            String uid = ((BaseActivity) getActivity()).getFirebaseUser().getUid();
            new SSLSendRequest(getActivity()).GET(false, "AirLines/Voucher/List/" + uid, new JSONObject(), new SSLSendRequest.CallBackInterface() {
                @Override
                public void onSuccess(JSONObject response, boolean cached) {
                    try {
                        mContentItems.clear();

                        Gson gsons = AppController.getInstance().getGSon();
                        Type listType = new TypeToken<ArrayList<Voucher>>() {
                        }.getType();

                        List<Voucher> parser = gsons.fromJson(response.getJSONArray("data").toString(), listType);

                        mContentItems.addAll(parser);
                        mAdapter.notifyDataSetChanged();

                        mShimmerViewContainer.stopShimmer();
                        mShimmerViewContainer.setVisibility(View.GONE);

                        if (parser.isEmpty()) {
                            emptyStateLayout.setVisibility(View.VISIBLE);
                        } else {
                            emptyStateLayout.setVisibility(View.GONE);
                        }


                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }

                @Override
                public void onFail(VolleyError error) {

                }
            });
        } else {
            mShimmerViewContainer.stopShimmer();
            mShimmerViewContainer.setVisibility(View.GONE);
            emptyStateLayout.setVisibility(View.VISIBLE);

        }


    }
    

    public void clearSelect() {

        mAdapter.notifyDataSetChanged();
    }

    public void initEmptyState(View view) {
        String emptyText = "Hiện chưa có voucher nào. Bạn đăng nhập để đổi điểm nhé !";
        if (((BaseActivity) getActivity()).isUserSigned()) {
            emptyText = "Hiện chưa có voucher nào.";
        }
        ViewUtil.initEmptyState(view, getContext(), emptyText, getActivity().getResources().getDrawable(R.drawable.ic_empty_ticket), -1, new ViewUtil.EmptyStateCallBackInterface() {
            @Override
            public void negativeButton(Button button) {
                if (((BaseActivity) getActivity()).isUserSigned()) {
                    button.setText("ĐẶT VÉ TÍCH ĐIỂM");
                    button.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {
//                            Intent in = new Intent(getActivity(), SearchActivity.class);
                            Intent in = new Intent(getActivity(), SearchActivityV2.class);
                            startActivity(in);
                        }
                    });

                } else {
                    button.setText("ĐĂNG NHẬP");
                    button.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            ((RewardActivity) getActivity()).signIn();
                        }
                    });
                }

            }

            @Override
            public void positiveButton(Button button) {
                button.setText("ĐỔI ĐIỂM");
                button.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        ((RewardActivity) getActivity()).changeViewPaper(0);
                    }
                });

            }
        });
    }
}
