package com.hqt.view.ui.reward.data.api

import com.hqt.base.model.HttpData
import com.hqt.data.prefs.SharedPrefsHelper
import com.hqt.util.ApiUtil
import com.hqt.view.ui.reward.data.model.Promotion
import com.hqt.view.ui.reward.data.model.Voucher
import javax.inject.Inject
import javax.inject.Singleton


@Singleton
class RewardApiHelper @Inject constructor(
    private val api : RewardService,
    private val sharedPrefsHelper: SharedPrefsHelper
) {

    suspend fun getPromotion(): HttpData<ArrayList<Promotion>>{
        return api.getPromotion()
    }
    suspend fun getPromotionById(id : String?): HttpData<Promotion>{
        return api.getPromotionById(id)
    }
    suspend fun getVoucherList(uid : String?): HttpData<ArrayList<Voucher>>{
        val header = ApiUtil.header(sharedPrefsHelper)
        return api.getVoucherList(header, uid)
    }


}