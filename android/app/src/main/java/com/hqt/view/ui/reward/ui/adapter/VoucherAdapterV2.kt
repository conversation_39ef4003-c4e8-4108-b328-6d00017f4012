package com.hqt.view.ui.reward.ui.adapter

import com.bumptech.glide.Glide
import com.hqt.base.BaseAdapter
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.AdapterVoucherBinding
import com.hqt.view.ui.reward.data.model.Voucher


class VoucherAdapterV2(private val listener: (Voucher) -> Unit) :
    BaseAdapter<Voucher, AdapterVoucherBinding>(listener) {



    override fun getLayoutRes(): Int {
        return R.layout.adapter_voucher
    }



    override fun bind(binding: AdapterVoucherBinding, position: Int, model: Voucher) {
        binding.apply {

            Glide.with(_context)
                .load(model.promotionDetail?.logo).skipMemoryCache(true)
                .fitCenter()
                .into(imgLogo)

        }

    }





    override fun onItemClickListener(model: Voucher) {
        listener(model)
    }

}