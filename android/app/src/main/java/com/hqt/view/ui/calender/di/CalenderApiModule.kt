package  com.hqt.view.ui.calender.di

import com.hqt.view.ui.calender.data.api.CalenderService
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
class CalenderApiModule {

    @Provides
    @Singleton
    fun provideSCalenderService(retrofit: Retrofit): CalenderService {
        return retrofit.create(CalenderService::class.java)
    }



}