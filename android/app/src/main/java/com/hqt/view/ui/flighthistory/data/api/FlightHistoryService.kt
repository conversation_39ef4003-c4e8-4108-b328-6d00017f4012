package com.hqt.view.ui.flighthistory.data.api

import com.hqt.base.model.HttpData
import com.hqt.data.model.SliderItem
import com.hqt.view.ui.calender.data.model.FlightHistoryBody
import com.hqt.view.ui.flighthistory.data.model.FlightHistory
import com.hqt.view.ui.flighthistory.data.model.FlightHistoryItem
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.HTTP
import retrofit2.http.HeaderMap
import retrofit2.http.Headers
import retrofit2.http.Path
import retrofit2.http.Query

interface FlightHistoryService {

    @GET("/api/v1/Flight/History")
    suspend fun getFlightHistory(
        @Query("flightNumber") flightNumber : String? = null,
        @Query("days") days : Int? = null,
    ): HttpData<FlightHistory>



    @GET("/api/v1/Flight/Images/{registration}")
    suspend fun getFlightImages(
        @Path("registration") registration : String? = null,
    ): HttpData<ArrayList<SliderItem>>


    @GET("/api/v1/Flight/Track}")
    suspend fun getFlightTrack(
        @Query("flightId") flightId : String? = null,
    ): HttpData<FlightHistoryItem>


}