package com.hqt.view.ui.flightSearch.model

import android.text.Spanned
import com.google.gson.annotations.SerializedName
import com.hqt.datvemaybay.Common
import java.io.Serializable

data class Segment(@SerializedName("id") var id: Int? = null,
    @SerializedName("airline") var airline: String? = null,
    @SerializedName("airlineInfo") var airlineInfo: String? = null,
    @SerializedName("marketingAirline") var marketingAirline: String? = null,
    @SerializedName("operatingAirline") var operatingAirline: String? = null,
    @SerializedName("startPoint") var startPoint: String? = null,
    @SerializedName("startPointInfo") var startPointInfo: PointInfo? = null,
    @SerializedName("endPoint") var endPoint: String? = null,
    @SerializedName("endPointInfo") var endPointInfo: PointInfo? = null,
    @SerializedName("startTime") var startTime: String? = null,
    @SerializedName("endTime") var endTime: String? = null,
    @SerializedName("flightNumber") var flightNumber: String? = null,
    @SerializedName("duration") var duration: Int? = null,
    @SerializedName("plane") var plane: String? = null,
    @SerializedName("planeInfo") var planeInfo: PlaneInfo? = null,
    @SerializedName("startTerminal") var startTerminal: String? = null,
    @SerializedName("endTerminal") var endTerminal: String? = null,
    @SerializedName("hasStop") var hasStop: Boolean? = null,
    @SerializedName("stopPoint") var stopPoint: String? = null,
    @SerializedName("stopPointInfo") var stopPointInfo: PointInfo? = null,
    @SerializedName("stopTime") var stopTime: Int = 0,
    @SerializedName("dayChange") var dayChange: Boolean? = null,
    @SerializedName("stopOvernight") var stopOvernight: Boolean? = null,
    @SerializedName("changeStation") var changeStation: Boolean? = null,
    @SerializedName("changeAirport") var changeAirport: Boolean? = null,
    @SerializedName("lastItem") var lastItem: Boolean? = null,
    @SerializedName("flightsMiles") var flightsMiles: Int? = null,
    @SerializedName("status") var status: String? = null,
    @SerializedName("seat") var seat: Int? = null,
    @SerializedName("cabin") var cabin: String? = null,
    @SerializedName("class") var fareClass: String? = null,
    @SerializedName("classInfo") var classInfo: String? = null,
    @SerializedName("fareBasis") var fareBasis: String? = null,
    @SerializedName("handBaggage") var handBaggage: String? = null,
    @SerializedName("allowanceBaggage") var allowanceBaggage: String? = null,
    var isShow: Boolean? = true) : Serializable {

    fun getStartTimeShort(): String {

        return startTime!!.substring(11, 16)
    }

    fun getEndTimeShort(): String {

        return endTime!!.substring(11, 16)
        var enddatetime = Common.stringToDate(endTime, "yyyy-MM-dd'T'HH:mm:ss")
        return Common.dateToString(enddatetime.time, "(dd/MM) HH:mm")
    }

    fun getStartDateShort(): String {

        var enddatetime = Common.stringToDate(startTime, "yyyy-MM-dd'T'HH:mm:ss")
        return Common.dateToString(enddatetime.time, "dd/MM")
    }

    fun getEndDateShort(): String {

        var enddatetime = Common.stringToDate(endTime, "yyyy-MM-dd'T'HH:mm:ss")
        return Common.dateToString(enddatetime.time, "dd/MM")
    }

    fun getAirlinesLogo(): String {
        return "https://ssl.12bay.vn/images/airlines/" + airline!!.lowercase() + ".gif"
    }

    fun getFlightTime(): String {
        val hour = duration!!.div(60)
        return "" + hour + "h" + (duration!! - hour * 60) + "p"
    }

    fun getStopString(): Spanned? {
        return Common.convertHTML("Dừng <b>" + getStopTimeStr() + "</b> để đổi chuyến ở <b>" + stopPointInfo?.name + "</b>");
    }

    fun getStopTimeStr(): String {
        val hour = duration!!.div(60)
        return "" + hour + "h" + (duration!! - hour * 60) + "p"
    }

    fun isSkipStart(): Boolean {
        if (id == 0) {
            return true
        }

        return false
    }
}