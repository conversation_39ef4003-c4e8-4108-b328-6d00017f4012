package com.hqt.view.ui.tour

import android.content.Context
import android.content.Intent
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.updateLayoutParams
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.github.rubensousa.gravitysnaphelper.OrientationAwareRecyclerView
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.AdapterTourItemFullTitleBinding
import com.hqt.util.AppConfigs
import com.hqt.util.AppController
import com.hqt.util.Widget
import com.hqt.view.ui.flighthistory.other.dpToPx

/**
 * Created by TN on 12/1/2016.
 */
class TourListItemAdapter(var mContext: Context, var contents: List<TourItem>, var style: String) :
    RecyclerView.Adapter<TourListItemAdapter.ViewHolder>() {
    class ViewHolder(var context: Context, val binding: AdapterTourItemFullTitleBinding) :
        RecyclerView.ViewHolder(binding.root) {
        var recyclerView: OrientationAwareRecyclerView? = null

        fun bind(tourListItem: TourItem) {

            binding.tour = tourListItem
            binding.root.setOnClickListener {

                val detailInt = Intent(context, TourDetailActivity::class.java)
                detailInt.putExtra("tourItemExtra", tourListItem)
                context.startActivity(detailInt)
            }
            binding.tourShortInfo.removeAllViews()
            tourListItem.info.forEach {
                Widget.createTourShortInfoItem(context, it, binding.tourShortInfo)
            }

        }
    }

    override fun getItemCount(): Int {
        return contents.size
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val layoutInflater = LayoutInflater.from(parent.context)
        val binding: AdapterTourItemFullTitleBinding = DataBindingUtil.inflate(layoutInflater,
            R.layout.adapter_tour_item_full_title,
            parent,
            false)
        if (style != "VERTICAL") {
            binding.cardView.updateLayoutParams<androidx.recyclerview.widget.RecyclerView.LayoutParams> {
                width = dpToPx(200, parent.context)
            }
        }
        return ViewHolder(mContext, binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        try {
            holder.bind(contents[position])
        } catch (e: Exception) {
            FirebaseCrashlytics.getInstance()
                .setCustomKey("data", AppController.instance.gSon.toJson(contents[position]))
            AppConfigs.logException(e)
        }
    }

    override fun getItemId(position: Int): Long {
        val booking: TourItem = contents[position]
        return position.toLong()
    }

    override fun getItemViewType(position: Int): Int {
        return position
    }

    companion object {
        const val TYPE_HEADER = 0
        const val TYPE_CELL = 1
    }


}