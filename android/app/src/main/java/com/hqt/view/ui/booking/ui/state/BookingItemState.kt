package com.hqt.view.ui.booking.ui.state

import android.view.View
import com.hqt.datvemaybay.Common
import com.hqt.view.ui.booking.data.model.BookingV3

data class BookingItemState (val item : BookingV3?) {


    var totalPrices = 0
    var pointReward = 0
    var returnTotal = 0

    init {

        if (item?.departure_f != null) {
            totalPrices = ((item.departure_f?.adult ?: 0) * item.adult) + ((item.departure_f?.child ?: 0) * item.child) + ((item.departure_f?.infant ?: 0) * item.infant)
            pointReward = item.departure_f?.rewardPoint ?: 0
        }
        if (item?.return_f != null) {
            returnTotal = ((item.return_f?.adult ?: 0) * item.adult) + ((item.return_f?.child ?: 0) * item.child) + ((item.return_f?.infant ?: 0) * item.infant)
            totalPrices += returnTotal
//            quickViewRetLayout.visibility = View.VISIBLE
            pointReward += item.return_f?.rewardPoint ?: 0
        }
        item?.total = totalPrices
    }
    
    
    fun txtTotalPaxText() = ((item?.adult ?: 0) + (item?.child ?: 0) + (item?.infant ?: 0)).toString() + " người"
    fun quickViewDepTitleText() = (Common.getAirPortName(item?.departure_f?.originCode, true) + " ⇾ " + Common.getAirPortName(item?.departure_f?.destinationCode, true) + " " + item?.departure_f?.departureDateTime?.substring(0, 11))
    fun quickViewDepPriceText() = Common.dinhDangTien(item?.total ?: 0) ?: ""
    fun quickViewRetTitleText() = (Common.getAirPortName(item?.return_f?.originCode, true) + " ⇾ " + Common.getAirPortName(item?.return_f?.destinationCode, true) + " " + item?.return_f?.departureDateTime?.substring(0, 11))
    fun quickViewRetPriceText() = Common.dinhDangTien(returnTotal) ?: ""
    fun txtGrandTotalPriceText() = Common.dinhDangTien(totalPrices) ?: ""
    fun txtPointText() = "Nhận $pointReward điểm "


    fun getShowPointReward(): Boolean {
        return pointReward > 0
    }

    fun getShowQuickViewRetLayout() = item?.return_f != null


}