package com.hqt.view.ui.demo.ui

import android.os.Bundle
import android.widget.Toast
import androidx.activity.viewModels
import com.hqt.base.BaseActivity
import com.hqt.base.model.State
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ActivityDemoBinding
import com.hqt.util.Log
import dagger.hilt.android.AndroidEntryPoint


@AndroidEntryPoint
class DemoActivity : BaseActivity<ActivityDemoBinding>() {


    private val viewModel : DemoViewModel by viewModels()

    override fun getLayoutRes(): Int {
        return R.layout.activity_demo
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        observe()

        binding.btnTest.setOnClickListener {
            viewModel.getFeature()
        }

    }

    private fun observe(){
        viewModel.homeLiveData.observe(this){
            when(it){
                is State.Error -> {
                    loadingProgress.dismiss()
                }
                State.Loading ->{
                    loadingProgress.show()

                }
                is State.Success -> {
                    loadingProgress.dismiss()
                    Log.d("data", it.data)
                }
            }
        }
    }
}