package com.hqt.view.ui.train

import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import com.google.android.flexbox.FlexboxLayout
import com.hqt.data.model.response.TrainSeatDetail
import com.hqt.datvemaybay.databinding.TrainSeatViewItemBinding
import java.io.Serializable

class TrainSeatView(context: Context?, style: TrainSeatStyle) : LinearLayout(context) {


    lateinit var binding: TrainSeatViewItemBinding
    var seatDetail: TrainSeatDetail? = null
    lateinit var mStyle: TrainSeatStyle
    private var selected = false

    init {
        binding = TrainSeatViewItemBinding.inflate(LayoutInflater.from(context), this, false)

        mStyle = style
        addView(binding.root)

        val lpLeft = FlexboxLayout.LayoutParams(FlexboxLayout.LayoutParams.MATCH_PARENT,
            FlexboxLayout.LayoutParams.MATCH_PARENT)

        lpLeft.flexBasisPercent = 0.18f
        layoutParams = lpLeft

        if (style == TrainSeatStyle.WAY_SPACE) {
            binding.seatGroupView.visibility = View.GONE

        } else if (style == TrainSeatStyle.CENTER_SPACE) {
            lpLeft.flexBasisPercent = 0.9f
            binding.seatGroupView.visibility = View.GONE
            binding.space.visibility = View.VISIBLE
        } else if (style == TrainSeatStyle.SEAT_ROOM_4) {
            lpLeft.flexBasisPercent = 0.40f
            binding.seatGroupView.visibility = View.VISIBLE
            binding.space.visibility = View.GONE
        } else if (style == TrainSeatStyle.SEAT_ROOM_6) {
            lpLeft.flexBasisPercent = 0.30f
            binding.seatGroupView.visibility = View.VISIBLE
            binding.space.visibility = View.GONE
        } else if (style == TrainSeatStyle.SEAT_ROOM_TITLE) {
            lpLeft.flexBasisPercent = 0.30f
            binding.seatGroupView.visibility = View.GONE
            binding.space.visibility = View.GONE
            binding.seatTitle.visibility = View.VISIBLE
        } else if (style == TrainSeatStyle.HIDDEN_SEAT) {

            binding.seatGroupView.visibility = View.GONE
            binding.space.visibility = View.GONE
            binding.seatTitle.visibility = View.GONE
        } else {
            binding.seatGroupView.visibility = View.VISIBLE
        }
        setSeatStyle()
        setOnClickListener {
            if (seatDetail != null) {

                if ((context as TrainSelectActivity).selectSeat(seatDetail!!, !selected)) {
                    selected = !selected

                    if (seatDetail!!.status == 3) {
                        if (selected) binding.seatGroupView.setCardBackgroundColor(Color.parseColor("#FB953B")) //  else binding.seatGroupView.setCardBackgroundColor(Color.parseColor("#03a1e4"))
                    }
                }
            }

        }

    }

    private fun setSeatStyle() {

        binding.isShowSeatLineTop = false
        binding.isShowSeatLineBottom = false
        binding.isRoomSeat = false

        if (mStyle == TrainSeatStyle.SEAT) {
            binding.isShowSeatLineTop = true
            binding.isShowSeatLineBottom = false
            binding.isRoomSeat = false
        } else if (mStyle == TrainSeatStyle.REVERT_SEAT) {
            binding.isShowSeatLineTop = false
            binding.isShowSeatLineBottom = true
            binding.isRoomSeat = false
        } else if (mStyle == TrainSeatStyle.SEAT_ROOM_4 || mStyle == TrainSeatStyle.SEAT_ROOM_6) {
            binding.isShowSeatLineTop = false
            binding.isShowSeatLineBottom = false
            binding.isRoomSeat = true
        }
    }

    fun setSeat(mSeatDetail: TrainSeatDetail) {
        seatDetail = mSeatDetail
        binding.textTitle.text = seatDetail!!.seatNumber.toString()

        if (seatDetail!!.status == 3) { // ok
            binding.seatGroupView.setCardBackgroundColor(Color.parseColor("#03a1e4"))
        } else if (seatDetail!!.status == 2) { // dang giao dich
            binding.seatGroupView.setCardBackgroundColor(Color.parseColor("#e66363"))
        } else if (seatDetail!!.status == 5) { // chan dai hon
            binding.seatGroupView.setCardBackgroundColor(Color.parseColor("#57899F"))
        } else if (seatDetail!!.status == 6) { // đang chọn
            selected = true
            binding.seatGroupView.setCardBackgroundColor(Color.parseColor("#FB953B"))
        } else { // da ban
            binding.seatGroupView.setCardBackgroundColor(Color.parseColor("#57899F"))
        }
    }

    enum class TrainSeatStyle : Serializable {
        WAY_SPACE, CENTER_SPACE, SEAT, REVERT_SEAT, SEAT_ROOM_4, SEAT_ROOM_6, SEAT_ROOM_TITLE, HIDDEN_SEAT
    }


}