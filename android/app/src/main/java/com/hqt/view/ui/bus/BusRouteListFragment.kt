package com.hqt.view.ui.bus

import android.graphics.Color
import android.graphics.Typeface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProviders
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.FragmentBusRouteListBinding
import com.hqt.viewmodel.WidgetFilterButtonViewModel
import java.util.Locale


class BusRouteListFragment(var isRoundTrip: Boolean) : Fragment() {
    lateinit var recyclerView: RecyclerView
    private lateinit var mAdapter: BusRouteAdapter
    private var arraylistBusRoute: ArrayList<BusRoute> = ArrayList()
    private var listBusRoute: List<BusRoute> = ArrayList()
    lateinit var toolbar: Toolbar
    lateinit var binding: FragmentBusRouteListBinding

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding =
            DataBindingUtil.inflate(inflater, R.layout.fragment_bus_route_list, container, false)

        var rootView = binding.root
        toolbar = binding.toolbar
        toolbar.inflateMenu(R.menu.main)

        toolbar.title = "Chọn chuyến xe"

        toolbar.setNavigationIcon(R.drawable.ic_action_back_home)

        (activity as AppCompatActivity).setSupportActionBar(toolbar)
        (activity as AppCompatActivity).supportActionBar!!.setDisplayShowHomeEnabled(true)

        toolbar.setNavigationOnClickListener {
            (activity as BusSelectActivity).onBackPressed()
        }


        initSortListen()
        setSubTitleInfo()
        initAnalytics()

        recyclerView = binding.recyclerView
        recyclerView.setHasFixedSize(true)
        recyclerView.layoutManager = LinearLayoutManager(activity)
        mAdapter = BusRouteAdapter((activity as AppCompatActivity), arraylistBusRoute)
        recyclerView.adapter = mAdapter

        binding.btnBack.setOnClickListener {
            (activity as BusSelectActivity).onBackPressed()
        }

        return rootView
    }

    fun initSortListen() {

        var viewModel = ViewModelProviders.of(this).get(WidgetFilterButtonViewModel::class.java)
        viewModel.initOne()
        binding.viewModel = viewModel
        viewModel.onSort.observe(viewLifecycleOwner, androidx.lifecycle.Observer {
            sortListTrain(viewModel.sortKey)
        })

    }

    private fun sortListTrain(byKey: String) {
        var listSortTrain: List<BusRoute> = listBusRoute

        when (byKey.replace("Desc", "")) {
            "departureTime" -> {
                listSortTrain = arraylistBusRoute.sortedWith(compareBy { it.departureTime })
            }

            "duration" -> {
                listSortTrain = arraylistBusRoute.sortedWith(compareBy { it.duration })
            }

            "allTrain" -> {
                listSortTrain = listBusRoute.filter { train -> train.distance!! > 0 }
            }

            "availableTrain" -> {
                listSortTrain = listBusRoute.filter { train -> train.distance!! > 0 }
            }
        }

        if (byKey.toLowerCase(Locale.ROOT).contains("desc")) {
            listSortTrain = listSortTrain.reversed()
        }
        arraylistBusRoute.clear()
        arraylistBusRoute.addAll(listSortTrain)
        mAdapter.notifyDataSetChanged()
    }

    fun genListRoute(busRoutes: ArrayList<BusRoute>) {
        listBusRoute = busRoutes

        arraylistBusRoute.clear()
        arraylistBusRoute.addAll(listBusRoute.filter { route -> route.schedules!!.availableSeats!! > 0 })

        if (arraylistBusRoute.isEmpty()) {
            arraylistBusRoute.addAll(listBusRoute)
        }
        mAdapter.notifyDataSetChanged()
        binding.filter.btnOne.performClick()

        if (busRoutes.size > 0) {

            binding.shimmerViewContainer.stopShimmer()
            binding.shimmerViewContainer.visibility = View.GONE
            binding.filter.root.visibility = View.VISIBLE
            binding.emptyState.visibility = View.GONE

        } else {

            binding.shimmerViewContainer.stopShimmer()
            binding.shimmerViewContainer.visibility = View.GONE
            binding.filter.root.visibility = View.GONE
            binding.emptyState.visibility = View.VISIBLE

        }


    }

    private fun setSubTitleInfo() {
        toolbar.setSubtitleTextColor(Color.WHITE)

        toolbar.setNavigationIcon(R.drawable.ic_action_back_home)
        val view = toolbar.getChildAt(3)
        if (view is TextView) {
            view.typeface = Typeface.createFromAsset(
                (activity as BusSelectActivity).assets,
                "fonts/fontawesome-webfont.ttf"
            )
        }
    }

    private fun initAnalytics() {

        (activity as BusSelectActivity).firebaseAnalytics.setCurrentScreen(
            (activity as BusSelectActivity),
            "bus_select",
            null
        )
    }


}
