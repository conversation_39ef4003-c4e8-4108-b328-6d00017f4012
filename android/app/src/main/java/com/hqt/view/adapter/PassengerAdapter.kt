package com.hqt.view.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.Filter
import android.widget.TextView
import com.hqt.data.model.Passenger
import com.hqt.datvemaybay.R
import com.hqt.util.AppConfigs
import java.util.*
import kotlin.collections.ArrayList


class PassengerAdapter(context: Context, resource: Int, items: List<Passenger>, showFullName: Boolean) : ArrayAdapter<Passenger>(context, resource, items) {

    var resource: Int = resource
    var items: List<Passenger> = ArrayList()
    var tempItems: List<Passenger> = ArrayList()
    var suggestions: MutableList<Passenger> = ArrayList()
    var showFullName = true

    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
        var view = convertView
        if (convertView == null) {
            val inflater: LayoutInflater = context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
            view = inflater.inflate(R.layout.autocomplete_row_layout, parent, false)
        }
        val people: Passenger = items[position]
        if (people != null) {
            val lblName: TextView = view?.findViewById(R.id.txtFullName) as TextView
            if (lblName != null) lblName.setText(people.fullName)
        }

        return view!!
    }

    override fun getFilter(): Filter {
        return nameFilter
    }

    /**
     * Custom Filter implementation for custom suggestions we provide.
     */
    private var nameFilter: Filter = object : Filter() {
        override fun convertResultToString(resultValue: Any): CharSequence {
            return if (showFullName)
                (resultValue as Passenger).fullName
            else
                (resultValue as Passenger).lastName
        }

        override fun performFiltering(constraint: CharSequence?): FilterResults {

            return if (constraint != null) {
                suggestions.clear()
                for (people in tempItems) {

                    if (people.fullName.toLowerCase(Locale.ROOT).contains(constraint.toString().toLowerCase(Locale.ROOT))) {
                        suggestions.add(people)
                    }
                }
                val filterResults = FilterResults()
                filterResults.values = suggestions
                filterResults.count = suggestions.size
                filterResults
            } else {
                FilterResults()
            }
        }

        override fun publishResults(constraint: CharSequence?, results: FilterResults) {
            if (results != null && results.count > 0) {
                val filterList: List<Passenger> = results.values as ArrayList<Passenger>
                clear()
                for (people in filterList) {
                    add(people)
                    notifyDataSetChanged()
                }
            }
        }
    }

    init {
        this.items = items
        tempItems = ArrayList<Passenger>(items) // this makes the difference.
        suggestions = ArrayList<Passenger>()
        this.showFullName = showFullName
    }
}