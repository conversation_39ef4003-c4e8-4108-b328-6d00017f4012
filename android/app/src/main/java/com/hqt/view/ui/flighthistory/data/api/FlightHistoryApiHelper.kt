package com.hqt.view.ui.flighthistory.data.api

import com.hqt.base.model.HttpData
import com.hqt.data.model.SliderItem
import com.hqt.data.prefs.SharedPrefsHelper
import com.hqt.util.ApiUtil
import com.hqt.view.ui.calender.data.model.FlightHistoryBody
import com.hqt.view.ui.flighthistory.data.model.FlightHistory
import com.hqt.view.ui.flighthistory.data.model.FlightHistoryItem
import javax.inject.Inject
import javax.inject.Singleton


@Singleton
class FlightHistoryApiHelper @Inject constructor(
    private val api : FlightHistoryService,
    private val sharedPrefsHelper: SharedPrefsHelper
) {

    suspend fun getFlightHistory(request : FlightHistoryBody?) : HttpData<FlightHistory>{

        val header = ApiUtil.header(sharedPrefsHelper)
        return api.getFlightHistory( request?.flightNumber, request?.days)
    }
    suspend fun getFlightImages(registration : String?) : HttpData<ArrayList<SliderItem>>{

        return api.getFlightImages( registration)
    }
    suspend fun getFlightTrack(id : String?) : HttpData<FlightHistoryItem>{

        return api.getFlightTrack( id)
    }

}