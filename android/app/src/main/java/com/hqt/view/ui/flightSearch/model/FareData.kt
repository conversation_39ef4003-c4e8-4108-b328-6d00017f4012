package com.hqt.view.ui.flightSearch.model

import com.google.gson.annotations.SerializedName
import com.hqt.util.AppConfigs
import java.io.Serializable

data class FareData(

    @SerializedName("fareDataId") var fareDataId: Int? = null,
    @SerializedName("session") var session: String? = null,
    @SerializedName("airline") var airline: String? = null,
    @SerializedName("airlineName") var airlineName: String? = null,
    @SerializedName("isRoundTrip") var isRoundTrip: Boolean = false,
    @SerializedName("leg") var leg: Int? = null,
    @SerializedName("promo") var promo: Boolean? = null,
    @SerializedName("currency") var currency: String? = null,
    @SerializedName("adt") var adt: Int? = null,
    @SerializedName("chd") var chd: Int? = null,
    @SerializedName("inf") var inf: Int? = null,
    @SerializedName("fareAdt") var fareAdt: Int? = null,
    @SerializedName("fareChd") var fareChd: Int? = null,
    @SerializedName("fareInf") var fareInf: Int? = null,
    @SerializedName("taxAdt") var taxAdt: Int? = null,
    @SerializedName("taxChd") var taxChd: Int? = null,
    @SerializedName("taxInf") var taxInf: Int? = null,
    @SerializedName("feeAdt") var feeAdt: Int? = null,
    @SerializedName("feeChd") var feeChd: Int? = null,
    @SerializedName("feeInf") var feeInf: Int? = null,
    @SerializedName("serviceFeeAdt") var serviceFeeAdt: Int? = null,
    @SerializedName("serviceFeeChd") var serviceFeeChd: Int? = null,
    @SerializedName("serviceFeeInf") var serviceFeeInf: Int? = null,
    @SerializedName("discountAdt") var discountAdt: Int? = null,
    @SerializedName("discountChd") var discountChd: Int? = null,
    @SerializedName("discountInf") var discountInf: Int? = null,
    @SerializedName("totalNetPrice") var totalNetPrice: Int? = null,
    @SerializedName("totalServiceFee") var totalServiceFee: Int? = null,
    @SerializedName("totalDiscount") var totalDiscount: Int? = null,
    @SerializedName("totalCommission") var totalCommission: Int? = null,
    @SerializedName("totalPrice") var totalPrice: Int? = null,
    @SerializedName("depTime") var depTime: Int? = null,
    @SerializedName("arrTime") var arrTime: Int? = null,
    @SerializedName("duration") var duration: Int? = null,
    @SerializedName("description") var description: String = "",
    @SerializedName("listFlight") var listFlight: ArrayList<Flight> = arrayListOf()) : Serializable {

    fun isShowLabel(): Boolean {
        if (description.isNotEmpty()) return true
        return false
    }

    fun filterFlight(): FareData {
        val newList: ArrayList<Flight> = ArrayList()

        for (it in listFlight) {
            if (it.leg == 0) {
                it.isReturn = false
                newList.add(it)
                break
            }

        }
        for (it in listFlight) {
            if (it.leg == 1) {
                it.isReturn = false
                newList.add(it)
                break
            }

        }
        listFlight = newList
        return this
    }

    fun getDepartureFlight(): Flight? {

        listFlight.forEach {
            if (it.leg == 0) {
                it.isReturn = false
                return it

            }

        }

        return null
    }

    fun getReturnFlight(): Flight? {
        listFlight.forEach {
            if (it.leg == 1) {
                it.isReturn = true
                return it

            }

        }

        return null
    }


}