package com.hqt.view.ui


import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.SharedPreferences
import android.graphics.Color
import android.os.Bundle
import android.view.Menu
import android.view.View
import android.view.animation.AnimationSet
import android.view.animation.AnimationUtils
import android.view.animation.DecelerateInterpolator
import android.view.animation.RotateAnimation
import android.widget.Button
import android.widget.LinearLayout
import android.widget.Switch
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.widget.AppCompatButton
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.snackbar.Snackbar
import com.google.firebase.analytics.FirebaseAnalytics
import com.hqt.view.ui.search.data.model.AirportInfo
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.PnrActivity
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.SearchResult
import com.hqt.datvemaybay.databinding.ActivitySearchBinding
import com.hqt.util.AppConfigs
import com.hqt.util.amlich.AmLich
import com.hqt.view.ui.airport.AirportSearchActivity
import com.hqt.view.ui.booking.FlightWeekViewActivity
import com.hqt.view.ui.flightSearch.FlightSearchActivity
import com.mikepenz.iconics.view.IconicsImageView
import uk.co.deanwild.materialshowcaseview.MaterialShowcaseSequence
import uk.co.deanwild.materialshowcaseview.MaterialShowcaseView
import uk.co.deanwild.materialshowcaseview.ShowcaseConfig
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date

class SearchActivity : BaseActivityKt<ActivitySearchBinding>() {
    private var txtDepDate: TextView? = null
    private var txtRetDate: TextView? = null
    private var btnCheckOneWay: Button? = null
    private var btnCheckRoundTrip: Button? = null
    private var btnSearch: AppCompatButton? = null
    private var depDate: Calendar? = null
    private var retDate: Calendar? = null
    private var isRoundTrip = true
    private var isDomestic = true
    private var isOriginDomectic = true
    private var isDestinationDomectic = true
    var coordinatorLayout: CoordinatorLayout? = null
    private var isCheapSearch: Switch? = null
    private var page: String? = null
    private var originCode: String? = null
    private var destinationCode: String? = null
    private val adult: String? = null
    private val child: String? = null
    private val infant: String? = null

    var adultCount: Int = 1
    var childCount: Int = 0
    var infantCount: Int = 0
    var showCase: Boolean = true

    val REQUEST_CODE_FROM: Int = 0
    val REQUEST_CODE_TO: Int = 1
    val REQUEST_CODE_DEP_DATE: Int = 2
    val REQUEST_CODE_RE_DATE: Int = 3
    var dateFormat: SimpleDateFormat = SimpleDateFormat("dd/MM/yyyy")


    //private AdView adView;
    var dialog: BottomSheetDialog? = null
    var sheetBehavior: BottomSheetBehavior<*>? = null
    var selectPassengerView: View? = null
    var sequence: MaterialShowcaseSequence? = null
    var showcaseView: MaterialShowcaseView? = null
    var settings: SharedPreferences? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        coordinatorLayout = findViewById(R.id.coordinatorLayout)
        selectPassengerView = layoutInflater.inflate(R.layout.select_passenger_layout, null)

        Common.SHOWFULLPRICE = false
        val config: ShowcaseConfig = ShowcaseConfig()
        config.setDelay(500) // half second between each showcase view

        var sequence = MaterialShowcaseSequence(this, "SHOWCASE_SEARCH") //
        sequence.setConfig(config)
        sequence.setOnItemShownListener(object :
            MaterialShowcaseSequence.OnSequenceItemShownListener {
            override fun onShow(itemView: MaterialShowcaseView, position: Int) {
                showcaseView = itemView
                if (showCase) {
                    Toast.makeText(
                        applicationContext,
                        "Nhấn OK ĐÃ HIỂU để tiếp tục",
                        Toast.LENGTH_SHORT
                    ).show()
                    showCase = false
                }
            }
        })


        depDate = Calendar.getInstance()
        retDate = Calendar.getInstance()

        depDate!!.add(Calendar.DAY_OF_MONTH, 3)
        retDate!!.add(Calendar.DAY_OF_MONTH, 5)

        //ADD new date
        getToolbar().title = "Tìm chuyến bay"
        getToolbar().setNavigationIcon(R.drawable.ic_action_back_home)

        selectPassger()

        btnCheckOneWay = findViewById(R.id.checkOneWay)
        btnCheckRoundTrip = findViewById(R.id.checkRoundtrip)

        btnSearch = findViewById(R.id.search)
        isCheapSearch = findViewById(R.id.cheapSearch)

        txtDepDate = findViewById(R.id.txtDepDate)
        txtRetDate = findViewById(R.id.txtRetDate)

        sequence.addSequenceItem(
            findViewById<View>(R.id.checkOneWay),
            "Hướng dẫn cơ bản", "Nhấn chọn bay một chiều hoặc khứ hồi", "OK Đã hiểu"
        )

        settings = getSharedPreferences("12BAY-APP-CONFIG", 0)

        isDomestic = settings!!.getBoolean("isDomestic", true)

        val originHistory = settings!!.getString("from", "SGN")!!
        if (originHistory.length == 3) {
            getViewBindding().originCode.setText(originHistory)
            getViewBindding().originName.setText(Common.getAirPortName(originHistory, false))
        }
        sequence.addSequenceItem(
            findViewById<View>(R.id.originCode),
            "Nhấn chọn sân bay đi", "OK Đã hiểu"
        )


        val destinationHistory = settings!!.getString("to", "HAN")!!
        if (destinationHistory.length == 3) {
            getViewBindding().destinationCode.setText(destinationHistory)
            getViewBindding().destinationName.setText(
                Common.getAirPortName(
                    destinationHistory,
                    false
                )
            )
        }
        sequence.addSequenceItem(
            getViewBindding().destinationCode,
            "Nhấn chọn sân bay đến", "OK Đã hiểu"
        )

        getViewBindding().selectOrigin.setOnClickListener(View.OnClickListener { // TODO Auto-generated method stub
            //Intent i = new Intent(getApplicationContext(), AirportSearch.class);
            val i = Intent(
                applicationContext,
                AirportSearchActivity::class.java
            )
            startActivityForResult(i, REQUEST_CODE_FROM)
        })
        getViewBindding().selectDestination.setOnClickListener(View.OnClickListener { // TODO Auto-generated method stub
            //Intent i = new Intent(getApplicationContext(), AirportSearch.class);
            val i = Intent(
                applicationContext,
                AirportSearchActivity::class.java
            )
            i.putExtra("dep", getViewBindding().originCode.getText().toString())
            startActivityForResult(i, REQUEST_CODE_TO)
        })
        getViewBindding().swapRouteLayout.setOnClickListener(View.OnClickListener { getViewBindding().swapRoute.performClick() })
        getViewBindding().swapRoute.setOnClickListener(View.OnClickListener {
            val x = isOriginDomectic
            isOriginDomectic = isDestinationDomectic
            isDestinationDomectic = x


            val tem: String = getViewBindding().originCode.getText().toString()
            val temName: String = getViewBindding().originName.getText().toString()

            getViewBindding().originName.setText(
                getViewBindding().destinationName.getText().toString()
            )
            getViewBindding().originCode.setText(
                getViewBindding().destinationCode.getText().toString()
            )

            getViewBindding().destinationCode.setText(tem)
            getViewBindding().destinationName.setText(temName)

            val animSet = AnimationSet(true)
            animSet.interpolator = DecelerateInterpolator()
            animSet.fillAfter = true
            animSet.isFillEnabled = true

            val animRotate = RotateAnimation(
                0.0f, 360.0f,
                RotateAnimation.RELATIVE_TO_SELF, 0.5f,
                RotateAnimation.RELATIVE_TO_SELF, 0.5f
            )

            animRotate.duration = 200
            animRotate.fillAfter = true
            animSet.addAnimation(animRotate)
            getViewBindding().swapRoute.startAnimation(animSet)
        })



        sequence.addSequenceItem(
            getViewBindding().selectDepDate,
            "Nhấn chọn ngày đi", "OK Đã hiểu"
        )

        getViewBindding().selectDepDate.setOnClickListener(View.OnClickListener {
            try {
                val i = Intent(
                    applicationContext,
                    AmLich::class.java
                )
                i.putExtra("depDate", dateFormat.format(depDate!!.getTime()))
                i.putExtra("origin", getViewBindding().originCode.getText().toString())
                i.putExtra("destination", getViewBindding().destinationCode.getText().toString())
                startActivityForResult(i, REQUEST_CODE_DEP_DATE)
            } catch (except: Exception) {
            }
        })

        sequence.addSequenceItem(
            getViewBindding().selectDepDate,
            "Nhấn chọn ngày về", "OK Đã hiểu"
        )
        getViewBindding().selectRetDate.setOnClickListener(View.OnClickListener {
            try {
                getViewBindding().checkRoundtrip.performClick()

                if (retDate!!.after(depDate)) {
                } else {
                    retDate!!.setTime(depDate!!.getTime())
                    retDate!!.add(Calendar.DATE, 2)
                }

                val i = Intent(
                    applicationContext,
                    AmLich::class.java
                )
                i.putExtra("depDate", dateFormat.format(depDate!!.getTime()))
                i.putExtra("reDate", dateFormat.format(retDate!!.getTime()))
                i.putExtra("origin", getViewBindding().originCode.getText().toString())
                i.putExtra("destination", getViewBindding().destinationCode.getText().toString())
                startActivityForResult(i, REQUEST_CODE_RE_DATE)

                setDateToView(retDate!!.getTime(), true)
            } catch (except: Exception) {
            }
        })

        sequence.addSequenceItem(
            findViewById<View>(R.id.showPaxSelect),
            "Chọn số lượng hành khách", "OK Đã hiểu"
        )
        sequence.addSequenceItem(
            findViewById<View>(R.id.search),
            "Nhấn tìm chuyến bay", "OK. Đã hiểu. Bắt đầu đặt vé"
        )


        //setCurrentDate();
        addButtonListener()
        if (AppConfigs.getInstance().getConfig().getBoolean("on_roundtrip")) {
            getViewBindding().checkRoundtrip.performClick()
        } else {
            getViewBindding().checkOneWay.performClick()
        }

        // ATTENTION: This was auto-generated to handle app links.
        val appLinkIntent = intent
        val appLinkAction = appLinkIntent.action
        val appLinkData = appLinkIntent.data

        if (AppConfigs.getInstance().getConfig().getBoolean("show_tip")) {
            sequence.start()
        }
        searchRequest()
    }

    fun searchRequest() {
        val `in` = intent
        if (`in`.hasExtra("originCode")) {
            getViewBindding().originCode.setText(`in`.getStringExtra("originCode"))
            getViewBindding().originName.setText(
                Common.getAirPortName(
                    `in`.getStringExtra("originCode"),
                    false
                )
            )
        }

        if (`in`.hasExtra("destinationCode")) {
            getViewBindding().destinationCode.setText(`in`.getStringExtra("destinationCode"))
            getViewBindding().destinationName.setText(
                Common.getAirPortName(
                    `in`.getStringExtra("destinationCode"),
                    false
                )
            )
        }
        if (`in`.hasExtra("departureDate")) {
            depDate!!.time = Common.stringToDate(
                `in`.getStringExtra("departureDate"),
                "yyyy-MM-dd"
            ).time
            setDateToView(depDate!!.time, false)
        }
        if (`in`.hasExtra("returnDate")) {
            btnCheckRoundTrip!!.performClick()
            retDate!!.time = Common.stringToDate(
                `in`.getStringExtra("returnDate"),
                "yyyy-MM-dd"
            ).time
            setDateToView(retDate!!.time, true)
        }
        if (`in`.hasExtra("adultCount")) {
            adultCount = `in`.getIntExtra("adultCount", 1)
            getViewBindding().adultCount.setText((adultCount.toString() + ""))
        }
        if (`in`.hasExtra("childCount")) {
            childCount = `in`.getIntExtra("childCount", 0)
            getViewBindding().childCount.setText((childCount.toString() + ""))
        }
        if (`in`.hasExtra("infantCount")) {
            infantCount = `in`.getIntExtra("infantCount", 0)
            getViewBindding().infantCount.setText((infantCount.toString() + ""))
        }
    }

    fun setCurrentDate() {
        txtDepDate!!.text = dateFormat.format(depDate!!.time)
    }


    fun selectPassger() {
        try {
            dialog = BottomSheetDialog(this)
            dialog!!.setContentView(selectPassengerView!!)

            val txtAdult = selectPassengerView!!.findViewById<TextView>(R.id.sheet_adult_number)
            val txtChild = selectPassengerView!!.findViewById<TextView>(R.id.sheet_child_number)
            val txtInfant = selectPassengerView!!.findViewById<TextView>(R.id.sheet_infant_number)

            val inAdult = selectPassengerView!!.findViewById<LinearLayout>(R.id.btn_in_adult)
            val deAdult = selectPassengerView!!.findViewById<LinearLayout>(R.id.btn_de_adult)

            val inChild = selectPassengerView!!.findViewById<LinearLayout>(R.id.btn_in_child)
            val deChild = selectPassengerView!!.findViewById<LinearLayout>(R.id.btn_de_child)

            val inInfant = selectPassengerView!!.findViewById<LinearLayout>(R.id.btn_in_infant)
            val deInfant = selectPassengerView!!.findViewById<LinearLayout>(R.id.btn_de_infant)

            val choicePassengerBtn =
                selectPassengerView!!.findViewById<AppCompatButton>(R.id.select_passenger_button)
            choicePassengerBtn.setOnClickListener { if (dialog!!.isShowing()) dialog!!.dismiss() }


            val inAnim = AnimationUtils.loadAnimation(
                this,
                android.R.anim.fade_in
            )
            inAnim.duration = 250


            inAdult.setOnClickListener {
                if (adultCount < 9 && (adultCount + childCount) < 9) {
                    adultCount++
                    txtAdult.text = (adultCount.toString() + "")
                    getViewBindding().adultCount.setText((adultCount.toString() + ""))
                    txtAdult.startAnimation(inAnim)
                } else {
                    Toast.makeText(
                        applicationContext,
                        "Tối đa 9 hành khách (người lớn và trẻ em)",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }
            deAdult.setOnClickListener {
                if (adultCount > 1) {
                    adultCount = adultCount - 1
                    txtAdult.text = (adultCount.toString() + "")
                    getViewBindding().adultCount.setText((adultCount.toString() + ""))
                    txtAdult.startAnimation(inAnim)
                }
            }
            inChild.setOnClickListener {
                if (childCount < 9 && (adultCount + childCount) < 9) {
                    childCount++
                    txtChild.text = (childCount.toString() + "")
                    getViewBindding().childCount.setText((childCount.toString() + ""))
                    txtChild.startAnimation(inAnim)
                } else {
                    Toast.makeText(
                        applicationContext,
                        "Tối đa 9 hành khách (người lớn và trẻ em)",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }
            deChild.setOnClickListener {
                if (childCount > 0) {
                    childCount = childCount - 1
                    txtChild.text = (childCount.toString() + "")
                    getViewBindding().childCount.setText((childCount.toString() + ""))
                    txtChild.startAnimation(inAnim)
                }
            }
            inInfant.setOnClickListener {
                if (infantCount < adultCount) {
                    infantCount++
                    txtInfant.text = (infantCount.toString() + "")
                    getViewBindding().infantCount.setText((infantCount.toString() + ""))
                    getViewBindding().infantCount.setText((infantCount.toString() + ""))
                    txtInfant.startAnimation(inAnim)
                } else {
                    Toast.makeText(
                        applicationContext,
                        "Số lượng em bé phải nhỏ hơn hoặc bằng người lớn",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }
            deInfant.setOnClickListener {
                if (infantCount > 0) {
                    infantCount = infantCount - 1
                    txtInfant.text = (infantCount.toString() + "")
                    getViewBindding().infantCount.setText((infantCount.toString() + ""))
                    txtInfant.startAnimation(inAnim)
                }
            }
        } catch (E: Exception) {
        }
    }


    fun addButtonListener() {
        //set default dep date

        setDateToView(depDate!!.time, false)
        btnCheckOneWay!!.setOnClickListener { // TODO Auto-generated method stub
            isRoundTrip = false
            setDateToView(null, true)

            btnCheckOneWay!!.background =
                resources.getDrawable(R.drawable.button_one_way)
            btnCheckOneWay!!.setTextColor(Color.parseColor("#FFFFFF"))

            btnCheckRoundTrip!!.setBackgroundColor(resources.getColor(R.color.fui_transparent))
            btnCheckRoundTrip!!.setTextColor(Color.parseColor("#00a2e3"))
        }


        btnCheckRoundTrip!!.setOnClickListener {
            if (retDate!!.after(depDate) && retDate!![Calendar.DATE] != depDate!![Calendar.DATE]) {
                setDateToView(retDate!!.time, true)
            } else {
                retDate!!.time = depDate!!.time
                retDate!!.add(Calendar.DATE, 2)
                setDateToView(retDate!!.time, true)
            }
            isRoundTrip = true

            btnCheckRoundTrip!!.background =
                resources.getDrawable(R.drawable.button_return)
            btnCheckRoundTrip!!.setTextColor(Color.parseColor("#FFFFFF"))

            btnCheckOneWay!!.setBackgroundColor(resources.getColor(R.color.fui_transparent))
            btnCheckOneWay!!.setTextColor(Color.parseColor("#00a2e3"))
        }


        btnSearch!!.setOnClickListener {
            if (isInternetConnected) {
                if (isRoundTrip && (depDate!!.timeInMillis > retDate!!.timeInMillis)) {
                    Toast.makeText(
                        this@SearchActivity,
                        "Ngày chuyến bay lượt về phải sau ngày lượt đi !",
                        Toast.LENGTH_SHORT
                    ).show()
                } else if (getViewBindding().originCode.getText().toString()
                        .equals(getViewBindding().destinationCode.getText().toString())
                ) {
                    Toast.makeText(
                        this@SearchActivity,
                        "Vui lòng chọn điểm đến và điểm đi khác nhau !",
                        Toast.LENGTH_SHORT
                    ).show()
                } else if (getViewBindding().originCode.text.length !== 3) {
                    Toast.makeText(
                        this@SearchActivity,
                        "Vui lòng chọn điểm đi !",
                        Toast.LENGTH_SHORT
                    ).show()
                } else if (getViewBindding().destinationCode.text.length !== 3) {
                    Toast.makeText(
                        this@SearchActivity,
                        "Vui lòng chọn điểm đến !",
                        Toast.LENGTH_SHORT
                    ).show()
                } else {
                    val settings = getSharedPreferences("12BAY-APP-CONFIG", 0)
                    val editor = settings.edit()
                    editor.putString("from", getViewBindding().originCode.getText().toString())
                    editor.putString("to", getViewBindding().destinationCode.getText().toString())
                    editor.putBoolean("isDomestic", isDomestic)

                    editor.apply()


                    page = "1"
                    originCode = getViewBindding().originCode.getText().toString()
                    destinationCode = getViewBindding().destinationCode.getText().toString()


                    val dF = SimpleDateFormat("yyyy-MM-dd")
                    val departureTime = dF.format(depDate!!.time)
                    val returnTime = dF.format(retDate!!.time)


                    if (getViewBindding().originCode.getText().toString().trim().isEmpty()) {
                        Toast.makeText(
                            this@SearchActivity,
                            "Chưa chọn điểm đi ",
                            Toast.LENGTH_SHORT
                        ).show()
                    } else if (getViewBindding().destinationCode.getText().toString().trim()
                            .isEmpty()
                    ) {
                        Toast.makeText(
                            this@SearchActivity,
                            "Chưa chọn điểm đến",
                            Toast.LENGTH_SHORT
                        ).show()
                        getViewBindding().destinationCode.requestFocus()
                    }

                    try {
                        val params = Bundle()
                        params.putString(
                            FirebaseAnalytics.Param.ORIGIN,
                            Common.getAirPortCode(originCode)
                        )
                        params.putString(
                            FirebaseAnalytics.Param.DESTINATION,
                            Common.getAirPortCode(destinationCode)
                        )
                        params.putString(FirebaseAnalytics.Param.START_DATE, departureTime)
                        params.putString(FirebaseAnalytics.Param.END_DATE, returnTime)
                        params.putInt(
                            FirebaseAnalytics.Param.NUMBER_OF_PASSENGERS,
                            (adultCount + childCount + infantCount)
                        )
                        firebaseAnalytics.logEvent(FirebaseAnalytics.Event.SEARCH, params)
                    } catch (e: Exception) {
                        AppConfigs.logException(e)
                    }


                    //NEW LA CHON XEM VE RE
                    var `in` = Intent()
                    `in` = if (isCheapSearch!!.isChecked && isDomestic) {
                        Intent(
                            applicationContext, FlightWeekViewActivity::class.java
                        )
                    } else {
                        if (isDomestic) {
                            Intent(
                                applicationContext,
                                SearchResult::class.java
                            )
                        } else {
                            Intent(
                                applicationContext,
                                FlightSearchActivity::class.java
                            )
                        }
                    }


                    AppConfigs.Log("departureTime", departureTime)
                    `in`.putExtra("originCode", getViewBindding().originCode.getText().toString())
                    `in`.putExtra(
                        "destinationCode",
                        getViewBindding().destinationCode.getText().toString()
                    )
                    `in`.putExtra("departureTime", departureTime)
                    `in`.putExtra("returnTime", returnTime)
                    `in`.putExtra("adult", adultCount)
                    `in`.putExtra("child", childCount)
                    `in`.putExtra("infant", infantCount)
                    `in`.putExtra("isRoundTrip", isRoundTrip)

                    startActivity(`in`)
                    overridePendingTransition(
                        R.anim.enter,
                        R.anim.exit
                    )
                }
            } else {
                Common.showAlertDialog(
                    this@SearchActivity,
                    "Không có Internet",
                    "Xin vui lòng kiểm tra lại Wifi/3G để tiếp tục",
                    false,
                    true
                )
            }
        }
    }


    override fun onPrepareOptionsMenu(menu: Menu): Boolean {
        val searchMenu = menu.findItem(R.id.action_search)
        searchMenu.setIcon(R.drawable.ic_action_call)
        return true
    }


    override fun onStart() {
        super.onStart()
        LocalBroadcastManager.getInstance(this)
            .registerReceiver(mMessageReceiver, IntentFilter("bookingupdate"))
    }

    override fun onBackPressed() {
        if (isTaskRoot) {
            val intent = Intent(
                this,
                HomeActivity::class.java
            )
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            startActivity(intent)
            finish()
            super.onBackPressed()
        } else {
            super.onBackPressed()
        }
    }

    override fun onStop() {
        super.onStop()
        LocalBroadcastManager.getInstance(this).unregisterReceiver(mMessageReceiver)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (resultCode == Activity.RESULT_OK && requestCode == REQUEST_CODE_FROM) {
            if (data!!.hasExtra("code")) {
                getViewBindding().originName.setText(data.extras!!.getString("name"))
                getViewBindding().originCode.setText(data.extras!!.getString("code"))

                if (data.hasExtra("airportInfo")) {
                    val airport: AirportInfo? =
                        data.getSerializableExtra("airportInfo") as AirportInfo?

                    getViewBindding().originName.setText(airport?.name)
                    getViewBindding().originCode.setText(airport?.code)
                    isOriginDomectic = airport?.isDomestic!!

                    isDomestic = (isOriginDomectic && isDestinationDomectic)
                    settings!!.edit().putBoolean("isDomestic", isDomestic).apply()
                }
            }
        } else if (resultCode == Activity.RESULT_OK && requestCode == REQUEST_CODE_TO) {
            if (data!!.hasExtra("code")) {
                getViewBindding().destinationName.setText(data!!.extras!!.getString("name"))
                getViewBindding().destinationCode.setText(data!!.extras!!.getString("code"))

                if (data.hasExtra("airportInfo")) {
                    val airport: AirportInfo? =
                        data.getSerializableExtra("airportInfo") as AirportInfo?
                    getViewBindding().destinationName.setText(airport!!.name)
                    getViewBindding().destinationCode.setText(airport!!.code)
                    isDestinationDomectic = airport!!.isDomestic
                    isDomestic = (isOriginDomectic && isDestinationDomectic)
                    settings!!.edit().putBoolean("isDomestic", isDomestic).apply()
                }
            }
        } else if (resultCode == Activity.RESULT_OK && requestCode == REQUEST_CODE_DEP_DATE) {
            if (data!!.hasExtra("date")) {
                depDate!!.time =
                    Common.getDateFromString(data.extras!!.getString("date")).time
                setDateToView(depDate!!.time, false)
                if (depDate!!.after(retDate) && isRoundTrip) {
                    btnCheckRoundTrip!!.performClick()
                }
            }
        } else if (resultCode == Activity.RESULT_OK && requestCode == REQUEST_CODE_RE_DATE) {
            if (data!!.hasExtra("date")) {
                retDate!!.time =
                    Common.getDateFromString(data.extras!!.getString("date")).time
                setDateToView(retDate!!.time, true)
            }
        }
    }

    fun setDateToView(date: Date?, isReturn: Boolean) {
        try {
            if (date == null) {
                txtRetDate!!.text = ""
                (findViewById<View>(R.id.txtRetDayOfWeek) as TextView).text =
                    ""
                (findViewById<View>(R.id.txtRetYear) as TextView).text =
                    ""
                (findViewById<View>(R.id.txtRetLunarDate) as TextView).text =
                    ""
                (findViewById<View>(R.id.txtRetCaret) as IconicsImageView).setVisibility(
                    View.GONE
                )
            } else {
                if (isReturn) {
                    (findViewById<View>(R.id.txtRetCaret) as IconicsImageView).setVisibility(
                        View.VISIBLE
                    )
                    txtRetDate!!.text = Common.dateToString(date, "dd/MM")
                    (findViewById<View>(R.id.txtRetDayOfWeek) as TextView).text =
                        Common.getDayOfWeek(date)
                    (findViewById<View>(R.id.txtRetYear) as TextView).text =
                        Common.dateToString(date, "yyyy")
                    (findViewById<View>(R.id.txtRetLunarDate) as TextView).text =
                        ("Âm lịch " + Common.getLunarFromDate(date))
                } else {
                    txtDepDate!!.text = Common.dateToString(date, "dd/MM")
                    (findViewById<View>(R.id.txtDepDayofweek) as TextView).text =
                        Common.getDayOfWeek(date)
                    (findViewById<View>(R.id.txtDepYear) as TextView).text =
                        Common.dateToString(date, "yyyy")
                    (findViewById<View>(R.id.txtDepLunarDate) as TextView).text =
                        ("Âm lịch " + Common.getLunarFromDate(date))
                }
            }
        } catch (e: Exception) {
            AppConfigs.Log("setDepDateString ", e.toString())
        }
    }


    //    @Override
    //    protected void attachBaseContext(Context newBase) {
    //        super.attachBaseContext(IconicsContextWrapper.wrap(newBase));
    //    }
    private val mMessageReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            // Get extra data included in the Intent

            val message = intent.getStringExtra("message")
            val snackbar: Snackbar = Snackbar
                .make(getViewBindding().coordinatorLayout, message.toString(), 10000)
                .setAction("XEM CHI TIẾT", View.OnClickListener {
                    val `in` = Intent(
                        applicationContext,
                        PnrActivity::class.java
                    )
                    `in`.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                    startActivity(`in`)
                })
            snackbar.show()
            AppConfigs.Log("Message rc", "SEARCH")
        }
    }

    override val layoutId: Int
        get() = R.layout.activity_search

}
