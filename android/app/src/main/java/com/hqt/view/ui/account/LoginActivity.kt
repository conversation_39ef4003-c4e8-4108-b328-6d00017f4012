package com.hqt.view.ui.account

import android.app.Activity
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.databinding.DataBindingUtil
import com.android.volley.VolleyError
import com.facebook.AccessToken
import com.facebook.CallbackManager
import com.facebook.FacebookCallback
import com.facebook.FacebookException
import com.facebook.login.LoginResult
import com.google.android.gms.auth.api.identity.Identity
import com.google.android.gms.auth.api.identity.SignInClient
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInAccount
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.google.android.gms.common.api.ApiException
import com.google.android.gms.tasks.Task
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.firebase.auth.FacebookAuthProvider
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseAuthInvalidCredentialsException
import com.google.firebase.auth.FirebaseAuthUserCollisionException
import com.google.firebase.auth.GoogleAuthProvider
import com.google.firebase.auth.PhoneAuthCredential
import com.hqt.data.model.User
import com.hqt.data.model.request.OtpSendRequest
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ActivityLoginBinding
import com.hqt.datvemaybay.databinding.WidgetOtpLayoutBinding
import com.hqt.util.AppConfigs
import com.hqt.util.AppController
import com.hqt.util.AppController.Companion.instance
import com.hqt.util.Helper.clickWithDebounce
import com.hqt.util.SSLSendRequest
import com.hqt.util.SSLSendRequest.CallBackInterface
import com.hqt.view.ui.BaseActivityKt
import `in`.aabhasjindal.otptextview.OTPListener
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import org.json.JSONException
import org.json.JSONObject


class LoginActivity : BaseActivityKt<ActivityLoginBinding>() {
    override val layoutId: Int = R.layout.activity_login

    private var disposable: Disposable? = null
    private lateinit var auth: FirebaseAuth
    private lateinit var newUserInfo: User
    private lateinit var dialog: BottomSheetDialog
    private var oneTapClient: SignInClient? = null
    private var mAuthListener: FirebaseAuth.AuthStateListener? = null

    private lateinit var callbackManager: CallbackManager


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        auth = FirebaseAuth.getInstance()
        auth.setLanguageCode("vi")
        callbackManager = CallbackManager.Factory.create()


        getToolbar().title = "Đăng nhập tài khoản"
        getToolbar().setNavigationIcon(R.drawable.ic_action_back_home)

        getViewBindding().btnFacebook.setReadPermissions("email", "public_profile")
        getViewBindding().btnFacebook.registerCallback(
            callbackManager,
            object : FacebookCallback<LoginResult> {
                override fun onSuccess(loginResult: LoginResult) {
                    handleFacebookAccessToken(loginResult.accessToken)
                }

                override fun onCancel() {
                    Toast.makeText(
                        applicationContext,
                        "Đăng nhập không thành công",
                        Toast.LENGTH_SHORT
                    ).show()
                }

                override fun onError(error: FacebookException) {
                    Toast.makeText(
                        applicationContext,
                        "Đăng nhập không thành công",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            },
        )

        getViewBindding().btnFacebookSignIn.setOnClickListener {
            getViewBindding().btnFacebook.performClick()
        }


        supportActionBar!!.setDisplayShowHomeEnabled(true)
        dialog = BottomSheetDialog(this)

        if (isUserSigned && instance.user != null) {
            Toast.makeText(this, "Bạn đã đăng nhập rồi !", Toast.LENGTH_SHORT).show()
        }


        mAuthListener = FirebaseAuth.AuthStateListener { firebaseAuth ->
            val user = firebaseAuth.currentUser
            if (user != null) { // User is signed in
                Common.commonSave(
                    applicationContext,
                    user.displayName,
                    user.email,
                    user.uid,
                    if (user.photoUrl == null) "" else user.photoUrl.toString(),
                    ""
                )
                getUserInfo()
            }

        }

        // initFirebaseAuth()
        oneTapClient = Identity.getSignInClient(this)

        initClickListener()
    }

    private fun initClickListener() {
        getViewBindding().btnPhoneSignIn.setOnClickListener {
            showOtpVerify()
        }
        getViewBindding().btnGoogleSignIn.setOnClickListener {

            val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
                .requestIdToken(getString(R.string.default_web_client_id))
                .requestEmail()
                .build()


            val googleSignInClient = GoogleSignIn.getClient(this, gso)

            // Start the sign-in intent
            val signInIntent = googleSignInClient.signInIntent
            startForResult.launch(signInIntent)


        }


    }

    private fun handleFacebookAccessToken(token: AccessToken) {

        val credential = FacebookAuthProvider.getCredential(token.token)
        auth.signInWithCredential(credential)
            .addOnCompleteListener(this) { task ->
                if (task.isSuccessful) {
                    // Sign in success, update UI with the signed-in user's information

                    val user = auth.currentUser
                    Toast.makeText(
                        applicationContext,
                        "Chao " + user?.displayName,
                        Toast.LENGTH_SHORT
                    ).show()

                    getUserInfo(user?.uid)


                } else {
                    // If sign in fails, display a message to the user.
                    Log.w("TAG", "signInWithCredential:failure", task.exception)
                    Toast.makeText(
                        baseContext,
                        "Authentication failed.",
                        Toast.LENGTH_SHORT,
                    ).show()
                    updateUser()
                }
            }
    }


    private fun getUserInfo(userId: String? = null) {
        var uid = auth.uid
        if (userId != null) {
            uid = userId
        }

        SSLSendRequest(this).GET(
            true,
            "users/$uid",
            JSONObject(),
            object : CallBackInterface {

                override fun onSuccess(response: JSONObject, cached: Boolean) {
                    try {
                        val jData = response.getJSONObject("data")
                        val user = instance.gSon.fromJson(jData.toString(), User::class.java)
                        instance.user = user
                        newUserInfo = user.copy()

                        finish()
                    } catch (e: JSONException) {
                        AppConfigs.logException(e)
                    }
                }

                override fun onFail(error: VolleyError) { // LOGIN ERROR
                }
            })
    }

    private fun updateUser() {

        try {
            disposable = AppController.instance.getService()
                .updateUser(AppController.instance.user!!.uid!!, getViewBindding().user!!)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe {


                }.observeOn(AndroidSchedulers.mainThread()).toObservable().doOnComplete {
                    showSnackBarMessage("Cập nhật thông tin thành công!", R.color.green)

                }.subscribe({ response ->


                }, { throwable ->

                    showSnackBarMessage(
                        "Cập nhật thông tin không thành công. Vui lòng thử lại sau",
                        R.color.black
                    )
                    AppConfigs.logException(throwable)
                })
        } catch (e: Exception) {
            AppConfigs.logException(e)
        }
    }

    val profile: Unit
        get() {
            SSLSendRequest(this).GET(true,
                "users/" + FirebaseAuth.getInstance().currentUser!!.uid,
                JSONObject(),
                object : CallBackInterface {
                    override fun onSuccess(response: JSONObject, cached: Boolean) {
                        try {
                            val jData = response.getJSONObject("data")
                            val user = instance.gSon.fromJson(jData.toString(), User::class.java)
                            instance.user = user
                        } catch (e: JSONException) {
                            AppConfigs.logException(e)
                        }
                    }

                    override fun onFail(error: VolleyError) { // LOGIN ERROR
                    }
                })
        }

    override fun onDestroy() {
        disposable?.dispose()
        disposable = null

        super.onDestroy()
    }

    private fun showOtpVerify() {
        try {
            val layoutInflater = LayoutInflater.from(this)
            val binding: WidgetOtpLayoutBinding = DataBindingUtil.inflate(
                layoutInflater,
                R.layout.widget_otp_layout,
                null,
                false
            )


            binding.phoneSendBtn.clickWithDebounce(1000) {

                val phoneString = binding.phoneNumber.text.toString()
                if (phoneString.length != 10 || !phoneString.startsWith("0")) {
                    Toast.makeText(
                        applicationContext,
                        "Số điện thoại không đúng định dạng",
                        Toast.LENGTH_SHORT
                    ).show()
                } else {


                    sendRequestOTP(phoneString, binding)

                }


            }
            binding.btnVerify.clickWithDebounce(1000) {
                val phoneString = binding.phoneNumber.text.toString()
                val otp = binding.otpView.otp
                sendVerifyOTP(phoneString, binding, otp!!)
            }


            binding.otpView.otpListener = object : OTPListener {
                override fun onInteractionListener() {
                    binding.btnVerify.isEnabled = false
                }

                override fun onOTPComplete(otp: String) {
                    binding.btnVerify.isEnabled = true
                    binding.btnVerify.performClick()
                }
            }

            dialog.setContentView(binding.root)
            (binding.root.parent as View).setBackgroundColor(Color.TRANSPARENT)
            (binding.root.parent as View).setBackgroundResource(R.drawable.rounded_dialog)
            val mBehavior = BottomSheetBehavior.from(binding.root.parent as View)
            mBehavior.state = BottomSheetBehavior.STATE_EXPANDED
            dialog.window!!.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)

            if (!dialog.isShowing) dialog.show()

            binding.phoneNumber.requestFocus()
        } catch (e: java.lang.Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }

    }

    val startForResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode == Activity.RESULT_OK) {
                val data = result.data
                val task: Task<GoogleSignInAccount> = GoogleSignIn.getSignedInAccountFromIntent(
                    data
                )

                val account = task.getResult(ApiException::class.java)
                val idToken = account.idToken
                when {
                    idToken != null -> {

                        val firebaseCredential = GoogleAuthProvider.getCredential(idToken, null)
                        auth.signInWithCredential(firebaseCredential)
                            .addOnCompleteListener(this) { task ->
                                if (task.isSuccessful) {
                                    // Sign in success, update UI with the signed-in user's information
                                    Toast.makeText(
                                        applicationContext,
                                        "Xin chào ${auth.currentUser?.displayName}",
                                        Toast.LENGTH_SHORT
                                    ).show()
                                    getUserInfo()

                                } else {
                                    // If sign in fails, display a message to the user.
                                    AppConfigs.Log(
                                        "TAG",
                                        "signInWithCredential:failure" + task.exception
                                    )
                                    //updateUI(null)
                                }
                            }

                    }

                    else -> {
                        // Shouldn't happen.

                    }
                }
            }
        }

    private fun loginWithToken(authToken: String) {


        auth.signInWithCustomToken(authToken).addOnCompleteListener(this) { task ->
            if (task.isSuccessful) {
                // Sign in success, update UI with the signed-in user's information
                val user = auth.currentUser

                dismissProgressDialog()
                updateUser()

                Toast.makeText(
                    applicationContext,
                    "Xin chào ${auth.currentUser?.displayName}",
                    Toast.LENGTH_SHORT
                ).show()

                finish()
            } else {
                // If sign in fails, display a message to the user.
                dismissProgressDialog()
                Toast.makeText(
                    baseContext,
                    "Đăng nhập không thành công. Vui lòng thử lại sau.",
                    Toast.LENGTH_SHORT,
                ).show()
            }
        }

    }

    private fun linkWithCredential(credential: PhoneAuthCredential) {

        auth.currentUser!!.linkWithCredential(credential).addOnCompleteListener(this) { task ->
            if (task.isSuccessful) {
                if (dialog.isShowing) dialog.hide()
                updateUser()
                auth.currentUser!!.uid
            } else {

                if (task.exception is FirebaseAuthInvalidCredentialsException) {
                    dialog.findViewById<TextView>(R.id.otp_error)!!.visibility = View.VISIBLE
                } else if (task.exception is FirebaseAuthUserCollisionException) {
                    dialog.findViewById<TextView>(R.id.otp_error)!!.text =
                        "Số điện thoại đã được sử dụng ở tài khoản khác. Vui lòng đăng nhập lại!"
                    dialog.findViewById<TextView>(R.id.otp_error)!!.visibility = View.VISIBLE
                } else {
                    dialog.findViewById<TextView>(R.id.otp_error)!!.text =
                        "Xác minh OTP không thành công. Vui lòng thử lại sau!"
                    dialog.findViewById<TextView>(R.id.otp_error)!!.visibility = View.VISIBLE
                }
            }
        }
    }

    private fun sendRequestOTP(phoneNumber: String, bindingOtp: WidgetOtpLayoutBinding) {

        val request = OtpSendRequest()
        request.phoneNumber = phoneNumber
        request.deviceToken = Common.FCM_TOKEN
        request.hash = Common.getKeyHash()


        disposable =
            AppController.instance.getService().postRequestOTP(request).subscribeOn(Schedulers.io())
                .doOnSubscribe {
                    showProgressDialog(
                        "Đang tải dữ liệu ...",
                        false,
                        false
                    )


                }.observeOn(AndroidSchedulers.mainThread()).subscribe({ response ->
                    try {

                        if (response.data != null) {

                            if (response.data.status) {
                                bindingOtp.sendStatus.text =
                                    Common.convertHTML("Mã OTP đang được gửi đến số điện thoại <b>$phoneNumber</b>")
                                bindingOtp.otpConfirmView.visibility = View.VISIBLE
                                bindingOtp.otpPhoneInputView.visibility = View.GONE
                                bindingOtp.otpView.requestFocusOTP()
                                if (!response.data.otp.isNullOrEmpty()) {
                                    bindingOtp.otpView.setOTP(response.data.otp!!)
                                }
                            } else {
                                bindingOtp.phoneError.text = response.data.message
                                bindingOtp.phoneError.visibility = View.VISIBLE
                            }
                        }
                    } catch (e: Exception) {

                    }
                    dismissProgressDialog()

                }, { throwable ->

                    throwable.printStackTrace()
                    dismissProgressDialog()
                })
    }

    private fun sendVerifyOTP(
        phoneNumber: String,
        bindingOtp: WidgetOtpLayoutBinding,
        otp: String
    ) {

        val request = OtpSendRequest()
        request.phoneNumber = phoneNumber
        request.deviceToken = Common.FCM_TOKEN
        request.hash = Common.getKeyHash()
        request.otp = otp


        disposable =
            AppController.instance.getService().postVerifyOTP(request).subscribeOn(Schedulers.io())
                .doOnSubscribe {
                    showProgressDialog(
                        "Đang tải dữ liệu ...",
                        false,
                        false
                    )

                }.observeOn(AndroidSchedulers.mainThread()).subscribe({ response ->

                    if (response.data != null) {

                        if (response.data.status) {
                            bindingOtp.otpError.text = response.data.message

                            val phoneToken = response.data.token
                            loginWithToken(phoneToken!!)
                        } else {
                            bindingOtp.otpError.text = response.data.message
                            dismissProgressDialog()
                        }

                    } else {
                        dismissProgressDialog()
                    }


                }, { throwable ->

                    throwable.printStackTrace()
                    dismissProgressDialog()
                })
    }

}