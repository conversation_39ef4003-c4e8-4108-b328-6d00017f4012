package com.hqt.view.ui.seatmap

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import android.widget.Toast
import androidx.core.view.children
import com.google.android.flexbox.FlexboxLayout
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.SeatViewItemBinding
import com.hqt.view.ui.bus.BusSelectActivity

@SuppressLint("ViewConstructor") class SeatView(context: Context,
    seatTitle: SeatIdentifierTitle,
    rowId: String,
    percent: Float,
    isReturnTrip: Boolean,
    isBusSeat: Boolean) : LinearLayout(context) {
    var seatOption: SeatOption? = null
    var seatId = ""
    var isIndex = false
    var isSelect = false
    var isReturn = false
    var isBusSeat = isBusSeat


    lateinit var binding: SeatViewItemBinding

    init {
        binding = SeatViewItemBinding.inflate(LayoutInflater.from(context), this, false)
        binding.textTitle.text = seatTitle.value

        addView(binding.root)

        if (seatTitle.value == null || seatTitle.value!!.isEmpty()) {
            binding.seatGroupView.visibility = View.GONE
            isIndex = true
        } else {
            binding.seatTitleView.visibility = View.GONE
            isIndex = false
        }
        val lpLeft = FlexboxLayout.LayoutParams(FlexboxLayout.LayoutParams.MATCH_PARENT,
            FlexboxLayout.LayoutParams.MATCH_PARENT)

        isReturn = isReturnTrip
        lpLeft.flexBasisPercent = percent
        layoutParams = lpLeft

        if (isBusSeat) {
            val vLayoutParams = binding.seatGroupView.layoutParams
            val newV = (vLayoutParams.height * 1.2).toInt()
            vLayoutParams.width = newV
            vLayoutParams.height = newV
            binding.seatGroupView.layoutParams = vLayoutParams

        }
    }

    fun setBackgroundColor() {
        setBackgroundResource(R.drawable.corner_full_primary)
    }


    fun selectSeat(select: Boolean) {
        try {
            if (isBusSeat) {
                val activity = (context as BusSelectActivity)
                if (select) {
                    if (activity.getFragment().getAllowSelect()) {
                        binding.seatGroupView.setCardBackgroundColor(Color.parseColor("#FB953B"))
                    } else {
                        Toast.makeText(context, "Bạn đã chọn đủ số ghế cho phép", Toast.LENGTH_SHORT).show()
                    }

                    activity.getFragment().clearSelect(this)

                } else {
                    if (seatOption != null) binding.seatGroupView.setCardBackgroundColor(Color.parseColor("#" + seatOption?.color))

                    isSelect = select
                }
            } else {
                val activity = (context as SelectSeatActivity)
                if (select) {
                    if (activity.getFragment(isReturn).getAllowSelect()) {
                        binding.seatGroupView.setCardBackgroundColor(Color.parseColor("#FB953B"))
                    } else {
                        Toast.makeText(context, "Bạn đã chọn đủ số ghế cho phép", Toast.LENGTH_SHORT).show()
                    }

                    (context as SelectSeatActivity).getFragment(isReturn).clearSelect(this)

                } else {
                    if (seatOption != null) binding.seatGroupView.setCardBackgroundColor(Color.parseColor("#" + seatOption?.color))

                    isSelect = select
                }
            }


        } catch (E: Exception) {
            E.printStackTrace()
        }
    }

    fun setSeatInfo(seatOption: SeatOption) {
        try {
            binding.seatView.visibility = View.VISIBLE
            if (isBusSeat) {
                binding.textTitle.text = seatOption.text.toString()
            } else {
                binding.rowNumber.text = seatOption.rowIndex.toString()
            }
            this.seatOption = seatOption
            binding.seatGroupView.setCardBackgroundColor(Color.parseColor("#" + seatOption.color))
            binding.seatGroupView.visibility = View.VISIBLE
            if (seatOption.available) {
                binding.seatGroupView.setOnClickListener {
                    selectSeat(true)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }


}