package com.hqt.view.ui.priceboard

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentTransaction
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ActivityPriceBoardBinding
import com.hqt.util.AppConfigs
import com.hqt.view.ui.BaseActivityKt
import com.hqt.view.ui.HomeActivity


class PriceBoardActivity : BaseActivityKt<ActivityPriceBoardBinding>() {

    override val layoutId: Int = R.layout.activity_price_board
    val REQUEST_AIRPORT = 2

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val fragmentManager: FragmentManager = supportFragmentManager
        val fragmentTransaction: FragmentTransaction = fragmentManager.beginTransaction()
        fragmentTransaction.add(R.id.fragment_container, PricesBoardFragment())
        fragmentTransaction.commit()
        initAnalytics()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (resultCode == Activity.RESULT_OK && requestCode == REQUEST_AIRPORT) {
            try {
                val manager = supportFragmentManager
                for (frag in manager.fragments) {
                    if (frag.javaClass == PricesBoardFragment::class.java && frag.isVisible) {
                        if (data != null && data.hasExtra("SELECTALL")) {
                            (frag as PricesBoardFragment).setRoute(true)
                        } else {
                            (frag as PricesBoardFragment).setRoute(false)
                        }
                    }
                }
            } catch (e: Exception) {
                AppConfigs.logException(e)
                e.printStackTrace()
            }
        }
        super.onActivityResult(requestCode, resultCode, data)
    }

    override fun onResume() {
        val manager = supportFragmentManager
        for (frag in manager.fragments) {
            if (frag.javaClass == PricesBoardFragment::class.java && frag.isVisible) {
                ((frag) as PricesBoardFragment).killTimer()
            }
        }
        super.onResume()
    }

    override fun onBackPressed() {
        try {
            val manager = supportFragmentManager
            for (frag in manager.fragments) {
                if (frag.javaClass == PricesBoardFragment::class.java && frag.isVisible) {
                    ((frag) as PricesBoardFragment).killTimer()
                }
            }
        } catch (e: Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }

        if (this.isTaskRoot) {
            val `in` = Intent(this, HomeActivity::class.java)
            startActivity(`in`)
            finish()
        } else {
            super.onBackPressed()
        }


    }

    override fun onRestart() {

        super.onRestart()
    }

    override fun onSystemSettingChange(type: AppConfigs.SystemSettingType) {

    }

    private fun initAnalytics() {

        val params = Bundle()
        firebaseAnalytics.logEvent("price_board_view", params)
        firebaseAnalytics.setCurrentScreen(this, "price_board_view", null)
    }


}
