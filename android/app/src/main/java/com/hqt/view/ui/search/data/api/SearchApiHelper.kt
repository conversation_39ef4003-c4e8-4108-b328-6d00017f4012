package com.hqt.view.ui.search.data.api

import com.hqt.base.model.HttpData
import com.hqt.view.ui.search.data.model.AirportGroup
import com.hqt.view.ui.search.data.model.FightTask
import com.hqt.view.ui.search.data.model.FlightV2
import javax.inject.Inject
import javax.inject.Singleton


@Singleton
class SearchApiHelper @Inject constructor(
    private val api : SearchService
) {

    suspend fun getAirport(q : String? = null) : HttpData<ArrayList<AirportGroup>> {
        return api.getAirport(q)
    }
    suspend fun getAirportList(): HttpData<Any> {
        return api.getAirportList()
    }
    suspend fun getFlightTask(airline : String?, originCode : String?, destinationCode : String?, request : Any?): HttpData<FightTask> {

        return api.getFlightTask(airline, originCode, destinationCode, request)
    }
}