package com.hqt.view.ui.flightSearch.model

import com.google.gson.annotations.SerializedName
import com.hqt.datvemaybay.Common
import java.io.Serializable
import java.util.*
import kotlin.collections.ArrayList

data class Flight(@SerializedName("isReturn") var isReturn: Boolean = false,
    @SerializedName("leg") var leg: Int? = null,
    @SerializedName("flightId") var flightId: Int? = null,
    @SerializedName("airline") var airline: String? = null,
    @SerializedName("airlineName") var airlineName: String? = null,
    @SerializedName("operating") var operating: String? = null,
    @SerializedName("operatingName") var operatingName: String? = null,
    @SerializedName("startPoint") var startPoint: String? = null,
    @SerializedName("startPointInfo") var startPointInfo: PointInfo? = null,
    @SerializedName("endPoint") var endPoint: String? = null,
    @SerializedName("endPointInfo") var endPointInfo: PointInfo? = null,
    @SerializedName("startDate") var startDate: String? = null,
    @SerializedName("endDate") var endDate: String? = null,
    @SerializedName("flightNumber") var flightNumber: String? = null,
    @SerializedName("duration") var duration: Int = 0,
    @SerializedName("stopNum") var stopNum: Int? = null,
    @SerializedName("flightValue") var flightValue: String? = null,
    @SerializedName("listSegment") var listSegment: ArrayList<Segment> = arrayListOf()) : Serializable {

    fun getStopString(): String {
        if (stopNum == 0) {
            return "Bay thẳng"
        } else {
            return "$stopNum Điểm dừng"
        }
    }

    fun getStopInfoString(): String {
        if (stopNum == 0) {
            return "Bay thẳng"
        } else {
            return "Nối chuyến tại " + getStopList()
        }
    }

    fun getTitle(): String {
        if (isReturn) return "Lượt về: "

        return "Lượt đi: "
    }

    fun getStartTime(): String {

        return startDate!!.substring(11, 16)
    }

    fun getEndTime(): String {
        var enddatetime = Common.stringToDate(endDate, "yyyy-MM-dd'T'HH:mm:ss")
        return Common.dateToString(enddatetime.time, "(dd/MM) HH:mm")
    }

    fun getAirlinesLogo(): String {
        return "https://ssl.12bay.vn/images/airlines/" + airline!!.lowercase() + ".gif"
    }

    fun getDiffDate(): String {

        val startdatetime = Common.stringToDate(startDate, "yyyy-MM-dd'T'HH:mm:ss")
        val enddatetime = Common.stringToDate(endDate, "yyyy-MM-dd'T'HH:mm:ss")


        return "+" + (enddatetime.get(Calendar.DAY_OF_YEAR) - startdatetime.get(Calendar.DAY_OF_YEAR)) + "D"

    }

    fun getEndTimeShort(): String {

        return endDate!!.substring(11, 16)
        var enddatetime = Common.stringToDate(endDate, "yyyy-MM-dd'T'HH:mm:ss")
        return Common.dateToString(enddatetime.time, "(dd/MM) HH:mm")
    }

    fun getEndDateShort(): String {

        var enddatetime = Common.stringToDate(endDate, "yyyy-MM-dd'T'HH:mm:ss")
        return Common.dateToString(enddatetime.time, "dd/MM")
    }

    fun getFlightTime(): String {
        val hour = duration.div(60)
        return "" + hour + "h" + (duration - hour * 60) + "p"
    }

    fun getStopList(): String {

        val l: ArrayList<String> = ArrayList()
        listSegment.forEach {
            if (it.hasStop!!) {
                l.add(it.stopPoint!!)
            }
        }

        return l.joinToString("-", "", "")
    }

    fun getStopDuration(): ArrayList<Int> {
        var stops = ArrayList<Int>()
        listSegment.forEach {

            stops.add(it.duration!!)
        }

        return stops
    }

    fun getFareRuleInfo(): String {
        if (listSegment[0].classInfo == null) {
            return "Phổ thông"
        }
        return listSegment[0].classInfo!!
    }

    fun getLunarDate(isStart: Boolean): String {
        return if (isStart) Common.getLunarDate(Common.stringToDate(startDate!!.substring(0, 10), "yyyy-MM-dd").time)
        else Common.getLunarDate(Common.stringToDate(endDate!!.substring(0, 10), "yyyy-MM-dd").time)

    }
}

