package com.hqt.view.ui.demo.ui

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.hqt.base.BaseViewModelV2
import com.hqt.base.model.State
import com.hqt.data.model.response.HomeFeatureResponse
import com.hqt.data.prefs.SharedPrefsHelper
import com.hqt.util.Log
import com.hqt.view.ui.demo.data.api.DemoApiHelper
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject


@HiltViewModel
class DemoViewModel @Inject constructor(
    private val demoApiHelper: DemoApiHelper,
    private val sharedPrefsHelper: SharedPrefsHelper,
) : BaseViewModelV2(){


    private val _homeLiveData: MutableLiveData<State<ArrayList<HomeFeatureResponse>>> = MutableLiveData()
    val homeLiveData: LiveData<State<ArrayList<HomeFeatureResponse>>> get() = _homeLiveData


    fun getFeature(){
        viewModelScope.launch {
            try {
                _homeLiveData.postValue(State.Loading)
                val result = demoApiHelper.getFeature()


                _homeLiveData.postValue(State.Success(result.data as ArrayList<HomeFeatureResponse>))

                Log.d("aaaa", result)

            }catch (ex : Exception){
                Log.logException(ex)
            }

        }
    }
}