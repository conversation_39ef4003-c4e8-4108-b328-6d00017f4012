package com.hqt.view.ui.booking.ui.adapter

import com.hqt.base.BaseAdapter
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ListFlightHistoryItemBinding
import com.hqt.view.ui.flighthistory.data.model.FlightHistoryItem


class FlightHistoryItemAdapterV2(private val listener: (FlightHistoryItem) -> Unit) :
    BaseAdapter<FlightHistoryItem, ListFlightHistoryItemBinding>(listener) {



    override fun getLayoutRes(): Int {
        return R.layout.list_flight_history_item
    }



    override fun bind(binding: ListFlightHistoryItemBinding, position: Int, model: FlightHistoryItem) {
        binding.apply {
            binding.viewmodel = model
            binding.executePendingBindings()



        }

    }





    override fun onItemClickListener(model: FlightHistoryItem) {
        listener(model)




    }

}