package com.hqt.view.ui.booking.ui.dialog

import android.app.DatePickerDialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.textfield.TextInputEditText
import com.hqt.base.model.State
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.PaxInputLayoutV2Binding
import com.hqt.util.Helper
import com.hqt.util.Log
import com.hqt.view.ui.booking.data.model.PassengerType
import com.hqt.view.ui.booking.ui.BookingViewModelV2
import com.hqt.view.ui.booking.ui.TypePaxViewModelV2
import com.hqt.view.ui.booking.ui.adapter.BaggageAdapter
import dagger.hilt.android.AndroidEntryPoint
import java.util.Calendar
import java.util.Locale

@AndroidEntryPoint
class InputPassengerDialog(private val isTrain: Boolean) : BottomSheetDialogFragment() {

    val viewModel: BookingViewModelV2 by activityViewModels()


    var binding: PaxInputLayoutV2Binding? = null


    var onActionDone: () -> Unit = {}

    private val baggageTripAdapter by lazy {
        BaggageAdapter {
            viewModel.clickPassenger.baggage = it

        }

    }
    private val baggageReturnAdapter by lazy {
        BaggageAdapter {

            viewModel.clickPassenger.returnBaggage = it
        }

    }

    companion object {
        const val TAG = "ModalBottomSheet"
    }


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = PaxInputLayoutV2Binding.inflate(layoutInflater, container, false)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)


        val dialogPromotionView = BottomSheetDialog(requireContext())

        binding?.apply {
            initRcv()


            paxState = <EMAIL>
            val paxViewModel = TypePaxViewModelV2(viewModel.clickPassenger, isTrain)
            viewModelTypePax = paxViewModel


            toolbar.title = ("Thông tin hành khách")

            toolbar.setBackgroundColor(
                ContextCompat.getColor(
                    requireContext(),
                    R.color.primary_dark
                )
            )
            var showYear = false
            var currentBirthDay =
                Common.stringToDate(viewModel.clickPassenger.birthday, "yyyy-MM-dd")

            if (currentBirthDay == null) {
                currentBirthDay = Calendar.getInstance()
                showYear = true
            }
            val dateSetListener =
                DatePickerDialog.OnDateSetListener { view, year, monthOfYear, dayOfMonth ->
                    currentBirthDay.set(Calendar.YEAR, year)
                    currentBirthDay.set(Calendar.MONTH, monthOfYear)
                    currentBirthDay.set(Calendar.DAY_OF_MONTH, dayOfMonth)
                    inputPaxBirthday.setText(
                        Common.dateToString(
                            currentBirthDay.time,
                            "yyyy-MM-dd"
                        )
                    )

                }


            //em bé thì ẩn
            if (isTrain || viewModel.clickPassenger.type == PassengerType.INFANT) {
                baggageSelectLayout.visibility = View.GONE
            } else {
                baggageSelectLayout.visibility = View.VISIBLE

            }

            selectBagReturnLayout.isVisible = viewModel.mBooking.value?.is_round_trip ?: false


            inputPaxBirthday.setOnClickListener {
                val dialog = DatePickerDialog(
                    requireContext(),
                    dateSetListener,
                    currentBirthDay.get(Calendar.YEAR),
                    currentBirthDay.get(Calendar.MONTH),
                    currentBirthDay.get(Calendar.DAY_OF_MONTH)
                )

                dialog.datePicker.maxDate =
                    viewModel.clickPassenger.getMaxBirthDate(isTrain).timeInMillis
                dialog.datePicker.minDate =
                    viewModel.clickPassenger.getMinBirthDate(isTrain).timeInMillis
                dialog.show()

                try {
                    if (showYear) dialog.findViewById<TextView>(
                        requireContext().resources?.getIdentifier(
                            "android:id/date_picker_header_year",
                            null,
                            null
                        ) ?: -1
                    ).performClick()
                } catch (e: Exception) {
                    e.printStackTrace()
                }

            }


            btnSelectPax.setOnClickListener {

                if (inputPaxGender.selectedItem == null) {
                    inputPaxGender.error = "Vui lòng chọn danh xưng"

                } else if (Helper.validatorInput(
                        inputPaxFullName.text.toString(),
                        Helper.ValidatorType.FULLNAME.text
                    ) != null && Helper.isInputRequired(
                        viewModel.clickPassenger,
                        Helper.ValidatorType.FULLNAME,
                        isTrain
                    )
                ) {
                    inputPaxFullName.setText(inputPaxFullName.text.toString())
                    inputPaxFullName.requestFocus()
                    return@setOnClickListener
                } else if (validateInput(inputPaxBirthday, Helper.ValidatorType.BIRTDATE)) {
                    return@setOnClickListener

                } else if (validateInput(inputPaxIdNumber, Helper.ValidatorType.IDNUMBER)) {
                    return@setOnClickListener

                } else {


                    val pax = viewModel.clickPassenger
                    pax.title = paxViewModel.getTitleByText(inputPaxGender.selectedItem.toString())
                    pax.isValidated = true

                    Log.d("data", viewModel.mBooking.value)


                    val first = pax.firstName.uppercase(Locale.ROOT)
                    val last = pax.lastName.uppercase(Locale.ROOT)

                    val isNameDuplicate = first.contains(last) || last.contains(first)

                    if (isNameDuplicate) {
                        MaterialAlertDialogBuilder(requireContext())
                            .setIcon(R.drawable.ic_bell_alert)
                            .setTitle("Kiểm tra thông tin hành khách")
                            .setMessage(
                                Common.convertHTML(
                                    "<strong> &emsp;${pax.getFullNameDetail()}</strong><br/>" +
                                            "<strong> &emsp;Ngày sinh: ${pax.birthday}</strong>"
                                )
                            )
                            .setPositiveButton("Đúng rồi") { _, _ ->

                                viewModel.updateAdult()
                                dialogPromotionView.dismiss()
                                dismiss()
                            }
                            .setNegativeButton("Nhập lại", null)
                            .show()
                    } else {
                        viewModel.updateAdult()
                        dialogPromotionView.dismiss()
                        dismiss()


                    }
                }
            }

            inputPaxClose.setOnClickListener {
                dismiss()
            }


        }




        dialog?.let {
            val sheet = it as BottomSheetDialog
            sheet.behavior.state = BottomSheetBehavior.STATE_EXPANDED
            sheet.behavior.isHideable = false
            sheet.behavior.isDraggable = false
        }


    }

    private fun initRcv() {
        binding?.apply {
            rcvBaggageTurns.apply {
                layoutManager = LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
                adapter = baggageTripAdapter
            }
            rcvBaggageReturn.apply {
                layoutManager =
                    LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
                adapter = baggageReturnAdapter
            }
        }
        observe()
    }


    fun observe() {
        viewModel.baggageListLiveData.observe(viewLifecycleOwner) {
            when (it) {
                is State.Error -> {

                }

                State.Loading -> {

                }

                is State.Success -> {
                    baggageTripAdapter.setData(it.data)
                }
            }
        }
        viewModel.baggageListReturnLiveData.observe(viewLifecycleOwner) {
            when (it) {
                is State.Error -> {

                }

                State.Loading -> {

                }

                is State.Success -> {
                    baggageReturnAdapter.setData(it.data)
                }
            }
        }


        viewModel.getBaggageSequentially()
    }

    private fun validateInput(input: TextInputEditText, type: Helper.ValidatorType): Boolean {
        val text = input.text.toString()
        val isInvalid = Helper.validatorInput(text, type.text) != null &&
                Helper.isInputRequired(viewModel.clickPassenger, type, isTrain)
        if (isInvalid) {
            input.setText(text)
            input.requestFocus()
        }
        return isInvalid
    }


}