package com.hqt.view.ui.search.data.model

import com.google.gson.annotations.SerializedName
import java.io.Serializable


data class AirportInfo(

    @field:SerializedName("code") val code: String? = null,
    @field:SerializedName("name") val name: String? = null,
    @field:SerializedName("short_name") val shortName: String? = null,
    @field:SerializedName("city") val city: String? = null,
    @field:SerializedName("country") val country: String? = null,
    @field:SerializedName("is_domestic") val isDomestic: Boolean = false
) : Serializable