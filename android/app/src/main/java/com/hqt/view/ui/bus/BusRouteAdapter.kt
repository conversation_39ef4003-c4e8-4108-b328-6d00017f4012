package com.hqt.view.ui.bus

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import androidx.databinding.DataBindingUtil
import androidx.databinding.ObservableField
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ListBusRouteItemBinding


class BusRouteAdapter(var mContext: Context, internal var contents: ArrayList<BusRoute>) :
    RecyclerView.Adapter<BusRouteAdapter.ViewHolder>() {
    class ViewHolder(var context: Context, val binding: ListBusRouteItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        val divisionName = ObservableField<String>("")
        fun bind(route: BusRoute) {

            binding.route = route


            //            if (binding.viewHolder == null) {
            //                binding.viewHolder = this
            //            }

            binding.route = route
            if (binding.handler == null) {
                binding.handler = BusSelectHandler(context)
            } // divisionName.set(train.originName)
            binding.executePendingBindings()
        }

    }

    override fun getItemCount(): Int {
        return contents.size
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {

        val layoutInflater = LayoutInflater.from(parent.context)
        val binding: ListBusRouteItemBinding = DataBindingUtil.inflate(layoutInflater,
            R.layout.list_bus_route_item,
            parent,
            false)

        return ViewHolder(mContext, binding)
    }


    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(contents[position])
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getItemViewType(position: Int): Int {
        return position
    }


}