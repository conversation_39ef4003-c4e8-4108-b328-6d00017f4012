package  com.hqt.view.ui.reward.di

import com.hqt.view.ui.reward.data.api.RewardService
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
class RewardApiModule {

    @Provides
    @Singleton
    fun provideRewardService(retrofit: Retrofit): RewardService {
        return retrofit.create(RewardService::class.java)
    }



}