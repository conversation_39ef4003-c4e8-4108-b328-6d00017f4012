package  com.hqt.view.ui.flighthistory.di

import com.hqt.view.ui.flighthistory.data.api.FlightHistoryService
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
class FlightHistoryApiModule {

    @Provides
    @Singleton
    fun provideFlightHistoryService(retrofit: Retrofit): FlightHistoryService {
        return retrofit.create(FlightHistoryService::class.java)
    }



}