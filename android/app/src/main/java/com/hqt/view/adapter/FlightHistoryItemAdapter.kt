package com.hqt.view.adapter

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.PopupMenu
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.hqt.view.ui.flighthistory.data.model.FlightHistoryItem
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ListFlightHistoryItemBinding
import com.hqt.util.Widget
import com.hqt.view.ui.flighthistory.ui.activity.MapViewActivity
import com.hqt.view.ui.flightwaches.FlightWachesList


class FlightHistoryItemAdapter(var mContext: Context, var contents: List<FlightHistoryItem>) : RecyclerView.Adapter<FlightHistoryItemAdapter.ViewHolder>() {
    class ViewHolder(var context: Context, val binding: ListFlightHistoryItemBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(flightHistoryItem: FlightHistoryItem) {

            binding.viewmodel = flightHistoryItem
            binding.executePendingBindings()
            binding.item.setOnClickListener {

                if (flightHistoryItem.status.live) {
                    val intent = Intent(context, MapViewActivity::class.java)
                    intent.putExtra("flightId", flightHistoryItem.id)
                    intent.putExtra("onMoveCamera", true)
                    if (!flightHistoryItem.status.live)
                        intent.putExtra("isHistory", true)
                    intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    context.startActivity(intent)
                    (context as Activity).overridePendingTransition(R.anim.enter, R.anim.exit)

                } else {
                    Widget.showFlightHistory(context, flightHistoryItem, false)
                }


            }

        }

        fun deleteClick(view: View, id: Int) {
            val popup = PopupMenu(context, view)

            popup.menuInflater
                    .inflate(R.menu.delete, popup.menu)
            popup.setOnMenuItemClickListener { item: MenuItem ->
                when (item!!.itemId) {
                    R.id.delete -> {
                        (context as FlightWachesList).deleteItemTask(id)
                    }
                }
                true
            }
            popup.show()
        }

    }

    override fun getItemCount(): Int {
        return contents.size
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {

        val layoutInflater = LayoutInflater.from(parent.context)
        val binding: ListFlightHistoryItemBinding = DataBindingUtil.inflate(layoutInflater, R.layout.list_flight_history_item, parent, false)
        return ViewHolder(mContext, binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(contents[position])
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getItemViewType(position: Int): Int {
        return position
    }


}