package com.hqt.view.ui.payment.ui.main

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.hqt.data.model.OrderInfo
import com.hqt.data.model.PaymentOrderXml
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.PaymentPaylateStoreFragmentBinding
import com.hqt.util.AppConfigs
import com.hqt.util.Widget
import com.hqt.view.ui.payment.NewPaymentActivity

class PaylateStorePaymentFragment : Fragment() {

    companion object {
        fun newInstance() = PaylateStorePaymentFragment()
    }

    lateinit var binding: PaymentPaylateStoreFragmentBinding
    private lateinit var viewModel: MainViewModel
    lateinit var orderInfo: PaymentOrderXml

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {


        binding = DataBindingUtil.inflate(
            inflater,
            R.layout.payment_paylate_store_fragment,
            container,
            false
        )
        setupToolbar()

        binding.content.btnDonePayment.setOnClickListener {

            Common.sendSms(
                context,
                "Hi 12bay. Toi da thanh toan xong don hang: " + orderInfo.bookingId,
                AppConfigs.getInstance().config.getString("sms")
            )

        }
        
        Widget.chatInfo(requireContext(), binding.content.widgetChat)
        return binding.root
    }

    fun bindOrder(order: PaymentOrderXml) {
        orderInfo = order
        binding.content.orderInfo = orderInfo
        setTimeLimit(orderInfo)

    }

    private fun setTimeLimit(orderInfo: PaymentOrderXml) {
        binding.toolbar.subtitle = "Đơn hàng #" + orderInfo.bookingId
        (activity as NewPaymentActivity).setTimeLimit(orderInfo.expiredDate, binding.headStatus)

    }

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        viewModel = ViewModelProvider(this).get(MainViewModel::class.java)
        // TODO: Use the ViewModel
    }

    private fun setupToolbar() {
        try {
            binding.toolbar.title = "Thanh toán sau tại cửa hàng"

            (activity as AppCompatActivity).setSupportActionBar(binding.toolbar)
            (activity as AppCompatActivity).supportActionBar!!.setDisplayHomeAsUpEnabled(true)
            binding.toolbar.bringToFront()
            binding.toolbar.setNavigationOnClickListener {
                (activity as AppCompatActivity).onBackPressed()
            }
        } catch (e: Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }

    }

}