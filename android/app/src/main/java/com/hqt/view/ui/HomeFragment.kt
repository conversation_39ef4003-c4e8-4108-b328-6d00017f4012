package com.hqt.view.ui

import android.content.Intent
import android.graphics.Color
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.Gravity
import android.view.View
import android.widget.ImageView
import android.widget.Toast
import androidx.recyclerview.widget.LinearLayoutManager
import com.bumptech.glide.Glide
import com.hqt.data.model.Post
import com.hqt.data.model.Snap
import com.hqt.data.model.response.HomeFeatureResponse
import com.hqt.datvemaybay.Checkin
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.ThanhToan
import com.hqt.datvemaybay.databinding.FragmentHomeBinding
import com.hqt.util.AppConfigs
import com.hqt.util.AppController.Companion.instance
import com.hqt.util.ViewUtil
import com.hqt.util.base.BaseFragment
import com.hqt.view.adapter.SnapAdapter
import com.hqt.view.ui.bus.BusSearchActivity
import com.hqt.view.ui.chart.WeeklyPriceChartActivity
import com.hqt.view.ui.flighthistory.ui.activity.MapViewActivity
import com.hqt.view.ui.flightwaches.FlightWachesList
import com.hqt.view.ui.reward.ui.activity.RewardActivity
import com.hqt.view.ui.search.ui.activity.SearchActivityV2
import com.hqt.view.ui.tour.TourListActivity
import com.hqt.view.ui.train.TrainSearchActivity

import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import org.json.JSONException
import java.util.Random

class HomeFragment : BaseFragment<FragmentHomeBinding>() {

    override fun getLayoutRes(): Int {
        return R.layout.fragment_home
    }

    private var snapAdapter: SnapAdapter? = null
    var onLoading = false
    var onRefresh = false
    var disposable: Disposable? = null
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)


        binding.toolbar.title = "12bay.vn"

        if (Build.VERSION.SDK_INT >= 21) {
            requireActivity().window.statusBarColor = Color.TRANSPARENT
        }
        (requireActivity() as BaseActivity).setSupportActionBar(binding.toolbar)
        binding.toolbar.setNavigationIcon(R.drawable.ic_action_home)
        binding.toolbar.setNavigationOnClickListener(View.OnClickListener {
            Toast.makeText(
                activity,
                "Chúc một ngày tốt lành :)",
                Toast.LENGTH_SHORT
            ).show()
        })


        binding.refresh.setOnRefreshListener {

            if (!onLoading) {
                binding.contentScrolling.shimmerViewContainer.startShimmer()
                binding.contentScrolling.shimmerViewContainer.visibility = View.VISIBLE
                binding.contentScrolling.recyclerView.visibility = View.GONE
                Handler(Looper.getMainLooper()).postDelayed(object : Runnable {
                    override fun run() {
                        onRefresh = true
                        getTopNew()
                    }
                }, 800)
            }


        }
        binding.contentScrolling.recyclerView.apply {
            setHasFixedSize(true)
            layoutManager =
                LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            isNestedScrollingEnabled = false
        }

        val rand = Random()
        val n = rand.nextInt(10)
        if (n % 2 == 0) {
            if (n < 3) {
                ViewUtil.addBadgeAt(context, binding.contentScrolling.btnStatus, "Mới")
            } else if (n < 7) {
                ViewUtil.addBadgeAt(context, binding.contentScrolling.btnTrainSearch, "Mới")
            } else {
                ViewUtil.addBadgeAt(context, binding.contentScrolling.btnFlightWatches, "Mới")
            }
        }
        Glide.with(this)
            .load(AppConfigs.getInstance().config.getString("top_banner"))
            .skipMemoryCache(true)
            .centerCrop()
            .placeholder(R.drawable.top_banner)
            .into((binding.root.findViewById<View>(R.id.headerBG) as ImageView))
        bindBtnListen()


        snapAdapter = SnapAdapter(activity)
        binding.contentScrolling.recyclerView.adapter = snapAdapter
        getTopNew()

    }

    fun getTopNew() {

        try {
            disposable = instance.getService()
                .getHomeFeature().subscribeOn(Schedulers.io())
                .doOnSubscribe {


                }.observeOn(AndroidSchedulers.mainThread()).toObservable().doOnComplete {

                }.subscribe({ response ->
                    processJson(response)
                    binding.refresh.isRefreshing = false
                }, { throwable ->

                    Common.showAlertDialog(
                        context,
                        "\uD83D\uDEA7 Thông báo",
                        "12Bay.vn đang tiến hành nâng cấp hệ thống. Bạn vui lòng quay lại sau ít phút ⏰",
                        true,
                        true
                    )
                    binding.refresh.isRefreshing = false
                    AppConfigs.logException(throwable)
                })
        } catch (e: Exception) {
            AppConfigs.logException(e)
        }
    }

    fun processJson(response: HomeFeatureResponse) {
        try {
            if (snapAdapter!!.itemCount > 0) snapAdapter!!.clear()
            val listNew = response.data
            for (i in 0 until (listNew?.size ?: 0)) {
                val featureItem = listNew!![i]
                val posts: MutableList<Post> = ArrayList()
                val postList = featureItem.posts
                for (y in 0 until postList!!.size) {
                    val post = postList.get(y)
                    posts.add(
                        Post(
                            post.title,
                            post.subtitle,
                            post.image,
                            post.url
                        )
                    )
                }
                snapAdapter!!.addSnap(
                    Snap(
                        Gravity.START,
                        featureItem.title,
                        featureItem.subtitle,
                        "bg",
                        posts,
                        featureItem.type
                    )
                )
            }
            binding.contentScrolling.shimmerViewContainer.stopShimmer()
            binding.contentScrolling.shimmerViewContainer.visibility = View.GONE
            binding.contentScrolling.recyclerView.visibility = View.VISIBLE
            binding.contentScrolling.recyclerView.adapter = snapAdapter
            onLoading = false
            if (onRefresh) {
                (requireActivity() as BaseActivity).shakeItBaby()
                onRefresh = false
            }

        } catch (e: JSONException) {
            AppConfigs.logException(e)
        }
    }

    fun bindBtnListen() {
        binding.contentScrolling.btnSearch.setOnClickListener {
            if ((activity as HomeActivity?)!!.isInternetConnected) {
//                val intent = Intent(context, SearchActivity::class.java)
                val intent = Intent(context, SearchActivityV2::class.java)
                startActivity(intent)
            }
            val params = Bundle()
            params.putString("home_screen", "search_button")
            (requireActivity() as BaseActivity).firebaseAnalytics.logEvent(
                "click_search_button",
                params
            )
        }
        binding.contentScrolling.btnPromo.setOnClickListener {
            if ((activity as HomeActivity?)!!.isInternetConnected) {
                (requireActivity() as HomeActivity).clickNavigation(2)
            }
        }
        binding.contentScrolling.txtHotline.setOnClickListener {
            val i = Intent(Intent.ACTION_DIAL)
            i.data = Uri.parse("tel:19002642")
            i.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            requireActivity().startActivity(i)
        }



        if (AppConfigs.getInstance().config.getBoolean("on_bus")) {
            binding.contentScrolling.btnBus.visibility = View.VISIBLE
            binding.contentScrolling.btnRewardLayout.visibility = View.GONE
        } else {
            binding.contentScrolling.btnRewardLayout.visibility = View.VISIBLE
            binding.contentScrolling.btnBus.visibility = View.GONE
        }
        binding.contentScrolling.btnTrain.setOnClickListener {
            val `in` = Intent(context, TrainSearchActivity::class.java)
            startActivity(`in`)
        }
        binding.contentScrolling.btnRewardLayout.setOnClickListener {
            val `in` = Intent(context, RewardActivity::class.java)
            startActivity(`in`)
        }
        binding.contentScrolling.btnBus.setOnClickListener {
            val `in` = Intent(context, BusSearchActivity::class.java)
            startActivity(`in`)
        }
        binding.contentScrolling.btnFlightWatches.setOnClickListener {
            val `in` = Intent(context, FlightWachesList::class.java)
            startActivity(`in`)
        }
        binding.contentScrolling.btnCheckin.setOnClickListener {
            val `in` = Intent(context, Checkin::class.java)
            startActivity(`in`)
        }
        binding.contentScrolling.btnTour.setOnClickListener {
            val `in` = Intent(context, TourListActivity::class.java)
            startActivity(`in`)
        }
        binding.contentScrolling.btnStatus.setOnClickListener {
            val `in` = Intent(context, MapViewActivity::class.java)
            startActivity(`in`)
        }
        binding.contentScrolling.btnPaymendt?.setOnClickListener {
            val `in` = Intent(context, ThanhToan::class.java)
            startActivity(`in`)
        }
        binding.contentScrolling.btnWeeklyChart.setOnClickListener {
            val intent = Intent(context, WeeklyPriceChartActivity::class.java)
            startActivity(intent)
        }
    }

    override fun onResume() {
        super.onResume()
        if ((requireActivity() as BaseActivity).isUserSigned) {
            binding.toolbar.title =
                "Chào " + (requireActivity() as BaseActivity).firebaseUser.displayName
        }
    }
}