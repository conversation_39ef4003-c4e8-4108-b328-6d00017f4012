package com.hqt.view.ui.reward.ui

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.hqt.base.BaseViewModelV2
import com.hqt.base.model.State
import com.hqt.util.Log
import com.hqt.view.ui.reward.data.api.RewardApiHelper
import com.hqt.view.ui.reward.data.model.Promotion
import com.hqt.view.ui.reward.data.model.Voucher
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import javax.inject.Inject


@HiltViewModel
class RewardViewModel @Inject constructor(
    private val seatApiHelper: RewardApiHelper
) : BaseViewModelV2() {





    private val _promotionListLiveData: MutableLiveData<State<ArrayList<Promotion>>> = MutableLiveData()
    val promotionListLiveData: LiveData<State<ArrayList<Promotion>>> get() = _promotionListLiveData


    private val _promotionLiveData: MutableLiveData<State<Promotion>> = MutableLiveData()
    val promotionLiveData: LiveData<State<Promotion>> get() = _promotionLiveData



    private val _voucherListLiveData: MutableLiveData<State<ArrayList<Voucher>>> = MutableLiveData()
    val voucherListLiveData: LiveData<State<ArrayList<Voucher>>> get() = _voucherListLiveData

    var promotion = Promotion()

    var offset = 0


    fun getPromotion() {
        viewModelScope.launch(Dispatchers.IO) {

            try {

                _promotionListLiveData.postValue(State.Loading)
                val result = seatApiHelper.getPromotion()

                _promotionListLiveData.postValue(State.Success(result.data ?: arrayListOf()))


            }catch (ex : Exception){
                _promotionListLiveData.postValue(State.Error(ex))
                Log.logException(ex)
            }



        }


    }
    fun getPromotionById(id : String?) {
        viewModelScope.launch(Dispatchers.IO) {

            try {

                _promotionLiveData.postValue(State.Loading)
                val result = seatApiHelper.getPromotionById(id)

                result.value?.let {
                    _promotionLiveData.postValue(State.Success(it))
                }


            }catch (ex : Exception){
                _promotionLiveData.postValue(State.Error(ex))
                Log.logException(ex)
            }



        }


    }
    fun getVoucherList(uid : String?) {
        viewModelScope.launch(Dispatchers.IO) {

            try {

                _voucherListLiveData.postValue(State.Loading)
                val result = seatApiHelper.getVoucherList(uid)

                _voucherListLiveData.postValue(State.Success(result.data ?: arrayListOf()))


            }catch (ex : Exception){
                _voucherListLiveData.postValue(State.Error(ex))
                Log.logException(ex)
            }



        }


    }




}