package com.hqt.view.ui.addon


import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.view.WindowManager
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.gson.Gson
import com.hqt.data.model.*
import com.hqt.datvemaybay.R
import com.hqt.util.AppConfigs
import com.hqt.view.ui.BaseActivityKt
import com.hqt.view.ui.reward.ui.activity.RewardActivity
import com.hqt.view.ui.booking.data.model.BaseBooking
import com.hqt.view.ui.booking.data.model.BookingV3
import com.hqt.view.ui.booking.data.model.PaxInfoListV2
import com.hqt.viewmodel.BookingViewModel
import q.rorbin.badgeview.QBadgeView


class SelectAddOnActivity() : BaseActivityKt<com.hqt.datvemaybay.databinding.ActivitySelectAddonLayoutBinding>() {


    override val layoutId: Int = R.layout.activity_select_addon_layout
    lateinit var viewModel: BookingViewModel
    lateinit var adapter: AddOnFragmentAdapter
    var addOnList: ArrayList<AddOnInfo> = ArrayList()
    var addOnListReturn: ArrayList<AddOnInfo> = ArrayList()
    var totalPax = 1
    lateinit var booking: BookingV3

    var paxSeatString = ""
    var paxSeatReturnString = ""
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        getToolbar().title = "Chọn suất ăn nóng"
        getToolbar().setNavigationIcon(R.drawable.ic_action_back_home)
        supportActionBar!!.setDisplayShowHomeEnabled(true)

        booking = intent.getSerializableExtra("bookingDetail") as BookingV3

        if (booking.type == BaseBooking.BookingType.FLIGHT) {
            totalPax = booking.adult + booking.child
        }
        getViewBindding().lifecycleOwner = this
        adapter = AddOnFragmentAdapter(supportFragmentManager, booking)


        getViewBindding().viewPager.adapter = adapter
        getViewBindding().tabLayout.setupWithViewPager(getViewBindding().viewPager)



        setWindowFlag(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS, false)
        window.statusBarColor = ContextCompat.getColor(this, R.color.primary_dark)
    }

    fun getPaxSeatStr(isReturn: Boolean): String {

        AppConfigs.Log("getPaxSeatStr", paxSeatString.toString())
        if (isReturn) return paxSeatReturnString
        return paxSeatString

    }


    private fun addAddOnToPax(pax_info: PaxInfoListV2, addOnList: ArrayList<AddOnInfo>, isReturn: Boolean): PaxInfoListV2 {
        try {
            pax_info.adult[0].updateAddOn(AddOnType.MEAL, addOnList, isReturn)

            return pax_info

        } catch (e: Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }
        return pax_info

    }

    fun getCurrentAddOn(isReturn: Boolean): ArrayList<AddOnInfo> {

        val addOnListPax: ArrayList<AddOnInfo> = ArrayList()
        booking.pax_info.adult.forEach { it ->
            if (isReturn) {
                it.addOnReturn.forEach {
                    if (it.type == AddOnType.MEAL) {
                        addOnListPax.add(it)
                    }

                }
            } else {
                it.addOn.forEach {
                    if (it.type == AddOnType.MEAL) {
                        addOnListPax.add(it)
                    }

                }
            }

        }
        booking.pax_info.child.forEach { it ->
            if (isReturn) {
                it.addOnReturn.forEach {
                    if (it.type == AddOnType.MEAL) {
                        addOnListPax.add(it)
                    }

                }
            } else {
                it.addOn.forEach {
                    if (it.type == AddOnType.MEAL) {
                        addOnListPax.add(it)
                    }

                }
            }

        }


        return addOnListPax

    }

    fun updateSeatSelected(list: ArrayList<AddOnInfo>, isReturn: Boolean) {
        try {
            if (isReturn) {
                addOnListReturn = list
                booking.pax_info = addAddOnToPax(booking.pax_info, list, true)
            } else {
                addOnList = list
                booking.pax_info = addAddOnToPax(booking.pax_info, list, false)

            }
        } catch (e: Exception) {
            e.printStackTrace()
            AppConfigs.logException(e)
        }
    }

    fun backToBooking() {
        val data = Intent()
        data.putExtra("bookingDetail", Gson().toJson(booking.pax_info))
        setResult(RESULT_OK, data)
        finish()
    }


    private fun initAnalytics(booking: BookingV2, event: String) {
        try {

            val params = Bundle()
            params.putString(FirebaseAnalytics.Param.ORIGIN, booking.departure_f?.originCode)
            params.putString(FirebaseAnalytics.Param.DESTINATION, booking.departure_f?.destinationCode)
            params.putString(FirebaseAnalytics.Param.START_DATE, booking.departure_f?.departureDateTime.toString())
            params.putString(FirebaseAnalytics.Param.END_DATE, booking.departure_f?.arriverDateTime.toString())
            params.putString(FirebaseAnalytics.Param.FLIGHT_NUMBER, booking.departure_f?.flightNumber)
            params.putString(FirebaseAnalytics.Param.ITEM_NAME,
                booking.departure_f?.originCode + booking.departure_f?.destinationCode + booking.departure_f?.flightNumber)
            params.putString(FirebaseAnalytics.Param.NUMBER_OF_PASSENGERS,
                (booking.departure_f!!.adult + booking.departure_f!!.child + booking.departure_f!!.infant).toString() + "")
            firebaseAnalytics.logEvent(event, params)
            firebaseAnalytics.setCurrentScreen(this, "booking_input", null)

        } catch (e: Exception) {

        }
    }

    override fun onResume() {
        super.onResume()
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean { // Inflate the menu; this adds items to the action bar if it is present.
        menuInflater.inflate(R.menu.main_reward, menu)
        try {
            Handler(Looper.getMainLooper()).postDelayed({
                val rewardButton = findViewById(R.id.action_reward) as View
                QBadgeView(applicationContext).setGravityOffset(0f, 0f, true).bindTarget(rewardButton).setBadgeText("!")
                    .setOnDragStateChangedListener { dragState, badge, targetView ->
                    }
            }, 1000)
        } catch (e: java.lang.Exception) {
        }
        return true
    }

    fun doneClick(): Boolean {

        //        if (booking.is_round_trip && seatViewListReturn.size == 0 && booking.return_f?.provider == "VJ") {
        //            Toast.makeText(this, "Vui lòng chọn ghế lượt về", Toast.LENGTH_SHORT).show()
        //            getViewBindding().viewPager.currentItem = 1
        //            return false
        //        }
        //        if (!booking.is_round_trip && seatViewList.size == 0 && booking.departure_f?.provider == "VJ") {
        //            Toast.makeText(this, "Vui lòng chọn ghế lượt đi", Toast.LENGTH_SHORT).show()
        //            getViewBindding().viewPager.currentItem = 0
        //            return false
        //        }

        backToBooking()
        return true
    }

    fun backClick() {
        finish()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean { // Take appropriate action for each action item click
        when (item.itemId) {
            android.R.id.home -> {

                finish()
            }
            R.id.action_reward -> {
                val `in` = Intent(this, RewardActivity::class.java)

                startActivity(`in`)
                return true
            }
            else -> {
                finish()
            }
        }
        return false
    }

    override fun onSystemSettingChange(type: AppConfigs.SystemSettingType) {

        if (type == AppConfigs.SystemSettingType.INTERNET) {
            if (this::viewModel.isInitialized) {
                viewModel.updateInternetStatus(isInternetConnected)
            }
        }


    }


}

class AddOnFragmentAdapter(fm: FragmentManager, booking: BookingV3) : FragmentPagerAdapter(fm) {
    var bookingx = booking

    override fun getItem(position: Int): AddOnSelectFragment {

        var totalPax = bookingx.adult + bookingx.child
        if (bookingx.is_round_trip) {
            return when (position) {
                0 -> AddOnSelectFragment.newInstance(bookingx.departure_f!!.flightKey ?: "", false, totalPax)
                1 -> AddOnSelectFragment.newInstance(bookingx.return_f!!.flightKey ?: "", true, totalPax)
                else -> AddOnSelectFragment.newInstance("", false, totalPax)
            }
        } else {
            return AddOnSelectFragment.newInstance(bookingx.departure_f!!.flightKey ?: "", false, totalPax)
        }

    }

    override fun getPageTitle(position: Int): CharSequence { // if (bookingx.is_round_trip) {
        when (position) {
            0 -> return "Lượt đi"
            1 -> return "Lượt về"
            else -> return ""
        } //}
    }


    override fun getCount(): Int {
        if (!bookingx.is_round_trip) {
            return 1
        }
        return 2
    }
}
