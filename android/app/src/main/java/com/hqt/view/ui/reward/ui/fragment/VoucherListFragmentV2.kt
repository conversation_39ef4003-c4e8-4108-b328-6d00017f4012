package com.hqt.view.ui.reward.ui.fragment

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Button
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.github.florent37.materialviewpager.MaterialViewPagerHelper
import com.github.florent37.materialviewpager.header.MaterialViewPagerHeaderDecorator
import com.google.firebase.analytics.FirebaseAnalytics
import com.hqt.base.BaseFragment
import com.hqt.base.model.State
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.FragmentRecyclerviewRewardBinding
import com.hqt.util.ViewUtil
import com.hqt.view.ui.reward.ui.RewardViewModel
import com.hqt.view.ui.reward.ui.activity.RewardActivityV2
import com.hqt.view.ui.reward.ui.adapter.VoucherAdapterV2
import com.hqt.view.ui.search.ui.activity.SearchActivityV2
import com.hqt.view.ui.search.ui.helper.MaterialViewPagerHeaderDecoratorV2
import dagger.hilt.android.AndroidEntryPoint


@AndroidEntryPoint
class VoucherListFragmentV2 : BaseFragment<FragmentRecyclerviewRewardBinding>() {
    private val viewModel : RewardViewModel by activityViewModels()

    private var emptyStateLayout: LinearLayout? = null

    private val voucherAdapter by lazy {
        VoucherAdapterV2{

        }
    }



    override fun getLayoutRes(): Int {
        return R.layout.fragment_recyclerview_reward
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        MaterialViewPagerHelper.registerRecyclerView(activity, binding.recyclerView)
        emptyStateLayout = view.findViewById(R.id.emptyStateLayout)
        initEmptyState(view)
        initRcv()
//        binding.shimmerViewContainer.startShimmer()
//        binding.shimmerViewContainer.visibility = View.VISIBLE

        FirebaseAnalytics.getInstance(requireActivity()).logEvent("voucher_view", null)


        voucherList
    }

    private fun initRcv(){
        binding.recyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext())
            setHasFixedSize(true)
            adapter = voucherAdapter
            addItemDecoration(MaterialViewPagerHeaderDecoratorV2(viewModel.offset))
        }
        observe()
    }
    private fun observe(){
        viewModel.voucherListLiveData.observe(viewLifecycleOwner){
            when(it){
                is State.Error -> {
                    binding.shimmerViewContainer.stopShimmer()
                    binding.shimmerViewContainer.visibility = View.GONE
                    emptyStateLayout!!.visibility = View.VISIBLE
                }
                State.Loading -> {
                    binding.shimmerViewContainer.startShimmer()
                    binding.shimmerViewContainer.visibility = View.VISIBLE

                }
                is State.Success -> {
                    voucherAdapter.setData(it.data)

                    binding.shimmerViewContainer.stopShimmer()
                    binding.shimmerViewContainer.visibility = View.GONE

                    if (it.data.isEmpty()) {
                        emptyStateLayout!!.visibility = View.VISIBLE
                    } else {
                        emptyStateLayout!!.visibility = View.GONE
                    }
                }
            }
        }
    }

    private val voucherList: Unit
        get() {
            if (isUserSigned) {
                val uid = firebaseUser?.uid
                viewModel.getVoucherList(uid)
            } else {
                binding.shimmerViewContainer.stopShimmer()
                binding.shimmerViewContainer.visibility = View.GONE
                emptyStateLayout!!.visibility = View.VISIBLE
            }
        }


    @SuppressLint("NotifyDataSetChanged")
    fun clearSelect() {
        voucherAdapter.notifyDataSetChanged()
    }

    fun initEmptyState(view: View?) {
        var emptyText = "Hiện chưa có voucher nào. Bạn đăng nhập để đổi điểm nhé !"
        if (isUserSigned) {
            emptyText = "Hiện chưa có voucher nào."
        }


        ViewUtil.initEmptyState(
            view,
            context,
            emptyText,
            ContextCompat.getDrawable(requireContext(), R.drawable.ic_empty_ticket),
            -1,
            object : ViewUtil.EmptyStateCallBackInterface {
                override fun negativeButton(button: Button) {
                    if (isUserSigned) {
                        button.text = "ĐẶT VÉ TÍCH ĐIỂM"
                        button.setOnClickListener { //                            Intent in = new Intent(getActivity(), SearchActivity.class);
                            val `in` = Intent(activity, SearchActivityV2::class.java)
                            startActivity(`in`)
                        }
                    } else {
                        button.text = "ĐĂNG NHẬP"
                        button.setOnClickListener { (activity as RewardActivityV2).signIn() }
                    }
                }

                override fun positiveButton(button: Button) {
                    button.text = "ĐỔI ĐIỂM"
                    button.setOnClickListener { (activity as RewardActivityV2).changeViewPaper(0) }
                }
            })
    }

    companion object {
        private const val ITEM_COUNT = 5
        fun newInstance(): VoucherListFragmentV2 {
            return VoucherListFragmentV2()
        }
    }
}
