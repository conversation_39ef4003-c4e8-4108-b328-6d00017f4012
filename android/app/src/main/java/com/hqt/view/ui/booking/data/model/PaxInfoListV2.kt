package com.hqt.view.ui.booking.data.model

import java.io.Serializable

class PaxInfoListV2 : Serializable {

    var adult: ArrayList<PassengerV2> = ArrayList()
    var infant: ArrayList<PassengerV2> = ArrayList()
    var child: ArrayList<PassengerV2> = ArrayList()
    var student: ArrayList<PassengerV2> = ArrayList()
    var older: ArrayList<PassengerV2> = ArrayList()

    fun addPassenger(pax: PassengerV2) {
        when (pax.type) {
            PassengerType.ADULT -> {
                adult.add(pax)
            }

            PassengerType.CHILD -> {
                child.add(pax)
            }

            PassengerType.INFANT -> {
                infant.add(pax)
            }

            PassengerType.STUDENT -> {
                student.add(pax)
            }

            PassengerType.OLDER -> {
                older.add(pax)
            }

            else -> adult.add(pax)
        }

    }

    fun isValidated(): Boolean {
        for (pax in adult) {
            if (!pax.isValidated) {
                return false
            }
        }
        for (pax in child) {
            if (!pax.isValidated) {
                return false
            }
        }
        for (pax in student) {
            if (!pax.isValidated) {
                return false
            }
        }
        for (pax in infant) {
            if (!pax.isValidated) {
                return false
            }
        }
        for (pax in older) {
            if (!pax.isValidated) {
                return false
            }
        }
        return true
    }

    fun getTotalPax(): Int {
        var total = 0
        for (pax in adult) {
            if (!pax.isValidated) {
                total++
            }
        }
        for (pax in child) {
            if (!pax.isValidated) {
                total++
            }
        }
        for (pax in student) {
            if (!pax.isValidated) {
                total++
            }
        }
        for (pax in infant) {
            if (!pax.isValidated) {
                total++
            }
        }
        for (pax in older) {
            if (!pax.isValidated) {
                total++
            }
        }
        return total
    }

}
