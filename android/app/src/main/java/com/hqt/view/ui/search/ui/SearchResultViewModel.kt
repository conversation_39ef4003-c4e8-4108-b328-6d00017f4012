package com.hqt.view.ui.search.ui

import android.content.Intent
import android.view.View
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.android.volley.VolleyError
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.hqt.base.model.State
import com.hqt.data.prefs.SharedPrefsHelper
import com.hqt.util.AppConfigs
import com.hqt.util.AppController
import com.hqt.util.Log
import com.hqt.util.SSLSendRequest
import com.hqt.util.Widget.isAvailable
import com.hqt.view.ui.booking.data.model.BookingV3
import com.hqt.view.ui.calender.data.model.FlightHistoryBody
import com.hqt.view.ui.flighthistory.data.api.FlightHistoryApiHelper
import com.hqt.view.ui.flighthistory.data.model.FlightHistory
import com.hqt.view.ui.search.data.api.SearchApiHelper
import com.hqt.view.ui.search.data.model.FightTask
import com.hqt.view.ui.search.data.model.FlightV2
import com.hqt.view.ui.search.data.model.request.FlightTaskBody
import com.hqt.view.ui.search.ui.activity.SearchResultActivityV2.Companion.SORT_BY_PRICE
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import okhttp3.Call
import okhttp3.Callback
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import org.json.JSONException
import org.json.JSONObject
import java.io.IOException
import javax.inject.Inject
import kotlin.math.roundToInt


@HiltViewModel
class SearchResultViewModel @Inject constructor(

    private val searchApiHelper: SearchApiHelper,
    private val flightHistoryApiHelper: FlightHistoryApiHelper,
    private val sharedPrefsHelper: SharedPrefsHelper
) : ViewModel() {


    private val _flightTaskLiveData: MutableLiveData<State<FightTask>> = MutableLiveData()
    val flightTaskLiveData: LiveData<State<FightTask>> get() = _flightTaskLiveData


    private val _flightHistoryLiveData: MutableLiveData<State<FlightHistory>> = MutableLiveData()
    val flightHistoryLiveData: LiveData<State<FlightHistory>> get() = _flightHistoryLiveData

    var selectFlight: FlightV2? = null
    var bookingDetail: BookingV3 = BookingV3()
    var sortBy = MutableLiveData(SORT_BY_PRICE)

    var offset = 0

    private val airList = AppConfigs.getInstance().config.getString("search_airlines")
        .split("-".toRegex()).dropLastWhile { it.isEmpty() }
        .toTypedArray()


    fun getAllFlightTasks() {
        viewModelScope.launch(Dispatchers.IO) {
            val successResults = mutableListOf<FightTask>() // Tùy kiểu dữ liệu bạn nhận từ API
            val errors = mutableListOf<Exception>()

            _flightTaskLiveData.postValue(State.Loading)

            val tasks = airList.map { airline ->
                async {
                    try {
                        val request = FlightTaskBody(
                            departureDate = bookingDetail.departure_date,
                            returnDate = bookingDetail.return_date,
                            adultCount = bookingDetail.adult.toString(),
                            childCount = bookingDetail.child.toString(),
                            infantCount = bookingDetail.infant.toString(),
                            isRoundTrip = if (bookingDetail.is_round_trip) "1" else "0",
                        )

                        val result = searchApiHelper.getFlightTask(
                            airline,
                            bookingDetail.origin_code,
                            bookingDetail.destination_code,
                            request
                        )

                        result.data?.let {

                            synchronized(successResults) {
                                successResults.add(it)
                            }
                        }
                    } catch (ex: Exception) {
                        Log.logException(ex)
                        synchronized(errors) {
                            errors.add(ex)
                        }
                    }
                }
            }

            tasks.awaitAll()

            // Sau khi tất cả task hoàn thành
            if (successResults.isNotEmpty()) {


                successResults.forEach {
                    delay(100)
                    _flightTaskLiveData.postValue(State.Success(it))

                }
            } else {
                _flightTaskLiveData.postValue(State.Error(Throwable("Tất cả lời gọi đều thất bại")))
            }
        }
    }


    fun getFlightHistory() {
        viewModelScope.launch(Dispatchers.IO) {

            try {

                _flightHistoryLiveData.postValue(State.Loading)

                val body = FlightHistoryBody(
                    flightNumber = selectFlight?.flightNumber,
                )
                val result = flightHistoryApiHelper.getFlightHistory(body)

                _flightHistoryLiveData.postValue(State.Success(result.data ?: FlightHistory()))


            }catch (ex : Exception){
                _flightHistoryLiveData.postValue(State.Error(ex))
                Log.logException(ex)
            }



        }


    }




    fun handelIntent(intent: Intent?) {
        //HAND INTENT
        val intent = intent
        bookingDetail.origin_code = intent?.getStringExtra("originCode") ?: ""
        bookingDetail.destination_code = intent?.getStringExtra("destinationCode") ?: ""
        bookingDetail.departure_date = intent?.getStringExtra("departureTime") ?: ""
        bookingDetail.return_date = intent?.getStringExtra("returnTime") ?: ""
        bookingDetail.adult = intent?.getIntExtra("adult", 1) ?: 1
        bookingDetail.child = intent?.getIntExtra("child", 0) ?: 0
        bookingDetail.infant = intent?.getIntExtra("infant", 0) ?: 0
        bookingDetail.is_round_trip = intent?.getBooleanExtra("isRoundTrip", false) ?: false


    }



}