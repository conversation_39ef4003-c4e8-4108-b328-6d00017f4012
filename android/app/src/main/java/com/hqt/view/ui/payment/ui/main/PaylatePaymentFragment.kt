package com.hqt.view.ui.payment.ui.main

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.CountDownTimer
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.hqt.data.model.Payment
import com.hqt.data.model.PaymentOrderXml
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.PaymentPaylateFragmentBinding
import com.hqt.util.AppConfigs
import com.hqt.util.Widget
import com.hqt.view.ui.payment.NewPaymentActivity

class PaylatePaymentFragment : Fragment() {

    companion object {
        fun newInstance() = PaylatePaymentFragment()
    }

    private var timer: CountDownTimer? = null
    lateinit var binding: PaymentPaylateFragmentBinding
    private lateinit var viewModel: MainViewModel
    private lateinit var orderInfo: PaymentOrderXml
    private var deepLink = ""

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {


        binding = DataBindingUtil.inflate(inflater, R.layout.payment_paylate_fragment, container, false)
        setupToolbar()


        binding.content.btnOpenApp.setOnClickListener {

            (activity as NewPaymentActivity).showSelectAppBank()
        }
        binding.content.btnDonePayment.setOnClickListener {

            Common.sendSms(context,
                "Hi 12bay. Toi da thanh toan xong don hang: " + orderInfo.bookingId,
                AppConfigs.getInstance().config.getString("sms"))

        }

        Widget.chatInfo(requireContext(), binding.content.widgetChat)

        return binding.root
    }

    private fun setupToolbar() {
        try {
            binding.toolbar.title = "Chuyển khoản ngân hàng"

            (activity as AppCompatActivity).setSupportActionBar(binding.toolbar)
            (activity as AppCompatActivity).supportActionBar!!.setDisplayHomeAsUpEnabled(true)
            binding.toolbar.bringToFront()
            binding.toolbar.setNavigationOnClickListener {
                (activity as AppCompatActivity).onBackPressed()
            }
        } catch (e: Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }

    }

    fun bindOrder(selectedBank: Payment, order: PaymentOrderXml) {
        orderInfo = order
        deepLink = selectedBank.DeepLink!!
        binding.content.paymentBank = selectedBank
        binding.content.order = orderInfo
        setTimeLimit(orderInfo)

    }

    private fun setTimeLimit(orderInfo: PaymentOrderXml) {
        var timeLimit = Common.stringToDate(orderInfo.expiredDate, "yyyy-MM-dd HH:mm:ss")
        binding.content.timeLimit.text = Common.dateToString(timeLimit.time, "HH:mm dd-MM-yyyy")
        binding.toolbar.subtitle = "Đơn hàng #" + orderInfo.bookingId

        (activity as NewPaymentActivity).setTimeLimit(orderInfo.expiredDate, binding.headStatus)


    }

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        viewModel = ViewModelProvider(this).get(MainViewModel::class.java) // TODO: Use the ViewModel
    }

}