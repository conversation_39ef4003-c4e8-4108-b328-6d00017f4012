package com.hqt.view.ui.flighthistory.ui.adapter

import android.graphics.Color
import android.text.style.BackgroundColorSpan
import android.text.style.ForegroundColorSpan
import android.text.style.RelativeSizeSpan
import android.view.View
import androidx.core.content.ContextCompat
import com.bumptech.glide.Glide
import com.hqt.base.BaseAdapter
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ViewFlightDetailBinding
import com.hqt.view.ui.flighthistory.ui.state.FlightDetailItemState
import com.hqt.view.ui.search.data.model.FlightV2
import com.mikepenz.iconics.Iconics


class FlightDetailAdapter(private val listener: (FlightV2) -> Unit) :
    BaseAdapter<FlightV2, ViewFlightDetailBinding>(listener) {
        var adult = 0
        var child = 0
        var infant = 0
        var isReturn = false

    override fun getLayoutRes(): Int {
        return R.layout.view_flight_detail
    }


    override fun bind(binding: ViewFlightDetailBinding, position: Int, model: FlightV2) {
        binding.apply {


            itemViewState = FlightDetailItemState(model)

            // Icon class
            txtClassIconDi.text = when {
                model.quickDep -> "{faw_history} "
                model.promo -> "{faw_star} "
                else -> ""
            }
            if (txtClassIconDi.text.isNotEmpty()) {
                Iconics.Builder()
                    .style(
                        ForegroundColorSpan(
                            ContextCompat.getColor(
                                _context,
                                R.color.google_yellow
                            )
                        ),
                        BackgroundColorSpan(Color.TRANSPARENT),
                        RelativeSizeSpan(0.8f)
                    )
                    .on(txtClassIconDi)
                    .build()
            }


            val stopsText = model.stopsText.toString()
            txtDiDiemDung.text = "{faw_circle} $stopsText"
            Iconics.Builder()
                .style(
                    ForegroundColorSpan(
                        if (stopsText.equals("bay thẳng", ignoreCase = true))
                            _context.resources.getColor(R.color.primary) else Color.RED
                    ),
                    BackgroundColorSpan(Color.TRANSPARENT),
                    RelativeSizeSpan(0.8f)
                )
                .on(txtDiDiemDung)
                .build()

            Glide.with(_context).load(model.airlinesLogo).into(diLogo)

            // Giá vé
            val total = adult * model.adult!! + child * model.child!! + infant * model.infant!!

            txtDiTongGia.text = Common.dinhDangTien(total)
            txtDiGiaNguoiLon.text = "$adult x ${Common.dinhDangTien(model.adult ?: 0)}"
            txtDiGiaTreem.text = "$child x ${Common.dinhDangTien(model.child ?: 0)}"
            txtDiGiaEmBe.text = "$infant x ${Common.dinhDangTien(model.infant ?: 0)}"
            txtTotalTitle.text = "Tổng giá vé lượt ${if (isReturn) "về" else "đi"} +"

            viewTotalDi.setOnClickListener {
                val showing = viewTotalDiDetail.visibility == View.VISIBLE
                viewTotalDiDetail.visibility = if (showing) View.GONE else View.VISIBLE
                txtTotalTitle.text = "Tổng giá vé lượt ${if (isReturn) "về" else "đi"} ${if (showing) "+" else "-"}"
            }

            diTreEm.visibility = if (child > 0) View.VISIBLE else View.GONE
            diEmbe.visibility = if (infant > 0) View.VISIBLE else View.GONE

        }

    }


    override fun onItemClickListener(model: FlightV2) {
        listener(model)


    }

    fun initData(adult : Int, child : Int, infant : Int, isReturn : Boolean){

        this.adult = adult
        this.child = child
        this.infant = infant
        this.isReturn = isReturn
    }

}