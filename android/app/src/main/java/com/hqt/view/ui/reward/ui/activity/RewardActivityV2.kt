package com.hqt.view.ui.reward.ui.activity

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.text.Html
import android.view.View
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import androidx.activity.viewModels
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentStatePagerAdapter
import com.android.volley.VolleyError
import com.github.florent37.materialviewpager.MaterialViewPager
import com.github.florent37.materialviewpager.MaterialViewPagerHelper
import com.github.florent37.materialviewpager.Utils
import com.github.florent37.materialviewpager.header.HeaderDesign
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.firebase.analytics.FirebaseAnalytics
import com.hqt.base.BaseActivity
import com.hqt.base.model.State
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ActivityRewardBinding
import com.hqt.util.AppConfigs
import com.hqt.util.AppController.Companion.instance
import com.hqt.util.SSLSendRequest
import com.hqt.util.SSLSendRequest.CallBackInterface
import com.hqt.view.ui.reward.data.model.Promotion
import com.hqt.view.ui.reward.data.model.Voucher
import com.hqt.view.ui.reward.ui.RewardViewModel
import com.hqt.view.ui.reward.ui.dialog.PromotionViewDialog
import com.hqt.view.ui.reward.ui.fragment.PromoListFragmentV2
import com.hqt.view.ui.reward.ui.fragment.VoucherListFragment
import com.hqt.view.ui.reward.ui.fragment.VoucherListFragmentV2
import dagger.hilt.android.AndroidEntryPoint
import org.json.JSONException
import org.json.JSONObject
import java.util.Locale


@AndroidEntryPoint
class RewardActivityV2 : BaseActivity<ActivityRewardBinding>() {
    private val viewModel : RewardViewModel by viewModels()


    private var coordinatorLayout: CoordinatorLayout? = null

    var action: String? = "nomal"
    override fun getLayoutRes(): Int {
        return R.layout.activity_reward
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        coordinatorLayout = findViewById(R.id.coordinatorLayout)

        val intent = intent
        if (intent.hasExtra("action")) {
            action = intent.getStringExtra("action")
            val token = intent.getStringExtra("token")
            if (action == "viewVoucher") {
                getVoucher(token)
            } else if (action == "viewPromotion") {
                viewModel.getPromotionById(token)
            }
        }

        observe()
        viewPaperInit()
        getToolbar()?.title = "Voucher ưu đãi"
        getToolbar()?.setTitleTextColor(Color.WHITE)
        getToolbar()?.setSubtitleTextColor(Color.WHITE)
        supportActionBar?.setDisplayShowHomeEnabled(true)
        profile
        
    }
    private fun observe(){
        viewModel.promotionLiveData.observe(this){
            when(it){
                is State.Error -> {

                }
                State.Loading -> {

                }
                is State.Success -> {
                    viewModel.promotion = it.data
                    val dialog = PromotionViewDialog()
                    dialog.onSignIn = {
                        signIn()
                    }
                    dialog.redeemVoucher = {

                    }
                    dialog.show(supportFragmentManager, "")
                }
            }
        }
    }




    override fun refreshLayout() {
        profile
        viewPaperInit()

    }

    fun showVoucherView(voucher: Voucher) {
        try {
            val dialog = BottomSheetDialog(this)
            val viewUseVoucher = layoutInflater.inflate(R.layout.bottom_sheet_voucher_view, null)
            dialog.setContentView(viewUseVoucher)

            (viewUseVoucher.findViewById<View>(R.id.txtTitle) as TextView).text =
                voucher.promotionDetail?.name
            (viewUseVoucher.findViewById<View>(R.id.txtDescription) as TextView).text =
                Html.fromHtml(voucher.promotionDetail?.description)
            (viewUseVoucher.findViewById<View>(R.id.txtVoucherCode) as TextView).text =
                voucher.voucher?.uppercase(
                    Locale.getDefault()
                )
            val expriredTime = Common.dateToString(voucher.expiredAt, "dd/MM/yyyy")
            (viewUseVoucher.findViewById<View>(R.id.txtExpiredTime) as TextView).text =
                "Hạn sử dụng: $expriredTime"

            val btnCopy = viewUseVoucher.findViewById<Button>(R.id.copyVoucher)
            val clipboard = application.getSystemService(CLIPBOARD_SERVICE) as ClipboardManager

            btnCopy.setOnClickListener {
                val clip = ClipData.newPlainText("text", voucher.voucher)
                clipboard.setPrimaryClip(clip)

                if (callingActivity != null) {
                    val intent = Intent()
                    intent.putExtra("voucherCode", voucher.voucher)
                    setResult(RESULT_OK, intent)

                    dialog.dismiss()
                    finish()
                } else {
                    val toast = Toast.makeText(
                        applicationContext,
                        "Đã sao chép mã " + voucher.voucher,
                        Toast.LENGTH_SHORT
                    )
                    toast.show()
                }
                FirebaseAnalytics.getInstance(applicationContext).logEvent("voucher_use", null)
            }

            if (!<EMAIL> && !dialog.isShowing) {
                dialog.show()
//                dialogPromotionView!!.behavior.state = BottomSheetBehavior.STATE_EXPANDED
            }
        } catch (e: Exception) {
            AppConfigs.logException(e)
        }
    }

    private fun viewPaperInit() {

        MaterialViewPagerHelper.getAnimator(this)?.let {
            viewModel.offset = it.headerHeight
        } ?: run {
            viewModel.offset = Utils.dpToPx(150f, this).toInt()
        }
        binding.materialViewPager.viewPager.adapter = object : FragmentStatePagerAdapter(
            supportFragmentManager
        ) {
            override fun getItem(position: Int): Fragment {
                return when (position % 2) {
                    0 -> PromoListFragmentV2.newInstance()
                    1 -> VoucherListFragmentV2.newInstance()
                    else -> PromoListFragmentV2.newInstance()
                }
            }

            override fun getCount(): Int {
                return 2
            }

            override fun getPageTitle(position: Int): CharSequence {
                when (position % 2) {
                    0 -> return "Danh sách ưu đãi"
                    1 -> return "Voucher của tôi"
                }
                return ""
            }
        }

        binding.materialViewPager.setMaterialViewPagerListener(MaterialViewPager.Listener { page ->
            when (page) {
                0 -> {
                    return@Listener HeaderDesign.fromColorResAndUrl(
                        R.color.primary, "https://source.unsplash.com/800x600/?airplane"
                    )
                }

                1 -> {
                    return@Listener HeaderDesign.fromColorResAndUrl(
                        R.color.google_blue, "https://source.unsplash.com/800x600/?landspace"
                    )
                }
            }
            null
        }

        )

        binding.materialViewPager.viewPager.offscreenPageLimit = binding.materialViewPager.viewPager.adapter!!.count
        binding.materialViewPager.pagerTitleStrip.setViewPager(binding.materialViewPager.viewPager)
    }

    fun changeViewPaper(id: Int) {
        binding.materialViewPager.viewPager.currentItem = id
    }



    override fun onPointerCaptureChanged(hasCapture: Boolean) {
    }

//    override fun setupToolbar() {
//        mViewPager = findViewById(R.id.materialViewPager)
//
//        toolbar = mViewPager.getToolbar()
//        if (toolbar != null) {
//            setSupportActionBar(toolbar)
//            toolbar!!.inflateMenu(R.menu.main)
//            supportActionBar!!.setDisplayShowHomeEnabled(true)
//            toolbar!!.setNavigationIcon(R.drawable.ic_action_back_home)
//            toolbar!!.setTitleTextColor(Color.WHITE)
//            toolbar!!.setSubtitleTextColor(Color.WHITE)
//            toolbar!!.setNavigationOnClickListener { finish() }
//
//            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
//                window.setFlags(
//                    WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS,
//                    WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS
//                )
//            }
//        }
//    }

    fun getVoucher(voucherToken: String?) {
        SSLSendRequest(this).GET(
            false,
            "AirLines/Voucher/$voucherToken",
            JSONObject(),
            object : CallBackInterface {
                override fun onSuccess(response: JSONObject, cached: Boolean) {
                    try {
                        binding.materialViewPager.viewPager.currentItem = 1
                        val jData = response.getJSONObject("data")
                        val voucher = instance.gSon.fromJson(jData.toString(), Voucher::class.java)
                        showVoucherView(voucher)
                    } catch (e: JSONException) {
                        e.printStackTrace()
                        AppConfigs.logException(e)
                    }
                }

                override fun onFail(error: VolleyError) {
                }
            })
    }



    fun redeemVoucher(promotion: Promotion) {
        if (isUserSigned) {
            val authUser = firebaseUser
            val postParam = JSONObject()
            try {
                postParam.put("promotion_id", promotion.id)
                postParam.put("point", promotion.point)
                postParam.put("uid", authUser?.uid)
            } catch (e: JSONException) {
            }

            SSLSendRequest(this).POST(
                false,
                "AirLines/Voucher/Redeem",
                postParam,
                object : CallBackInterface {
                    override fun onSuccess(response: JSONObject, cached: Boolean) {
                        try {
                            val jData = response.getJSONObject("data")

                            val redeemText = jData.getString("text")
                            Toast.makeText(applicationContext, redeemText, Toast.LENGTH_SHORT)
                                .show()

                            if (jData.getBoolean("success")) {
                                Common.isNewData = true
                                val manager = supportFragmentManager
                                val ft = supportFragmentManager.beginTransaction()

                                for (frag in manager.fragments) {
                                    if (frag.javaClass == VoucherListFragment::class.java) {
                                        (frag as VoucherListFragment).getVoucherList()
                                    }
                                }
//                                Handler().postDelayed({ dialogPromotionView!!.dismiss() }, 2000)
                            }
                        } catch (e: JSONException) {
                            AppConfigs.logException(e)
                        }
                    }

                    override fun onFail(error: VolleyError) {
                    }
                })
        } else {
//            btnRedeemVoucher!!.text = "Bạn vui lòng đăng nhập trước để đổi điểm nhé!"
        }
    }

    val profile: Unit
        get() {
            if (isUserSigned) {
                val authUser = firebaseUser
                SSLSendRequest(this).GET(
                    true,
                    "users/" + authUser?.uid,
                    JSONObject(),
                    object : CallBackInterface {
                        override fun onSuccess(response: JSONObject, cached: Boolean) {
                            try {
                                val jData = response.getJSONObject("data")
                                val pointText = jData.getJSONObject("point").getString("text")
                                getToolbar()?.subtitle = pointText
                                supportActionBar!!.title = "Chào " + jData.getString("name")
                            } catch (e: JSONException) {
                                AppConfigs.logException(e)
                            }
                        }

                        override fun onFail(error: VolleyError) {
                            error.printStackTrace()
                        }
                    })
            }
        }
}
