package com.hqt.view.ui.booking.ui.activity

import android.app.Activity
import android.app.ProgressDialog
import android.content.BroadcastReceiver
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.style.BackgroundColorSpan
import android.text.style.ForegroundColorSpan
import android.text.style.RelativeSizeSpan
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.android.volley.VolleyError
import com.bumptech.glide.Glide
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.snackbar.Snackbar
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.gson.Gson
import com.hqt.base.BaseActivity
import com.hqt.base.model.State
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.PnrActivity
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ActivityBookingLayoutV2Binding
import com.hqt.datvemaybay.databinding.ViewFlightDetailBinding
import com.hqt.util.AppConfigs
import com.hqt.util.AppController
import com.hqt.util.Helper
import com.hqt.util.Log
import com.hqt.util.SSLSendRequest
import com.hqt.util.SSLSendRequest.CallBackInterface
import com.hqt.util.SharedPrefs
import com.hqt.util.ViewUtil
import com.hqt.util.Widget
import com.hqt.util.Widget.retrievePassenger
import com.hqt.util.common.ConnectionState
import com.hqt.view.ui.booking.data.model.BaseBooking
import com.hqt.view.ui.booking.data.model.BookingV3
import com.hqt.view.ui.booking.data.model.PaxInfoListV2
import com.hqt.view.ui.booking.ui.BookingViewModelV2
import com.hqt.view.ui.booking.ui.adapter.PassengerAdapterV2
import com.hqt.view.ui.booking.ui.dialog.InputPassengerDialog
import com.hqt.view.ui.flightSearch.model.FareData
import com.hqt.view.ui.flighthistory.ui.adapter.FlightDetailAdapter
import com.hqt.view.ui.meal.ui.activity.SelectAddMealActivity
import com.hqt.view.ui.reward.ui.activity.RewardActivityV2
import com.hqt.view.ui.search.data.model.FlightV2
import com.hqt.view.ui.seatmap.ui.activity.SelectSeatActivityV2
import com.mikepenz.iconics.Iconics
import dagger.hilt.android.AndroidEntryPoint
import org.json.JSONException
import org.json.JSONObject
import q.rorbin.badgeview.QBadgeView
import java.util.Locale

@AndroidEntryPoint
class BookingActivityV2 : BaseActivity<ActivityBookingLayoutV2Binding>() {

    private val viewModel : BookingViewModelV2 by viewModels()


    var sheetDialog: BottomSheetDialog? = null
    var sheetViewLayout: View? = null
    val REQUEST_CODE_ADDON_SELECT = 4


    private val passengerAdapter by lazy {
        PassengerAdapterV2{

            viewModel.clickPassenger = it
            val dialog = InputPassengerDialog(false)
            dialog.show(supportFragmentManager, "")
        }
    }
    private val flightDetailAdapter by lazy {
        FlightDetailAdapter{


        }
    }

    override fun getLayoutRes(): Int {
        return R.layout.activity_booking_layout_v2
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        getToolbar()?.title = "Điền thông tin"
        supportActionBar?.setDisplayShowHomeEnabled(true)


        if (intent.hasExtra("interBooking")) {
            viewModel.mBooking.value = intent.getSerializableExtra("bookingDetail") as BookingV3
            viewModel.mBooking.value?.let {
                initAnalytics(it, FirebaseAnalytics.Event.BEGIN_CHECKOUT)
            }
            viewModel.mBooking.value?.type = BaseBooking.BookingType.INTER

//            AppConfigs.Log("baseBooking.type", baseBooking.type.toString())

        } else if (intent.hasExtra("BookingTour")) {
            viewModel.mBooking.value = intent.getSerializableExtra("bookingDetail") as BookingV3
            viewModel.mBooking.value?.let {
                initAnalytics(it, FirebaseAnalytics.Event.BEGIN_CHECKOUT)
            }
            viewModel.mBooking.value?.type = BaseBooking.BookingType.FLIGHT


        } else {

            viewModel.mBooking.value = Gson().fromJson(intent.getStringExtra("bookingDetail"), BookingV3::class.java)

            viewModel.mBooking.value?.let {
                initAnalytics(it, FirebaseAnalytics.Event.BEGIN_CHECKOUT)
            }
            viewModel.mBooking.value?.type = BaseBooking.BookingType.FLIGHT
        }

        Common.MAX_DEPATURE_DATE = viewModel.mBooking.value?.departure_date
        if (viewModel.mBooking.value?.is_round_trip == true) {
            Common.MAX_DEPATURE_DATE = viewModel.mBooking.value?.return_date
        }


//        viewModel.mBooking.value = (baseBooking)

        viewModel.isUserSigned.value = (isUserSigned)

        binding.viewModel = viewModel
        binding.content.vm = viewModel
        binding.lifecycleOwner = this

        initRecycleView()

        viewModel.mBooking.value?.let {
            showBookingTripDetail(it)
        }

        if (viewModel.mBooking.value?.type == BaseBooking.BookingType.FLIGHT) {
//            getBagFeeApi(viewModel.mBooking.value!!, false)
//            viewModel.getBaggageSequentially()
        }


        initDefaultInfo()
        setClickHandlers() //START SHOW SHOWCASE
        setUpFlight()

    }

    private fun initRecycleView(){
        binding.content.rcvPassenger.apply {
            layoutManager = LinearLayoutManager(this@BookingActivityV2)
            adapter = passengerAdapter
        }
        binding.content.rcvFlightDetail.apply {
            layoutManager = LinearLayoutManager(this@BookingActivityV2, LinearLayoutManager.HORIZONTAL, false)
            adapter = flightDetailAdapter
        }
        observe()
    }

    private fun initDefaultInfo() {
        try {
            if (isUserSigned && AppController.instance.user != null) {
                viewModel.mBooking.value?.contact_name = AppController.instance.user?.userName ?: ""
                viewModel.mBooking.value?.contact_email = AppController.instance.user?.userEmail ?: ""
                viewModel.mBooking.value?.contact_phone = AppController.instance.user?.phoneNumber ?: ""
            }
        } catch (ex: Exception) {

            Log.logException(ex)
        }

        val settings = getSharedPreferences("12BAY-APP-CONFIG", 0)
        if (!isUserSigned) {
            binding.content.txtContactPhone.setText(
                settings.getString("phone", "").toString()
                    .replace("+84", "0")
            )
            binding.content.txtContactName.setText(
                settings.getString("name", "").toString()
            )
            val Umail = settings.getString("Uemail", "").toString()
            binding.content.txtContactEmail.setText(
                settings.getString("email", Umail).toString()
            )
        } else {
            refreshLayout()
        }
    }

    private fun setClickHandlers() {

        binding.content.paxInPut.setOnClickListener { //re calculator fee
//            viewModel.mBooking.value = baseBooking
        }
        binding.content.btnGetVoucher.setOnClickListener {

            if (viewModel.mBooking.value?.voucher.isNullOrEmpty()) {
                showSelectVoucherDialog()

            } else {
                useVoucher()
            }
        }
        binding.content.btnBookVe.setOnClickListener {
            AppConfigs.Log("baseBooking.type", viewModel.mBooking.value?.type.toString())
            if (viewModel.mBooking.value?.pax_info?.isValidated() != true) {

                ViewUtil.makeMeShake(binding.content.loadBag, 30, 5)

                Toast.makeText(
                    applicationContext,
                    "Vui lòng nhập đầy đủ thông tin hành khách",
                    Toast.LENGTH_SHORT
                )
                    .show()
                binding.content.scrollView.smoothScrollTo(
                    0,
                    (binding.content.horizalScroll.bottom) - 200
                )
            } else if (isValidatedContact()) {

                showConfirmDialog()

            }
        }
        binding.content.loginLayout.setOnClickListener {
            signIn()
        }
        binding.content.showSeatSelect.setOnClickListener {
            val seatMap = Intent(applicationContext, SelectSeatActivityV2::class.java)
//            val seatMap = Intent(applicationContext, SelectSeatActivity::class.java)
            seatMap.putExtra("bookingDetail", Gson().toJson(viewModel.mBooking.value))
            startActivityForResult(seatMap, REQUEST_CODE_ADDON_SELECT)
        }
        binding.content.showAddOnSelect.setOnClickListener {
//            val seatMap = Intent(applicationContext, SelectAddOnActivity::class.java)
            val seatMap = Intent(applicationContext, SelectAddMealActivity::class.java)
            seatMap.putExtra("bookingDetail", viewModel.mBooking.value)
            startActivityForResult(seatMap, REQUEST_CODE_ADDON_SELECT)
        }
    }

    private fun isValidatedContact(): Boolean {
        AppConfigs.Log("baseBooking", viewModel.mBooking.value?.contact_name)
        val contactName = Common.unAccent(binding.content.txtContactName.text.toString())

        binding.content.txtContactName.setText(contactName)

        if (binding.content.txtContactName.text.isEmpty()) {
            Toast.makeText(applicationContext, "Vui lòng điền tên liên hệ", Toast.LENGTH_SHORT)
                .show()
            binding.content.txtContactName.requestFocus()
            return false
        } else if (Helper.validatorInput(
                contactName.toString(),
                Helper.ValidatorType.FULLNAME.text
            ) != null
        ) {
            Toast.makeText(
                applicationContext,
                "Vui lòng nhập đúng định dạng tên liên hệ",
                Toast.LENGTH_SHORT
            ).show()
            binding.content.txtContactName.requestFocus()
            return false
        } else if (binding.content.txtContactPhone.text.isEmpty()) {
            Toast.makeText(
                applicationContext,
                "Vui lòng điền số điện thoại liên hệ",
                Toast.LENGTH_SHORT
            ).show()
            binding.content.txtContactPhone.requestFocus()
            return false
        } else if (!Common.isEmailValid(
                binding.content.txtContactEmail.getText().toString()
            )
        ) {
            AlertDialog.Builder(this).setIcon(R.drawable.ic_bell_alert).setTitle("Chú ý")
                .setMessage(Common.convertHTML("Vui lòng nhập đúng email hoặc bỏ trống nếu không có"))
                .setPositiveButton("Nhập Email") { dialogInterface, i -> binding.content.txtContactEmail.requestFocus() }
                .setNegativeButton("Bỏ qua") { dialogInterface, i ->
                    binding.content.txtContactEmail.setText("<EMAIL>")
                    showConfirmDialog()

                }.show()
            return false
        }
        return true
    }

    private fun sendBooking() {
        mergeBooking()

        initAnalytics(viewModel.mBooking.value!!, FirebaseAnalytics.Event.PURCHASE)
        viewModel.sendBooking()


    }

    private fun useVoucher() {
        val dialog = ProgressDialog(this)
        dialog.setMessage("Đang tải dữ liệu ...")
        dialog.isIndeterminate = false
        dialog.max = 100
        dialog.setCanceledOnTouchOutside(true)
        dialog.setCancelable(true)
        dialog.setProgressStyle(ProgressDialog.STYLE_SPINNER)
        dialog.show()

        val postParam = JSONObject()
        postParam.put("voucher", viewModel.mBooking.value?.voucher)
        postParam.put("booking", viewModel.mBooking.value?.getBaseBookingSortInfo())
        postParam.put(
            "checksum",
            Common.getMd5Hash(viewModel.mBooking.value?.voucher + viewModel.mBooking.value?.getBaseBookingSortInfo())
        )

        SSLSendRequest(this).POST(
            false,
            "AirLines/Voucher/Use",
            postParam,
            object : CallBackInterface {
                override fun onSuccess(response: JSONObject, cached: Boolean) {
                    try {
                        if (!response.isNull("data")) {
                            val data = response.getJSONObject("data")
                            val rtCode = data.getString("voucher")
                            val rtDiscount = data.getInt("discount")
                            val rtresult = data.getString("text")

                            if (rtDiscount > 0) {
                                viewModel.mBooking.value?.discount = rtDiscount
                                viewModel.mBooking.value?.voucher = rtCode
                                binding.content.btnBookVe.requestFocus()
//                                viewModel.mBooking.value = baseBooking
                            } else {
                                binding.content.txtVoucherCode.setText("")
                            }
                            Toast.makeText(this@BookingActivityV2, rtresult, Toast.LENGTH_SHORT)
                                .show()

                        } else {
                            Common.showAlertDialog(
                                this@BookingActivityV2,
                                "Thật tiếc",
                                "Mã giảm giá không đúng!",
                                false,
                                false
                            )
                        }
                        if (dialog.isShowing) dialog.dismiss()
                    } catch (e: JSONException) {
                        AppConfigs.logException(e)
                        e.printStackTrace()
                    }
                }

                override fun onFail(e: VolleyError) {
                    if (dialog.isShowing) dialog.dismiss()
                    Common.showAlertDialog(
                        this@BookingActivityV2,
                        "Thông báo !",
                        "Không tìm thấy voucher \nVui lòng liên lạc chúng tôi để được hỗ trợ",
                        false,
                        false
                    )
                }
            })
    }

    private fun showPaxInput(isAllowEdit: Boolean, paxUpdate: Boolean) {

        binding.content.paxInPut.removeAllViews()
//        for (i in 1..(viewModel.mBooking.value?.adult ?: 0)) {
//            var pax = Passenger()
//            if (paxUpdate)
//                pax = viewModel.mBooking.value?.pax_info!!.adult[i - 1]
//
//            pax.startInput(PassengerType.ADULT, i)
//            Widget.showPaxList(
//                false,
//                pax,
//                viewModel.mBooking.value?.is_round_trip ?: false,
//                this,
//                binding.content.paxInPut,
//                isAllowEdit
//            )
//            if (!paxUpdate)
//                viewModel.mBooking.value?.pax_info?.addPassenger(pax)
//
//        }
//        for (i in 1..(viewModel.mBooking.value?.child ?: 0)) {
//            var pax = Passenger()
//            if (paxUpdate) pax = viewModel.mBooking.value?.pax_info!!.child[i - 1]
//            pax.startInput(PassengerType.CHILD, i)
//            Widget.showPaxList(
//                false,
//                pax,
//                viewModel.mBooking.value?.is_round_trip ?: false,
//                this,
//                binding.content.paxInPut,
//                isAllowEdit
//            )
//            if (!paxUpdate) viewModel.mBooking.value?.pax_info?.addPassenger(pax)
//        }
//        for (i in 1..(viewModel.mBooking.value?.student ?: 0)) {
//            val pax = Passenger()
//            pax.startInput(PassengerType.STUDENT, i)
//            Widget.showPaxList(
//                false,
//                pax,
//                viewModel.mBooking.value?.is_round_trip ?: false,
//                this,
//                binding.content.paxInPut,
//                isAllowEdit
//            )
//            if (!paxUpdate) viewModel.mBooking.value?.pax_info?.addPassenger(pax)
//        }
//        for (i in 1..(viewModel.mBooking.value?.infant ?: 0)) {
//            var pax = Passenger()
//            if (paxUpdate) pax = viewModel.mBooking.value?.pax_info?.infant?.getOrNull(i -1) ?: Passenger()
//
//            pax.startInput(PassengerType.INFANT, i)
//            Widget.showPaxList(
//                false,
//                pax,
//                viewModel.mBooking.value?.is_round_trip ?: false,
//                this,
//                binding.content.paxInPut,
//                isAllowEdit
//            )
//
//            if (!paxUpdate) viewModel.mBooking.value?.pax_info?.addPassenger(pax)
//        }
//        viewModel.mBooking.value = baseBooking
    }

    private fun initAnalytics(booking: BaseBooking, event: String) {
        try {

            val params = Bundle()
            params.putString(FirebaseAnalytics.Param.ORIGIN, booking.origin_code)
            params.putString(FirebaseAnalytics.Param.DESTINATION, booking.destination_code)
            params.putString(FirebaseAnalytics.Param.START_DATE, booking.departure_date)
            params.putString(
                FirebaseAnalytics.Param.END_DATE,
                booking.return_date
            ) //  params.putString(FirebaseAnalytics.Param.FLIGHT_NUMBER, booking.departure_f?.flightNumber)
            //            params.putString(FirebaseAnalytics.Param.ITEM_NAME,
            //                booking.departure_f?.originCode + booking.departure_f?.destinationCode + booking.departure_f?.flightNumber)
            params.putString(
                FirebaseAnalytics.Param.NUMBER_OF_PASSENGERS,
                (booking.adult + booking.child + booking.infant).toString() + ""
            )
            FirebaseAnalytics.getInstance(this).logEvent(event, params)

            FirebaseAnalytics.getInstance(this).setCurrentScreen(this, "booking_input", null)

        } catch (e: Exception) {
            Log.logException(e)
        }
    }

    override fun onResume() {
        super.onResume()

        if (isUserSigned && binding.content.loginLayout.isVisible) {
            refreshLayout()
        }
    }

    fun mergeBooking() {
        if (viewModel.mBooking.value?.type == BaseBooking.BookingType.INTER || viewModel.mBooking.value?.type == BaseBooking.BookingType.FLIGHT) {

            viewModel.mBooking.value?.voucher = viewModel.mBooking.value?.voucher
            viewModel.mBooking.value?.discount = viewModel.mBooking.value?.discount ?: 0
            viewModel.mBooking.value?.pax_info = viewModel.mBooking.value?.pax_info ?: PaxInfoListV2()
            viewModel.mBooking.value?.bag_fee = viewModel.mBooking.value?.getTotalBagFee() ?: 0

        }
    }



    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.main_reward, menu)
        try {
            Handler(Looper.getMainLooper()).postDelayed({
                val rewardButton: View = findViewById(R.id.action_reward)
                QBadgeView(applicationContext).setGravityOffset(0f, 0f, true)
                    .bindTarget(rewardButton).setBadgeText("!")
                    .setOnDragStateChangedListener { _, _, _ ->
                    }
            }, 1000)
        } catch (e: java.lang.Exception) {
            Log.logException(e)
        }
        return true
    }



    override fun onOptionsItemSelected(item: MenuItem): Boolean { // Take appropriate action for each action item click
        when (item.itemId) {
            android.R.id.home -> return true
            R.id.action_reward -> {
//                val intent = Intent(this, RewardActivity::class.java)
                val intent = Intent(this, RewardActivityV2::class.java)
                getResult.launch(intent)
                return true
            }

            else -> {
            }
        }
        return false
    }

    private val getResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
            if (it.resultCode == Activity.RESULT_OK) {
                if (it.data?.hasExtra("voucherCode") == true) {
                    binding.content.txtVoucherCode.setText(it.data?.getStringExtra("voucherCode"))
                    binding.content.btnGetVoucher.performClick()
                }
                val value = it.data?.getStringExtra("input")
            }
        }

    fun getBagFeeApi(booking: BookingV3?, isReturnTrip: Boolean): Boolean {
        try {
            if (booking == null)
                return false
            val params = JSONObject()
            try {
                params.put(
                    "fareBasis",
                    if (booking.departure_f == null) "" else booking.departure_f!!.fareBasis
                )
                params.put(
                    "stops",
                    if (booking.departure_f == null) "" else booking.departure_f!!.stops
                )
                params.put(
                    "flightKey",
                    if (booking.departure_f == null) "" else booking.departure_f!!.flightKey
                )
            } catch (e: JSONException) {
                AppConfigs.logException(e)
            }
            var request = booking.departure_f!!.provider + "/" + booking.origin_code + "/" + booking.destination_code
            if (isReturnTrip) {
                request = booking.return_f!!.provider + "/" + booking.destination_code + "/" + booking.origin_code
                try {
                    params.put(
                        "fareBasis",
                        if (booking.return_f != null) booking.return_f!!.fareBasis else ""
                    )
                    params.put(
                        "stops",
                        if (booking.return_f != null) booking.return_f!!.stops else ""
                    )
                    params.put(
                        "flightKey",
                        if (booking.return_f != null) booking.return_f!!.flightKey else ""
                    )
                } catch (e: JSONException) {
                }
            }
            Log.d("request", request)
            SSLSendRequest(this).GET(
                true,
                "AirLines/BagFee/$request",
                params,
                object : CallBackInterface {
                    override fun onSuccess(response: JSONObject, cached: Boolean) {
                        try {
                            val listBags = response.getJSONArray("data")
                            if (isReturnTrip) {
                                Common.BAGGAGE_RETURN = listBags
                            } else {
                                Common.BAGGAGE_FEE = listBags
                            }

                            if (booking.is_round_trip && !isReturnTrip) {
                                getBagFeeApi(booking, true)
                            } else {

                                showPaxInput(true, false)
                            }
                        } catch (e: JSONException) {
                            AppConfigs.logException(e)
                        }
                    }

                    override fun onFail(error: VolleyError) {
                        error.printStackTrace()
                        AppConfigs.logException(error)

                    }
                })
        } catch (e: Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }
        return true
    }

    @RequiresApi(Build.VERSION_CODES.VANILLA_ICE_CREAM)
    private fun observe(){


        viewModel.mBooking.observe(this){



            binding.content.txtContactName.setText(viewModel.mBooking.value?.contact?.fullname)

            if (it.pax_info.adult.size > 0 && binding.content.txtContactName.text.isEmpty()) {
                binding.content.txtContactName.setText(it.pax_info.adult.getOrNull(0)?.fullName)
            }

            passengerAdapter.clearData()
            passengerAdapter.addData(viewModel.mBooking.value?.pax_info?.adult  ?: arrayListOf())
            passengerAdapter.addData(viewModel.mBooking.value?.pax_info?.child  ?: arrayListOf())
            passengerAdapter.addData(viewModel.mBooking.value?.pax_info?.student  ?: arrayListOf())
            passengerAdapter.addData(viewModel.mBooking.value?.pax_info?.infant  ?: arrayListOf())

            binding.content.paxInPut.removeAllViews()




            flightDetailAdapter.initData(
                it.adult,
                it.child,
                it.infant,
                it.is_round_trip
            )


            flightDetailAdapter.clearData()
            it.departure_f?.let { flight ->
                flightDetailAdapter.addData(arrayListOf(flight))
            }
            if (it.is_round_trip) {
                it.return_f?.let { flight ->
                    flightDetailAdapter.addData(arrayListOf(flight))
                }
            }


        }

        viewModel.mealCount.observe(this){

            if (it > 0) {
                binding.content.txtAddonSelect.text = "Đã chọn $it món"
                binding.content.txtAddonSelectDetail.text = resources.getString(R.string.txt_seat_select_detail_done)
            } else {
                binding.content.txtAddonSelect.text = resources.getString(R.string.txt_addon_select)
                binding.content.txtAddonSelectDetail.text = resources.getString(R.string.txt_addon_select_detail)
            }

        }
        viewModel.seatCount.observe(this){

            if (it > 0) {
                binding.content.txtSeatSelect.text = "Đã chọn $it ghế"
                binding.content.txtSeatSelectDetail.text = resources.getString(R.string.txt_seat_select_detail_done)
            } else {
                binding.content.txtSeatSelect.text =  resources.getString(R.string.txt_seat_select)
                binding.content.txtSeatSelectDetail.text = resources.getString(R.string.txt_seat_select_detail)
            }

        }

        viewModel.bookingLiveData.observe(this){

            val dialog = ProgressDialog(this)
            dialog.setMessage("Đang thực hiện đặt chỗ ...\nVui lòng đợi giây lát!")
            dialog.isIndeterminate = false
            dialog.max = 100
            dialog.setCanceledOnTouchOutside(false)
            dialog.setCancelable(false)
            dialog.setProgressStyle(ProgressDialog.STYLE_SPINNER)


            when(it){
                is State.Error -> {
                    dialog.dismiss()
                    Toast.makeText(
                        applicationContext,
                        "Thật xin lỗi :( \nCó lỗi xảy ra khi đặt chỗ \n Vui lòng thử lại hoặc gọi " + AppConfigs.getInstance().config.getString(
                            "hotline"
                        ) + " để được hỗ trợ!",
                        Toast.LENGTH_SHORT
                    ).show()
                }
                State.Loading -> {

                    dialog.show()
                }
                is State.Success -> {
                    if (it.data.id.isNullOrEmpty()) {
                        Toast.makeText(
                            applicationContext,
                            "Thật xin lỗi :( \nCó lỗi xảy ra khi đặt chỗ \n Vui lòng thử lại hoặc gọi " + AppConfigs.getInstance().config.getString(
                                "hotline"
                            ) + " để được hỗ trợ!",
                            Toast.LENGTH_SHORT
                        ).show()
                    } else {

                        val intent = Intent(applicationContext, PnrActivity::class.java)
                        intent.putExtra("email", it.data.contact_email)
                        intent.putExtra("bookingId", it.data.id)
                        intent.putExtra("showLoading", true)

                        startActivity(intent)

                        val intentBookingInsert = Intent("bookingInsert")
                        LocalBroadcastManager.getInstance(applicationContext).sendBroadcast(intentBookingInsert)
                        finish()
                    }
                }
            }
        }

        viewModel.initAllPassengers()



    }

    private fun showBookingTripDetail(bookingBase: BaseBooking) {
        binding.swipeRefreshLayout.isEnabled = false
        binding.shimmerViewContainer.visibility = View.GONE

        binding.content.tripContainer.removeAllViews()
        if (bookingBase.type == BaseBooking.BookingType.FLIGHT) {
//            val viewDeparture = ViewUtil.bindFlightDataToView(
//                booking.departure_f,
//                binding.content.tripContainer,
//                this,
//                false,
//                booking.adult,
//                booking.child,
//                booking.infant
//            )
//
//            viewDeparture.setOnClickListener {
//                binding.content.horizalScroll.fullScroll(View.FOCUS_LEFT)
//            }
//            if (booking.is_round_trip) {
//                binding.content.tripContainerRt.removeAllViews()
//                val viewReturn = ViewUtil.bindFlightDataToView(
//                    booking.return_f,
//                    binding.content.tripContainerRt,
//                    this,
//                    true,
//                    booking.adult,
//                    booking.child,
//                    booking.infant
//                )
//                viewReturn.setOnClickListener {
//                    binding.content.horizalScroll.fullScroll(View.FOCUS_RIGHT)
//                }
//            }
        }
        if (bookingBase.type == BaseBooking.BookingType.INTER) {
            showPaxInput(true, false)
            val viewDeparture = Widget.createViewFlightInterInfo(
                this,
                viewModel.mBooking.value?.fareData!!.getDepartureFlight()!!,
                binding.content.tripContainer
            )

            viewDeparture?.setOnClickListener {
                binding.content.horizalScroll.fullScroll(View.FOCUS_LEFT)
            }
            viewDeparture?.findViewById<TextView>(R.id.viewFlightDetail)?.setOnClickListener {
                showBottomSheetFlightInfo(viewModel.mBooking.value?.fareData, false)
            }
            if (bookingBase.is_round_trip && viewModel.mBooking.value?.fareData!!.getReturnFlight() != null) {
                binding.content.tripContainerRt.removeAllViews()
                val viewReturn = Widget.createViewFlightInterInfo(
                    this,
                    viewModel.mBooking.value?.fareData!!.getReturnFlight()!!,
                    binding.content.tripContainerRt
                )

                viewReturn?.setOnClickListener {
                    binding.content.horizalScroll.fullScroll(View.FOCUS_RIGHT)


                }
                viewReturn?.findViewById<TextView>(R.id.viewFlightDetail)?.setOnClickListener {
                    showBottomSheetFlightInfo(viewModel.mBooking.value?.fareData, true)
                }
            }

        }
    }

    override fun refreshLayout() {
        try {
            if (isUserSigned && AppController.instance.user != null) {
                retrievePassenger(this, firebaseUser?.uid)
                binding.content.loginLayout.visibility = (View.GONE)
                binding.content.layoutPointReward.visibility = (View.VISIBLE)
                binding.content.txtContactName.setText(
                    Common.unAccent(AppController.instance.user?.userName)
                        .uppercase(Locale.getDefault())
                )

                val phone =
                    if (firebaseUser?.phoneNumber != null && !firebaseUser?.phoneNumber.equals(
                            "",
                            ignoreCase = true
                        )
                    ) AppController.instance.user?.phoneNumber else SharedPrefs.getInstance()
                        .get("phone", String::class.java).toString()

                binding.content.txtContactPhone.setText(phone?.replace("+84", "0"))
                binding.content.txtContactEmail.setText(AppController.instance.user?.userEmail)
            } else {
                binding.content.loginLayout.visibility = View.VISIBLE
                binding.content.layoutPointReward.visibility = (View.GONE)
            }
        } catch (e: java.lang.Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }
    }

    private fun showConfirmDialog() {
        val alertDialog = AlertDialog.Builder(this).create()
        alertDialog.setTitle("Chú ý")
        alertDialog.setIcon(R.drawable.ic_bell_alert)
        if (viewModel.mBooking.value?.type != BaseBooking.BookingType.INTER) {
            if (viewModel.mBooking.value?.departure_f?.stops!! > 0) {
                alertDialog.setMessage(Common.convertHTML("Chuyến bay của bạn chọn là chuyến bay <b>NỐI CHUYẾN</b> (có " + viewModel.mBooking.value?.departure_f?.stops + " điểm dừng)<br>Thời gian bay là <b>" + viewModel.mBooking.value?.departure_f?.duration + "</b><br>Bạn có chắc chắn muốn đặt vé ?"))
            } else if (viewModel.mBooking.value?.is_round_trip == true) {
                if (viewModel.mBooking.value?.return_f?.stops!! > 0) {
                    alertDialog.setMessage(Common.convertHTML("Chuyến bay lượt về của bạn là chuyến bay <b>NỐI CHUYẾN</b> (có " + viewModel.mBooking.value?.departure_f?.stops + " điểm dừng)<br>Thời gian bay là <b>" + viewModel.mBooking.value?.return_f?.duration + "</b><br>Bạn có chắc chắn muốn đặt vé ?"))
                } else {
                    alertDialog.setMessage(Common.convertHTML(Common.AppPopup))
                }
            } else {
                alertDialog.setMessage(Common.convertHTML(Common.AppPopup))
            }
        } else {
            alertDialog.setMessage(Common.convertHTML("Quý khách cần chủ động kiểm tra thông tin <b>VISA, điều kiện nhập cảnh </b>của mình trước khi đặt vé."))
        }
        alertDialog.setButton(
            DialogInterface.BUTTON_NEGATIVE,
            "Đặt vé"
        ) { dialog, which -> sendBooking() }
        alertDialog.setButton(DialogInterface.BUTTON_POSITIVE, "Kiểm tra lại") { dialog, which -> }
        alertDialog.show()
    }

    fun showSelectVoucherDialog() {
        val alertDialog = AlertDialog.Builder(this).create()
        alertDialog.setTitle("Chú ý")
        alertDialog.setIcon(R.drawable.ic_bell_alert)

        alertDialog.setMessage(Common.convertHTML("Vui lòng nhập mã <b>Giảm Giá </b>. Nếu chưa có nhấn vào lấy mã để nhận mã giảm giá!"))

        alertDialog.setButton(DialogInterface.BUTTON_NEGATIVE, "Lấy mã") { dialog, which ->
//            val intent = Intent(this, RewardActivity::class.java)
            val intent = Intent(this, RewardActivityV2::class.java)
            getResult.launch(intent)
        }
        alertDialog.setButton(
            DialogInterface.BUTTON_POSITIVE,
            "Nhập lại"
        ) { dialog, which -> binding.content.txtVoucherCode.requestFocus() }
        alertDialog.show()
    }

    override fun onStart() {
        super.onStart()
        LocalBroadcastManager.getInstance(this)
            .registerReceiver(mMessageReceiver, IntentFilter("bookingupdate"))
    }

    private val mMessageReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val message = intent.getStringExtra("message")
            val snackbar = Snackbar.make(binding.coordinatorLayout, message!!, 60000)
                .setAction("XEM CHI TIẾT") {
                    val `in` = Intent(applicationContext, PnrActivity::class.java)
                    startActivity(`in`)
                    finish()
                }
            snackbar.show()
        }
    }

//    override fun onSystemSettingChange(type: AppConfigs.SystemSettingType) {
//
//        if (type == AppConfigs.SystemSettingType.INTERNET) {
//            if (this::viewModel.isInitialized) {
//                viewModel.updateInternetStatus(isInternetConnected)
//            }
//        }
//
//        if (type == AppConfigs.SystemSettingType.USER) {
//            if (this::viewModel.isInitialized) {
//                viewModel.updateUserStatus(isUserSigned)
//                binding.viewModel = viewModel
//                if (isUserSigned && AppController.instance.user != null) {
//                    binding.content.txtContactName.setText(AppController.instance.user?.userName)
//                    binding.content.txtContactEmail.setText(AppController.instance.user?.userEmail)
//                    binding.content.txtContactPhone.setText(AppController.instance.user?.phoneNumber)
//                }
//            }
//        }
//    }


    override fun onConnectionChange(it: ConnectionState) {
        super.onConnectionChange(it)
        try {

            if (it != ConnectionState.CONNECTED) {
//                viewModel.isUserSigned(true)

                viewModel.isUserSigned.value = true
            }


        } catch (e: Exception) {
            Log.logException(e)
        }

    }


    private fun setUpFlight() {
        sheetDialog = BottomSheetDialog(this, R.style.SheetDialogTransparent)
        sheetViewLayout = layoutInflater.inflate(R.layout.select_flight_inter_layout, null)
        sheetDialog!!.setContentView(sheetViewLayout!!)
    }

    private fun showBottomSheetFlightInfo(fare: FareData?, isReturnTrip: Boolean) {
        try {

            Widget.createFareDetailInfo(
                this,
                fare!!,
                sheetViewLayout!!.findViewById(R.id.flightInfo)
            )
            sheetDialog!!.behavior.state = BottomSheetBehavior.STATE_EXPANDED
            sheetDialog!!.behavior.isHideable = true
            sheetDialog!!.behavior.isDraggable = true
            sheetDialog!!.show()

            sheetViewLayout!!.findViewById<TextView>(R.id.txtGrandTotalPrice).text =
                Common.dinhDangTien(fare.totalPrice!!)

            if (isReturnTrip) {
                sheetViewLayout!!.findViewById<View>(R.id.departure_container).visibility =
                    View.GONE
            } else {
                sheetViewLayout!!.findViewById<View>(R.id.return_container).visibility = View.GONE
            }
            sheetViewLayout?.findViewById<View>(R.id.quickViewLayout)?.visibility = View.GONE
            sheetViewLayout?.findViewById<View>(R.id.close_view)?.visibility = View.VISIBLE

            sheetViewLayout?.findViewById<Button>(R.id.btnBackFlight)?.setOnClickListener {
                if (sheetDialog != null && sheetDialog!!.isShowing) sheetDialog?.hide()
            }


        } catch (e: java.lang.Exception) {
            e.printStackTrace()
            if (sheetDialog!!.isShowing) sheetDialog!!.dismiss()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {

        if (resultCode == RESULT_OK && requestCode == REQUEST_CODE_ADDON_SELECT) {
            if (data != null && data.hasExtra("bookingDetail")) {
                val paxInfo = Gson().fromJson(data.getStringExtra("bookingDetail"), PaxInfoListV2::class.java)
                viewModel.addAddOnInfo(paxInfo)
                showPaxInput(true, true)
            }
        }
        super.onActivityResult(requestCode, resultCode, data)
    }







}
