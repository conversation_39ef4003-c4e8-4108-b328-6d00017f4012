package com.hqt.view.adapter;


import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.Typeface;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.hqt.datvemaybay.R;
import com.hqt.data.model.Airport;
import com.hqt.util.SharedPrefs;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by TN on 12/1/2016.
 */
public class AirPortAdapter extends RecyclerView.Adapter<AirPortAdapter.ViewHolder> {

    List<Airport> contents;
    Context mContext;
    final int RESULT_OK = -1;

    AppCompatActivity mActivity;
    static final int TYPE_HEADER = 0;
    static final int TYPE_CELL = 1;
    int SEARCH_TYPE = 0; // 1 origon 2 destination 0 default

    public class ViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener {
        // each data item is just a string in this case
        public TextView txtAirportName, txtAirportCode, txtAirportCity;
        public ImageView imgBanner;

        public ViewHolder(View v) {
            super(v);

            txtAirportName = v.findViewById(R.id.airPortName);
            txtAirportCode = v.findViewById(R.id.airPortCode);
            imgBanner = v.findViewById(R.id.image);
            txtAirportCity = v.findViewById(R.id.airPortCity);

            v.setOnClickListener(this);
        }

        @Override
        public void onClick(View v) {
            Intent data = new Intent();

            if (SEARCH_TYPE == 0) {
                data.putExtra("code", contents.get(getAdapterPosition()).getCode());
                data.putExtra("name", contents.get(getAdapterPosition()).getCity());
                ((Activity) mContext).setResult(RESULT_OK, data);
                ((Activity) mContext).finish();
            } else if (SEARCH_TYPE == 1) {
                txtAirportCode.setBackground(ContextCompat.getDrawable(mContext, R.drawable.corner_full_primary));
                txtAirportCode.setTextColor(Color.WHITE);

                SharedPrefs.getInstance().put("PRICEBOARD_ORIGIN", contents.get(getAdapterPosition()));
                SharedPrefs.getInstance().delete("PRICEBOARD_DESTINATION");

                ((Activity) mContext).setResult(RESULT_OK, data);
                ((Activity) mContext).finish();

            } else if (SEARCH_TYPE == 2) {
                boolean checked = contents.get(getAdapterPosition()).getIsCheck();
                if (!checked) {
                    txtAirportCode.setBackground(ContextCompat.getDrawable(mContext, R.drawable.corner_full_primary));
                    txtAirportCode.setTextColor(Color.WHITE);

                } else {
                    txtAirportCode.setBackground(ContextCompat.getDrawable(mContext, R.drawable.round_airport_code));
                    txtAirportCode.setTextColor(ContextCompat.getColor(mContext, R.color.stt_gray));
                }
                contents.get(getAdapterPosition()).setIsCheck(!checked);
            }
        }
    }

    public AirPortAdapter(Context context, List<Airport> contents, int searchType) {
        this.mContext = context;
        this.contents = contents;
        this.SEARCH_TYPE = searchType;
    }

    @Override
    public int getItemCount() {
        return contents.size();
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = null;

        view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.listairport_item_multi, parent, false);

        ViewHolder v = new ViewHolder(view);

        return v;
    }


    @Override
    public void onBindViewHolder(ViewHolder holder, int position) {
        // - get element from your dataset at this position
        // - replace the contents of the view with that element
        Typeface fontAwesome = Typeface.createFromAsset(mContext.getAssets(), "fonts/fontawesome-webfont.ttf");

        holder.txtAirportName.setText(("Sân bay " + contents.get(position).getName()));
        holder.txtAirportCity.setText((contents.get(position).getCity() != null ? contents.get(position).getCity() : ""));
        holder.txtAirportCode.setText(contents.get(position).getCode());

        Glide.with(mContext)
                .load("https://ssl.12bay.vn/api/v1/AirLines/Image/" + contents.get(position).getCode() + "?size=s")
                .skipMemoryCache(true)
                .override(60, 60)
                .placeholder(R.mipmap.ic_launcher)
                .into(holder.imgBanner);


    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemViewType(int position) {
        return position;
    }

    public ArrayList<Airport> getSelectItem() {
        ArrayList<Airport> listSelect = new ArrayList<Airport>();
        for (Airport airport : contents) {
            if (airport.getIsCheck()) {
                listSelect.add(airport);
            }
        }
        return listSelect;
    }

}