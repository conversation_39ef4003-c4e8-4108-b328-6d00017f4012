package com.hqt.view.ui.tour


import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.view.WindowManager
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.hqt.datvemaybay.R
import com.hqt.util.AppConfigs
import com.hqt.util.AppController
import com.hqt.view.ui.BaseActivityKt
import com.hqt.view.ui.reward.ui.activity.RewardActivity
import com.hqt.viewmodel.BookingViewModel
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import q.rorbin.badgeview.QBadgeView


class TourListActivity() :
    BaseActivityKt<com.hqt.datvemaybay.databinding.ActivityTourListLayoutBinding>() {


    override val layoutId: Int = R.layout.activity_tour_list_layout
    lateinit var viewModel: BookingViewModel
    lateinit var mAdapter: TourListViewAdapter
    var tourList: ArrayList<TourList> = ArrayList()
    private var disposable: Disposable? = null


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        getToolbar().title = "Bạn muốn đi đâu"
        getToolbar().setNavigationIcon(R.drawable.ic_action_back_home)
        supportActionBar!!.setDisplayShowHomeEnabled(true)

        getViewBindding().recyclerView.layoutManager = LinearLayoutManager(applicationContext)
        getViewBindding().lifecycleOwner = this

        mAdapter = TourListViewAdapter(this, tourList)
        getViewBindding().recyclerView.adapter = mAdapter

        setWindowFlag(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS, false)
        window.statusBarColor = ContextCompat.getColor(this, R.color.primary_dark)

        if (intent.hasExtra("link")) {
            val link = intent.getStringExtra("link")
            val catId = link!!.replace("https://12bay.vn/tour/cat/", "")
            val title = intent.getStringExtra("title")
            getToolbar().title = title
            getListTourByCat(catId)
        } else {
            getListTour()
        }
        initAnalytics()
    }

    fun getListTourByCat(id: String) {

        disposable =
            AppController.instance.getService().getTourByCategory(id).subscribeOn(Schedulers.io())
                .doOnSubscribe {

                }.observeOn(AndroidSchedulers.mainThread()).subscribe({ response ->

                    if (response.data != null) {
                        tourList.clear()
                        tourList.addAll(response.data)
                        mAdapter.notifyDataSetChanged()


                        getViewBindding().shimmerViewContainer.stopShimmer()
                        getViewBindding().shimmerViewContainer.visibility = View.GONE
                        getViewBindding().recyclerView.visibility = View.VISIBLE

                    } else {
                        getViewBindding().shimmerViewContainer.stopShimmer()
                        getViewBindding().shimmerViewContainer.visibility = View.GONE
                    }

                }, { throwable ->


                    getViewBindding().shimmerViewContainer.stopShimmer()
                    getViewBindding().shimmerViewContainer.visibility = View.GONE

                    throwable.printStackTrace()
                })
    }

    fun getListTour() {


        disposable =
            AppController.instance.getService().getTourFeature().subscribeOn(Schedulers.io())
                .doOnSubscribe {

                }.observeOn(AndroidSchedulers.mainThread()).subscribe({ response ->

                if (response.data != null) {
                    tourList.clear()
                    tourList.addAll(response.data)
                    mAdapter.notifyDataSetChanged()


                    getViewBindding().shimmerViewContainer.stopShimmer()
                    getViewBindding().shimmerViewContainer.visibility = View.GONE
                    getViewBindding().recyclerView.visibility = View.VISIBLE


                } else {
                    getViewBindding().shimmerViewContainer.stopShimmer()
                    getViewBindding().shimmerViewContainer.visibility = View.GONE
                }

            }, { throwable ->


                getViewBindding().shimmerViewContainer.stopShimmer()
                getViewBindding().shimmerViewContainer.visibility = View.GONE

                throwable.printStackTrace()
            })
    }

    private fun initAnalytics() {
        try {

            val params = Bundle()

            firebaseAnalytics.logEvent("activity_view", params)
            firebaseAnalytics.setCurrentScreen(this, "tour_list_view", null)

        } catch (e: Exception) {

        }
    }

    override fun onResume() {
        super.onResume()
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean { // Inflate the menu; this adds items to the action bar if it is present.
        menuInflater.inflate(R.menu.main_reward, menu)
        try {
            Handler(Looper.getMainLooper()).postDelayed({
                val rewardButton = findViewById(R.id.action_reward) as View
                QBadgeView(applicationContext).setGravityOffset(0f, 0f, true)
                    .bindTarget(rewardButton).setBadgeText("!")
                    .setOnDragStateChangedListener { dragState, badge, targetView ->
                    }
            }, 1000)
        } catch (e: java.lang.Exception) {
        }
        return true
    }


    override fun onOptionsItemSelected(item: MenuItem): Boolean { // Take appropriate action for each action item click
        when (item.itemId) {
            android.R.id.home -> {

                finish()
            }

            R.id.action_reward -> {
                val `in` = Intent(this, RewardActivity::class.java)

                startActivity(`in`)
                return true
            }

            else -> {
                finish()
            }
        }
        return false
    }

    override fun onSystemSettingChange(type: AppConfigs.SystemSettingType) {

        if (type == AppConfigs.SystemSettingType.INTERNET) {
            if (this::viewModel.isInitialized) {
                viewModel.updateInternetStatus(isInternetConnected)
            }
        }


    }


}
