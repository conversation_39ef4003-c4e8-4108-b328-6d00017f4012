package com.hqt.view.ui.search.ui.dialog

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.firebase.analytics.FirebaseAnalytics
import com.hqt.base.model.State
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.databinding.SelectFlightLayoutBinding
import com.hqt.util.Helper.clickWithDebounce
import com.hqt.view.ui.booking.ui.state.BookingItemState
import com.hqt.view.ui.flighthistory.ui.activity.FlightHistoryActivityV2
import com.hqt.view.ui.search.data.model.FlightV2
import com.hqt.view.ui.search.ui.SearchResultViewModel
import com.hqt.view.ui.search.ui.state.FlightItemState
import dagger.hilt.android.AndroidEntryPoint
import kotlin.math.roundToInt

@AndroidEntryPoint
class SelectFlightDialog : BottomSheetDialogFragment() {

    val viewModel: SearchResultViewModel by activityViewModels()


    var binding: SelectFlightLayoutBinding? = null


    var onActionDone: (Boolean) -> Unit = {}

    companion object {
        const val TAG = "ModalBottomSheet"
    }


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = SelectFlightLayoutBinding.inflate(layoutInflater, container, false)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding?.apply {



            viewModel.getFlightHistory()


            val params = Bundle()
            params.putString(
                FirebaseAnalytics.Param.ORIGIN,
                Common.getAirPortCode(viewModel.bookingDetail.origin_code)
            )
            params.putString(
                FirebaseAnalytics.Param.DESTINATION,
                Common.getAirPortCode(viewModel.bookingDetail.destination_code)
            )
            params.putString(
                FirebaseAnalytics.Param.START_DATE,
                viewModel.bookingDetail.departure_date
            )
            params.putString(FirebaseAnalytics.Param.END_DATE, viewModel.bookingDetail.return_date)
            params.putString(
                FirebaseAnalytics.Param.FLIGHT_NUMBER,
                viewModel.selectFlight?.flightNumber
            )
            params.putString(
                FirebaseAnalytics.Param.ITEM_NAME,
                viewModel.bookingDetail.origin_code + viewModel.bookingDetail.destination_code + viewModel.selectFlight?.flightNumber
            )
            params.putString(
                FirebaseAnalytics.Param.TRAVEL_CLASS,
                viewModel.selectFlight?.fareBasis
            )
            params.putString(
                FirebaseAnalytics.Param.NUMBER_OF_PASSENGERS,
                (viewModel.bookingDetail.adult + viewModel.bookingDetail.child + viewModel.bookingDetail.infant).toString() + ""
            )
            FirebaseAnalytics.getInstance(requireContext())
                .logEvent(FirebaseAnalytics.Event.VIEW_ITEM, params)

            if ((viewModel.selectFlight?.originCode == viewModel.bookingDetail.origin_code)) {
                viewModel.bookingDetail.departure_f = viewModel.selectFlight
            } else {
                viewModel.bookingDetail.return_f = viewModel.selectFlight
            }

            info.airlineLogo.setImageResource(
                Integer.valueOf(
                    resources.getIdentifier(
                        viewModel.selectFlight?.logo,
                        "drawable",
                        requireContext().packageName
                    )
                )
            )


            info.flightDetail = viewModel.selectFlight ?: FlightV2()
            info.itemViewState = FlightItemState(viewModel.selectFlight ?: FlightV2())

            itemViewState = BookingItemState(viewModel.bookingDetail)

            btnBookVe.clickWithDebounce(500) {

                onActionDone.invoke(true)
                dismiss()
            }

            info.metaLayout.setOnClickListener {
                val flin = Intent(requireContext(), FlightHistoryActivityV2::class.java)
                flin.putExtra("flightNumber", viewModel.selectFlight?.flightNumber)
                startActivity(flin)
            }







            observe()


        }




        dialog?.let {
            val sheet = it as BottomSheetDialog
            sheet.behavior.state = BottomSheetBehavior.STATE_EXPANDED
        }


    }



    fun observe() {

        viewModel.flightHistoryLiveData.observe(viewLifecycleOwner) {
            when (it) {
                is State.Error -> {

                }

                State.Loading -> {

                }

                is State.Success -> {
                    binding?.apply {


                        val score = (it.data.meta?.score ?: 0f) * 100
                        info.scoreProgress.progress = score
                        info.scoreProgress.text = "" + score.roundToInt()
                        info.txtMetaFlightNumber.text = it.data.meta?.flight
                        info.txtMetaGreen.text = it.data.meta?.green?.total.toString()
                        info.txtMetaRed.text = it.data.meta?.red?.total.toString()
                        info.txtMetaYellow.text = it.data.meta?.yellow?.total.toString()
                        info.txtMetaTotalFlight.text = it.data.meta?.total.toString()



//                        binding?.info?.flightHistory = FlightHistoryItemState(it.data)


                        info.metaLayout.visibility = View.VISIBLE
                        info.metaShimmer.visibility = View.GONE
                    }

                }
            }
        }


    }


}