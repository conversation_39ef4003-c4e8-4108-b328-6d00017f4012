package com.hqt.view.ui.reward.ui.adapter

import com.bumptech.glide.Glide
import com.hqt.base.BaseAdapter
import com.hqt.view.ui.reward.data.model.Voucher
import com.hqt.view.ui.reward.data.model.Promotion
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.AdapterPromotionBinding
import com.hqt.util.Helper.clickWithDebounce
import com.hqt.view.ui.reward.ui.state.PromotionItemState


class PromotionAdapterV2(private val listener: (Promotion) -> Unit) :
    BaseAdapter<Promotion, AdapterPromotionBinding>(listener) {



    override fun getLayoutRes(): Int {
        return R.layout.adapter_promotion
    }



    override fun bind(binding: AdapterPromotionBinding, position: Int, model: Promotion) {
        binding.apply {


            itemViewState = PromotionItemState(model)

            Glide.with(_context).load(model.logo).override(120, 120)
                .placeholder(R.mipmap.ic_launcher).skipMemoryCache(true)
                .into(logo)

            Glide.with(_context)
                .load(model.banner).skipMemoryCache(true)
                .placeholder(R.drawable.top_banner)
                .into(banner)

            getVoucher.clickWithDebounce(500){
                listener(model)
            }

        }

    }





    override fun onItemClickListener(model: Promotion) {
    }

}