package com.hqt.view.ui.payment

import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.os.CountDownTimer
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.viewpager.widget.ViewPager
import com.hqt.data.model.OrderInfo
import com.hqt.data.model.Payment
import com.hqt.data.model.PaymentOrderXml
import com.hqt.data.model.PaymentType
import com.hqt.data.model.request.GetOrderXmlRequest
import com.hqt.data.model.response.GetListPaymentRespone
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.NewPaymentActivityBinding
import com.hqt.util.AppConfigs
import com.hqt.util.Widget
import com.hqt.util.payment.OrderResponse
import com.hqt.view.adapter.ViewPagerAdapter
import com.hqt.view.ui.BaseActivityKt
import com.hqt.view.ui.payment.ui.main.NewPaymentFragment
import com.hqt.view.ui.payment.ui.main.PaylatePaymentFragment
import com.hqt.view.ui.payment.ui.main.PaylateStorePaymentFragment
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import vn.payoo.model.GroupType
import vn.payoo.model.Language
import vn.payoo.paymentsdk.OnPayooPaymentCompleteListener
import vn.payoo.paymentsdk.PayooPaymentSDK
import vn.payoo.paymentsdk.data.model.PaymentMethod
import vn.payoo.paymentsdk.data.model.PaymentOption
import vn.payoo.paymentsdk.data.model.ResponseObject


class NewPaymentActivity : BaseActivityKt<NewPaymentActivityBinding>() {
    override val layoutId: Int = R.layout.new_payment_activity
    private var disposable: Disposable? = null
    private var selectPayment: Payment? = null
    private var selectMethod: PaymentType? = null
    var headStatus: TextView? = null
    private var timer: CountDownTimer? = null
    private var bookingToken = ""
    var orderInfo: OrderInfo? = null
    private lateinit var viewPager: ViewPager
    var adapter: ViewPagerAdapter? = null
    private var selectAppBank: ArrayList<Payment> = ArrayList()
    lateinit var mProgressDialog: Dialog


    lateinit var context: Context

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        viewPager = findViewById(R.id.viewpager)
        mProgressDialog = com.hqt.util.ProgressDialog.progressDialog(this)


        setupViewPager(viewPager)

        if (intent.hasExtra("token")) {
            bookingToken = intent.getStringExtra("token").toString()

            if (bookingToken.isNotEmpty()) {
                getOrderInfo()
            }
        }

        initAnalytics(null)
    }


    fun getOrderInfo() {
        try {
            disposable = sslService.getPayment(bookingToken).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread()).toObservable().doOnComplete { }
                .subscribe({ response ->
                    onSuccess(response)

                }, { throwable ->
                    AppConfigs.logException(throwable)
                })
        } catch (e: Exception) {
            e.printStackTrace()
            AppConfigs.logException(e)
        }
    }

    private fun onSuccess(response: GetListPaymentRespone) {

        try {
            orderInfo = response.data?.order

            //            if (!PayMeWallet.isLogin) {
            //                PayMeWallet.payMeLogin(this, Common.removeWordPhoneFormat(orderInfo?.contact?.contactPhone!!))
            //            }

            if (orderInfo?.status == "done") {
                finish()
                Toast.makeText(this, "Bạn đã thanh toán thành công đơn hàng", Toast.LENGTH_SHORT)
                    .show()
            } else {

                response.data!!.paymentMethod.forEach { it ->
                    if (it.PaymentType == "ATM") {
                        selectAppBank = it.list
                    }
                }

                val manager = supportFragmentManager
                val ft = supportFragmentManager.beginTransaction()
                for (frag in manager.fragments) {
                    if (frag.javaClass == NewPaymentFragment::class.java) {
                        (frag as (NewPaymentFragment)).updateViewData(response)
                    }
                }
                ft.commit()

            }


        } catch (e: Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }
    }

    fun setTimeLimit(timeLimitX: String, headStatusFrag: TextView) {
        try {
            val timeLimit = Common.stringToDate(timeLimitX, "yyyy-MM-dd HH:mm:ss")
            headStatus = headStatusFrag
            if (timeLimit != null && headStatus != null) {

                val endDate = timeLimit.time.time
                val startTime = System.currentTimeMillis()
                if (timer == null && endDate - startTime > 0) {
                    timer = object : CountDownTimer(endDate, 1000) {
                        override fun onTick(millisUntilFinished: Long) {
                            headStatus?.text = Common.convertHTML(
                                "Hạn thanh toán " + Common.getDateTextCountDown(
                                    millisUntilFinished,
                                    startTime
                                ) + " ⏱️"
                            )
                            headStatus?.setBackgroundColor(Color.parseColor("#ff0088cd"))
                        }

                        override fun onFinish() {
                            headStatus?.text = "Hết hạn thanh toán ⏱️"
                            headStatus?.setBackgroundColor(Color.parseColor("#B1B1B1"))
                        }
                    }.start()

                } else {
                    if (timer != null) timer!!.start()
                }
            } else {
                timer?.cancel()
            }
        } catch (e: java.lang.Exception) {
            AppConfigs.logException(e)
        }
    }

    fun onPaymentCall() {


        val paymentMethods = mutableListOf<PaymentMethod>()
        if (selectPayment?.Type == "CC") {
            paymentMethods.add(PaymentMethod.from(PaymentMethod.INTERNATIONAL_CARD_VALUE))
        }

        if (selectPayment?.Type == "ATM") {
            paymentMethods.add(PaymentMethod.from(PaymentMethod.DOMESTIC_CARD_VALUE))
        }

        if (selectPayment?.Type == "QR") {
            paymentMethods.add(PaymentMethod.from(PaymentMethod.QR_CODE_VALUE))
        }

        if (selectPayment != null && orderInfo != null) {
            var orderRequest = GetOrderXmlRequest(
                orderInfo?.sessionId,
                orderInfo?.contact?.contactEmail,
                orderInfo?.contact?.contactName,
                orderInfo?.contact?.contactPhone,
                selectPayment?.Type,
                selectPayment?.Bank
            )

            disposable = sslService.getOrderXml(orderRequest).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread()).doOnSubscribe { mProgressDialog.show() }
                .toObservable()
                .doOnComplete { mProgressDialog.dismiss() }.subscribe({ response ->
                    val orderResonse = OrderResponse()

                    orderResonse.checksum = response.data?.form?.checksum
                    orderResonse.orderInfo = response.data?.form?.orderInfo
                    orderResonse.cashAmount = response.data?.grandTotal!! + 0.0
                    var logParams = Bundle()

                    AppConfigs.Log("response", response.toString())
                    if (response.data.status == "waiting_payment") {

                        logParams.putString("PAY_TYPE", selectPayment?.Type)
                        initAnalytics(logParams)

                        if (selectPayment?.Type!!.contains("PAYME")) {

                            // PayMeWallet.pay(this, selectPayment!!.Type!!, response.data)

                        } else if (selectPayment?.Type == "PAYLATE") {
                            paylateSelect(response.data)

                        } else if (selectPayment?.Type == "PAYLATESTORE") {
                            paylateStoreSelect(response.data)
                        } else if (selectPayment?.Type == "SHOPEEPAY") {

                            payShopeePay(response.data)

                        } else {

                            val paymentOption =
                                PaymentOption.newBuilder().withLanguage(Language.VIETNAMESE)
                                    .withCustomerEmail(orderRequest.ContactEmail!!)
                                    .withCustomerPhone(orderRequest.ContactPhone!!)
                                    .withUserId(orderRequest.ContactPhone!!)
                                    .withStyleResId(R.style.PayooSdkTheme_Blue)
                                    .withAuthToken(orderRequest.SessionId!!)
                                    .withSupportedPaymentMethods(paymentMethods)
                                    .withBankCode(response.data.paymentBank)
                                    .build()


                            makePayment(orderResonse, paymentOption)
                        }

                    } else {
                        showStatusAlert(response.data.status)
                    }

                }, { throwable ->
                    throwable.printStackTrace()
                    AppConfigs.logException(throwable)
                })
        } else {
            Toast.makeText(this, "Vui lòng chọn hình thức thanh toán", Toast.LENGTH_SHORT).show()
        }

    }


    private fun makePayment(orderResonse: OrderResponse, paymentOption: PaymentOption) {
        try {
            PayooPaymentSDK.selectMethod(
                this,
                orderResonse,
                paymentOption,
                requireNotNull(listener)
            )
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun showSelectAppBank() {
        Widget.showBankAppSelect(this, selectAppBank)
    }

    override fun onDestroy() {

        disposable?.dispose()
        disposable = null
        listener = null
        try {
            PayooPaymentSDK.unregisterOnPaymentCompleteListener()
        } catch (e: Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }
        super.onDestroy()
    }

    private fun paylateSelect(orderInfo: PaymentOrderXml) {
        try {

            viewPager.currentItem = 1
            val manager = supportFragmentManager
            val ft = supportFragmentManager.beginTransaction()
            for (frag in manager.fragments) {
                if (frag.javaClass == PaylatePaymentFragment::class.java) {
                    (frag as (PaylatePaymentFragment)).bindOrder(selectPayment!!, orderInfo)
                }
            }
            ft.commit()

        } catch (e: Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }

    }

    private fun payShopeePay(orderInfo: PaymentOrderXml) {

        try {
            if (orderInfo.form.orderInfo != null) {
                val i = Intent(Intent.ACTION_VIEW)
                i.data = Uri.parse(orderInfo.form.orderInfo)
                startActivity(i)
            }

        } catch (e: Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }

    }

    private fun paylateStoreSelect(orderInfo: PaymentOrderXml) {

        try {

            viewPager.currentItem = 2
            val manager = supportFragmentManager
            val ft = supportFragmentManager.beginTransaction()
            for (frag in manager.fragments) {
                if (frag.javaClass == PaylateStorePaymentFragment::class.java) {
                    (frag as (PaylateStorePaymentFragment)).bindOrder(orderInfo)
                }
            }
            ft.commit()

        } catch (e: Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }

    }

    fun clearSelectBank(tag: String) {
        try {
            val manager = supportFragmentManager
            val ft = supportFragmentManager.beginTransaction()
            for (frag in manager.fragments) {
                if (frag.javaClass == NewPaymentFragment::class.java) {
                    (frag as (NewPaymentFragment)).clearSelectBank(tag)
                }
            }
            ft.commit()

        } catch (e: Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }
    }

    fun showSelectBankAppList() {


    }

    fun onSelectBank(payment: Payment, method: PaymentType) {

        selectPayment = payment
        selectMethod = method
        try {
            val manager = supportFragmentManager
            val ft = supportFragmentManager.beginTransaction()
            for (frag in manager.fragments) {
                if (frag.javaClass == NewPaymentFragment::class.java) {
                    (frag as (NewPaymentFragment)).selectBank(payment, method)
                }
            }
            ft.commit()

        } catch (e: Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }
    }


    private var listener: OnPayooPaymentCompleteListener? =
        object : OnPayooPaymentCompleteListener {
            override fun onPaymentComplete(groupType: Int, responseObject: ResponseObject?) {
                if (groupType == GroupType.SUCCESS) {
                    if (responseObject?.data != null) {
                        val authTokenResponse: String = responseObject.data?.authToken.orEmpty()
                        if (authTokenResponse.isNotEmpty()) {

                            saveAuthToken(authTokenResponse)
                        }
                        Toast.makeText(
                            applicationContext,
                            "Thanh toán thành công",
                            Toast.LENGTH_SHORT
                        ).show()
                        finish()
                    }
                } else {
                    showMessage(groupType, responseObject)
                }

            }
        }

    private fun showMessage(@GroupType groupType: Int, responseObject: ResponseObject?): Boolean {
        if (groupType == GroupType.CANCEL || groupType == GroupType.UNKNOWN) {
            return true
        }

        AlertDialog.Builder(this).setTitle(
            when (groupType) {
                GroupType.CANCEL -> "CANCEL"
                GroupType.SUCCESS -> "SUCCESS"
                GroupType.FAILURE -> "FAILURE"
                else -> "UNKNOWN"
            }
        ).setMessage(responseObject?.message)
            .setPositiveButton(R.string.popup_close) { dialog: DialogInterface, _: Int -> dialog.dismiss() }
            .show()
        return false
    }

    private fun showStatusAlert(status: String) {
        AlertDialog.Builder(this).setTitle("Thông báo").setMessage(
            when (status) {
                "done" -> "Đơn hàng đã thanh toán thành công!. Bạn không cần thanh toán nữa đâu ạ.:)"
                "expired" -> "Đơn hàng hết hạn thanh toán. Bạn vui lòng đặt lại đơn hàng khác."
                "fail" -> "Đơn hàng đã bị hủy. Bạn vui lòng đặt đơn khác. Hoặc gọi 19002642 để được hổ trợ nhé."
                "created" -> "Đơn hàng mới khởi tạo. Đang tiến hàng giữ chỗ. Bạn vui lòng đợi trong giây lát rồi thử lại."
                else -> "Đơn hàng hết hạn hoặc đã bị hủy"
            }

        ).setPositiveButton(R.string.popup_close) { dialog: DialogInterface, _: Int ->
            dialog.dismiss()
            finish()
        }.setNegativeButton("Gọi hổ trợ") { dialog: DialogInterface, _: Int ->
            val i = Intent(Intent.ACTION_DIAL)
            i.data = Uri.parse("tel:19002642")
            i.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            startActivity(i)
        }.show()
    }

    private fun saveAuthToken(authToken: String) {
        sharedPref.edit().putString("KEY_AUTH_TOKEN", authToken).apply()
    }

    private val sharedPref by lazy {
        getSharedPreferences("PREF_NAME", Context.MODE_PRIVATE)
    }


    override fun onBackPressed() {

        if (viewPager.currentItem == 0) {
            super.onBackPressed()
        } else {
            viewPager.currentItem = 0
        }

    }

    private fun setupViewPager(viewPager: ViewPager) {
        adapter = ViewPagerAdapter(supportFragmentManager)
        adapter!!.addFragment(NewPaymentFragment(), "NewPaymentFragment")
        adapter!!.addFragment(PaylatePaymentFragment(), "PaylatePaymentFragment")
        adapter!!.addFragment(PaylateStorePaymentFragment(), "PaylateStorePaymentFragment")

        viewPager.adapter = adapter
        viewPager.offscreenPageLimit = 4

    }

    private fun initAnalytics(params: Bundle?) {
        var pr = Bundle()
        if (params != null) {
            pr = params
        }

        firebaseAnalytics.logEvent("payment_view", pr)
        firebaseAnalytics.setUserProperty("user_start_checkout", "true")

    }
}