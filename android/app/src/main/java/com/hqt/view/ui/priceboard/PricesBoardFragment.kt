package com.hqt.view.ui.priceboard

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.CountDownTimer
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.widget.Toolbar
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import com.android.volley.VolleyError
import com.google.gson.reflect.TypeToken
import com.hqt.data.model.Airport
import com.hqt.data.model.PriceBoardTable
import com.hqt.datvemaybay.AirportSearch
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.FragmentPriceBoardBinding
import com.hqt.util.AppConfigs
import com.hqt.util.SSLSendRequest
import com.hqt.util.SharedPrefs
import com.hqt.util.tableview.TableViewAdapter
import com.hqt.view.ui.BaseActivity
import com.hqt.view.ui.HomeActivity
import org.json.JSONArray
import org.json.JSONException
import org.json.JSONObject
import java.util.*

class PricesBoardFragment : Fragment() {
    lateinit var binding: FragmentPriceBoardBinding
    lateinit var toolbar: Toolbar

    var mContext: Context? = null
    var originAirport: Airport? = null
    var tableViewAdapter = TableViewAdapter()
    private val priceBoardTable = PriceBoardTable()
    var timer: CountDownTimer? = null
    var listRoute = JSONArray()
    val REQUEST_AIRPORT = 2
    var isInternetConnected = false
    var onFetch = true


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        try {
            binding = DataBindingUtil.inflate(inflater, R.layout.fragment_price_board, container, false)
            toolbar = binding.toolbar

            if (activity?.javaClass?.name == HomeActivity::class.java.name) {

                var iActivity = activity as BaseActivity
                iActivity.setSupportActionBar(toolbar)
                iActivity.supportActionBar!!.setDisplayShowHomeEnabled(true)
                toolbar.setNavigationIcon(R.drawable.ic_action_back_home)
                toolbar.setNavigationOnClickListener {
                    (getActivity() as HomeActivity?)!!.clickNavigation(0)
                }

                isInternetConnected = iActivity.isInternetConnected

            } else if (activity?.javaClass?.name == PriceBoardActivity::class.java.name) {

                var iActivity = activity as PriceBoardActivity

                iActivity.setSupportActionBar(toolbar)
                iActivity.supportActionBar!!.setDisplayShowHomeEnabled(true)
                toolbar.setNavigationIcon(R.drawable.ic_action_back_home)
                toolbar.setNavigationOnClickListener { iActivity.onBackPressed() }
                isInternetConnected = iActivity.isInternetConnected
            }

            mContext = activity?.applicationContext


            toolbar.inflateMenu(R.menu.main)
            toolbar.title = "Săn vé"

            tableViewAdapter = TableViewAdapter()
            binding.tableview.setAdapter(tableViewAdapter)


            Handler(Looper.getMainLooper()).postDelayed({
                binding.shimmerViewContainer.startShimmer()
                getListRoute()

                Handler(Looper.getMainLooper()).postDelayed({
                    getListTask(true, true)
                }, 500)

            }, 200)

            initBinddingClick()

            return binding.root
        } catch (e: Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
            return null
        }
    }

    private fun initBinddingClick() {
        binding.txtOrigin.setOnClickListener {

            // TODO Auto-generated method stub
            val i = Intent(mContext, AirportSearch::class.java)
            i.putExtra("PRICEBOARD_ORIGIN", true)
            activity?.startActivityForResult(i, REQUEST_AIRPORT)
        }
        binding.txtDestination.setOnClickListener {

            // TODO Auto-generated method stub
            val i = Intent(mContext, AirportSearch::class.java)
            i.putExtra("PRICEBOARD_DESTINATION", true)
            if (originAirport != null) i.putExtra("dep", originAirport?.code)
            activity?.startActivityForResult(i, REQUEST_AIRPORT)
        }

    }

    private fun getListRoute(): Boolean {
        originAirport = SharedPrefs.getInstance().get("PRICEBOARD_ORIGIN", Airport::class.java)
        if (originAirport != null) {
            binding.txtOrigin.text = originAirport?.city
        } else {
            binding.txtOrigin.text = "Chọn nơi đi"
        }

        val listType = object : TypeToken<ArrayList<Airport>>() {}.type
        val destinations = SharedPrefs.getInstance().get("PRICEBOARD_DESTINATION", listType, Airport::class.java)
        var listDestinationText = ""

        if (destinations != null) {
            listRoute = JSONArray()
            var i = 0
            destinations.map { item ->

                if (i > 0) {
                    listDestinationText = listDestinationText + " - " + item.city
                } else {
                    listDestinationText = listDestinationText + item.city + " "
                }
                i++
                if (originAirport != null) {
                    listRoute.put(item.code + originAirport?.code)
                    listRoute.put(originAirport?.code + item.code)

                }
            }
            binding.txtDestination.text = listDestinationText
            return true
        } else {
            binding.txtDestination.text = "Chọn nơi đến"
        }
        return false
    }

    fun setRoute(all: Boolean) {
        if (all) {
            listRoute = JSONArray()
            getListTask(true, true)
            binding.txtOrigin.text = "Chọn nơi đi"
            binding.txtDestination.text = "Chọn nơi đến"
        } else {
            if (getListRoute()) {
                getListTask(true, true)
            }

        }


    }

    fun setDefaultRoute(routes: JSONArray) {
        try {
            listRoute = routes
            AppConfigs.Log("listRoute", listRoute.toString())
            getListTask(true, true)
        } catch (e: java.lang.Exception) {

        }
    }

    private fun getListTask(new: Boolean, force: Boolean) {
        if (!isInternetConnected) {
            Toast.makeText(mContext, "Không có kết nối internet !", Toast.LENGTH_SHORT).show()
        } else {
            if ((onFetch && Common.isAppRunning) || force) {
                if (new) {
                    binding.shimmerViewContainer.visibility = View.VISIBLE
                    binding.shimmerViewContainer.startShimmer()
                    binding.notfound.visibility = View.GONE
                }

                val now = Calendar.getInstance()
                val startDate = Common.dateToString(now.time, "yyyyMMdd")
                now.add(Calendar.MONTH, 7)
                val endDate = Common.dateToString(now.time, "yyyyMM28")
                val request = JSONObject()

                try {
                    request.put("type", "month")
                    request.put("startDate", startDate)
                    request.put("endDate", endDate)
                    request.put("token", Common.getKeyHash())
                    request.put("routes", listRoute)

                } catch (e: JSONException) {
                    AppConfigs.logException(e)
                }
                var isFull = "/Full"
                if (listRoute.length() > 0) {
                    isFull = ""
                }
                SSLSendRequest(mContext).POST(false,
                    "AirLines/PricesBoard$isFull",
                    request,
                    object : SSLSendRequest.CallBackInterface {
                        override fun onSuccess(response: JSONObject, cached: Boolean) {

                            if (!response.isNull("data") && response.getJSONObject("data").length() > 0) {
                                Handler(Looper.getMainLooper()).postDelayed({
                                    processListData(response.getJSONObject("data").getJSONObject("full"), new)
                                }, 800)

                            } else {
                                if (new) {
                                    binding.shimmerViewContainer.visibility = View.GONE
                                    binding.notfound.visibility = View.VISIBLE

                                }
                            }
                        }

                        override fun onFail(er: VolleyError) {

                        }
                    })
            }
        }
    }

    fun processListData(json: JSONObject, isNew: Boolean) {

        var rowHeaders = priceBoardTable.getRowHeaderList(json)
        var colHeaders = priceBoardTable.getColumnHeaderList()
        tableViewAdapter.setAllItems(colHeaders, rowHeaders, priceBoardTable.getCellList(colHeaders.size, json))

        if (isNew) {
            binding.shimmerViewContainer.stopShimmer()
            binding.shimmerViewContainer.visibility = View.GONE
            binding.notfound.visibility = View.GONE
        }

        autoReloadTimer()
    }

    private fun autoReloadTimer(): Boolean {
        if (timer == null) {

            var endDate = Calendar.getInstance()
            endDate.add(Calendar.DATE, 1)
            var live = 30
            timer = object : CountDownTimer(endDate.timeInMillis, 1000) {
                override fun onTick(millisUntilFinished: Long) {
                    if (isVisible) {
                        tableViewAdapter.setLiveTitleText("Live $live")
                        if (live == 0) {
                            live = 30
                            getListTask(false, false)
                        }
                        live--
                    }
                }

                override fun onFinish() {

                }
            }.start()
            return true
        } else {
            return false
        }
    }

    fun killTimer() {
        if (timer != null) {
            timer?.cancel()
            timer = null
        }
    }

    @Override override fun onPause() {
        onFetch = false
        super.onPause()
    }

    override fun onResume() {
        onFetch = true
        Common.isAppRunning = true
        super.onResume()
    }
}