package com.hqt.view.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.hqt.datvemaybay.databinding.ListTrainItemBinding
import com.hqt.data.model.Train
import androidx.databinding.DataBindingUtil
import androidx.databinding.ObservableField
import com.hqt.data.model.TrainCoach
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ListTrainCoachItemBinding
import com.hqt.view.ui.train.TrainSelectHanlder
import io.reactivex.subjects.PublishSubject


class TrainCoachAdapter(internal var contents: List<TrainCoach>) :
    RecyclerView.Adapter<TrainCoachAdapter.ViewHolder>() {
    val itemClickStream: PublishSubject<TrainCoach> = PublishSubject.create()

    class ViewHolder(val itemClickStream: PublishSubject<TrainCoach>, val binding: ListTrainCoachItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(coach: TrainCoach) {

            binding.coach = coach
            if (binding.viewHolder == null) {
                binding.viewHolder = this
            }

            binding.coach = coach
            if (binding.handler == null) {

            } //  divisionName.set(train.originName)

            binding.root.setOnClickListener {
                itemClickStream.onNext(coach)
            }
            binding.executePendingBindings()
        }

    }

    override fun getItemCount(): Int {
        return contents.size
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {

        val layoutInflater = LayoutInflater.from(parent.context)
        val binding: ListTrainCoachItemBinding = DataBindingUtil.inflate(layoutInflater,
            R.layout.list_train_coach_item,
            parent,
            false)

        return ViewHolder(itemClickStream, binding)
    }


    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(contents[position])
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getItemViewType(position: Int): Int {
        return position
    }


}