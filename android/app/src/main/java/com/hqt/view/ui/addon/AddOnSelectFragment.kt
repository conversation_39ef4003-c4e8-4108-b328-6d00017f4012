package com.hqt.view.ui.addon

import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.fragment.app.Fragment
import com.google.android.flexbox.FlexboxLayout
import com.google.android.material.behavior.HideBottomViewOnScrollBehavior
import com.hqt.data.model.AddOnInfo
import com.hqt.data.model.request.GetAddOnRequest
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.FragmentSelectAddonLayoutBinding
import com.hqt.datvemaybay.databinding.ListMealItemBinding
import com.hqt.datvemaybay.databinding.SeatViewTitleItemBinding
import com.hqt.util.AppConfigs
import com.hqt.util.AppController
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers

class AddOnSelectFragment : Fragment() {
    private var _binding: FragmentSelectAddonLayoutBinding? = null
    private val binding get() = _binding!! //    lateinit var emptyState: LinearLayout

    private var disposable: Disposable? = null
    private val addOnList: ArrayList<AddOnInfo> = ArrayList()
    private var selectedAddOnList: ArrayList<AddOnInfo> = ArrayList()
    var totalPax = 1
    var isReturnTrip = false


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        _binding = FragmentSelectAddonLayoutBinding.inflate(inflater, container, false)

        getViewBinding().bottomSheet.findViewById<Button>(R.id.btnNext).setOnClickListener {

            (activity as SelectAddOnActivity).updateSeatSelected(selectedAddOnList, isReturnTrip)
            (activity as SelectAddOnActivity).doneClick()
        }
        getViewBinding().bottomSheet.findViewById<Button>(R.id.btnBack).setOnClickListener {
            (activity as SelectAddOnActivity).backClick()
        }
        getViewBinding().btnBack.setOnClickListener {
            (activity as SelectAddOnActivity).backClick()
        }

        selectedAddOnList = (activity as SelectAddOnActivity).getCurrentAddOn(isReturnTrip)
        updateAddOnSelected()

        return binding.root
    }

    private fun getViewBinding(): FragmentSelectAddonLayoutBinding {
        return _binding!!
    }

    private fun updateAddOnSelected() {
        try {

            var totalPrice = 0
            var seatSummaryText = ""
            val fillerList = selectedAddOnList.filter {
                it.price > 0

            }.toList()

            totalPrice = selectedAddOnList.sumOf { addOnInfo -> addOnInfo.price }

            val group = fillerList.distinct()

            group.forEach { addOn ->

                seatSummaryText += addOn.text + " x " + selectedAddOnList.filter { t -> t.value == addOn.value }
                    .count() + "\n"
            }
            seatSummaryText = seatSummaryText.removeSuffix("\n")

            seatSummaryText = "" + selectedAddOnList.size + " Món"


            if (totalPrice > 0) {
                getViewBinding().bottomSheet.findViewById<TextView>(R.id.txtGrandTotalPrice).text = Common.dinhDangTien(
                    totalPrice)
                getViewBinding().bottomSheet.findViewById<TextView>(R.id.txt_seat_select).text = seatSummaryText
            } else {
                getViewBinding().bottomSheet.findViewById<TextView>(R.id.txtGrandTotalPrice).text = Common.dinhDangTien(
                    0)
                getViewBinding().bottomSheet.findViewById<TextView>(R.id.txt_seat_select).text = "Vui lòng chọn"


            }


            val params = getViewBinding().bottomLayout.layoutParams as CoordinatorLayout.LayoutParams
            (params.behavior as HideBottomViewOnScrollBehavior).slideUp(getViewBinding().bottomLayout)

            (activity as SelectAddOnActivity).updateSeatSelected(ArrayList(fillerList), isReturnTrip)

        } catch (e: Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }

    }

    private fun addToCart(addOn: AddOnInfo): Int {

        val totalSelect = selectedAddOnList.count { it -> it.value == addOn.value }
        if (totalSelect < totalPax * 4) {
            selectedAddOnList.add(addOn)
            updateAddOnSelected()
        }

        return selectedAddOnList.count { it -> it.value == addOn.value }
    }

    private fun removeToCart(addOn: AddOnInfo): Int {
        val rm = selectedAddOnList.find { it -> it.value == addOn.value }
        for (i in 0 until selectedAddOnList.size) {
            if (selectedAddOnList[i].value == addOn.value) {
                selectedAddOnList.removeAt(i)
                break;
            }
        }
        updateAddOnSelected()
        return selectedAddOnList.count { it -> it.value == addOn.value }
    }

    fun initAddOnList(flightKey: String) {
        try {
            val addOnRequest = GetAddOnRequest()

            addOnRequest.flightKey = flightKey
            if (isReturnTrip) Thread.sleep(500)
            disposable = AppController.instance.getService().postAddOn("VJ", addOnRequest).subscribeOn(Schedulers.io())
                .doOnSubscribe {

                }.observeOn(AndroidSchedulers.mainThread()).subscribe({ response ->
                    try {
                        if (response.status != null && response.status) {
                            addOnList.clear()
                            addOnList.addAll(response.data!!)

                            response.data.forEach { addOn ->

                                val addOnItem = ListMealItemBinding.inflate(LayoutInflater.from(requireContext()),
                                    null,
                                    false)
                                addOnItem.meal = addOn
                                addOnItem.total = selectedAddOnList.count { it.value == addOn.value }
                                addOnItem.btnInAddOn.setOnClickListener {
                                    addOnItem.total = addToCart(addOn)
                                }

                                addOnItem.btnDeAddOn.setOnClickListener {
                                    addOnItem.total = removeToCart(addOn)
                                }

                                getViewBinding().container.addView(addOnItem.root)
                            }


                            getViewBinding().shimmerViewContainer.stopShimmer()
                            getViewBinding().shimmerViewContainer.visibility = View.GONE

                            getViewBinding().emptyState.visibility = View.GONE
                            getViewBinding().bottomSheet.visibility = View.VISIBLE
                        } else {
                            getViewBinding().emptyState.visibility = View.VISIBLE
                            getViewBinding().shimmerViewContainer.stopShimmer()
                            getViewBinding().shimmerViewContainer.visibility = View.GONE
                        }
                    } catch (e: Exception) {
                        AppConfigs.logException(e)
                       
                    }

                }, { throwable ->

                    getViewBinding().emptyState.visibility = View.VISIBLE
                    getViewBinding().shimmerViewContainer.stopShimmer()
                    getViewBinding().shimmerViewContainer.visibility = View.GONE

                    throwable.printStackTrace()
                })
        } catch (e: Exception) {
            AppConfigs.logException(e)
            activity?.finish()
        }
    }

    companion object {
        fun newInstance(flightK: String, isReturn: Boolean, totalPax: Int): AddOnSelectFragment {
            val x = AddOnSelectFragment()
            x.isReturnTrip = isReturn

            Handler(Looper.getMainLooper()).postDelayed({
                x.initAddOnList(flightK)
            }, 1000)

            x.totalPax = totalPax

            return x
        }
    }
}