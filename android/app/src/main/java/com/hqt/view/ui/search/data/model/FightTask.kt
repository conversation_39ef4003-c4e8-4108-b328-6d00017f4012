package com.hqt.view.ui.search.data.model

import com.google.gson.annotations.SerializedName

data class FightTask(

    @SerializedName("apiKey")
    var apiKey : String? = null,

    @SerializedName("lastUpdateTime")
    var lastUpdateTime : String? = null,

    @SerializedName("cached")
    var cached : String? = null,

    @SerializedName("departure")
    var departure : ArrayList<FlightV2> = arrayListOf(),
    @SerializedName("return")
    var returnList : ArrayList<FlightV2> = arrayListOf(),
)