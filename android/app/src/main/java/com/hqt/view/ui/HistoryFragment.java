package com.hqt.view.ui;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.fragment.app.Fragment;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.SimpleItemAnimator;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.android.volley.VolleyError;
import com.facebook.shimmer.ShimmerFrameLayout;
import com.google.firebase.auth.FirebaseUser;
import com.google.gson.reflect.TypeToken;
import com.hqt.data.model.BookingJson;
import com.hqt.datvemaybay.Common;
import com.hqt.datvemaybay.R;
import com.hqt.util.AppConfigs;
import com.hqt.util.AppController;
import com.hqt.util.SSLSendRequest;
import com.hqt.view.adapter.BookingViewAdapter;
import com.hqt.view.ui.search.ui.activity.SearchActivityV2;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class HistoryFragment extends Fragment {
    Toolbar toolbar;
    HomeActivity activity;
    RecyclerView mRecyclerView;
    SwipeRefreshLayout swipeRefreshLayout;
    Boolean onRefresh = false, loadMore = false, onLoadMore = false;
    BookingViewAdapter mAdapter;
    LinearLayout notfound;
    SSLSendRequest APISERVICE;
    private ShimmerFrameLayout mShimmerViewContainer;
    private List<BookingJson> listBookings = new ArrayList<>();
    Button btnNewBook, btnLogin;
    Integer page = 1;
    FirebaseUser authUser;
    int waitingPayment = 0;

    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View rootView = inflater.inflate(R.layout.fragment_history, null);
        activity = (HomeActivity) getActivity();
        toolbar = rootView.findViewById(R.id.toolbar);
        toolbar.inflateMenu(R.menu.main);
        toolbar.setTitle("Đặt chỗ của tôi");
        activity.setSupportActionBar(toolbar);
        Objects.requireNonNull(activity.getSupportActionBar()).setDisplayShowHomeEnabled(true);

        mShimmerViewContainer = rootView.findViewById(R.id.shimmer_view_container);
        mShimmerViewContainer.startShimmer();
        mShimmerViewContainer.setVisibility(View.VISIBLE);


        waitingPayment = 0;

        btnNewBook = rootView.findViewById(R.id.btnNewBook);
        btnLogin = rootView.findViewById(R.id.btnLogin);
        APISERVICE = new SSLSendRequest(activity);

        mRecyclerView = rootView.findViewById(R.id.recyclerView);
        mRecyclerView.setLayoutManager(new LinearLayoutManager(activity));
        mRecyclerView.setHasFixedSize(true);
        notfound = rootView.findViewById(R.id.notfound);
        activity.setSupportActionBar(toolbar);
        toolbar.setNavigationIcon(R.drawable.ic_action_back_home);
        toolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                ((HomeActivity) getActivity()).clickNavigation(0);
            }
        });

        swipeRefreshLayout = rootView.findViewById(R.id.swipe_refresh_layout);
        swipeRefreshLayout.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                // Xử lý khi vuốt để làm mới
                if (!onLoadMore) {
                    onRefresh = true;
                    new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            page = 1;
                            getHistoryBooking();
                        }
                    }, 1000);
                }
            }
        });


//                onLoadMore = true;
//                if (!onRefresh) {
//                    new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
//                        @Override
//                        public void run() {
//                            if (activity.isInternetConnected()) {
//                                getHistoryBooking();
//                            } else {
//                                swipeRefreshLayout.finishLoadmore();
//                            }
//
//                        }
//                    }, 1000);
//                }


        String Contactmail = AppConfigs.getStringLocalCache(activity, "email");
        Common.UEmail = AppConfigs.getStringLocalCache(activity, "Uemail");

        mAdapter = new BookingViewAdapter(activity, listBookings);
        mAdapter.setHasStableIds(true);

        RecyclerView.ItemAnimator animator = mRecyclerView.getItemAnimator();
        if (animator instanceof SimpleItemAnimator) {
            ((SimpleItemAnimator) animator).setSupportsChangeAnimations(false);
        }

        mRecyclerView.setAdapter(mAdapter);
        mAdapter.notifyDataSetChanged();


        if (((HomeActivity) getActivity()).isUserSigned()) {

            authUser = ((HomeActivity) getActivity()).getFirebaseUser();
            new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                @Override
                public void run() {
                    page = 1;
                    waitingPayment = 0;
                    listBookings.clear();
                    getHistoryBooking();
                }
            }, 2000);

        } else {
            notfound.setVisibility(View.VISIBLE);
            swipeRefreshLayout.setEnabled(false);
            mShimmerViewContainer.setVisibility(View.GONE);
            mRecyclerView.setVisibility(View.GONE);

        }

        LocalBroadcastManager.getInstance(requireContext()).registerReceiver(mMessageReceiver, new IntentFilter("bookingInsert"));
        addButtonListener();
        return rootView;
    }

    public void addButtonListener() {
        btnNewBook.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (((HomeActivity) getActivity()).isInternetConnected()) {
//                    Intent in = new Intent(getContext(), SearchActivity.class);
                    Intent in = new Intent(getContext(), SearchActivityV2.class);
                    startActivity(in);
                }
            }
        });
        btnLogin.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (((HomeActivity) getActivity()).isInternetConnected()) {
                    ((HomeActivity) getActivity()).signIn();
                }
            }
        });
    }

    public void getHistoryBooking() {
        JSONObject postParam = new JSONObject();
        if (authUser != null) {
            try {
                postParam.put("email", authUser.getEmail());
                postParam.put("uid", authUser.getUid());
                postParam.put("status", "all");
                postParam.put("page", page);
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }

        APISERVICE.POST(true, "AirLines/Bookings/History", postParam, new SSLSendRequest.CallBackInterface() {
            @Override
            public void onSuccess(JSONObject response, boolean cached) {
                mRecyclerView.setVisibility(View.VISIBLE);
                ProcessData(response);
            }

            @Override
            public void onFail(VolleyError error) {
                notfound.setVisibility(View.VISIBLE);
                swipeRefreshLayout.setEnabled(false);
                mShimmerViewContainer.stopShimmer();
                mShimmerViewContainer.setVisibility(View.GONE);
                mRecyclerView.setVisibility(View.GONE);
            }
        });

    }

    public void ProcessData(JSONObject response) {
        try {
            if (!response.isNull("data")) {

                int lastPosition = listBookings.size() - 1;
                int currentPage = response.getJSONObject("meta").getJSONObject("pagination").getInt("current_page");
                int totalPage = response.getJSONObject("meta").getJSONObject("pagination").getInt("total_pages");

                if (currentPage < totalPage) {
                    loadMore = true;
                    page = currentPage + 1;
                    if (onLoadMore) {
                        // swipeRefreshLayout.finishLoadmore();
                        onLoadMore = false;
                    }
                    // swipeRefreshLayout.setEnableLoadmore(true);
                } else {
                    //swipeRefreshLayout.setEnableLoadmore(false);
                }

                Type listType = new TypeToken<ArrayList<BookingJson>>() {
                }.getType();

                JSONArray rawList = response.getJSONArray("data");
                List<BookingJson> listBks = AppController.getInstance().getGSon().fromJson(rawList.toString(), listType);

                for (int i = 0; i < rawList.length(); i++) {
                    if (listBks.get(i).getStatus() != null && listBks.get(i).getStatus().equalsIgnoreCase("waiting_payment")) {
                        waitingPayment++;
                    }

                }

                if (onRefresh && listBookings.size() > 0) {
                    onRefresh = false;
                    listBookings.clear();
                    listBookings.addAll(listBks);
                    mAdapter.notifyDataSetChanged();
                    activity.shakeItBaby();

                } else if (onLoadMore) {
                    onLoadMore = false;
                    listBookings.addAll(listBks);
                    mAdapter.notifyItemInserted(lastPosition);
                    activity.shakeItBaby();
                } else {
                    listBookings.addAll(listBks);
                    mAdapter.notifyDataSetChanged();
                }


                if (listBookings.isEmpty()) {
                    notfound.setVisibility(View.VISIBLE);
                } else {
                    notfound.setVisibility(View.GONE);
                }
                mShimmerViewContainer.stopShimmer();
                mShimmerViewContainer.setVisibility(View.GONE);

            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private BroadcastReceiver mMessageReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    page = 1;
                    waitingPayment = 0;
                    listBookings.clear();
                    getHistoryBooking();
                }
            }, 3000);
        }
    };

}
