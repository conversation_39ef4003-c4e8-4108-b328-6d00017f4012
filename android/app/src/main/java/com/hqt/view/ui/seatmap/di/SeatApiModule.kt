package  com.hqt.view.ui.seatmap.di

import com.hqt.view.ui.seatmap.data.api.SeatService
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
class SeatApiModule {

    @Provides
    @Singleton
    fun provideSeatService(retrofit: Retrofit): SeatService {
        return retrofit.create(SeatService::class.java)
    }



}