package com.hqt.view.ui.meal.ui.activity


import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.Menu
import android.view.MenuItem
import android.view.View
import androidx.activity.viewModels
import androidx.fragment.app.Fragment
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.gson.Gson
import com.hqt.base.BaseActivity
import com.hqt.base.PagerAdapter
import com.hqt.data.model.BookingV2
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ActivitySelectAddMealLayoutBinding
import com.hqt.util.AppConfigs
import com.hqt.util.Log
import com.hqt.view.ui.booking.data.model.BaseBooking
import com.hqt.view.ui.booking.data.model.BookingV3
import com.hqt.view.ui.meal.ui.MealManagerViewModel
import com.hqt.view.ui.meal.ui.fragment.AddMealSelectFragment
import com.hqt.view.ui.reward.ui.activity.RewardActivityV2
import dagger.hilt.android.AndroidEntryPoint
import q.rorbin.badgeview.QBadgeView


@AndroidEntryPoint
class SelectAddMealActivity : BaseActivity<ActivitySelectAddMealLayoutBinding>() {

    private val viewModel : MealManagerViewModel by viewModels()


    var paxSeatString = ""
    var paxSeatReturnString = ""


    override fun getLayoutRes(): Int {
        return R.layout.activity_select_add_meal_layout
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        getToolbar()?.title = "Chọn suất ăn nóng"


        viewModel.booking = intent.getSerializableExtra("bookingDetail") as BookingV3

        if (viewModel.booking.type == BaseBooking.BookingType.FLIGHT) {
            viewModel.totalPax = viewModel.booking.adult + viewModel.booking.child
        }
        binding.lifecycleOwner = this

        observe()
        initTab()
    }

    private fun observe(){
        viewModel.doneClick.observe(this){
            if (it){
                doneClick()
            }
        }
    }

    private fun initTab(){

        var fragments: List<Fragment> = listOf(
            AddMealSelectFragment.newInstance(viewModel.booking.departure_f?.flightKey ?: "", false, viewModel.booking.departure_f?.provider ?: "")
        )
        if (viewModel.booking.is_round_trip){
            fragments = listOf(
                AddMealSelectFragment.newInstance(viewModel.booking.departure_f?.flightKey ?: "", false, viewModel.booking.departure_f?.provider ?: ""),
                AddMealSelectFragment.newInstance(viewModel.booking.return_f?.flightKey ?: "", true, viewModel.booking.return_f?.provider ?: "")
            )
        }
        val adapter = PagerAdapter(this, fragments)
        binding.viewPager.adapter = adapter
        binding.viewPager.offscreenPageLimit = 2
        binding.viewPager.setCurrentItem(0, false)
        binding.viewPager.isUserInputEnabled = false
        TabLayoutMediator(
            binding.tabLayout, binding.viewPager
        ) { tab, position ->
            when (position) {
                0 -> tab.text = "Lượt đi"
                1 -> tab.text = "Lượt về"

            }
        }.attach()
        binding.tabLayout.tabMode = TabLayout.MODE_FIXED




    }


    fun getPaxSeatStr(isReturn: Boolean): String {

        AppConfigs.Log("getPaxSeatStr", paxSeatString.toString())
        if (isReturn) return paxSeatReturnString
        return paxSeatString

    }




    private fun backToBooking() {
        val data = Intent()
        data.putExtra("bookingDetail", Gson().toJson(viewModel.booking.pax_info))
        setResult(RESULT_OK, data)
        finish()
    }


    private fun initAnalytics(booking: BookingV2, event: String) {
        try {

            val params = Bundle()
            params.putString(FirebaseAnalytics.Param.ORIGIN, booking.departure_f?.originCode)
            params.putString(FirebaseAnalytics.Param.DESTINATION, booking.departure_f?.destinationCode)
            params.putString(FirebaseAnalytics.Param.START_DATE, booking.departure_f?.departureDateTime.toString())
            params.putString(FirebaseAnalytics.Param.END_DATE, booking.departure_f?.arriverDateTime.toString())
            params.putString(FirebaseAnalytics.Param.FLIGHT_NUMBER, booking.departure_f?.flightNumber)
            params.putString(FirebaseAnalytics.Param.ITEM_NAME,
                booking.departure_f?.originCode + booking.departure_f?.destinationCode + booking.departure_f?.flightNumber)
            params.putString(FirebaseAnalytics.Param.NUMBER_OF_PASSENGERS,
                ((booking.departure_f?.adult ?: 0) + (booking.departure_f?.child ?: 0) + (booking.departure_f?.infant ?: 0)).toString() + "")
            FirebaseAnalytics.getInstance(this).logEvent(event, params)
            FirebaseAnalytics.getInstance(this).setCurrentScreen(this, "booking_input", null)

        } catch (e: Exception) {
            Log.logException(e)
        }
    }

    override fun onResume() {
        super.onResume()
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean { // Inflate the menu; this adds items to the action bar if it is present.
        menuInflater.inflate(R.menu.main_reward, menu)
        try {
            Handler(Looper.getMainLooper()).postDelayed({
                val rewardButton = findViewById(R.id.action_reward) as View
                QBadgeView(applicationContext).setGravityOffset(0f, 0f, true).bindTarget(rewardButton).setBadgeText("!")
                    .setOnDragStateChangedListener { dragState, badge, targetView ->
                    }
            }, 1000)
        } catch (e: java.lang.Exception) {
            Log.logException(e)
        }
        return true
    }

    fun doneClick(): Boolean {

        //        if (booking.is_round_trip && seatViewListReturn.size == 0 && booking.return_f?.provider == "VJ") {
        //            Toast.makeText(this, "Vui lòng chọn ghế lượt về", Toast.LENGTH_SHORT).show()
        //            binding.viewPager.currentItem = 1
        //            return false
        //        }
        //        if (!booking.is_round_trip && seatViewList.size == 0 && booking.departure_f?.provider == "VJ") {
        //            Toast.makeText(this, "Vui lòng chọn ghế lượt đi", Toast.LENGTH_SHORT).show()
        //            binding.viewPager.currentItem = 0
        //            return false
        //        }

        backToBooking()
        return true
    }

    fun backClick() {
        finish()
    }



    override fun onOptionsItemSelected(item: MenuItem): Boolean { // Take appropriate action for each action item click
        when (item.itemId) {
            android.R.id.home -> {

                finish()
            }
            R.id.action_reward -> {
//                val intent = Intent(this, RewardActivity::class.java)
                val intent = Intent(this, RewardActivityV2::class.java)

                startActivity(intent)
                return true
            }
            else -> {
                finish()
            }
        }
        return false
    }

//    override fun onSystemSettingChange(type: AppConfigs.SystemSettingType) {
//
//        if (type == AppConfigs.SystemSettingType.INTERNET) {
//            if (this::viewModel.isInitialized) {
//                viewModel.updateInternetStatus(isInternetConnected)
//            }
//        }
//
//
//    }


}


