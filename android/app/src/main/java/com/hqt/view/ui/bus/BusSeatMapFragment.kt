package com.hqt.view.ui.bus


import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.fragment.app.Fragment
import com.google.android.flexbox.FlexboxLayout
import com.google.android.material.behavior.HideBottomViewOnScrollBehavior
import com.hqt.data.model.request.BusSearchRequest
import com.hqt.data.model.response.BusSeatMapInfo
import com.hqt.data.model.response.SeatGroups
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.FragmentBusSeatMapListBinding
import com.hqt.datvemaybay.databinding.SeatGroupTitleViewBinding
import com.hqt.util.AppConfigs
import com.hqt.util.AppController
import com.hqt.view.ui.seatmap.SeatIdentifierTitle
import com.hqt.view.ui.seatmap.SeatOption
import com.hqt.view.ui.seatmap.SeatView
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers

class BusSeatMapFragment : Fragment() {
    private var _binding: FragmentBusSeatMapListBinding? = null
    private val binding get() = _binding!! //    lateinit var emptyState: LinearLayout

    private var disposable: Disposable? = null
    private var arraylistSeatGroup: ArrayList<SeatGroups> = ArrayList()
    private var seatViewList: ArrayList<SeatView> = ArrayList()
    private var routeInfo: BusRoute? = null
    private var seatMapInfo: BusSeatMapInfo? = null


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        _binding = FragmentBusSeatMapListBinding.inflate(inflater, container, false)

        getViewBindding().bottomSheet.findViewById<Button>(R.id.btnNext).setOnClickListener {
            if (seatViewList.filter { st -> st.isSelect }.count() > 0) {

                seatMapInfo!!.dropOffPoints.forEach { st -> st.selected = false }
                seatMapInfo!!.pickupPoints.forEach { st -> st.selected = false }

                (activity as BusSelectActivity).showStationPointSelect(seatViewList, seatMapInfo!!)
            } else {
                Toast.makeText(requireContext(), "Vui lòng chọn chỗ ngồi", Toast.LENGTH_SHORT).show()
            }
        }
        getViewBindding().bottomSheet.findViewById<Button>(R.id.btnBack).setOnClickListener {
            (activity as BusSelectActivity).backClick()

        }
        getViewBindding().btnBack.setOnClickListener {
            (activity as BusSelectActivity).backClick()
        }



        return binding.root
    }

    private fun getViewBindding(): FragmentBusSeatMapListBinding {
        return _binding!!
    }

    private fun updateSeatSelected() {
        try {
            var totalPrice = 0
            var seatSummaryText = ""
            val fillerList = seatViewList.filter { st -> st.isSelect }

            fillerList.forEach { seat ->
                totalPrice += seat.seatOption!!.seatCharges
                seatSummaryText += seat.seatOption!!.text + ", "
            }
            seatSummaryText = seatSummaryText.removeSuffix(", ")


            if (totalPrice > 0) {
                getViewBindding().bottomSheet.findViewById<TextView>(R.id.txtGrandTotalPrice).text = Common.dinhDangTien(
                    totalPrice)
                getViewBindding().bottomSheet.findViewById<TextView>(R.id.txt_seat_select).text = seatSummaryText
            } else {
                getViewBindding().bottomSheet.findViewById<TextView>(R.id.txtGrandTotalPrice).text = Common.dinhDangTien(
                    0)
                getViewBindding().bottomSheet.findViewById<TextView>(R.id.txt_seat_select).text = "Vui lòng chọn"

                getViewBindding().currentSeatSelecte.text = ""
                getViewBindding().seatPrice.text = ""
            }

            val params = getViewBindding().bottomLayout.layoutParams as CoordinatorLayout.LayoutParams
            (params.behavior as HideBottomViewOnScrollBehavior).slideUp(getViewBindding().bottomLayout) //(activity as BusSelectActivity).updateSeatSelected(ArrayList(fillerList), isReturnTrip)

        } catch (e: Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }

    }

    fun getAllowSelect(): Boolean {
        val totalSelected = seatViewList.count { seatView -> seatView.isSelect }
        if (totalSelected < seatMapInfo!!.maxTotalSeats!!) {

            return true
        }

        return false
    }

    fun clearSelect(seat: SeatView) {

        if (seat.seatOption != null) {
            getViewBindding().currentSeatSelecte.text = seat.seatOption?.text
            getViewBindding().seatPrice.text = Common.dinhDangTien(seat.seatOption!!.seatCharges)
            getViewBindding().currentCoachText.text = seat.seatOption?.rowIdentifier
            getViewBindding().currentSeatSelectText.text = seat.seatOption?.seatIdentifier!!
        }

        seatViewList.forEach { it ->
            if (it.seatId != "") {
                if (it.seatId != seat.seatId) { //clear select
                    if (seatMapInfo!!.maxTotalSeats == 1) {
                        it.selectSeat(false)
                    }
                } else {
                    if (it.isSelect) {
                        seat.binding.seatGroupView.setCardBackgroundColor(Color.parseColor("#" + seat.seatOption?.color))
                        seat.isSelect = false

                    } else {
                        if (getAllowSelect()) seat.isSelect = true
                    }

                }
            }
        }
        updateSeatSelected()
    }

    fun getSeatMap(busRoute: BusRoute) {
        routeInfo = busRoute //

        getViewBindding().toolbar.setNavigationIcon(R.drawable.ic_action_back_home)
        getViewBindding().toolbar.title = routeInfo?.name
        getViewBindding().toolbar.subtitle = routeInfo?.company?.name + " - " + routeInfo?.schedules?.vehicleType
        getViewBindding().toolbar.setNavigationOnClickListener {
            (activity as BusSelectActivity).backClick()
        }

        initSeatMap(true)
    }

    fun genSeatMap(seatGroup: SeatGroups) {
        var startRow = 1
        val maxRow = seatGroup.numRows!!
        val maxCol = seatGroup.numCols!!

        val percent = (1 / (maxCol + 0f)) - 0.005f
        for (i in startRow until maxRow + 1) {
            if (maxCol > 0) {
                val seatRowIndex = startRow
                for (j in 1 until maxCol + 1) {
                    val seatView = SeatView(requireContext(),
                        SeatIdentifierTitle(),
                        seatRowIndex.toString(),
                        percent,
                        false,
                        true)

                    seatView.seatId = "" + seatGroup.coachNum + "-" + seatRowIndex + "-" + j
                    seatViewList.add(seatView)

                    seatGroup.seats.forEach { it ->
                        var seatKey = "" + seatGroup.coachNum + "-" + it.rowNum + "-" + it.colNum
                        if (seatKey == seatView.seatId) {

                            var seatOption = SeatOption()

                            seatOption.available = it.isAvailable!!
                            if (seatOption.available) {
                                seatOption.color = it.seatColor!!.substringAfter("#")
                            } else {
                                seatOption.color = "BAB9B9"
                            }
                            seatOption.text = it.seatCode
                            seatOption.selectionKey = it.seatValue!!
                            seatOption.seatCharges = it.fare!!
                            seatOption.seatIdentifier = it.seatGroup!!
                            seatOption.rowIdentifier = seatGroup.coachName!!
                            seatView.setSeatInfo(seatOption)

                        }
                    }

                    getViewBindding().flexBoxContainer.addView(seatView)
                }
                startRow++
            }
        }
    }


    fun initSeatMap(loadSeatMap: Boolean) {
        val seatRequest = BusSearchRequest()
        if (loadSeatMap) {
            disposable = AppController.instance.getService()
                .getBusSeatMap(routeInfo!!.schedules!!.tripCode!!, seatRequest).subscribeOn(Schedulers.io())
                .doOnSubscribe {

                    getViewBindding().shimmerViewContainer.startShimmer()
                    getViewBindding().shimmerViewContainer.visibility = View.VISIBLE

                    seatViewList.clear()
                    arraylistSeatGroup.clear()
                    getViewBindding().flexBoxContainer.removeAllViews()
                    updateSeatSelected()

                }.observeOn(AndroidSchedulers.mainThread()).subscribe({ response ->

                    if (response.data != null) {

                        arraylistSeatGroup.addAll(response.data.seatGroups)


                        seatMapInfo = response.data
                        getViewBindding().originCode.text = routeInfo!!.from!!.name
                        getViewBindding().destinationCode.text = routeInfo!!.to!!.name
                        getViewBindding().maxSeatSelect.text = response.data.maxTotalSeats.toString() + " Chỗ"


                        val scale = resources.displayMetrics.density
                        val dpAsPixels = (20 * scale + 0.5f).toInt()

                        response.data.seatGroups.forEach { sg ->
                            try {
                                val seatTitleView = SeatGroupTitleViewBinding.inflate(LayoutInflater.from(requireContext()),
                                    null,
                                    false)
                                seatTitleView.seatGroupTitle.text = sg.coachName
                                val lpPlexItem = FlexboxLayout.LayoutParams(FlexboxLayout.LayoutParams.MATCH_PARENT,
                                    FlexboxLayout.LayoutParams.MATCH_PARENT)
                                lpPlexItem.flexBasisPercent = 1f
                                seatTitleView.root.layoutParams = lpPlexItem

                                getViewBindding().flexBoxContainer.addView(seatTitleView.root)
                                genSeatMap(sg)

                            } catch (e: Exception) {
                                e.printStackTrace()
                                AppConfigs.logException(e)

                            }
                        }

                        getViewBindding().shimmerViewContainer.stopShimmer()
                        getViewBindding().shimmerViewContainer.visibility = View.GONE

                        getViewBindding().emptyState.visibility = View.GONE
                        getViewBindding().bottomSheet.visibility = View.VISIBLE
                    } else {
                        getViewBindding().emptyState.visibility = View.VISIBLE
                        getViewBindding().shimmerViewContainer.stopShimmer()
                        getViewBindding().shimmerViewContainer.visibility = View.GONE
                    }

                }, { throwable ->

                    getViewBindding().emptyState.visibility = View.VISIBLE
                    getViewBindding().shimmerViewContainer.stopShimmer()
                    getViewBindding().shimmerViewContainer.visibility = View.GONE

                    throwable.printStackTrace()
                })
        }
    }

    companion object {
        fun newInstance(): BusSeatMapFragment {
            val x = BusSeatMapFragment()
            Handler(Looper.getMainLooper()).postDelayed({
                x.initSeatMap(false)
            }, 1000)

            return x
        }
    }
}