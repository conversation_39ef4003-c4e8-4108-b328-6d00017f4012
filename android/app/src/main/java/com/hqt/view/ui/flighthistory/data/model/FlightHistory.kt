package com.hqt.view.ui.flighthistory.data.model

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Matrix
import com.caverock.androidsvg.SVG
import com.google.android.gms.maps.model.LatLng
import com.hqt.util.AirPlaneUtil
import com.hqt.util.helper.Utils
import java.io.Serializable
import java.util.Date
import java.util.Locale
import kotlin.math.ceil


class FlightHistory : Serializable {
    var meta: FlightHistoryMeta? = null
    var history: List<FlightHistoryItem> = ArrayList()
    fun getAirlineLogo(): String {
        try {
            val code = meta?.flight?.substring(0, 2)
            return "logo_" + code!!.toLowerCase(Locale.ROOT)
        } catch (e: Exception) {
            e.printStackTrace()
            return "logo_trans"
        }
    }
}

class FlightHistoryMeta : Serializable {
    var flight: String = ""
    var total: Int = 0
    var score: Float = 0.0f
    var green: FScore? = null
    var yellow: FScore? = null
    var red: FScore? = null
    var logo: String = ""

}

class FScore : Serializable {
    var total = 0
    var max_delaytime = ""
}

class FlightHistoryItem : Serializable {
    var id: String = ""
    var number: String = ""
    var status: FStatus = FStatus()
    var airline: FAirline = FAirline()
    var airport: FAirport = FAirport()
    var aircraft: FAircraft? = null
    var time: FTime? = null
    var updated: Date? = null
    var live: LiveStatus? = null
    var track: ArrayList<FPosition> = ArrayList()
    fun getProgress(): Int {
        if (live != null && live!!.progress > 0) {
            return live!!.progress
        } else {
            return status.getProgress()
        }
    }

    fun getCurrentPosition(): FPosition {
        if (track.size > 0) {
            return track.last()
        }
        return FPosition()
    }

}

class FAircraft : Serializable {
    var model: FADetail? = null
    var registration = ""
    var hex = ""
}

class ColorByAlt(mAlt: Int, mValue: Int) {
    var alt = mAlt
    var value = mValue

}

class FPosition : Serializable {
    var id = 0
    var latitude: Double = 0.0
    var longitude: Double = 0.0
    var altitude = 0
    var speed = 0
    var heading = 0
    var key = ""
    var aircraftType = ""
    var aircraftImage = ""
    var timestamp: Date? = null
    var hash = ""
    fun getLatLng(): LatLng {
        return LatLng(latitude, longitude)
    }

    fun Bitmap.rotate(degrees: Float): Bitmap {
        val matrix = Matrix().apply { postRotate(degrees) }
        return Bitmap.createBitmap(this, 0, 0, width, height, matrix, false)
    }

    fun getAircraftBitmap(selected: Boolean): Bitmap {
        val svg = SVG.getFromString(AirPlaneUtil().getShapes(aircraftType, selected))
        // Create a bitmap and canvas to draw onto
        val svgWidth = if (svg.documentWidth != -1f) svg.documentWidth else 500f
        val svgHeight = if (svg.documentHeight != -1f) svg.documentHeight else 500f
        val newBM = Bitmap.createBitmap(ceil(svgWidth.toDouble()).toInt(),
                ceil(svgHeight.toDouble()).toInt(),
                Bitmap.Config.ARGB_8888)
        val bmcanvas = Canvas(newBM)
        svg.renderToCanvas(bmcanvas)
        return Utils.addShadow(newBM, newBM.height, newBM.width, Color.BLACK, 3, 1f, 3f)
    }

}


class LiveMapData : Serializable {
    var stats = 0
    var flights: ArrayList<FPosition> = ArrayList()
}

class FADetail : Serializable {
    var code = ""
    var text = ""
}

class FStatus : Serializable {
    var text = "Scheduled"
    var value = "scheduled"
    var type = "departure"
    var color = "gray"
    var live = false

    fun getIconColor(): Int {

        return when (color) {
            "gray" -> {
                Color.GRAY
            }
            "red" -> {
                Color.parseColor("#d62d20")
            }
            "yellow" -> {
                Color.parseColor("#FB953B")
            }
            "green" -> {
                Color.parseColor("#4CAF50")
            }
            else -> {
                Color.WHITE
            }
        }

    }

    fun getFlightStatus(): String {
        if (live) return "Đang bay"
        if (value == "scheduled") return "Lên lịch"
        if (value == "canceled") return "Hủy chuyến"

        if (color == "gray") {
            return ""
        } else if (color == "red") {
            return "Trễ chuyến"
        } else if (color == "yellow") {
            return "Chậm chuyến"
        } else if (color == "green") {
            return "Đúng giờ"
        } else {
            return ""
        }
    }

    fun getProgress(): Int {
        return when (value) {
            "scheduled" -> {
                0
            }

            "landed" -> {
                100
            }

            "unknown" -> {
                0
            }

            "canceled" -> {
                0
            }

            else -> 0
        }
    }

    fun getStatusText(): String {
        if (live) return "Đang bay"
        text = text.replace("Canceled", "Hủy chuyến")
        text = text.replace("Landed", "Hạ cánh")
        text = text.replace("Unknown", "--")
        text = text.replace("Scheduled", "Lên kế hoạch bay")
        text = text.replace("Estimated", "Dự kiến")
        return text
    }
}

class FAirline : Serializable {
    var code = ""
    var name = ""
    var logo = ""
}

class FTime : Serializable {
    var scheduled: FlightTime? = null
    var real: FlightTime? = null
    var estimated: FlightTime? = null
    var duration = ""
}

class FlightTime : Serializable {
    var departure: Date? = null
    var arrival: Date? = null

}

class FAirport : Serializable {
    var origin: Location = Location()
    var destination: Location = Location()

}

class Location : Serializable {
    var city: String = ""
    var code: String = ""
    var latitude: Float? = null
    var longitude: Float? = null
    var tz: String = ""
}

class LiveStatus : Serializable {
    var live = false
    var duration = ""
    var flight_time = ""
    var flight_distance = 0
    var remain_time = ""
    var remain_distance = 0
    var progress = 0
    fun getFlightTextShort(): String {
        return "$flight_time trước"

    }

    fun getFlightRemainTextShort(): String {
        return "Hạ cánh trong $remain_time"
    }

    fun getFlightText(): String {
        return "$flight_distance km, $flight_time trước"

    }

    fun getFlightRemainText(): String {
        return "$remain_distance km, trong $remain_time"
    }
}