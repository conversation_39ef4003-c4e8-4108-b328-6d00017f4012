package com.hqt.view.ui.seatmap


import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.view.WindowManager
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.gson.Gson
import com.hqt.data.model.*
import com.hqt.datvemaybay.R
import com.hqt.util.AppConfigs
import com.hqt.util.AppController
import com.hqt.util.Log
import com.hqt.view.ui.BaseActivityKt
import com.hqt.view.ui.reward.ui.activity.RewardActivity
import com.hqt.view.ui.booking.data.model.BaseBooking
import com.hqt.view.ui.booking.data.model.BookingV3
import com.hqt.view.ui.booking.data.model.PaxInfoListV2
import com.hqt.viewmodel.BookingViewModel
import q.rorbin.badgeview.QBadgeView


class SelectSeatActivity() : BaseActivityKt<com.hqt.datvemaybay.databinding.ActivitySelectSeatLayoutBinding>() {


    override val layoutId: Int = R.layout.activity_select_seat_layout
    lateinit var viewModel: BookingViewModel
    lateinit var adapter: SeatFragmentAdapter
    var seatViewList: ArrayList<SeatView> = ArrayList()
    var seatViewListReturn: ArrayList<SeatView> = ArrayList()
    var totalPax = 1
    lateinit var booking: BookingV3

    var paxSeatString = ""
    var paxSeatReturnString = ""
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        getToolbar().title = "Chọn ghế ngồi"
        getToolbar().setNavigationIcon(R.drawable.ic_action_back_home)
        supportActionBar!!.setDisplayShowHomeEnabled(true)


        booking = Gson().fromJson(intent.getStringExtra("bookingDetail"), BookingV3::class.java)

        if (booking.type == BaseBooking.BookingType.FLIGHT) {
            totalPax = booking.adult + booking.child
        }
        getViewBindding().lifecycleOwner = this
        adapter = SeatFragmentAdapter(supportFragmentManager, booking)


        getViewBindding().viewPager.adapter = adapter
        getViewBindding().tabLayout.setupWithViewPager(getViewBindding().viewPager)


        getPaxSeat()

        setWindowFlag(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS, false)
        window.statusBarColor = ContextCompat.getColor(this, R.color.primary_dark)
    }

    fun getPaxSeatStr(isReturn: Boolean): String {

//        AppConfigs.Log("getPaxSeatStr", paxSeatString.toString())
        Log.d("getPaxSeatStr", paxSeatString + paxSeatReturnString)

        if (isReturn) return paxSeatReturnString
        return paxSeatString

    }

    fun getPaxSeat() {

        booking.pax_info.adult.forEach { passenger ->
            passenger.addOn.filter { addOnInfo -> addOnInfo.type == AddOnType.SEAT }.forEach {
                paxSeatString += "-" + it.text
            }


            passenger.addOnReturn.filter { addOnInfo -> addOnInfo.type == AddOnType.SEAT }.forEach {
                paxSeatReturnString += "-" + it.text
            }
        }
        booking.pax_info.child.forEach { passenger ->
            passenger.addOn.filter { addOnInfo -> addOnInfo.type == AddOnType.SEAT }.forEach {
                paxSeatString += "-" + it.text
            }

            passenger.addOnReturn.filter { addOnInfo -> addOnInfo.type == AddOnType.SEAT }.forEach {
                paxSeatReturnString += "-" + it.text
            }
        }
    }

    private fun addSeatToPax(pax_info: PaxInfoListV2, seatList: ArrayList<SeatView>, isReturn: Boolean): PaxInfoListV2 {

        var pi = 0
        var i = 0
        pax_info.adult.forEach { passenger ->

            if (pi == i && i < seatList.size) {
                val currentSeat = seatList[i]
                val seatInfo = AddOnInfo()
                seatInfo.price = currentSeat.seatOption!!.seatCharges
                seatInfo.text = currentSeat.seatOption!!.rowIdentifier + currentSeat.seatOption!!.seatIdentifier
                seatInfo.value = currentSeat.seatOption!!.selectionKey

                val addOnList: ArrayList<AddOnInfo> = ArrayList()
                addOnList.add(seatInfo)
                passenger.updateAddOn(AddOnType.SEAT, addOnList, isReturn)


                i++
            } else if (pi >= seatList.size) {
                passenger.updateAddOn(AddOnType.SEAT, ArrayList(), isReturn)
            }
            pi++
        }
        pax_info.child.forEach { passenger ->

            if (pi == i && i < seatList.size) {
                val currentSeat = seatList[i]
                val seatInfo = AddOnInfo()
                seatInfo.price = currentSeat.seatOption!!.seatCharges
                seatInfo.text = currentSeat.seatOption!!.rowIdentifier + currentSeat.seatOption!!.seatIdentifier
                seatInfo.value = currentSeat.seatOption!!.selectionKey

                val addOnList: ArrayList<AddOnInfo> = ArrayList()
                addOnList.add(seatInfo)
                passenger.updateAddOn(AddOnType.SEAT, addOnList, isReturn)


                i++
            } else if (pi >= seatList.size) {
                passenger.updateAddOn(AddOnType.SEAT, ArrayList(), isReturn)
            }
            pi++
        }
        return pax_info

    }

    fun updateSeatSelected(list: ArrayList<SeatView>, isReturn: Boolean) {
        try {
            if (isReturn) {
                seatViewListReturn = list
                booking.pax_info = addSeatToPax(booking.pax_info, seatViewListReturn, true)
            } else {
                seatViewList = list
                booking.pax_info = addSeatToPax(booking.pax_info, seatViewList, false)

            }
        } catch (e: Exception) {
            e.printStackTrace()
            AppConfigs.logException(e)
        }
    }

    fun backToBooking() {
        val data = Intent()
        data.putExtra("bookingDetail", Gson().toJson(booking.pax_info))
        setResult(RESULT_OK, data)
        finish()
    }


    fun getFragment(isReturn: Boolean): SeatSelectFragment {
        if (isReturn) {
            return supportFragmentManager.fragments[1] as SeatSelectFragment;
        }
        return supportFragmentManager.fragments[0] as SeatSelectFragment;
    }


    private fun initAnalytics(booking: BookingV2, event: String) {
        try {

            val params = Bundle()
            params.putString(FirebaseAnalytics.Param.ORIGIN, booking.departure_f?.originCode)
            params.putString(FirebaseAnalytics.Param.DESTINATION, booking.departure_f?.destinationCode)
            params.putString(FirebaseAnalytics.Param.START_DATE, booking.departure_f?.departureDateTime.toString())
            params.putString(FirebaseAnalytics.Param.END_DATE, booking.departure_f?.arriverDateTime.toString())
            params.putString(FirebaseAnalytics.Param.FLIGHT_NUMBER, booking.departure_f?.flightNumber)
            params.putString(FirebaseAnalytics.Param.ITEM_NAME,
                booking.departure_f?.originCode + booking.departure_f?.destinationCode + booking.departure_f?.flightNumber)
            params.putString(FirebaseAnalytics.Param.NUMBER_OF_PASSENGERS,
                ((booking.departure_f!!.adult ?: 0) + (booking.departure_f!!.child ?: 0) + (booking.departure_f!!.infant ?: 0)).toString() + "")
            firebaseAnalytics.logEvent(event, params)
            firebaseAnalytics.setCurrentScreen(this, "booking_input", null)

        } catch (e: Exception) {

        }
    }

    override fun onResume() {
        super.onResume()
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean { // Inflate the menu; this adds items to the action bar if it is present.
        menuInflater.inflate(R.menu.main_reward, menu)
        try {
            Handler(Looper.getMainLooper()).postDelayed({
                val rewardButton = findViewById(R.id.action_reward) as View
                QBadgeView(applicationContext).setGravityOffset(0f, 0f, true).bindTarget(rewardButton).setBadgeText("!")
                    .setOnDragStateChangedListener { dragState, badge, targetView ->
                    }
            }, 1000)
        } catch (e: java.lang.Exception) {
        }
        return true
    }

    fun doneClick(): Boolean {

        //        if (booking.is_round_trip && seatViewListReturn.size == 0 && booking.return_f?.provider == "VJ") {
        //            Toast.makeText(this, "Vui lòng chọn ghế lượt về", Toast.LENGTH_SHORT).show()
        //            getViewBindding().viewPager.currentItem = 1
        //            return false
        //        }
        //        if (!booking.is_round_trip && seatViewList.size == 0 && booking.departure_f?.provider == "VJ") {
        //            Toast.makeText(this, "Vui lòng chọn ghế lượt đi", Toast.LENGTH_SHORT).show()
        //            getViewBindding().viewPager.currentItem = 0
        //            return false
        //        }

        backToBooking()
        return true
    }

    fun backClick() {
        finish()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean { // Take appropriate action for each action item click
        when (item.itemId) {
            android.R.id.home -> {

                finish()
            }

            R.id.action_reward -> {
                val `in` = Intent(this, RewardActivity::class.java)

                startActivity(`in`)
                return true
            }

            else -> {
                finish()
            }
        }
        return false
    }

    override fun onSystemSettingChange(type: AppConfigs.SystemSettingType) {

        if (type == AppConfigs.SystemSettingType.INTERNET) {
            if (this::viewModel.isInitialized) {
                viewModel.updateInternetStatus(isInternetConnected)
            }
        }


    }


}

class SeatFragmentAdapter(fm: FragmentManager, booking: BookingV3) : FragmentPagerAdapter(fm) {
    var bookingx = booking

    override fun getItem(position: Int): SeatSelectFragment {

        AppConfigs.Log("book", AppController.instance.gSon.toJson(bookingx))
        if (bookingx.is_round_trip) {
            return when (position) {
                0 -> SeatSelectFragment.newInstance(bookingx.departure_f!!.provider ?: "",
                    bookingx.departure_f!!.flightKey ?: "",
                    false)
                1 -> SeatSelectFragment.newInstance(bookingx.return_f!!.provider ?: "", bookingx.return_f!!.flightKey ?: "", true)
                else -> SeatSelectFragment.newInstance("", "", false)
            }
        } else {
            return SeatSelectFragment.newInstance(bookingx.departure_f!!.provider ?: "",
                bookingx.departure_f!!.flightKey ?: "",
                false)
        }

    }

    override fun getPageTitle(position: Int): CharSequence { // if (bookingx.is_round_trip) {
        when (position) {
            0 -> return "Lượt đi"
            1 -> return "Lượt về"
            else -> return ""
        } //}
    }


    override fun getCount(): Int {
        if (!bookingx.is_round_trip) {
            return 1
        }
        return 2
    }
}
