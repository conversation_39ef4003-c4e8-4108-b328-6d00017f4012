package com.hqt.view.ui.reward.data.model

import com.google.gson.annotations.SerializedName
import java.io.Serializable
import java.util.Date

data class Promotion(

    @SerializedName("id")
    var id: Int = 0,

    @SerializedName("isActive")
    var isActive: Boolean = false,

    @SerializedName("category")
    var category: String? = null,

    @SerializedName("name")
    var name: String? = null,

    @SerializedName("description")
    var description: String? = null,

    @SerializedName("banner")
    var banner: String? = null,

    @SerializedName("logo:")
    var logo: String? = null,

    @SerializedName("point")
    var point: Int? = 0,

    @SerializedName("total")
    var total: Int? = 0,

    @SerializedName("remain")
    var remain: Int? = 0,

    @SerializedName("expired_time")
    var expiredTime: Date? = null,

    @SerializedName("created_at")
    var createdAt: Date? = null,
) : Serializable
