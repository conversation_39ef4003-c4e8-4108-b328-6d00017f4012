package com.hqt.view.ui.bus

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import androidx.databinding.DataBindingUtil
import androidx.databinding.ObservableField
import com.hqt.data.model.response.StationPoints
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.BusListStationItemBinding
import com.hqt.util.AppConfigs


class BusStationPointAdapter(var mContext: Context, internal var contents: ArrayList<StationPoints>) :
    RecyclerView.Adapter<BusStationPointAdapter.ViewHolder>() {
    class ViewHolder(var context: Context, val binding: BusListStationItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(station: StationPoints) {

            binding.station = station
            binding.checkBox.isChecked = station.selected
            binding.cardView.setOnClickListener {
                binding.checkBox.performClick()
            }

            binding.checkBox.setOnClickListener {
                station.selected = binding.checkBox.isChecked
                (context as BusSelectActivity).clearStationPointSelect(station)

            }
            binding.btnOpenMap.setOnClickListener {
                try {
                    AppConfigs.Log("station.location", station.location)
                    
                    if (station.location != null && station.location!!.length > 5) {
                        val gmapIntent = Intent(Intent.ACTION_VIEW, Uri.parse(station.location)).apply {
                            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
                        }
                        context.startActivity(gmapIntent)
                    }

                } catch (e: Exception) {

                }
            }


            binding.executePendingBindings()
        }

    }

    override fun getItemCount(): Int {
        return contents.size
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {

        val layoutInflater = LayoutInflater.from(parent.context)
        val binding: BusListStationItemBinding = DataBindingUtil.inflate(layoutInflater,
            R.layout.bus_list_station_item,
            parent,
            false)

        return ViewHolder(mContext, binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(contents[position])
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getItemViewType(position: Int): Int {
        return position
    }


}