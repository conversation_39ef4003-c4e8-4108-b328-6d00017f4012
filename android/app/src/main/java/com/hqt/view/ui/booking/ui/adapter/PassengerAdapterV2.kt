package com.hqt.view.ui.booking.ui.adapter

import com.hqt.base.BaseAdapter
import com.hqt.data.model.Passenger
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ListFlightHistoryItemBinding
import com.hqt.datvemaybay.databinding.PaxInputItemListBinding
import com.hqt.datvemaybay.databinding.PaxInputItemListV2Binding
import com.hqt.view.ui.booking.data.model.PassengerV2
import com.hqt.view.ui.flighthistory.data.model.FlightHistoryItem


class PassengerAdapterV2(private val listener: (PassengerV2) -> Unit) :
    BaseAdapter<PassengerV2, PaxInputItemListV2Binding>(listener) {



    override fun getLayoutRes(): Int {
        return R.layout.pax_input_item_list_v2
    }



    override fun bind(binding: PaxInputItemListV2Binding, position: Int, model: PassengerV2) {
        binding.apply {
//            binding.viewmodel = model

            pax = model


        }

    }





    override fun onItemClickListener(model: PassengerV2) {
        listener(model)




    }

}