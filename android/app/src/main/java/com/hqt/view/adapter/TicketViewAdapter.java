package com.hqt.view.adapter;


import android.annotation.SuppressLint;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Color;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.RecyclerView;

import android.text.style.BackgroundColorSpan;
import android.text.style.ForegroundColorSpan;
import android.text.style.RelativeSizeSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.ScaleAnimation;
import android.widget.ImageView;
import android.widget.TextView;

import com.hqt.datvemaybay.Common;
import com.hqt.datvemaybay.R;
import com.hqt.datvemaybay.SearchResult;
import com.hqt.data.model.Ticket;
import com.mikepenz.iconics.Iconics;

import java.util.List;

/**
 * Created by TN on 12/1/2016.
 */
public class TicketViewAdapter extends RecyclerView.Adapter<TicketViewAdapter.ViewHolder> {

    List<Ticket> contents;
    Context mContext;
    AppCompatActivity mActivity;
    static final int TYPE_HEADER = 0;
    static final int TYPE_CELL = 1;
    private int lastPosition = -1;
    String selected_position = "";

    // Provide a reference to the views for each data item
    // Complex data items may need more than one view per item, and
    // you provide access to all the views for a data item in a view holder
    public static class ViewHolder extends RecyclerView.ViewHolder {
        // each data item is just a string in this case
        public TextView txtGiaVe, txtNgayBay, txtThoiGianDen, txtThoiGianBay, txtGiaPhi, txtThuePhi, txtThueSanBay, txtHangBay, txtHangGhe, txtPos, txtSoHieu, txtDiemDung, txtPromo, txtDuration;
        public ImageView imgLogo;

        public ViewHolder(View v) {
            super(v);
            txtSoHieu = v.findViewById(R.id.soHieu);
            txtGiaVe = v.findViewById(R.id.giaVe);
            txtThoiGianDen = v.findViewById(R.id.thoiGianDen);
            txtThoiGianBay = v.findViewById(R.id.thoiGianBay);
            txtGiaPhi = v.findViewById(R.id.giaPhi);
            txtThuePhi = v.findViewById(R.id.thuePhi);
            txtThueSanBay = v.findViewById(R.id.thueSanBay);
            txtHangBay = v.findViewById(R.id.hangBay);
            txtHangGhe = v.findViewById(R.id.hangGhe);
            txtNgayBay = v.findViewById(R.id.gioBay);
            txtThoiGianDen = v.findViewById(R.id.thoiGianDen);
            txtPos = v.findViewById(R.id.pos);
            imgLogo = v.findViewById(R.id.logo);
            txtDiemDung = v.findViewById(R.id.stop);
            txtPromo = v.findViewById(R.id.promo);
            txtDuration = v.findViewById(R.id.txtDuration);

        }
    }


    public TicketViewAdapter(Context context, List<Ticket> contents) {
        this.mContext = context;
        this.contents = contents;
    }

    @Override
    public int getItemViewType(int position) {
        switch (position) {
            case 0:
                return TYPE_HEADER;
            default:
                return TYPE_CELL;
        }
    }

    @Override
    public int getItemCount() {
        return contents.size();
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = null;

        view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.list_flight_item, parent, false);

        ViewHolder v = new ViewHolder(view);
        view.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View arg0) {
                int pos = Integer.valueOf(((TextView) (arg0.findViewById(R.id.pos))).getText().toString());

                // TextView p = ((TextView) view.findViewById(R.id.pos));
                showInfo(contents.get(pos));


            }
        });
        return v;
    }


    @Override
    public void onBindViewHolder(ViewHolder holder, int position) {
        // - get element from your dataset at this position
        // - replace the contents of the view with that element
        holder.itemView.setSelected(selected_position.equals(contents.get(position).getFlightNumber() + contents.get(position).getDuration() + contents.get(position).getFullPrices()));
        holder.txtSoHieu.setText(contents.get(position).getFlightNumber());
        if (Common.SHOWFULLPRICE) {
            holder.txtGiaVe.setText(Common.dinhDangTien(contents.get(position).getFullPrices()));
        } else {
            holder.txtGiaVe.setText(Common.dinhDangTien(contents.get(position).getNetPrices()));
        }
        holder.txtThoiGianDen.setText(contents.get(position).getArrivalDate().substring(0, 5));
        holder.txtThoiGianBay.setText(contents.get(position).getDepatureDate().substring(0, 5));
        holder.txtGiaPhi.setText(Common.dinhDangTien(contents.get(position).getFullPrices()));
        holder.txtThuePhi.setText(Common.dinhDangTien(contents.get(position).getTax()));
        holder.txtThueSanBay.setText(Common.dinhDangTien(contents.get(position).getAirPortFee()));
        holder.txtHangBay.setText(contents.get(position).getProvider());
        holder.txtHangGhe.setText(contents.get(position).getSeatClass());
        holder.txtNgayBay.setText(contents.get(position).getDepatureDate().substring(6, 16));
        holder.txtDuration.setText(contents.get(position).getDuration());
        if (contents.get(position).getIsQuickDepart()) {
            holder.txtSoHieu.setText(("{faw_history} " + contents.get(position).getFlightNumber()));
            new Iconics.Builder().style(new ForegroundColorSpan(mContext.getResources().getColor(R.color.google_yellow)), new BackgroundColorSpan(Color.TRANSPARENT), new RelativeSizeSpan(0.8f)).on(holder.txtSoHieu).build();

        } else if (contents.get(position).getIsPromo()) {
            holder.txtSoHieu.setText(("{faw_star1} " + contents.get(position).getFlightNumber()));
            new Iconics.Builder().style(new ForegroundColorSpan(mContext.getResources().getColor(R.color.google_yellow)), new BackgroundColorSpan(Color.TRANSPARENT), new RelativeSizeSpan(0.8f)).on(holder.txtSoHieu).build();
        } else {
            holder.txtSoHieu.setText((contents.get(position).getFlightNumber()));
        }

        holder.txtPos.setText((position + ""));
        holder.txtDiemDung.setText(("{faw_circle1} " + contents.get(position).getStops()));
        new Iconics.Builder().style(new ForegroundColorSpan(contents.get(position).getStops().toLowerCase().equals("bay thẳng") ? mContext.getResources().getColor(R.color.primary) : Color.RED), new BackgroundColorSpan(Color.TRANSPARENT), new RelativeSizeSpan(0.5f)).on(holder.txtDiemDung).build();
        holder.imgLogo.setImageResource(Integer.valueOf(mContext.getResources().getIdentifier(contents.get(position).getLogo(), "drawable", mContext.getPackageName())));

        notifyItemChanged(holder.getAdapterPosition());

    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @SuppressLint("RestrictedApi")
    private void showInfo(Ticket tk) {

        AlertDialog.Builder builder = new AlertDialog.Builder(mContext);

        LayoutInflater inflater = LayoutInflater.from(mContext);
        View alertView = inflater.inflate(R.layout.ticket_view_layout, null);

        builder.setView(alertView, 0, 0, 0, 0);
        builder.setTitle("Thông tin chuyến bay");
        builder.setIcon(R.drawable.ic_dep);

        TextView txtTo = alertView.findViewById(R.id.to);
        TextView txtFrom = alertView.findViewById(R.id.from);
        TextView txtDepatureTime = alertView.findViewById(R.id.thoiGianBay);
        TextView txtArrivalTime = alertView.findViewById(R.id.thoiGianDen);
        TextView txtDuration = alertView.findViewById(R.id.duration);
        TextView txtHangBay = alertView.findViewById(R.id.hangBay);
        TextView txtChuyenBay = alertView.findViewById(R.id.chuyenBay);
        TextView txtLoaiVe = alertView.findViewById(R.id.loaiVe);
        TextView txtgia = alertView.findViewById(R.id.gia);
        TextView txtgiaPhi = alertView.findViewById(R.id.giaPhi);
        TextView txtthuePhi = alertView.findViewById(R.id.thuePhi);
        TextView txtthueSanBay = alertView.findViewById(R.id.thueSanBay);
        ImageView imgLogo = alertView.findViewById(R.id.logo);
        TextView txtDiemDung = alertView.findViewById(R.id.diemDung);

        txtTo.setText(tk.getDestination());
        txtFrom.setText(tk.getOrigin());
        txtDepatureTime.setText(tk.getDepatureDate().substring(0, 5));
        if (tk.isNextDay()) {
            txtArrivalTime.setText((tk.getArrivalDate().substring(0, 5) + " (+1 ngày)"));
        } else {
            txtArrivalTime.setText(tk.getArrivalDate().substring(0, 5));
        }

        txtDuration.setText(tk.getDuration());
        txtHangBay.setText(tk.getProvider());
        txtChuyenBay.setText(tk.getFlightNumber());

        if (tk.getIsQuickDepart()) {
            txtLoaiVe.setText(("{faw_history} " + tk.getSeatClass()));
            new Iconics.Builder().style(new ForegroundColorSpan(mContext.getResources().getColor(R.color.google_yellow)), new BackgroundColorSpan(Color.TRANSPARENT), new RelativeSizeSpan(0.8f)).on(txtLoaiVe).build();
        } else if (tk.getIsPromo()) {
            txtLoaiVe.setText(("{faw_star} " + tk.getSeatClass()));
            new Iconics.Builder().style(new ForegroundColorSpan(mContext.getResources().getColor(R.color.google_yellow)), new BackgroundColorSpan(Color.TRANSPARENT), new RelativeSizeSpan(0.8f)).on(txtLoaiVe).build();
        } else {
            txtLoaiVe.setText(tk.getSeatClass());
        }

        txtgia.setText(Common.dinhDangTien(tk.getNetPrices()));
        txtgiaPhi.setText(Common.dinhDangTien(tk.getFullPrices()));
        txtthuePhi.setText(Common.dinhDangTien(tk.getTax() + tk.getAirPortFee()));
        txtthueSanBay.setText((Common.dinhDangTien(tk.getAirPortFee()) + ""));
        txtDiemDung.setText(("{faw_circle1} " + tk.getStops()));
        new Iconics.Builder().style(new ForegroundColorSpan(tk.getStops().toLowerCase().equals("bay thẳng") ? mContext.getResources().getColor(R.color.primary) : Color.RED), new BackgroundColorSpan(Color.TRANSPARENT), new RelativeSizeSpan(0.8f)).on(txtDiemDung).build();

        imgLogo.setImageResource(Integer.valueOf(mContext.getResources().getIdentifier(tk.getLogo(), "drawable", mContext.getPackageName())));
        builder.setPositiveButton("Đặt vé", new DialogInterface.OnClickListener() {
            public void onClick(DialogInterface dialog, int id) {
                if (mContext instanceof SearchResult) {
                    ((SearchResult) mContext).clickNext();
                }
            }
        });
        builder.setNegativeButton("Trở về", new DialogInterface.OnClickListener() {
            public void onClick(DialogInterface dialog, int id) {

            }
        });


        builder.setCancelable(true);
        AlertDialog myDialog = builder.create();
        myDialog.show();

    }

    private void setScaleAnimation(View view) {
        ScaleAnimation anim = new ScaleAnimation(0.0f, 1.0f, 0.0f, 1.0f, Animation.RELATIVE_TO_SELF, 0.5f, Animation.RELATIVE_TO_SELF, 0.5f);
        anim.setDuration(1000);
        view.startAnimation(anim);
    }
}