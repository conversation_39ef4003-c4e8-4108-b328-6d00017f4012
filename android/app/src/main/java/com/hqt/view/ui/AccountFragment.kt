package com.hqt.view.ui

import android.annotation.SuppressLint
import android.content.ActivityNotFoundException
import android.content.Intent
import android.content.pm.PackageInfo
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import com.android.volley.VolleyError
import com.bumptech.glide.Glide
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import com.hqt.data.model.User
import com.hqt.datvemaybay.CheckVe
import com.hqt.datvemaybay.Checkin
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.WebViewActivity
import com.hqt.datvemaybay.databinding.FragmentAccountBinding
import com.hqt.util.AppConfigs
import com.hqt.util.AppController.Companion.instance
import com.hqt.util.SSLSendRequest
import com.hqt.util.SSLSendRequest.CallBackInterface
import com.hqt.util.SharedPrefs
import com.hqt.util.base.BaseFragment
import com.hqt.view.ui.account.LoginActivity
import com.hqt.view.ui.account.ProfileEditActivity
import com.hqt.view.ui.reward.ui.activity.RewardActivity
import org.json.JSONException
import org.json.JSONObject

/**
 * Created by Belal on 1/23/2018.
 */
class AccountFragment : BaseFragment<FragmentAccountBinding>() {
    var activity: HomeActivity? = null
    var APISERVICE: SSLSendRequest? = null
    var authUser: FirebaseUser? = null
    var user: User? = null
    override fun getLayoutRes(): Int {
        return R.layout.fragment_account
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        activity = requireActivity() as HomeActivity


        getToolbar()?.title = "Tài khoản"
        getToolbar()?.setNavigationOnClickListener(View.OnClickListener {
            (getActivity() as HomeActivity?)!!.clickNavigation(
                0
            )
        })
        APISERVICE = SSLSendRequest(activity)
        getToolbar()?.setNavigationOnClickListener(View.OnClickListener {
            Toast.makeText(activity, "Cảm ơn nhé :)", Toast.LENGTH_SHORT).show()
            (getActivity() as HomeActivity?)!!.clickNavigation(0)
        })

        //  PayMeWallet.INSTANCE.showPayme(toolbar);
        addButtonListen()
    }

    fun customFun() {
        AppConfigs.Log("call fragment", "from activity")
    }

    fun loadUserInfo() {

        Handler(Looper.getMainLooper()).postDelayed(object : Runnable {
            override fun run() {
                profile
            }
        }, 1000)


        val photo =
            if (authUser?.photoUrl != null) authUser?.photoUrl.toString() else ""
        binding.txtCustomerTitle.text = (authUser?.displayName)

        Glide.with(requireActivity())
            .load(photo)
            .placeholder(R.drawable.logo_mini)
            .skipMemoryCache(true)
            .into(binding.profileImage)
        Common.commonSave(
            requireContext(),
            authUser?.getDisplayName(),
            authUser?.getEmail(),
            authUser?.getUid(),
            photo,
            ""
        )
        binding.layoutLogin.visibility = View.GONE
        binding.accountView.visibility = View.VISIBLE
        binding.txtLogout.visibility = View.VISIBLE
    }

    override fun onResume() {
        super.onResume()
        if (FirebaseAuth.getInstance().currentUser != null) {
            loadUserInfo()
        }
    }

    @SuppressLint("SetTextI18n")
    fun addButtonListen() {

        authUser = (getActivity() as HomeActivity?)!!.firebaseUser
        var pInfo: PackageInfo? = null
        try {
            pInfo = activity?.packageManager?.getPackageInfo(requireActivity().packageName, 0)
        } catch (e: Exception) {
            e.printStackTrace()
        }

        binding.txtFooter.text =
            "Phiên bản: " + pInfo?.versionName + " (" + pInfo?.versionCode + ")"
        if ((getActivity() as HomeActivity?)!!.isUserSigned) {
            loadUserInfo()
        } else {
            binding.txtLogout.visibility = View.GONE
            binding.accountView.visibility = View.GONE
            binding.layoutLogin.visibility = View.VISIBLE
            binding.btnLogin.setOnClickListener {
                startActivity(Intent(requireContext(), LoginActivity::class.java))
            }

            binding.btnRegister.setOnClickListener {
                startActivity(Intent(requireContext(), LoginActivity::class.java))


            }
        }
        binding.accountView.setOnClickListener {
            try {
                if (authUser != null) {
                    val intent =
                        Intent(requireContext(), ProfileEditActivity::class.java)
                    requireActivity().startActivity(intent)
                }
            } catch (e: Exception) {
                AppConfigs.logException(e)
            }
        }
        binding.txtLogout.setOnClickListener {
            AlertDialog.Builder(requireActivity())
                .setIcon(R.drawable.log_out)
                .setTitle("Đăng xuất")
                .setMessage("Bạn có chắc chắn muốn thoát?")
                .setPositiveButton("Có") { dialog, which -> (getActivity() as HomeActivity?)!!.signOut() }
                .setNegativeButton("Không", null)
                .show()
        }
        binding.txtReward.setOnClickListener {
            val intent = Intent(requireContext(), RewardActivity::class.java)
            requireActivity().startActivity(intent)
        }
        binding.sendZalo.setOnClickListener {
            Toast.makeText(activity, "Gửi hỗ trợ Zalo", Toast.LENGTH_LONG).show()
            val uri = Uri.parse("https://zalo.me/4377741633721725644?message=xxxxx&msg=ssss")
            val intent = Intent(Intent.ACTION_VIEW, uri)
            intent.putExtra("message", "xxxxx")
            requireActivity().startActivity(intent)
        }
        binding.sendSms.setOnClickListener {
            Common.sendSms(
                activity,
                "Xin chao. Minh can ho tro dat ve may bay.",
                "0909725488"
            )
        }
        binding.sendFacebook.setOnClickListener {
            try {
                Toast.makeText(activity, "Gửi hỗ trợ facebook", Toast.LENGTH_LONG).show()
                val uri = Uri.parse("fb-messenger://user/1522635601351119")
                val intent = Intent(Intent.ACTION_VIEW, uri)
                requireActivity().startActivity(intent)
            } catch (e: ActivityNotFoundException) {
                val uri = Uri.parse("https://www.facebook.com/12bay.vn/")
                val intent = Intent(Intent.ACTION_VIEW, uri)
                requireActivity().startActivity(intent)
            }
        }
        binding.txtPhoneHotline.setOnClickListener {
            val i = Intent(Intent.ACTION_DIAL)
            i.data = Uri.parse("tel:19002642")
            i.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            requireActivity().startActivity(i)
        }
        binding.txtRatingAppDetail.setOnClickListener {
            requireActivity().startActivity(
                Intent(
                    Intent.ACTION_VIEW,
                    Uri.parse("market://details?id=" + requireActivity().packageName)
                )
            )
        }
        binding.txtShareApp.setOnClickListener {
            val intent = Intent()
            intent.action = Intent.ACTION_SEND
            intent.putExtra(
                Intent.EXTRA_TEXT,
                "App săn vé máy bay giá rẻ cực hay: http://bit.ly/app12bay"
            )
            intent.type = "text/plain"
            requireActivity().startActivity(intent)
        }
        binding.txtTermsAndconditions.setOnClickListener {
            val intent = Intent(requireContext(), WebViewActivity::class.java)
            intent.putExtra("webLink", "https://12bay.vn/ho-tro/dieu-khoan-website/?app=true")
            intent.putExtra(
                "webTitle",
                requireActivity().resources.getString(R.string.txt_terms_andconditions)
            )
            requireActivity().startActivity(intent)
        }
        binding.txtPrivacyPolicy.setOnClickListener {
            val intent = Intent(requireContext(), WebViewActivity::class.java)
            intent.putExtra("webLink", "https://12bay.vn/ho-tro/chinh-sach-bao-mat/?app=true")
            intent.putExtra(
                "webTitle",
                requireActivity().resources.getString(R.string.txt_privacy_policy)
            )
            requireActivity().startActivity(intent)
        }
        binding.txtGeneralRules.setOnClickListener {
            val intent = Intent(requireContext(), WebViewActivity::class.java)
            intent.putExtra("webLink", "https://12bay.vn/ho-tro/quy-dinh-chung/?app=true")
            intent.putExtra(
                "webTitle",
                requireActivity().resources.getString(R.string.txt_general_rules)
            )
            requireActivity().startActivity(intent)
        }
        binding.txtPayment.setOnClickListener {
            val intent = Intent(requireContext(), WebViewActivity::class.java)
            intent.putExtra("webLink", "https://12bay.vn/ho-tro/thanh-toan/?app=true")
            intent.putExtra(
                "webTitle",
                requireActivity().resources.getString(R.string.menuThanhToan)
            )
            requireActivity().startActivity(intent)
        }
        binding.txtCheckPnr.setOnClickListener {
            val intent = Intent(requireContext(), CheckVe::class.java)
            requireActivity().startActivity(intent)
        }
        binding.txtCheckinOnline.setOnClickListener {
            val intent = Intent(requireContext(), Checkin::class.java)
            requireActivity().startActivity(intent)
        }
    }

    val profile: Unit
        get() {
            val params = JSONObject()
            try {
                params.put(
                    "device_token",
                    SharedPrefs.getInstance().get("FCM-TOKEN", String::class.java)
                )
            } catch (e: JSONException) {
                AppConfigs.logException(e)
            }
            authUser = FirebaseAuth.getInstance().currentUser
            if (authUser != null) {
                APISERVICE!!.GET(
                    true,
                    "users/" + authUser!!.uid,
                    params,
                    object : CallBackInterface {
                        override fun onSuccess(response: JSONObject, cached: Boolean) {
                            try {
                                val jData = response.getJSONObject("data")
                                user = instance.gSon.fromJson(jData.toString(), User::class.java)
                                instance.user = user
                                val pointText = user?.point?.text
                                val phoneNumber = user?.phoneNumber
                                if (phoneNumber != "" && phoneNumber!!.length > 9) {
                                    //PayMeWallet.INSTANCE.payMeLogin(getActivity(), Common.removeWordPhoneFormat(phoneNumber));
                                    //PayMeWallet.INSTANCE.showBalance(((Toolbar) rootView.findViewById(R.id.toolbar)), getActivity());
                                }
                                binding.textPoint.text = pointText
                                binding.txtCustomerTitle.text = jData.getString("name")
                                if (user?.waitingBooking!! > 0) {
                                    activity!!.wattingBadge.badgeNumber =
                                        user?.waitingBooking!! + 10
                                } else {
                                    activity!!.wattingBadge.hide(true)
                                }
                                if (user?.unseenNotification!! > 0) {
                                    activity!!.unseenBadge.badgeNumber = user?.unseenNotification!!
                                } else {
                                    activity!!.unseenBadge.hide(true)
                                }
                            } catch (e: JSONException) {
                                AppConfigs.logException(e)
                            }
                        }

                        override fun onFail(error: VolleyError) {
                            // LOGIN ERROR
                        }
                    })
            }
        }
}