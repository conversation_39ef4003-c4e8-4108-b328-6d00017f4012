package com.hqt.view.ui.payment

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.text.Html
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.FragmentActivity
import com.hqt.data.model.PaymeConfig
import com.hqt.data.model.PaymentOrderXml
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.WidgetBalanceInfoBinding
import com.hqt.util.AppConfigs
import com.hqt.util.AppController
import com.hqt.view.ui.BaseActivity
import com.hqt.view.ui.account.ProfileEditActivity
import org.json.JSONObject

import java.text.DateFormat
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Singleton

@Singleton object PayMeWallet { //    var payme: PayME? = null
    //    var isLogin = false
    //    var accountStatus = AccountStatus.NOT_ACTIVATED
    //    var isInitOnclick = false
    //    var onPayMe = true
    //
    //    @SuppressLint("SimpleDateFormat") fun payMeLogin(activity: FragmentActivity, phone: String) {
    //        try {
    //            if (onPayMe) {
    //                val tz = TimeZone.getTimeZone("UTC")
    //                val df: DateFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm'Z'") // Quoted "Z" to indicate UTC, no timezone offset
    //
    //                df.setTimeZone(tz)
    //                val nowAsISO: String = df.format(Date())
    //
    //                val userData = "{\"userId\":\"${phone}\",\"timestamp\":\"${nowAsISO}\",\"phone\":\"${phone}\"}"
    //
    //                val connectToken = Common.CryptoAESencrypt(userData, PaymeConfig.SecretKey)
    //
    //                payme = PayME(activity.applicationContext, PaymeConfig.AppToken, PaymeConfig.PublicKey, connectToken, PaymeConfig.PrivateKey, arrayOf<String>("#03a1e4", "#0093ce"), LANGUAGES.VN, Env.PRODUCTION, false)
    //
    //                payme?.login(onSuccess = { aStatus ->
    //
    //                    isLogin = true
    //                    accountStatus = aStatus
    //
    //
    //                    if (aStatus == AccountStatus.NOT_ACTIVATED) {
    //                        //Tài khoản chưa kich hoạt
    //                    }
    //                    if (aStatus == AccountStatus.NOT_KYC) {
    //                        //Tài khoản chưa định danh
    //                    }
    //
    //                    if (aStatus == AccountStatus.KYC_APPROVED) {
    //                        //Tài khoản đã
    //                    }
    //                    AppConfigs.logAnalyticEvent("PAYME_LOGIN_SUCCESS", Bundle())
    //
    //                }, onError = { jsonObject, code, message ->
    //                    //PayME.showError(message)
    //                })
    //            }
    //        } catch (e: java.lang.Exception) {
    //            AppConfigs.logException(e)
    //        }
    //
    //    }
    //
    //    fun getPayMe(): PayME? {
    //        return payme
    //    }
    //
    //    fun pay(activity: FragmentActivity, type: String, order: PaymentOrderXml) {
    //        try {
    //            val payType = type.removePrefix("PAYME-")
    //            val infoPayment = InfoPayment("PAY", order.grandTotal, order.paymentNote, order.bookingId + "-" + payType, PaymeConfig.StoreId, "", "", "")
    //            AppConfigs.Log("infoPayment", payType.toString())
    //
    //            payme?.pay(activity.supportFragmentManager, infoPayment, false, payType, onSuccess = { json: JSONObject? ->
    //                PayME.showError("Thanh toán thành công")
    //                AppConfigs.logAnalyticEvent("PAYME_PAYMENT_SUCCESS", Bundle())
    //                activity.finish()
    //            }, onError = { jsonObject, code, message ->
    //                if (message != null && message.length > 0) {
    //                    PayME.showError(message)
    //
    //                }
    //                if (code == ERROR_CODE.EXPIRED) {
    //                    payme?.logout()
    //                }
    //                if (code == ERROR_CODE.ACCOUNT_NOT_KYC || code == ERROR_CODE.ACCOUNT_NOT_ACTIVATED) {
    //                }
    //            })
    //        } catch (e: Exception) {
    //            e.printStackTrace()
    //            AppConfigs.logException(e)
    //        }
    //    }
    //
    //    private fun openWallet(fragmentManager: androidx.fragment.app.FragmentManager) {
    //        try {
    //            payme?.openWallet(fragmentManager, onSuccess = { json: JSONObject? ->
    //                //PayME.showError(json.toString())
    //                AppConfigs.logAnalyticEvent("PAYME_OPENWALLET_SUCCESS", Bundle())
    //            }, onError = { jsonObject, code, message ->
    //                PayME.showError(message)
    //                if (code == ERROR_CODE.EXPIRED) {
    //                    payme?.logout()
    //                }
    //
    //            })
    //        } catch (e: Exception) {
    //            AppConfigs.logException(e)
    //        }
    //
    //    }
    //
    //    private fun initOpenProfile(view: View, fragmentManager: androidx.fragment.app.FragmentManager) {
    //        try {
    //
    //            view.setOnClickListener {
    //                if (!AppController.instance.isUserLogin()) {
    //
    //                    AlertDialog.Builder(view.context).setIcon(R.drawable.payme_logo).setTitle("Thông báo").setMessage(Common.convertHTML("Đây là tiện ích liên kết với ví điện tử <b>PAYME</b><br>" + "Hỗ trợ thanh toán tiền điện, nước, internet, mua card điên thoại..<br>" + "Quý khách vui lòng <b>Đăng nhập</b> để tiếp tục sử dụng!")).setPositiveButton("Đăng nhập") { _, _ ->
    //                        (view.context as BaseActivity).signIn()
    //                    }.setNegativeButton("Trở lại", null).show()
    //
    //
    //                } else if (AppController.instance.user?.phoneNumber.isNullOrEmpty()) {
    //                    AlertDialog.Builder(view.context).setIcon(R.drawable.payme_logo).setTitle("Thông báo").setMessage(Common.convertHTML("Đây là tiện ích liên kết với ví điện tử <b>PAYME</b><br>" + "Quý khách vui lòng cập nhật <b>số điện thoại</b> để tiếp tục sử dụng!")).setPositiveButton("Cập nhật") { _, _ ->
    //                        view.context.startActivity(Intent(view.context, ProfileEditActivity::class.java))
    //                    }.setNegativeButton("Trở lại", null).show()
    //
    //
    //                } else {
    //                    openWallet(fragmentManager)
    //                }
    //            }
    //            isInitOnclick = true
    //
    //        } catch (e: Exception) {
    //
    //        }
    //    }
    //
    //    fun showPayme(toolbar: androidx.appcompat.widget.Toolbar) {
    //        try {
    //
    //            val paymeInfo = toolbar.findViewById<LinearLayout>(R.id.paymeInfo)
    //            if (paymeInfo != null) {
    //                if (this.onPayMe) {
    //                    paymeInfo.visibility = View.VISIBLE
    //                } else {
    //                    paymeInfo.visibility = View.GONE
    //                }
    //            }
    //
    //        } catch (e: Exception) {
    //            AppConfigs.logException(e)
    //        }
    //    }
    //
    //    fun showBalance(toolbar: androidx.appcompat.widget.Toolbar, activity: FragmentActivity) {
    //        try {
    //
    //            var delay: Long = 1000
    //            if (!isLogin) {
    //                delay = 3000
    //            }
    //            val balanceText: TextView
    //            val paymeInfo = toolbar.findViewById<LinearLayout>(R.id.paymeInfo)
    //
    //            balanceText = if (paymeInfo == null) {
    //                val layoutInflater = LayoutInflater.from(activity)
    //                val binding: WidgetBalanceInfoBinding = DataBindingUtil.inflate(layoutInflater, R.layout.widget_balance_info, toolbar, true)
    //                initOpenProfile(binding.paymeBalance, activity.supportFragmentManager)
    //
    //                binding.paymeBalance
    //
    //            } else {
    //                toolbar.findViewById<TextView>(R.id.paymeBalance)
    //            }
    //
    //            showPayme(toolbar)
    //            Handler().postDelayed({
    //                payme?.getWalletInfo(onSuccess = { jsonObject: JSONObject ->
    //                    val balance = jsonObject.getJSONObject("Wallet").getInt("balance")
    //                    balanceText.text = Common.dinhDangTien(balance)
    //                    try {
    //                        AppController.instance.user?.payMeBalance = balance
    //                    } catch (e: Exception) {
    //
    //                    }
    //                }, onError = { jsonObject: JSONObject?, i: Int, s: String? ->
    //                    balanceText.text = "Ví của bạn"
    //                })
    //            }, delay)
    //        } catch (e: Exception) {
    //            AppConfigs.logException(e)
    //        }
    //
    //    }

}