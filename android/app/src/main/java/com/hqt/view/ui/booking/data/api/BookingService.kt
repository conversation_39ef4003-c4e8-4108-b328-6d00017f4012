package com.hqt.view.ui.booking.data.api

import com.hqt.base.model.HttpData
import com.hqt.view.ui.booking.data.model.Baggage
import com.hqt.view.ui.booking.data.model.BaseBooking
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path
import retrofit2.http.Query

interface BookingService {

    @GET("/api/v1/AirLines/BagFee/{route}")
    suspend fun getBagFeeApi(
        @Path("route") air: String,
        @Query("fareBasis") fareBasis : String?,
        @Query("stops") stops : String?,
        @Query("flightKey") flightKey : String?,
    ): HttpData<ArrayList<Baggage>>



    @POST("/api/v1/AirLines/Bookings")
    suspend fun sendBooking(
       @Body request : Any?
    ): HttpData<BaseBooking>
}