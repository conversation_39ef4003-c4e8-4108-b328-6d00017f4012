package com.hqt.view.ui.search.ui.helper

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.github.florent37.materialviewpager.MaterialViewPagerHelper
import com.github.florent37.materialviewpager.Utils

/**
 * Created by florentchampigny on 27/05/2016.
 */
class MaterialViewPagerHeaderDecoratorV2(val offset : Int) : RecyclerView.ItemDecoration() {
    var registered: Boolean = false

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        recyclerView: RecyclerView,
        state: RecyclerView.State
    ) {
        val holder = recyclerView.getChildViewHolder(view)
        val context = recyclerView.context

        if (!registered) {
            MaterialViewPagerHelper.registerRecyclerView(context, recyclerView)
            registered = true
        }

        var headerCells = 1

        //don't work with stagged
        val layoutManager = recyclerView.layoutManager
        if (layoutManager is GridLayoutManager) {
            headerCells = layoutManager.spanCount
        }

        val animator = MaterialViewPagerHelper.getAnimator(context)
        if (animator != null) {
            if (holder.adapterPosition < headerCells) {
                outRect.top = Math.round(Utils.dpToPx((animator.headerHeight + 10).toFloat(), context))
            }
        }else{
            if (holder.adapterPosition < headerCells) {
                outRect.top = Math.round(Utils.dpToPx((offset + 10).toFloat(), context))
            }
        }
    }
}