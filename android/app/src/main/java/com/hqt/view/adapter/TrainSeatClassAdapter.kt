package com.hqt.view.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import androidx.databinding.DataBindingUtil
import androidx.databinding.ObservableField
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ListTrainSeatItemBinding
import com.hqt.data.model.TrainSeatFare
import com.hqt.view.ui.train.TrainSelectHanlder

class TrainSeatClassAdapter(var mContext: Context, internal var contents: List<TrainSeatFare>) : RecyclerView.Adapter<TrainSeatClassAdapter.ViewHolder>() {
    class ViewHolder(var context: Context, val binding: ListTrainSeatItemBinding) : RecyclerView.ViewHolder(binding.root) {
        val divisionName = ObservableField<String>("")
        fun bind(seatClass: TrainSeatFare) {

            binding.trainSeat = seatClass
            if (binding.handler == null) {
                binding.handler = TrainSelectHanlder(context)
            }
            divisionName.set(seatClass.seatClass)
            binding.executePendingBindings()
            binding.trainLogo.setOnClickListener {
                //divisionName.set(seatClass.seatClass)
            }
        }

    }

    override fun getItemCount(): Int {
        return contents.size
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {

        val layoutInflater = LayoutInflater.from(parent.context)
        val binding: ListTrainSeatItemBinding = DataBindingUtil.inflate(layoutInflater, R.layout.list_train_seat_item, parent, false)

        return ViewHolder(mContext, binding)
    }


    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(contents[position])
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getItemViewType(position: Int): Int {
        return position
    }


}