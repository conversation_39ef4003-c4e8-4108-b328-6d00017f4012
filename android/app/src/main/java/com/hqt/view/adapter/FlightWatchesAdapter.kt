package com.hqt.view.adapter

import android.R.id.button1
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.widget.PopupMenu
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.hqt.data.model.FlightWatches
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ListFlightWatchesItemBinding
import com.hqt.view.ui.flightwaches.FlightWachesList
import com.hqt.view.ui.flightwaches.FlightWachesViewActivity


class FlightWatchesAdapter(var mContext: Context, var contents: List<FlightWatches>) : RecyclerView.Adapter<FlightWatchesAdapter.ViewHolder>() {
    class ViewHolder(var context: Context, val binding: ListFlightWatchesItemBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(flightWatches: FlightWatches) {

            if (binding.viewHolder == null) {
                binding.viewHolder = this
            }
            //if (binding.flightWatches == null) {
            binding.flightWatches = flightWatches
            //}

            binding.executePendingBindings()
            binding.item.setOnClickListener {
                val i = Intent(context, FlightWachesViewActivity::class.java)
                i.putExtra("flightWatch", flightWatches)
                i.putExtra("fromList", true)
                context.startActivity(i)
                val activity = context as Activity
                activity.overridePendingTransition(R.anim.enter, R.anim.exit)
            }
        }

        fun deleteClick(view: View, id: Int) {
            val popup = PopupMenu(context, view)

            popup.menuInflater
                    .inflate(R.menu.delete, popup.menu)
            popup.setOnMenuItemClickListener { item: MenuItem ->
                when (item!!.itemId) {
                    R.id.delete -> {
                        (context as FlightWachesList).deleteItemTask(id)
                    }
                }
                true
            }
            popup.show()
        }

    }

    override fun getItemCount(): Int {
        return contents.size
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {

        val layoutInflater = LayoutInflater.from(parent.context)
        val binding: ListFlightWatchesItemBinding = DataBindingUtil.inflate(layoutInflater, R.layout.list_flight_watches_item, parent, false)
        return ViewHolder(mContext, binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(contents[position])
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getItemViewType(position: Int): Int {
        return position
    }


}