package com.hqt.view.ui.train


import android.content.Intent
import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.os.CountDownTimer
import android.os.Handler
import android.os.Looper
import android.view.View
import androidx.lifecycle.ViewModelProvider
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout.OnRefreshListener
import com.android.volley.VolleyError
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.firebase.analytics.FirebaseAnalytics
import com.hqt.data.model.Booking
import com.hqt.data.model.BookingTrain
import com.hqt.data.model.BookingV2
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.Common.showAlertDialog
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ActivityTrainBookBinding
import com.hqt.util.AppConfigs
import com.hqt.util.AppController
import com.hqt.util.SSLSendRequest
import com.hqt.util.SSLSendRequest.CallBackInterface
import com.hqt.util.Widget
import com.hqt.view.ui.BaseActivityKt
import com.hqt.view.ui.payment.NewPaymentActivity
import com.hqt.viewmodel.TrainBookingViewModel
import org.json.JSONException
import org.json.JSONObject
import java.util.*

class TrainBookingViewActivity : BaseActivityKt<ActivityTrainBookBinding>() {

    override val layoutId: Int = R.layout.activity_train_book
    var booking: BookingTrain = BookingTrain()
    private var timer: CountDownTimer? = null
    lateinit var oldBooking: Booking
    var onRefresh = false
    lateinit var viewModel: TrainBookingViewModel
    var dialogLoading: BottomSheetDialog? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        getToolbar().title = "Thông tin đặt chỗ"
        getToolbar().setNavigationIcon(R.drawable.ic_action_back_home)
        supportActionBar!!.setDisplayShowHomeEnabled(true)


        firebaseAnalytics.setCurrentScreen(this, "train_booking_view", null)
        oldBooking = intent.getSerializableExtra("BookingInfo") as Booking

        if (intent.getBooleanExtra("showLoading", false)) {
            dialogLoading = Widget.showLoading(this)
        }

        viewModel = ViewModelProvider(this).get(TrainBookingViewModel::class.java)
        viewModel.isUserSigned.value = (isUserSigned)
        getViewBindding().viewModel = viewModel
        getViewBindding().lifecycleOwner = this

        setObserveListen()
        getDBookingDetail(oldBooking)
        setClickHandlers()

    }

    private fun setObserveListen() {
        viewModel.mBooking.observe(this, androidx.lifecycle.Observer { bk ->
            booking = bk!!
            getViewBindding().viewModel = viewModel

        })
        viewModel.isInternetConneted.observe(this, androidx.lifecycle.Observer {
            getViewBindding().viewModel = viewModel
        })
        viewModel.isUserSigned.observe(this, androidx.lifecycle.Observer {
            getViewBindding().viewModel = viewModel
        })
    }

    private fun initAnalytics(booking: BookingTrain) {

        val params = Bundle()
        params.putString(FirebaseAnalytics.Param.ORIGIN, booking.departure_f.originCode)
        params.putString(FirebaseAnalytics.Param.DESTINATION, booking.departure_f.destinationCode)
        params.putString(FirebaseAnalytics.Param.START_DATE, booking.departure_f.arrivalDateTime.toString())
        params.putString(FirebaseAnalytics.Param.END_DATE, booking.departure_f.arrivalDateTime.toString())
        params.putString(FirebaseAnalytics.Param.FLIGHT_NUMBER, booking.departure_f.trainNumber)
        params.putString(FirebaseAnalytics.Param.ITEM_NAME,
            booking.departure_f.originCode + booking.departure_f.destinationCode + booking.departure_f.trainNumber)
        params.putString(FirebaseAnalytics.Param.NUMBER_OF_PASSENGERS,
            (booking.departure_f.adultCount + booking.departure_f.student + booking.departure_f.childCount + booking.departure_f.older).toString() + "")
        firebaseAnalytics.logEvent(FirebaseAnalytics.Event.CAMPAIGN_DETAILS, params)
        firebaseAnalytics.setCurrentScreen(this, "train_booking_view", null)
    }

    fun setClickHandlers() {

        getViewBindding().content.btnBookVe.setOnClickListener {
            if (booking.payment.status) { // TODO Auto-generated method stub
                if (isInternetConnected) {
                    try {
                        val i: Intent
                        i = if (AppConfigs.getInstance().config.getBoolean("open_payment_in_webview")) {
                            Intent(Intent.ACTION_VIEW)
                        } else {
                            Intent(applicationContext, NewPaymentActivity::class.java)
                        }
                        i.data = Uri.parse(booking.payment.url)
                        i.putExtra("token", booking.token)
                        i.putExtra("paymentUrl", booking.payment.url)
                        i.putExtra("orderId", booking.id)
                        startActivity(i)
                    } catch (e: JSONException) {
                        AppConfigs.logException(e)
                        e.printStackTrace()
                    }
                }
            } else {
                Widget.showHelperSheet(this, booking)
            }
        }
        getViewBindding().btnFloatBookVe.setOnClickListener { getViewBindding().content.btnBookVe.performClick() }
        getViewBindding().swipeRefreshLayout.setColorSchemeResources(R.color.green, R.color.red, R.color.google_yellow);
        getViewBindding().swipeRefreshLayout.setOnRefreshListener(object : OnRefreshListener {
            override fun onRefresh() {
                if (!onRefresh) {
                    onRefresh = true
                    reFreshBooking()
                }
            }
        })
        getViewBindding().toolbar.setOnClickListener { viewModel.isUserSigned.postValue(true) }
        

    }

    private fun reFreshBooking() {
        getViewBindding().shimmerViewContainer.visibility = View.VISIBLE
        getViewBindding().shimmerViewContainer.startShimmer()
        getViewBindding().bottomSheet.visibility = View.GONE

        Handler(Looper.getMainLooper()).postDelayed({
            getDBookingDetail(oldBooking) // getViewBindding().swipeRefreshLayout.isRefreshing = false
        }, 500)
    }

    private fun getDBookingDetail(bk: Booking) {
        val postParam = JSONObject()
        try {
            postParam.put("email", bk.contact_email)
            postParam.put("id", bk.id)
            postParam.put("token", bk.token)
        } catch (e: Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }
        val mCtx = this
        SSLSendRequest(this).POST(true, "AirLines/Bookings/Detail", postParam, object : CallBackInterface {
            override fun onSuccess(response: JSONObject, cached: Boolean) {
                try {
                    if (!response.isNull("data")) {
                        val jsonBookingTrain = response.getJSONObject("data")
                        booking = AppController.instance.gSon.fromJson(jsonBookingTrain.toString(),
                            BookingTrain::class.java)

                        viewModel.mBooking.value = booking
                        showBookingTripDetail(booking)
                        showPaxInput()
                        initAnalytics(booking)

                        Handler(Looper.getMainLooper()).postDelayed({
                            getViewBindding().shimmerViewContainer.stopShimmer()
                            getViewBindding().shimmerViewContainer.visibility = View.GONE
                            getViewBindding().bottomSheet.visibility = View.VISIBLE
                            onRefresh = false
                            getViewBindding().swipeRefreshLayout.isRefreshing = false
                        }, 800)


                    } else {
                        showAlertDialog(mCtx,
                            "Thông báo !",
                            "Không tìm thấy đơn hàng \nVui lòng liên lạc chúng tôi để được hỗ trợ",
                            false,
                            false)
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    AppConfigs.logException(e)
                    getViewBindding().swipeRefreshLayout.isRefreshing = false
                }
            }

            override fun onFail(e: VolleyError) {
                AppConfigs.Log("TG", "Error: " + e.message)

                showAlertDialog(mCtx,
                    "Thông báo !",
                    "Không tìm thấy đơn hàng \nVui lòng liên lạc chúng tôi để được hỗ trợ",
                    false,
                    false)
                getViewBindding().swipeRefreshLayout.isRefreshing = false
            }
        })
    }

    private fun showPaxInput() {
        getViewBindding().content.paxInPut.removeAllViews()

        for (i in 0 until booking.adult) {
            val pax = booking.pax_info.adult[i]
            Widget.showPaxList(true, pax, false, this, getViewBindding().content.paxInPut, false)
        }
        for (i in 0 until booking.child) {
            val pax = booking.pax_info.child[i]
            Widget.showPaxList(true, pax, false, this, getViewBindding().content.paxInPut, false)
        }
        for (i in 0 until booking.student) {
            val pax = booking.pax_info.student[i]
            Widget.showPaxList(true, pax, false, this, getViewBindding().content.paxInPut, false)
        }
        for (i in 0 until booking.older) {
            val pax = booking.pax_info.older[i]
            Widget.showPaxList(true, pax, false, this, getViewBindding().content.paxInPut, false)
        }

    }

    private fun showBookingTripDetail(booking: BookingTrain) {
        getToolbar().subtitle = ("Mã đơn hàng #" + booking.id)

        var timeLimit = Common.getDateTimeFromFormat(booking.expired_date)
        if (booking.status == "waiting_payment" && timeLimit != null && timeLimit.after(Calendar.getInstance().time)) {
            getViewBindding().content.txtTimeLimit.text = (Common.dateToString(timeLimit, "HH:mm dd/MM/yyyy"))
            val endDate = timeLimit.time
            val startTime = System.currentTimeMillis()
            if (timer == null && endDate - startTime > 0) {
                timer = object : CountDownTimer(endDate, 1000) {
                    override fun onTick(millisUntilFinished: Long) {
                        getViewBindding().header.headStatus.text = Common.convertHTML("Đợi thanh toán " + Common.getDateTextCountDown(
                            millisUntilFinished,
                            startTime) + " ⏱️")
                        getViewBindding().header.headStatus.setBackgroundColor(Color.parseColor("#ff0088cd"))
                    }

                    override fun onFinish() {
                        getViewBindding().header.headStatus.text = "Hết hạn thanh toán ⏱️"
                        getViewBindding().header.headStatus.setBackgroundColor(Color.parseColor("#B1B1B1"))
                    }
                }.start()

            } else {
                timer!!.start()
            }
        } else {
            timer?.cancel()
        }

        getViewBindding().content.tripContainer.removeAllViews()
        val trainInfo = Widget.trainTripView(booking.departure_f, this, getViewBindding().content.tripContainer, true)

        trainInfo.setOnClickListener {
            getViewBindding().content.horizalScroll.fullScroll(View.FOCUS_LEFT)
        }

        if (booking.is_round_trip) {
            getViewBindding().content.tripContainerRt.removeAllViews()
            val trainInfoRt: View = Widget.trainTripView(booking.return_f,
                this,
                getViewBindding().content.tripContainerRt,
                true)
            trainInfoRt.setOnClickListener {
                getViewBindding().content.horizalScroll.fullScroll(View.FOCUS_RIGHT)
            }
        }
    }

    override fun onSystemSettingChange(type: AppConfigs.SystemSettingType) {

        if (type == AppConfigs.SystemSettingType.INTERNET) {
            if (this::viewModel.isInitialized) {
                viewModel.updateInternetStatus(isInternetConnected)
            }
        }
        if (type == AppConfigs.SystemSettingType.USER) {
            if (this::viewModel.isInitialized) viewModel.updateUserStatus(isUserSigned)
        }
        if (type == AppConfigs.SystemSettingType.BOOKING) {
            showSnackbarMessage("Trạng thái đơn hãng đã cập nhật", R.color.green, 3000, View.TEXT_ALIGNMENT_TEXT_START)
            getDBookingDetail(oldBooking)
            try {
                if (dialogLoading != null && dialogLoading!!.isShowing) dialogLoading!!.cancel()
            } catch (e: java.lang.Exception) {
            }
        }
    }

}
