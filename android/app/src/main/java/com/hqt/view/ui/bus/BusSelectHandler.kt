package com.hqt.view.ui.bus

import android.content.Context
import android.view.View
import android.widget.Toast
import com.hqt.data.model.Train
import com.hqt.data.model.TrainSeatFare


class BusSelectHandler(val context: Context) {

    fun onLongClickFriend(view: View): Boolean {
        Toast.makeText(context, "On Long Click Listener", Toast.LENGTH_SHORT).show()
        return true
    }

    fun onSelectRoute(route: BusRoute) { //Toast.makeText(context, "Train Click" + train.trainNumber, Toast.LENGTH_SHORT).show()
        (context as BusSelectActivity).selectRoute(route)
    }

    fun onSelectSeat(seat: BusRoute) {

        //        if (seat.seatCount <= 0) {
        //            Toast.makeText(context, "Vé " + seat.seatClassName + " đã hết chỗ !", Toast.LENGTH_SHORT).show()
        //        } else {
        //            (context as BusSelectActivity).selectRoute(seat)
        //        }


    }
}