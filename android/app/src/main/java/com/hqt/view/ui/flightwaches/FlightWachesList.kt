package com.hqt.view.ui.flightwaches

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.android.volley.VolleyError
import com.google.gson.reflect.TypeToken
import com.hqt.data.model.FlightWatches
import com.hqt.data.model.Train
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ActivityFlightWatchesListBinding
import com.hqt.util.AppConfigs
import com.hqt.util.AppController
import com.hqt.util.SSLSendRequest
import com.hqt.view.adapter.FlightWatchesAdapter
import com.hqt.view.adapter.TrainAdapter
import com.hqt.view.ui.BaseActivityKt
import com.hqt.view.ui.HomeActivity
import org.json.JSONException
import org.json.JSONObject
import java.util.ArrayList

class FlightWachesList : BaseActivityKt<ActivityFlightWatchesListBinding>() {

    override val layoutId: Int = R.layout.activity_flight_watches_list
    lateinit var recyclerView: RecyclerView
    private lateinit var mAdapter: FlightWatchesAdapter
    private var arraylistFlightWatches: ArrayList<FlightWatches> = ArrayList()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        firebaseAnalytics.setCurrentScreen(this, "flight_watches_list", null)
        recyclerView = getViewBindding().recyclerView
        recyclerView.setHasFixedSize(true)

        getToolbar().title = "Thông báo giá vé"
        getToolbar().setNavigationIcon(R.drawable.ic_action_back_home)
        supportActionBar!!.setDisplayShowHomeEnabled(true)

        recyclerView.layoutManager = LinearLayoutManager(applicationContext)
        mAdapter = FlightWatchesAdapter(this, arraylistFlightWatches)
        recyclerView.adapter = mAdapter

        initBinddingClick()
    }

    private fun initBinddingClick() {

        getViewBindding().btnLogin.setOnClickListener {
            signIn()
        }
        getViewBindding().btnAddFlightWatches.setOnClickListener {
            if (isUserSigned) getViewBindding().btnNewFlightWatches.performClick() else signIn()
        }
        getViewBindding().btnNewFlightWatches.setOnClickListener {

            if (isUserSigned) startActivity(Intent(this, NewFlightWatchesActivity::class.java)) else signIn()
        }
    }

    private fun getListTask() {

        var request = JSONObject()
        try {
            request.put("uid", firebaseUser!!.uid)
            request.put("email", firebaseUser!!.email)
            request.put("id", -1)

        } catch (e: JSONException) {
            e.printStackTrace()
            AppConfigs.logException(e)
        }

        SSLSendRequest(this).POST(false, "FlightWatches/List", request, object : SSLSendRequest.CallBackInterface {
            override fun onSuccess(response: JSONObject, cached: Boolean) {

                if (!response.isNull("data") && response.getJSONArray("data").length() > 0) {
                    processListData(response)
                } else {
                    getViewBindding().shimmerViewContainer.visibility = View.GONE
                    getViewBindding().notfound.visibility = View.VISIBLE
                }
            }

            override fun onFail(er: VolleyError) {
                Common.showAlertDialog(this@FlightWachesList,
                    "Thông báo",
                    "Không tìm thấy chuyến đi phù hợp! Vui lòng chọn lại chuyến đi khác",
                    true,
                    false)
            }
        })
    }

    fun processListData(json: JSONObject) {
        val listType = object : TypeToken<ArrayList<FlightWatches>>() {}.type
        var watchesList = AppController.instance.gSon.fromJson<List<FlightWatches>>(json.getJSONArray("data")
            .toString(), listType)

        recyclerView.removeAllViews()
        arraylistFlightWatches.clear()
        arraylistFlightWatches.addAll(watchesList)

        mAdapter.notifyDataSetChanged()

        getViewBindding().shimmerViewContainer.stopShimmer()
        getViewBindding().shimmerViewContainer.visibility = View.GONE
        getViewBindding().notfound.visibility = View.GONE
        getViewBindding().bottomView.visibility = View.VISIBLE

        if (watchesList.isEmpty()) {
            Common.showAlertDialog(this, "Thông báo", "Chưa có thông báo giá. Vui lòng tạo", true, false)
        }
    }

    override fun onBackPressed() {
        if (this.isTaskRoot) {
            val `in` = Intent(this, HomeActivity::class.java)
            startActivity(`in`)
            finish()
        } else {
            super.onBackPressed()
        }
    }

    override fun onResume() {

        if (isUserSigned) {
            getListTask()
        } else {
            getViewBindding().shimmerViewContainer.visibility = View.GONE
            getViewBindding().notfound.visibility = View.VISIBLE
        }
        super.onResume()

    }

    override fun onSystemSettingChange(type: AppConfigs.SystemSettingType) {
        if (type == AppConfigs.SystemSettingType.USER) {
            getListTask()
        }
    }

    fun deleteItemTask(id: Int) {

        val request = JSONObject()
        try {
            request.put("uid", firebaseUser!!.uid)
            request.put("email", firebaseUser!!.email)
            request.put("id", id)

        } catch (e: JSONException) {
            e.printStackTrace()
            AppConfigs.logException(e)
        }

        showSnackbarMessage("Đang xóa thông báo giá", R.color.red, 1000, View.TEXT_ALIGNMENT_TEXT_START)

        SSLSendRequest(this).POST(false, "FlightWatches/Delete", request, object : SSLSendRequest.CallBackInterface {
            override fun onSuccess(response: JSONObject, cached: Boolean) {
                getListTask()
            }

            override fun onFail(er: VolleyError) {
                Common.showAlertDialog(this@FlightWachesList,
                    "Thông báo",
                    "Không tìm thấy dữ liệu! Vui lòng thực hiện lại",
                    true,
                    false)
            }
        })
    }
}
