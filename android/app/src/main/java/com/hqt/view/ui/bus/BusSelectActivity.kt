package com.hqt.view.ui.bus

import android.content.Context
import android.os.Bundle
import android.widget.Toast

import com.hqt.data.model.BookingBus
import com.hqt.util.AppConfigs
import com.hqt.util.AppController
import com.hqt.util.helper.NonSwipeableViewPager
import com.hqt.view.adapter.ViewPagerAdapter
import com.hqt.datvemaybay.R
import com.hqt.data.model.request.BusSearchRequest
import com.hqt.data.model.response.BusSeatMapInfo
import com.hqt.data.model.response.StationPoints
import com.hqt.datvemaybay.databinding.*
import com.hqt.view.ui.*
import com.hqt.view.ui.seatmap.SeatView
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import org.json.JSONObject


class BusSelectActivity : BaseActivityKt<ActivityBusSelectAcvitityBinding>() {
    private lateinit var viewPager: NonSwipeableViewPager
    lateinit var adapter: ViewPagerAdapter
    override val layoutId: Int = R.layout.activity_bus_select_acvitity
    private var disposable: Disposable? = null
    private var tripDetail: JSONObject = JSONObject()
    private var departureData: ArrayList<BusRoute> = ArrayList()
    lateinit var mContext: Context
    var booking: BookingBus = BookingBus()
    private var seatViewList: ArrayList<SeatView> = ArrayList()
    private var busRoute: BusRoute? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        try {
            super.onCreate(savedInstanceState)
            viewPager = findViewById(R.id.viewpager)

            settingUpBooking()
            mContext = this

            firebaseAnalytics.setCurrentScreen(this, "bus_booking_result", null)

            viewPager.setOnClickListener {
                Toast.makeText(this, "viewPager Select", Toast.LENGTH_SHORT).show()
            }

            setupViewPager(viewPager)
            getBusTask()

        } catch (e: Exception) {
            AppConfigs.logException(e)
            finish()
        }

    }

    private fun setupViewPager(viewPager: NonSwipeableViewPager) {

        adapter = ViewPagerAdapter(supportFragmentManager)
        adapter.addFragment(BusRouteListFragment(false), "SelectRouteFragment")
        adapter.addFragment(BusSeatMapFragment(), "SeatMapFragment")
        adapter.addFragment(BusPointSelectFragment(), "BusPointSelectFragment")
        adapter.addFragment(BusBookingFragment(), "BusBookingFragment")

        viewPager.adapter = adapter
        viewPager.offscreenPageLimit = 4
    }

    private fun getBusTask() {


        var request = BusSearchRequest()
        request.departureDate = booking.departure_date
        request.adultCount = booking.adult
        request.childCount = booking.child


        AppConfigs.Log("request", request.departureDate)

        disposable = AppController.instance.getService()
            .getBusRoute(booking.origin_code, booking.destination_code, request).subscribeOn(Schedulers.io())
            .doOnSubscribe {

            }.observeOn(AndroidSchedulers.mainThread()).subscribe({ response ->

                if (response.data != null) {

                    departureData = response.data
                    genBusRouteList(response.data)
                } else {

                }

            }, { throwable ->
                throwable.printStackTrace()
            })

    }

    private fun settingUpBooking() {

        booking.origin_code = intent.getIntExtra("originCode", 0).toString()
        booking.destination_code = intent.getIntExtra("destinationCode", 0).toString()
        booking.departure_date = intent.getStringExtra("departureTime").toString()
        booking.return_date = intent.getStringExtra("returnTime").toString()
        booking.is_round_trip = intent.getBooleanExtra("isRoundTrip", false)

    }


    private fun genBusRouteList(busRoute: ArrayList<BusRoute>) { //
        viewPager.currentItem = 0
        val selectRouteFragment = viewPager.adapter!!.instantiateItem(viewPager,
            viewPager.currentItem) as BusRouteListFragment
        selectRouteFragment.genListRoute(busRoute)
    }

    fun showStationPointSelect(mSeatViewList: ArrayList<SeatView>, seatMapInfo: BusSeatMapInfo) {
        seatViewList = mSeatViewList
        viewPager.currentItem = 2
        val selectRouteFragment = viewPager.adapter!!.instantiateItem(viewPager,
            viewPager.currentItem) as BusPointSelectFragment

        selectRouteFragment.reinit(seatMapInfo, true)

    }

    fun clearStationPointSelect(stationPoints: StationPoints) {
        viewPager.currentItem = 2
        val selectRouteFragment = viewPager.adapter!!.instantiateItem(viewPager,
            viewPager.currentItem) as BusPointSelectFragment
        selectRouteFragment.clearSelectPoint(stationPoints)
    }

    fun selectRoute(mBusRoute: BusRoute) { //
        viewPager.currentItem = 1
        val selectRouteFragment = viewPager.adapter!!.instantiateItem(viewPager,
            viewPager.currentItem) as BusSeatMapFragment
        selectRouteFragment.getSeatMap(mBusRoute)
        busRoute = mBusRoute
    }

    fun nextBookingInput(mPickUpPoint: StationPoints, mDropOffPoints: StationPoints) { //
        viewPager.currentItem = 3
        val selectRouteFragment = viewPager.adapter!!.instantiateItem(viewPager,
            viewPager.currentItem) as BusBookingFragment

        selectRouteFragment.initBusData(busRoute!!, seatViewList, mPickUpPoint, mDropOffPoints)
    }

    fun getFragment(): BusSeatMapFragment {

        return supportFragmentManager.fragments[1] as BusSeatMapFragment;
    }

    override fun onBackPressed() {
        if (viewPager.currentItem == 0) {
            super.onBackPressed()
        } else {
            viewPager.currentItem = viewPager.currentItem - 1
        }
    }

    fun backClick() {
        if (viewPager.currentItem == 0) {
            super.onBackPressed()
        } else {
            viewPager.currentItem = viewPager.currentItem - 1
        }
    }

    override fun onSystemSettingChange(type: AppConfigs.SystemSettingType) {

        val bookingFragment = viewPager.adapter!!.instantiateItem(viewPager, 3) as BusBookingFragment
        bookingFragment.onSystemSettingChange(type)

    }


}
