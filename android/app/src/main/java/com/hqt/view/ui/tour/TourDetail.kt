package com.hqt.view.ui.tour

import com.google.gson.annotations.SerializedName
import com.hqt.util.AppConfigs


data class TourDetail(

    @SerializedName("id") var id: Int? = null,

    @SerializedName("tour_info") var tourInfo: TourInfo? = TourInfo(),
    @SerializedName("departure_dates") var departureDates: ArrayList<DepartureDates> = arrayListOf(),
    @SerializedName("photos") var photos: ArrayList<Photos> = arrayListOf(),
    @SerializedName("plans") var plans: ArrayList<Plans> = arrayListOf(),
    @SerializedName("note") var note: String? = null,
    @SerializedName("title") var title: String? = null,
    @SerializedName("start_locations") var startLocations: ArrayList<TourLocation> = arrayListOf(),
    @SerializedName("to_locations") var toLocations: ArrayList<TourLocation> = arrayListOf()

) {}

data class TourInfo(

    @SerializedName("duration") var duration: String? = null,
    @SerializedName("flight_tour") var flightTour: String? = null,
    @SerializedName("hotel_star") var hotelStar: String? = null,
    @SerializedName("departure_date") var departureDate: String? = null,
    @SerializedName("departure_location") var departureLocation: String? = null,
    @SerializedName("num_seats") var numSeats: Int? = null,
    @SerializedName("price") var price: Int? = null

) {

}

data class DepartureDates(

    @SerializedName("id") var id: Int? = null,
    @SerializedName("tour_id") var tourId: Int? = null,
    @SerializedName("estimate_price") var estimatePrice: Int? = null,
    @SerializedName("child_price") var childPrice: Int? = null,
    @SerializedName("baby_price") var babyPrice: Int? = null,
    @SerializedName("from_date") var fromDate: String? = null,
    @SerializedName("from_date_string") var fromDateString: String? = null,
    @SerializedName("book_seat") var bookSeat: Int? = null,
    @SerializedName("number_guest") var numberGuest: Int? = null,
    @SerializedName("departure_date") var departureDate: String? = null,
    @SerializedName("return_date") var returnDate: String? = null,
    @SerializedName("created_at") var createdAt: String? = null,
    @SerializedName("updated_at") var updatedAt: String? = null,
    @SerializedName("estimate_price_vat") var estimatePriceVat: Int? = null,
    @SerializedName("child_price_vat") var childPriceVat: Int? = null,
    @SerializedName("baby_price_vat") var babyPriceVat: Int? = null

)

data class Photos(

    @SerializedName("id") var id: Int? = null,
    @SerializedName("name") var name: String? = null,
    @SerializedName("path") var path: String? = null,
    @SerializedName("tour_id") var tourId: Int? = null,
    @SerializedName("created_at") var createdAt: String? = null,
    @SerializedName("updated_at") var updatedAt: String? = null,
    @SerializedName("deleted_at") var deletedAt: String? = null,
    @SerializedName("type_photo") var typePhoto: Int? = null

)

data class Plans(

    @SerializedName("id") var id: Int? = null,
    @SerializedName("title") var title: String? = null,
    @SerializedName("description") var description: String? = null,
    @SerializedName("transfer_id") var transferId: Int? = null,
    @SerializedName("is_breakfast") var isBreakfast: Int? = null,
    @SerializedName("is_lunch") var isLunch: Int? = null,
    @SerializedName("is_dinner") var isDinner: Int? = null,
    @SerializedName("tour_id") var tourId: Int? = null,
    @SerializedName("thumbnail") var thumbnail: String? = null,
    @SerializedName("is_midle_night") var isMidleNight: Int? = null,
    @SerializedName("time_title") var timeTitle: String? = null

) {
    fun hasThumbnail(): Boolean {

        if (thumbnail == null) {
            return false
        } else {
            if (thumbnail!!.length < 27) {
                return false
            }
        }
        return true
    }
}

data class TourLocation(

    @SerializedName("id") var id: Int? = null,
    @SerializedName("name") var name: String? = null,
    @SerializedName("code") var code: String? = null,
    @SerializedName("created_at") var createdAt: String? = null,
    @SerializedName("updated_at") var updatedAt: String? = null,
    @SerializedName("provinceid") var provinceid: String? = null,
    @SerializedName("type") var type: String? = null,
    @SerializedName("is_start_location") var isStartLocation: Int? = null,
    @SerializedName("identify") var identify: String? = null,
    @SerializedName("type_location") var typeLocation: String? = null,

    )
