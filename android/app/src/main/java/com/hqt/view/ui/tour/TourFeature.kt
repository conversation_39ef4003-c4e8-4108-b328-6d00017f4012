package com.hqt.view.ui.tour

import com.google.gson.annotations.SerializedName
import com.hqt.datvemaybay.Common
import com.mikepenz.iconics.IconicsDrawable
import com.mikepenz.iconics.typeface.IIcon
import java.io.Serializable


data class TourList(

    @SerializedName("title") var title: String? = null,
    @SerializedName("style") var style: String? = null,
    @SerializedName("link") var link: String? = null,
    @SerializedName("sub") var sub: ArrayList<SubCat>? = arrayListOf(),
    @SerializedName("tours") var tours: ArrayList<TourItem> = arrayListOf()

) : Serializable {}

data class SubCat(

    @SerializedName("text") var text: String? = null, @SerializedName("link") var link: String? = null

) : Serializable


data class TourBooking(

    @SerializedName("item") var item: TourItem? = null,
    @SerializedName("tourInfo") var tourInfo: TourInfo? = null,
    @SerializedName("departureDate") var departureDate: DepartureDates? = null,
    @SerializedName("start_locations") var startLocations: ArrayList<TourLocation> = arrayListOf(),
    @SerializedName("to_locations") var toLocations: ArrayList<TourLocation> = arrayListOf()


) : Serializable {

}

data class TourItem(

    @SerializedName("id") var id: String? = null,
    @SerializedName("link") var link: String? = null,
    @SerializedName("title") var title: String? = null,
    @SerializedName("image") var image: String? = null,
    @SerializedName("extra") var extra: String? = null,
    @SerializedName("info") var info: ArrayList<TourSubInfo> = arrayListOf(),
    @SerializedName("price") var price: Int = 0,
    @SerializedName("oldPrice") var oldPrice: Int = 0

) : Serializable {
    fun getTourPriceString(): String {
        if (price == null || price == 0) {
            return "Liên hệ"
        }
        return Common.dinhDangTien(price)
    }

    fun isShowExtra(): Boolean {
        if (extra == null || extra!!.length < 3) {
            return false
        }
        return true
    }
}

data class TourSubInfo(

    @SerializedName("icon") var icon: String? = null, @SerializedName("title") var title: String? = null

) : Serializable {

    fun getSubIcon(): String {

        if (icon == null) {
            return "faw_dot-circle"
        }
        return "faw_" + icon!!.replace("fa-", "").replace("clock-o", "clock")

    }
}

