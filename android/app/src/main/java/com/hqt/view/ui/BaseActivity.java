package com.hqt.view.ui;

import android.app.Activity;
import android.app.ProgressDialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Color;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.VibrationEffect;
import android.os.Vibrator;
import android.view.Gravity;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.result.ActivityResultCallback;
import androidx.activity.result.ActivityResultLauncher;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RawRes;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.airbnb.lottie.LottieAnimationView;
import com.bumptech.glide.Glide;
import com.firebase.ui.auth.AuthUI;
import com.firebase.ui.auth.ErrorCodes;
import com.firebase.ui.auth.FirebaseAuthUIActivityResultContract;
import com.firebase.ui.auth.IdpResponse;
import com.firebase.ui.auth.data.model.FirebaseAuthUIAuthenticationResult;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;
import com.google.android.material.snackbar.Snackbar;
import com.google.firebase.analytics.FirebaseAnalytics;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseUser;
import com.hqt.datvemaybay.Common;
import com.hqt.datvemaybay.R;
import com.hqt.datvemaybay.ThanhToan;
import com.hqt.util.AppConfigs;
import com.hqt.util.AppController;
import com.hqt.util.Log;
import com.hqt.util.TypefaceUtil;
import com.hqt.view.ui.account.LoginActivity;
import com.hqt.view.ui.search.ui.activity.SearchActivityV2;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;


/**
 * <p>
 * BaseActivity contains some modifications to the native AppCompatActivity.
 * Mainly, it use ButterKnife for view binding and it automatically check if
 * toolbar exists.
 * </p>
 */
public abstract class BaseActivity extends AppCompatActivity {

    private Toolbar mToolbar;
    private FirebaseAnalytics mFirebaseAnalytics;
    private boolean internetConnected = true;
    public static int TYPE_WIFI = 1;
    public static int TYPE_MOBILE = 2;
    public static int TYPE_NOT_CONNECTED = 0;
    private Snackbar snackbar;
    private ProgressDialog mProgressDialog;
    private FirebaseAuth auth;
    private FirebaseAuth.AuthStateListener mAuthListener;
    private static final int RC_SIGN_IN_UI = 123;
    private static boolean isCurentUserLogin = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {


        super.onCreate(savedInstanceState);

        Log.d("className", this.getClass().getName());
        TypefaceUtil.overrideFont(getApplicationContext(), "SERIF", "fonts/Roboto-Regular.ttf");
        setContentView(getLayoutId());

        transparentStatus();
        setupToolbar();
        initView();
        checkInternetConnection(this);
        firebaseAuthInit();
        isCurentUserLogin = isUserSigned();
    }


    public void firebaseAuthInit() {
        try {


            mFirebaseAnalytics = FirebaseAnalytics.getInstance(this);
            auth = FirebaseAuth.getInstance();

            mAuthListener = new FirebaseAuth.AuthStateListener() {
                @Override
                public void onAuthStateChanged(@NonNull FirebaseAuth firebaseAuth) {
                    FirebaseUser user = firebaseAuth.getCurrentUser();
                    if (user != null) {
                        isCurentUserLogin = true;
                        Common.commonSave(getApplicationContext(), user.getDisplayName(), user.getEmail(), user.getUid(), (user.getPhotoUrl() == null ? "" : user.getPhotoUrl().toString()), "");
                    } else {
                        // User is signed out
                        AppConfigs.Log("onAuthStateChanged", "onAuthStateChanged:signed_out");
                    }
                }
            };
        } catch (Exception e) {
            AppConfigs.logException(e);
        }
    }

    public boolean isUserSigned() {
        return auth.getCurrentUser() != null;
    }


    public FirebaseUser getFirebaseUser() {

        return auth.getCurrentUser();

    }


    public void initView() {
    }

    /**
     * Its common use a toolbar within activity, if it exists in the
     * layout this will be configured
     */
    public void setupToolbar() {
        mToolbar = findViewById(R.id.toolbar);
        if (mToolbar != null) {
            mToolbar.setTitle("12BAY.VN");
            setSupportActionBar(mToolbar);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            mToolbar.bringToFront();
            mToolbar.inflateMenu(R.menu.main);
            mToolbar.setNavigationOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {

                    if (isTaskRoot()) {
                        Intent in = new Intent(getApplicationContext(), HomeActivity.class);
                        startActivity(in);
                        finish();
                    } else {
                        onBackPressed();
                    }

                }
            });
        }

    }

    public void getUpTrackingClick() {
        try {
            Intent intentFrom = getIntent();
            if (intentFrom.hasExtra("track")) {
                String trackingCode = getIntent().getStringExtra("track");
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
                String current = LocalDateTime.now().format(formatter);

                AppConfigs.setStringLocalCache(this, "track_code_$current", trackingCode);
                AppController.getInstance().setTrackingCode(trackingCode);

            }
        } catch (Exception e) {
            e.printStackTrace();
            AppConfigs.logException(e);
        }
    }

    public LinearLayout initEmptyState(String textString, int background, @RawRes int animationResId, EmptyStateCallBackInterface callBackInterface) {
        LinearLayout emptyStateLayout = findViewById(R.id.emptyStateLayout);
        try {
            LottieAnimationView animationView = findViewById(R.id.animation_view);
            ImageView emptyStateBackground = findViewById(R.id.emptyStateBackground);
            if (animationResId != -1) {
                animationView.setAnimation(animationResId);
                animationView.loop(true);
                animationView.playAnimation();
                animationView.setVisibility(View.VISIBLE);
                emptyStateBackground.setVisibility(View.GONE);
            } else {
                Glide.with(this).load(background).into(emptyStateBackground);
                emptyStateBackground.setVisibility(View.VISIBLE);
                animationView.setVisibility(View.GONE);
            }


            emptyStateLayout.setVisibility(View.GONE);
            TextView emptyStateTitle = findViewById(R.id.emptyStateTitle);
            emptyStateTitle.setText(Common.convertHTML(textString));


            Button btnNegative = findViewById(R.id.btnNegative);
            Button btnPositive = findViewById(R.id.btnPositive);

            callBackInterface.negativeButton(btnNegative);
            callBackInterface.positiveButton(btnPositive);
        } catch (Exception e) {
            AppConfigs.logException(e);
        }

        return emptyStateLayout;

    }

    private void transparentStatus() {
        //make full transparent statusBar
        if (Build.VERSION.SDK_INT >= 19 && Build.VERSION.SDK_INT < 21) {
            setWindowFlag(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS, true);
        }

        if (Build.VERSION.SDK_INT >= 21) {
            setWindowFlag(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS, false);
            getWindow().setStatusBarColor(Color.TRANSPARENT);
        }

    }

    private void setWindowFlag(final int bits, boolean on) {
        Window win = getWindow();
        WindowManager.LayoutParams winParams = win.getAttributes();
        if (on) {
            winParams.flags |= bits;
        } else {
            winParams.flags &= ~bits;
        }
        win.setAttributes(winParams);
    }


    @Nullable
    public Toolbar getToolbar() {
        return mToolbar;
    }

    public FirebaseAnalytics getFirebaseAnalytics() {
        return mFirebaseAnalytics;
    }

    @Override
    protected void onResume() {
        super.onResume();
        registerInternetCheckReceiver();
        getUpTrackingClick();
        if (isCurentUserLogin == false) {
            if (isUserSigned()) {
                refreshLayout();
            }
        }

    }

    @Override
    protected void onPause() {
        super.onPause();
        unregisterReceiver(broadcastReceiver);
    }

    /**
     * @return The layout id that's gonna be the activity view.
     */
    protected abstract int getLayoutId();

    /**
     * Method to register runtime broadcast receiver to show snackbar alert for internet connection..
     */
    private void registerInternetCheckReceiver() {
        IntentFilter internetFilter = new IntentFilter();
        internetFilter.addAction("android.net.wifi.STATE_CHANGE");
        internetFilter.addAction("android.net.conn.CONNECTIVITY_CHANGE");
        registerReceiver(broadcastReceiver, internetFilter);
    }

    /**
     * Runtime Broadcast receiver inner class to capture internet connectivity events
     */
    public BroadcastReceiver broadcastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String status = getConnectivityStatusString(context);
            setSnackbarMessage(status, false);
        }
    };

    public void checkInternetConnection(Context context) {
        String status = getConnectivityStatusString(context);
        setSnackbarMessage(status, false);
    }

    public static int getConnectivityStatus(Context context) {
        ConnectivityManager cm = (ConnectivityManager) context
                .getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
        if (null != activeNetwork) {
            if (activeNetwork.getType() == ConnectivityManager.TYPE_WIFI)
                return TYPE_WIFI;

            if (activeNetwork.getType() == ConnectivityManager.TYPE_ETHERNET)
                return TYPE_WIFI;

            if (activeNetwork.getType() == ConnectivityManager.TYPE_MOBILE)
                return TYPE_MOBILE;
        }
        return TYPE_NOT_CONNECTED;
    }

    public static String getConnectivityStatusString(Context context) {
        int conn = getConnectivityStatus(context);
        String status = null;
        if (conn == TYPE_WIFI) {
            status = "Wifi enabled";
        } else if (conn == TYPE_MOBILE) {
            status = "Mobile data enabled";
        } else if (conn == TYPE_NOT_CONNECTED) {
            status = "Not connected to Internet";
        }
        return status;
    }

    public void setSnackbarMessage(String status, boolean showBar) {
        try {
            String internetStatus = "";
            if (status.equalsIgnoreCase("Wifi enabled") || status.equalsIgnoreCase("Mobile data enabled")) {
                internetStatus = "Đã kết nối internet";
            } else {
                internetStatus = "Mất kết nối internet";
            }
            //android.R.id.content
            if (findViewById(R.id.viewSnack) != null) {
                snackbar = Snackbar.make(findViewById(R.id.viewSnack), internetStatus, Snackbar.LENGTH_LONG);
            } else {
                snackbar = Snackbar.make(findViewById(android.R.id.content), internetStatus, Snackbar.LENGTH_LONG);
            }

            snackbar.setActionTextColor(Color.WHITE);
            View sbView = snackbar.getView();
            TextView textView = sbView.findViewById(com.google.android.material.R.id.snackbar_text);
            textView.setTextColor(Color.WHITE);
            textView.setTextAlignment(View.TEXT_ALIGNMENT_CENTER);
            textView.setGravity(Gravity.CENTER_HORIZONTAL);

            final FrameLayout snackBarView = (FrameLayout) snackbar.getView();
            final FrameLayout.LayoutParams params = (FrameLayout.LayoutParams) snackBarView.getChildAt(0).getLayoutParams();

            params.setMargins(params.leftMargin, -24, params.rightMargin, -24);
            snackBarView.getChildAt(0).setLayoutParams(params);

            if (internetStatus.equalsIgnoreCase("Mất kết nối internet")) {
                if (internetConnected) {
                    snackbar.setDuration(10000);
                    snackbar.show();
                    internetConnected = false;
                }
            } else {
                if (!internetConnected) {
                    if (snackbar.isShown()) snackbar.dismiss();
                    sbView.setBackground(getResources().getDrawable(R.color.green));
                    internetConnected = true;
                    snackbar.setDuration(2000);
                    snackbar.show();
                    new Handler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            //RELOAD ACTIVITY WHEN INTERNET BACK
                            refreshLayout();
                            // recreate();
                        }
                    }, 1100);

                }
            }
        } catch (Exception e) {
            AppConfigs.logException(e);
        }
    }

    public boolean isInternetConnected() {
        return internetConnected;
    }


    public void showProgressDialog(boolean cancelable, boolean canceledOnTouchOutside) {
        if (mProgressDialog != null) {
            mProgressDialog.setCanceledOnTouchOutside(canceledOnTouchOutside);
            mProgressDialog.setCancelable(cancelable);
        } else {
            showProgressDialog("Đang tải dữ liệu", false, false);
        }
    }

    public void showProgressDialog(String text, boolean cancelable, boolean canceledOnTouchOutside) {
        if (isFinishing()) {
            return;
        }
        if (mProgressDialog == null) {
            mProgressDialog = new ProgressDialog(this);
            mProgressDialog.setMessage(text);
            mProgressDialog.setIndeterminate(false);
            mProgressDialog.setMax(100);
            mProgressDialog.setCanceledOnTouchOutside(canceledOnTouchOutside);
            mProgressDialog.setCancelable(cancelable);
            mProgressDialog.setProgressStyle(ProgressDialog.STYLE_SPINNER);
            mProgressDialog.show();
        } else {
            mProgressDialog.setMessage(text);
            mProgressDialog.show();
        }
    }

    public void dismissProgressDialog() {
        if (mProgressDialog != null && mProgressDialog.isShowing()) {
            mProgressDialog.dismiss();
        }
    }

    /**
     * Open Login auth user {@link
     * #signIn()}
     */
    public void signIn() {


        if (auth.getCurrentUser() == null) {
            var loginActivity = new Intent(getApplicationContext(), LoginActivity.class);
            loginActivity.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            startActivity(loginActivity);
        } else {
            Toast.makeText(this, "Bạn đã đăng nhập rồi", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * Open Sigout auth user {@link
     * #signOut()}
     */
    private void onSignInResult(FirebaseAuthUIAuthenticationResult result) {
        AppConfigs.Log("login login ", result.toString());
        IdpResponse response = result.getIdpResponse();


        if (result.getResultCode() == RESULT_OK) {
            // Successfully signed in

            FirebaseUser user = FirebaseAuth.getInstance().getCurrentUser();
            Toast.makeText(getApplicationContext(), "Chào  " + user.getDisplayName(), Toast.LENGTH_SHORT).show();
            refreshLayout();

            // ...
        } else {
            if (response != null && response.getError().getErrorCode() == ErrorCodes.ANONYMOUS_UPGRADE_MERGE_CONFLICT) {
                Toast.makeText(getApplicationContext(), "Tài khoản đã tồn tại. Vui lòng chọn phương thức khác để đăng nhập lại", Toast.LENGTH_SHORT).show();
            }
            // Sign in failed. If response is null the user canceled the
            // sign-in flow using the back button. Otherwise check
            // response.getError().getErrorCode() and handle the error.
            // ...
        }
    }

    private final ActivityResultLauncher<Intent> signInLauncher = registerForActivityResult(
            new FirebaseAuthUIActivityResultContract(),
            new ActivityResultCallback<FirebaseAuthUIAuthenticationResult>() {
                @Override
                public void onActivityResult(FirebaseAuthUIAuthenticationResult result) {
                    onSignInResult(result);
                }
            }
    );

    public void signOut() {
        if (auth.getCurrentUser() != null) {
            AuthUI.getInstance()
                    .signOut(this)
                    .addOnCompleteListener(new OnCompleteListener<Void>() {
                        public void onComplete(@NonNull Task<Void> task) {
                            Toast.makeText(getApplicationContext(), "Đăng xuất thành công", Toast.LENGTH_SHORT).show();
                            refreshLayout();

                        }
                    });
            FirebaseAuth.getInstance().signOut();
        }
    }

    public void refreshLayout() {
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == RC_SIGN_IN_UI) {
            IdpResponse response = IdpResponse.fromResultIntent(data);
            if (resultCode == RESULT_OK) {
                FirebaseUser user = FirebaseAuth.getInstance().getCurrentUser();
                Toast.makeText(getApplicationContext(), "Chào  " + user.getDisplayName(), Toast.LENGTH_SHORT).show();
                refreshLayout();
            } else {
                Toast.makeText(this,
                        "Đăng nhập không thành công. Vui lòng thử lại với phương thức khác",
                        Toast.LENGTH_SHORT).show();
            }
        }
    }

    @Override
    public void onStart() {
        super.onStart();
        LocalBroadcastManager.getInstance(this).registerReceiver(mMessageReceiver, new IntentFilter("bookingupdate"));
    }

    @Override
    public void onStop() {
        super.onStop();
        LocalBroadcastManager.getInstance(this).unregisterReceiver(mMessageReceiver);

    }

    private BroadcastReceiver mMessageReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            // Get extra data included in the Intent
            setmMessageReceiverAction();
        }
    };

    public void setmMessageReceiverAction() {

    }

    public interface EmptyStateCallBackInterface {
        public void negativeButton(Button button);

        public void positiveButton(Button button);
    }

    public void hideKeyBoard(View view) {
        InputMethodManager imm = (InputMethodManager) getSystemService(Activity.INPUT_METHOD_SERVICE);
        imm.hideSoftInputFromWindow(view.getWindowToken(), 0);

    }

    public void shakeItBaby() {
        long[] mVibratePattern = new long[]{0, 30, 100, 20, 100, 10, 100, 5};
        if (Build.VERSION.SDK_INT >= 26) {
            ((Vibrator) getSystemService(VIBRATOR_SERVICE)).vibrate(VibrationEffect.createWaveform(mVibratePattern, VibrationEffect.DEFAULT_AMPLITUDE));

        } else {
            ((Vibrator) getSystemService(VIBRATOR_SERVICE)).vibrate(mVibratePattern, -1);
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        MenuInflater inflater = getMenuInflater();
        (inflater).inflate(R.menu.main, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        // Take appropriate action for each action item click

        int itemId = item.getItemId();
        if (itemId == android.R.id.home) {
            return true;
        } else if (itemId == R.id.action_search) {
//            Intent in = new Intent(this, SearchActivity.class);
            Intent in = new Intent(this, SearchActivityV2.class);
            startActivity(in);
            return true;
        } else if (itemId == R.id.thanhToan) {
            Intent in = new Intent(this, ThanhToan.class);
            startActivity(in);
            return true;
        }


        return false;
    }
}