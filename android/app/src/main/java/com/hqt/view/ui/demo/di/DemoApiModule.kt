package  com.hqt.view.ui.demo.di

import com.hqt.view.ui.demo.data.api.DemoService
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
class DemoApiModule {

    @Provides
    @Singleton
    fun provideDemoService(retrofit: Retrofit): DemoService {
        return retrofit.create(DemoService::class.java)
    }



}