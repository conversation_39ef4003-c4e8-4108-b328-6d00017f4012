package com.hqt.view.ui.seatmap.ui.fragment

import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.widget.Toast
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import com.google.android.flexbox.FlexboxLayout
import com.google.android.material.behavior.HideBottomViewOnScrollBehavior
import com.hqt.base.BaseFragment
import com.hqt.base.model.State
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.FragmentSelectSeatLayoutBinding
import com.hqt.datvemaybay.databinding.SeatGroupTitleViewBinding
import com.hqt.datvemaybay.databinding.SeatViewTitleItemBinding
import com.hqt.util.AppConfigs
import com.hqt.view.ui.seatmap.SeatGroup
import com.hqt.view.ui.seatmap.SeatIdentifierTitle
import com.hqt.view.ui.seatmap.SeatOption
import com.hqt.view.ui.seatmap.ui.SeatManagerViewModel
import com.hqt.view.ui.seatmap.ui.SeatViewModel
import com.hqt.view.ui.seatmap.ui.activity.SelectSeatActivityV2.Companion.doneClick
import com.hqt.view.ui.seatmap.ui.view.SeatViewV2
import dagger.hilt.android.AndroidEntryPoint
import io.reactivex.disposables.Disposable


@AndroidEntryPoint
class SeatSelectFragmentV2 : BaseFragment<FragmentSelectSeatLayoutBinding>() {

    private val viewModel : SeatViewModel by viewModels()
    private val viewModelManager : SeatManagerViewModel by activityViewModels()

    private var disposable: Disposable? = null
    private var arraylistSeatGroup: ArrayList<SeatGroup> = ArrayList()
    private val seatViewList: ArrayList<SeatViewV2> = ArrayList()
    var startRow = 1
    var isReturnTrip = false



    override fun getLayoutRes(): Int {
        return R.layout.fragment_select_seat_layout
    }


    companion object {
        fun newInstance(provider: String, flightK: String, isReturn: Boolean): SeatSelectFragmentV2 {
            val bundle = Bundle().apply {
                putBoolean("isReturnTrip", isReturn)
                putString("provider", provider)
                putString("flightK", flightK)
            }
            return SeatSelectFragmentV2().apply {
                arguments = bundle
            }
        }
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)




        arguments?.let {
            observe()

            isReturnTrip = it.getBoolean("isReturnTrip", false)
            val provider = it.getString("provider", "")
            viewModel.flightK = it.getString("flightK", "")
            Handler(Looper.getMainLooper()).postDelayed({

                viewModel.postSeatMap(provider)
            }, 1000)
        }


        binding.selectSeat.btnNext.setOnClickListener {
            doneClick.invoke()
        }
        binding.selectSeat.btnBack.setOnClickListener {
            activity?.finish()
        }
        binding.btnBack.setOnClickListener {
            activity?.finish()
        }



    }

    private fun observe(){

        viewModel.seatListLiveData.observe(viewLifecycleOwner){
            when(it){
                is State.Error -> {
                    binding.emptyState.visibility = View.VISIBLE
                    binding.shimmerViewContainer.stopShimmer()
                    binding.shimmerViewContainer.visibility = View.GONE
                }
                State.Loading -> {

                }
                is State.Success -> {


                    binding.originCode.text = viewModel.flightK.substring(0, 3)
                    binding.destinationCode.text = viewModel.flightK.substring(3, 6)
                    binding.airCraft.text = it.data.airCraft

                    it.data.seatType.forEach { seatType ->
                        try {
                            val seatTypeView = SeatViewTitleItemBinding.inflate(
                                LayoutInflater.from(requireContext()),
                                null,
                                false
                            )

                            seatTypeView.seatTitle.text = seatType.text
                            seatTypeView.seatColor.setCardBackgroundColor(Color.parseColor("#" + seatType.color))
                            val lpPlexItem = FlexboxLayout.LayoutParams(
                                FlexboxLayout.LayoutParams.MATCH_PARENT,
                                FlexboxLayout.LayoutParams.MATCH_PARENT
                            )
                            lpPlexItem.flexBasisPercent = 0.5f
                            seatTypeView.root.layoutParams = lpPlexItem
                            binding.flexBoxSeatTitle.addView(seatTypeView.root)
                        } catch (e: Exception) {
                            e.printStackTrace()
                            AppConfigs.logException(e)
                        }

                    }


                    it.data.seatGroups.forEach { sg ->
                        try {
                            val seatTitleView = SeatGroupTitleViewBinding.inflate(
                                LayoutInflater.from(requireContext()),
                                null,
                                false
                            )
                            seatTitleView.seatGroupTitle.text = sg.title
                            val lpPlexItem = FlexboxLayout.LayoutParams(
                                FlexboxLayout.LayoutParams.MATCH_PARENT,
                                FlexboxLayout.LayoutParams.MATCH_PARENT
                            )
                            lpPlexItem.flexBasisPercent = 1f
                            seatTitleView.root.layoutParams = lpPlexItem

                            binding.flexBoxContainer.addView(seatTitleView.root)
                            genSeatMap(sg.seatOptions, it.data.seatIdentifierTitles)

                            mappingSeatInfo(sg)
                        } catch (e: Exception) {
                            e.printStackTrace()
                            AppConfigs.logException(e)

                        }
                    }

                    binding.shimmerViewContainer.stopShimmer()
                    binding.shimmerViewContainer.visibility = View.GONE

                    binding.emptyState.visibility = View.GONE
                    binding.bottomSheet.visibility = View.VISIBLE
                }
            }
        }
    }

  

    private fun updateSeatSelected() {
        try {


            var totalPrice = 0
            var seatSummaryText = ""
            val fillerList = seatViewList.filter { st -> st.isSelect }


            fillerList.forEach { seat ->
                totalPrice += seat.seatOption?.seatCharges ?: 0
                seatSummaryText += seat.seatOption?.rowIdentifier + seat.seatOption?.seatIdentifier + ", "
            }
            seatSummaryText = seatSummaryText.removeSuffix(", ")


            if (totalPrice > 0) {
                binding.selectSeat.txtGrandTotalPrice.text = Common.dinhDangTien(totalPrice)
                binding.selectSeat.txtSeatSelect.text = seatSummaryText
            } else {
                binding.selectSeat.txtGrandTotalPrice.text = Common.dinhDangTien(0)
                binding.selectSeat.txtSeatSelect.text = "Vui lòng chọn"
                binding.currentSeatSelecte.text = ""
                binding.seatPrice.text = ""
            }


            val params = binding.bottomLayout.layoutParams as CoordinatorLayout.LayoutParams
            (params.behavior as HideBottomViewOnScrollBehavior).slideUp(binding.bottomLayout)


            viewModelManager.updateSeatSelected(ArrayList(fillerList), isReturnTrip)

        } catch (e: Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }

    }



    fun clearSelect(seat: SeatViewV2) {

        if (seat.seatOption != null) {
            (seat.seatOption?.rowIdentifier + seat.seatOption?.seatIdentifier).also {
                binding.currentSeatSelecte.text = it
            }
            binding.seatPrice.text = Common.dinhDangTien(seat.seatOption?.seatCharges ?: 0)
        }

        seatViewList.forEach { it ->
            if (it.seatId != "") {
                val allowSelect = getAllowSelect()
                if (it.seatId != seat.seatId) { //clear select
                    if (viewModelManager.totalPax == 1) {
                        it.selectSeat(false, allowSelect)
                    }
                } else {
                    if (it.isSelect) {

                        seat.binding.seatGroupView.setCardBackgroundColor(Color.parseColor("#" + seat.seatOption?.color))
                        seat.isSelect = false
                    } else {
                        if (allowSelect)
                            seat.isSelect = true
                    }
                }
            }
        }
        updateSeatSelected()
    }

    private fun genSeatMap(
        seatOptions: ArrayList<SeatOption>,
        seatIdentifierTitles: ArrayList<SeatIdentifierTitle>
    ) {

        val maxRow = seatOptions.maxOf { t -> t.rowIndex }

        val percent = (1 / (seatIdentifierTitles.count() + 0f)) - 0.005f
        for (i in startRow until maxRow + 1) {
            if (seatIdentifierTitles.size > 0) {
                val seatRowIndex = startRow
                seatIdentifierTitles.forEach { it ->
                    val seatView = SeatViewV2(
                        requireActivity(),
                        it,
                        seatRowIndex.toString(),
                        percent,
                        isReturnTrip,
                        false
                    )
                    seatView.seatId = "" + seatRowIndex + "-" + it.value
                    seatView.clearSelect = {
                        clearSelect(it)
                    }
                    seatView.callBackSelectSeat = {
                        val allowSelect = getAllowSelect()
                        if (!allowSelect) {
                            Toast.makeText(
                                requireContext(), "Bạn đã chọn đủ số ghế cho phép", Toast.LENGTH_SHORT
                            ).show()

                        }
                        it.selectSeat(true, allowSelect)
                    }
                    seatViewList.add(seatView)
                    binding.flexBoxContainer.addView(seatView)
                }
                startRow++
            }
        }
    }

    private fun mappingSeatInfo(seatGroup: SeatGroup) {
        val paxSeatStr = viewModelManager.getPaxSeatStr(isReturnTrip)
        seatGroup.seatOptions.forEach { seat ->
            val seatId = "" + seat.rowIndex + "-" + seat.seatIdentifier
            for (i in 0 until seatViewList.size) {
                val seatView = seatViewList[i]
                if (seatView.seatId == seatId) {
                    seatView.setSeatInfo(seat, getAllowSelect())

                    if (i > 1 && seatViewList[i - 1].isIndex) {
                        seatViewList[i - 1].binding.rowNumber.text = seat.rowIdentifier
                    }

                    val seatIdentifier = "-" + seat.rowIdentifier + seat.seatIdentifier
                    if (paxSeatStr.contains(seatIdentifier)) {
                        seatView.selectSeat(true, getAllowSelect())
                    }
                }

            }


        }
    }

    private fun getAllowSelect(): Boolean {
        val totalSelected = seatViewList.count { seatView -> seatView.isSelect }

        return totalSelected < viewModelManager.totalPax || viewModelManager.totalPax == 1
    }




}