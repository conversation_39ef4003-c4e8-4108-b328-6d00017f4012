package com.hqt.view.ui.flighthistory.ui.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.Toast
import androidx.lifecycle.ViewModelProviders
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.android.volley.VolleyError
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.gson.reflect.TypeToken
import com.hqt.view.ui.flighthistory.data.model.FlightHistory
import com.hqt.view.ui.flighthistory.data.model.FlightHistoryItem
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ActivityFlightHistoryBinding
import com.hqt.util.AppConfigs
import com.hqt.util.AppController
import com.hqt.util.SSLSendRequest
import com.hqt.util.Widget.isAvailable
import com.hqt.view.adapter.FlightHistoryItemAdapter
import com.hqt.view.ui.BaseActivityKt
import com.hqt.view.ui.HomeActivity
import com.hqt.view.ui.search.ui.activity.SearchActivityV2
import com.hqt.viewmodel.FlightHistoryViewModel
import org.json.JSONException
import org.json.JSONObject
import kotlin.math.roundToInt

class FlightHistoryActivity : BaseActivityKt<ActivityFlightHistoryBinding>() {

    override val layoutId: Int = R.layout.activity_flight_history
    lateinit var recyclerView: RecyclerView
    private lateinit var mAdapter: FlightHistoryItemAdapter
    private var arrayListFlightHistory: ArrayList<FlightHistoryItem> = ArrayList()
    lateinit var viewModel: FlightHistoryViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        recyclerView = getViewBindding().recyclerView
        recyclerView.setHasFixedSize(true)

        getToolbar().title = "Theo dõi chuyến bay"
        getToolbar().setNavigationIcon(R.drawable.ic_action_back_home)
        supportActionBar!!.setDisplayShowHomeEnabled(true)

        viewModel = ViewModelProviders.of(this).get(FlightHistoryViewModel::class.java)

        recyclerView.layoutManager = LinearLayoutManager(this)
        mAdapter = FlightHistoryItemAdapter(this, arrayListFlightHistory)
        recyclerView.adapter = mAdapter
//        getViewBindding().viewModel = viewModel
        getViewBindding().lifecycleOwner = this

        initBinddingClick()

        if (intent.hasExtra("flightNumber") || intent.hasExtra("link")) {
            try {


                var getFnumber = "";
                if (intent.hasExtra("flightNumber")) getFnumber = intent.getStringExtra("flightNumber")!!

                if (intent.hasExtra("link")) { //https://12bay.vn/lich-bay-vietnam-airlines-dong-hoi-di-ho-chi-minh-city-vn1405
                    val link = intent.getStringExtra("link")!!.replace("https://12bay.vn/", "")
                    val linkPart = link.split("/")
                    if (linkPart.size > 0) {
                        val fpart = linkPart[0].split("-");
                        getFnumber = fpart[fpart.size - 1]
                    }

                }


                getViewBindding().txtFlightNumber.setText(getFnumber.toUpperCase())
                Handler(Looper.getMainLooper()).postDelayed({
                    getViewBindding().btnFind.performClick()
                }, 100)
            } catch (e: Exception) {
                AppConfigs.logException(e)
            }
        } else {
            getViewBindding().txtFlightNumber.requestFocus()
        }
    }

    private fun initBinddingClick() {

        getViewBindding().btnSearchFlight.setOnClickListener {
//            var intent = Intent(this, SearchActivity::class.java)
            val intent = Intent(this, SearchActivityV2::class.java)
            startActivity(intent)

        }
        getViewBindding().btnFind.setOnClickListener {

            if (getViewBindding().txtFlightNumber.text.isNullOrEmpty()) {
                showSnackbarMessage("Vui lòng nhập mã chuyến bay (vd: VN110)",
                    R.color.red,
                    2000,
                    View.TEXT_ALIGNMENT_TEXT_START)
                getViewBindding().txtFlightNumber.requestFocus()
            } else {
                getListTask()
                val view = this.currentFocus
                if (view != null) {
                    val imm: InputMethodManager = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                    imm.hideSoftInputFromWindow(view.windowToken, 0)
                }
                initAnalytics(getViewBindding().txtFlightNumber.text.toString())

            }
        }
        getViewBindding().scoreProgress.setOnClickListener {

        }
        getViewBindding().btnAddNew.setOnClickListener {
            getViewBindding().txtFlightNumber.requestFocus()
        }

    }

    private fun getListTask() {
        if (!isInternetConnected) {
            Toast.makeText(this, "Không có kết nối internet !", Toast.LENGTH_SHORT).show()
        } else {

            getViewBindding().metaLayout.visibility = View.GONE
            getViewBindding().shimmerViewContainer.visibility = View.VISIBLE
            getViewBindding().shimmerViewContainer.startShimmer()
            getViewBindding().notfound.visibility = View.GONE


            var days = getViewBindding().inputDays.selectedItem.toString().replace(" ngày", "")
            if (days.isNullOrEmpty()) days = "14"

            var request = JSONObject()
            try {

                request.put("flightNumber", getViewBindding().txtFlightNumber.text.toString())
                request.put("days", days)

            } catch (e: JSONException) {
                AppConfigs.logException(e)
            }

            SSLSendRequest(this).GET(false, "Flight/History", request, object : SSLSendRequest.CallBackInterface {
                override fun onSuccess(response: JSONObject, cached: Boolean) {

                    if (!response.isNull("data") && response.getJSONObject("data").length() > 0) {
                        processListData(response)
                    } else {
                        getViewBindding().recyclerView.visibility = View.GONE
                        getViewBindding().shimmerViewContainer.visibility = View.GONE
                        getViewBindding().notfound.visibility = View.VISIBLE

                        showSnackbarMessage("Không tìm thấy thông tin chuyến bay.",
                            R.color.google_yellow,
                            2000,
                            View.TEXT_ALIGNMENT_TEXT_START)
                    }
                }

                override fun onFail(er: VolleyError) {

                    getViewBindding().recyclerView.visibility = View.GONE
                    getViewBindding().shimmerViewContainer.visibility = View.GONE
                    getViewBindding().notfound.visibility = View.VISIBLE
                    showSnackbarMessage("Không tìm thấy thông tin lịch sử chuyến bay.",
                        R.color.google_yellow,
                        2000,
                        View.TEXT_ALIGNMENT_TEXT_START)

                }
            })
        }
    }

    fun processListData(json: JSONObject) {
        try {
            val historyType = object : TypeToken<FlightHistory>() {}.type
            var flightHistory = AppController.instance.gSon.fromJson<FlightHistory>(json.getJSONObject("data")
                .toString(), historyType)

            viewModel.flightHistory.postValue(flightHistory)
            if (flightHistory.meta != null) {
                try {
                    val score = flightHistory.meta!!.score * 100
                    getViewBindding().scoreProgress.progress = score
                    getViewBindding().scoreProgress.text = "" + score.roundToInt()
                    getViewBindding().metaLayout.visibility = View.VISIBLE
                    if (this.isAvailable()) {
                        Glide.with(this).load(flightHistory.meta!!.logo).apply(RequestOptions().centerInside())
                            .skipMemoryCache(true).placeholder(R.drawable.logo_gray).into(getViewBindding().airLogo)
                    }
                    getViewBindding().metaLayout.visibility = View.VISIBLE
                } catch (e: Exception) {
                    getViewBindding().metaLayout.visibility = View.GONE
                    AppConfigs.logException(e)
                    AppConfigs.Log("E", e.message);
                }
            }

            getViewBindding().recyclerView.visibility = View.VISIBLE
            val listFlight = flightHistory.history

            arrayListFlightHistory.clear()
            arrayListFlightHistory.addAll(listFlight)
            mAdapter.notifyDataSetChanged()

            getViewBindding().shimmerViewContainer.stopShimmer()
            getViewBindding().shimmerViewContainer.visibility = View.GONE
            getViewBindding().notfound.visibility = View.GONE

            listFlight.forEach { flightHistoryItem ->

                if (flightHistoryItem.status.live) {
                    val intent = Intent(this, MapViewActivity::class.java)
                    intent.putExtra("flightId", flightHistoryItem.id)
                    intent.putExtra("onMoveCamera", true)
                    if (!flightHistoryItem.status.live) intent.putExtra("isHistory", true)
                    intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    startActivity(intent)
                    overridePendingTransition(R.anim.enter, R.anim.exit)
                    
                }

            }


        } catch (e: java.lang.Exception) {
            AppConfigs.logException(e)
            getViewBindding().shimmerViewContainer.stopShimmer()
            getViewBindding().shimmerViewContainer.visibility = View.GONE
            getViewBindding().notfound.visibility = View.VISIBLE
        }

    }

    override fun onBackPressed() {
        if (this.isTaskRoot) {
            val `in` = Intent(this, HomeActivity::class.java)
            startActivity(`in`)
            finish()
        } else {
            super.onBackPressed()
        }
    }

    override fun onRestart() {

        super.onRestart()
    }

    override fun onSystemSettingChange(type: AppConfigs.SystemSettingType) {

        if (type == AppConfigs.SystemSettingType.INTERNET) {
            getListTask()
        }
    }

    private fun initAnalytics(fnumber: String) {

        val params = Bundle()
        params.putString(FirebaseAnalytics.Param.FLIGHT_NUMBER, fnumber)
        firebaseAnalytics.logEvent("flight_history_view", params)
        firebaseAnalytics.setCurrentScreen(this, "flight_history_list", null)
    }


}
