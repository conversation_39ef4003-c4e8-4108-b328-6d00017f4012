package com.hqt.view.ui.booking.data.model

import java.io.Serializable

open class BaseBooking : Serializable {
    open var type: BookingType = BookingType.FLIGHT
    open var id: String? = null
    open var destination_code = ""
    open var destination_name = ""
    open var origin_code = ""
    open var origin_name = ""
    open var departure_date = ""
    open var return_date = ""
    open var is_round_trip: Boolean = false
    open var adult = 0
    open var child = 0
    open var infant = 0
    open var student = 0
    open var older = 0
    open var total = 0
    open var bag_fee = 0
    open var addon_fee = 0
    open var voucher: String? = null
    open var discount = 0
    open var contact_phone: String? = null
    open var contact_name: String? = null
    open var contact_email: String? = null
    open var created_at: String? = null
    open var is_active = false
    open var status: String? = null
    open var status_text: String? = null
    open var pnr: String? = null
    open var pnr_return: String? = null
    open var token: String? = null
    open var expired_date: String? = null
    open var uuid: String? = null
    open var payment: PaymentInfo = PaymentInfo()

    open var pax_info: PaxInfoListV2 = PaxInfoListV2()

    enum class BookingType : Serializable {
        FLIGHT, TRAIN, BUS, INTER, TOUR
    }

    class PaymentInfo : Serializable {
        var status: Boolean = false
        var url: String? = null
    }

    open fun getGrandTotal(): Int {
        return total
    }

    open fun getTotalBagFee(): Int {

        return 0
    }

    open fun getTotalAddOn(): Int {

        return 0
    }

    open fun getRewardPointTotal(): Int {
        return 0
    }

    fun getCalculatorFinalPrice(): Int {
        return total - discount + bag_fee + addon_fee
    }

    fun getTotalPax(): Int {
        return adult + child + infant + older + student
    }

    fun getBaseBookingSortInfo(): String {
        return "$type|$origin_code|$destination_code|$departure_date|$return_date|$adult-$child-$infant-$older-$student|$contact_phone"

    }
}