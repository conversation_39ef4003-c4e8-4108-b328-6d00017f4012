package com.hqt.view.ui.reward.data.api

import com.hqt.base.model.HttpData
import com.hqt.view.ui.reward.data.model.Promotion
import com.hqt.view.ui.reward.data.model.Voucher
import retrofit2.http.GET
import retrofit2.http.HeaderMap
import retrofit2.http.POST
import retrofit2.http.Path
import retrofit2.http.Query

interface RewardService {

    @GET("/api/v1/AirLines/Promotion")
    suspend fun getPromotion(): HttpData<ArrayList<Promotion>>

    @GET("/api/v1/AirLines/Promotion/{id}")
    suspend fun getPromotionById(
        @Path("id") id : String? = null
    ): HttpData<Promotion>

    @GET("/api/v1/AirLines/Voucher/List/{uid}")
    suspend fun getVoucherList(
        @HeaderMap headers: Map<String, String>,
        @Path("uid") uid : String? = null
    ): HttpData<ArrayList<Voucher>>
}