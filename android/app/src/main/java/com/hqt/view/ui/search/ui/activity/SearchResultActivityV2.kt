package com.hqt.view.ui.search.ui.activity

import android.annotation.SuppressLint
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.graphics.Color
import android.graphics.Typeface
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.Button
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentStatePagerAdapter
import androidx.lifecycle.lifecycleScope
import com.github.florent37.materialviewpager.MaterialViewPager
import com.github.florent37.materialviewpager.MaterialViewPagerHelper
import com.github.florent37.materialviewpager.Utils
import com.github.florent37.materialviewpager.header.HeaderDesign
import com.google.firebase.analytics.FirebaseAnalytics
import com.hqt.base.BaseActivity
import com.hqt.base.model.State
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.SearchResultLayoutBinding
import com.hqt.util.AppConfigs
import com.hqt.util.EventManager
import com.hqt.util.Log
import com.hqt.view.ui.BaseActivityKt.EmptyStateCallBackInterface
import com.hqt.view.ui.flightwaches.NewFlightWatchesActivity
import com.hqt.view.ui.search.ui.SearchResultViewModel
import com.hqt.view.ui.search.ui.dialog.PriceAlertInfoDialog
import com.hqt.view.ui.search.ui.fragment.FlightListFragmentV2
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch


@AndroidEntryPoint
class SearchResultActivityV2 : BaseActivity<SearchResultLayoutBinding>() {

    val viewModel: SearchResultViewModel by viewModels()

    var _xDelta: Int = 0
    var _yDelta: Int = 0



    private var toolbar: Toolbar? = null

    var totalViewPaper: Int = 1
    var fontAwesome: Typeface? = null
    var emptyState: LinearLayout? = null

    override fun getLayoutRes(): Int {
        return R.layout.search_result_layout
    }


    @SuppressLint("ClickableViewAccessibility")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)





        fontAwesome = Typeface.createFromAsset(assets, "fonts/fontawesome-webfont.ttf")

        emptyState = initEmptyState(
            "Ohh noo! Hết chỗ mất rồi bạn ơi ! <br>Bạn có thể dùng tính năng <b>Săn Vé </b> để thông báo ngay có vé lại",
            R.drawable.no_flight,
            -1,
            object : com.hqt.view.ui.BaseActivity.EmptyStateCallBackInterface,
                EmptyStateCallBackInterface {
                override fun negativeButton(button: Button) {
                    button.text = "Tìm ngày khác"
                    button.setOnClickListener {
                        finish()
                    }
                }

                override fun positiveButton(button: Button) {
                    button.text = "Săn vé cho hành trình này"
                    button.setOnClickListener {
                        val intent = Intent(
                            this@SearchResultActivityV2,
                            NewFlightWatchesActivity::class.java
                        )
                        intent.putExtra("origin", viewModel.bookingDetail.origin_code)
                        intent.putExtra("destination", viewModel.bookingDetail.destination_code)
                        intent.putExtra("depatureDate", viewModel.bookingDetail.departure_date)
                        startActivity(intent)
                        finish()
                    }
                }
            })


        setupToolbar()

        //HAND INTENT
        viewModel.handelIntent(intent)




        if (viewModel.bookingDetail.is_round_trip) totalViewPaper = 2


        initTabView()
        observe()

        api


        Handler().postDelayed({
            supportActionBar?.title = Common.getAirPortName(
                viewModel.bookingDetail.origin_code,
                true
            ) + " - " + Common.getAirPortName(viewModel.bookingDetail.destination_code, true)
        }, 50)



        binding.fabAlertPrice.setOnClickListener {
            val dialog = PriceAlertInfoDialog()
            dialog.show(supportFragmentManager, "")
        }

        if (Common.SHOWFULLPRICE)
            binding.fabPrice.setLabelText("Hiển thị giá NET")
        else
            binding.fabPrice.setLabelText("Hiển thị giá thuế phí")

        binding.fbMenu.isIconAnimated = false
        binding.fbMenu.hideMenuButton(false)
        if ((viewModel.sortBy.value == "price")) {
            binding.fabSortPrice.setEnabled(false)
        }

        binding.fabSortPrice.setEnabled(false)
        binding.fabSortPrice.setOnClickListener {
            viewModel.sortBy.value = SORT_BY_PRICE

            binding.fabSortPrice.setEnabled(false)
            binding.fabSortTime.setEnabled(true)
            binding.fbMenu.close(true)
        }
        binding.fabSortTime.setOnClickListener {

            viewModel.sortBy.value = SORT_BY_DATE
            binding.fabSortPrice.setEnabled(true)
            binding.fabSortTime.setEnabled(false)
            binding.fbMenu.close(true)
        }
        binding.fabSortRefesh.setOnClickListener {

            binding.shimmerViewContainer.startShimmer()
            binding.shimmerViewContainer.visibility = View.VISIBLE
            api
            binding.fbMenu.close(true)
        }
        binding.fabPrice.setOnClickListener {
            Common.SHOWFULLPRICE = !Common.SHOWFULLPRICE
            if (Common.SHOWFULLPRICE)
                binding.fabPrice.setLabelText("Hiển thị giá NET")
            else
                binding.fabPrice.setLabelText("Hiển thị giá thuế phí")

            viewModel.sortBy.postValue(viewModel.sortBy.value)
            binding.fbMenu.close(true)
        }
    }

    private fun observe() {

        lifecycleScope.launch {
            EventManager.eventFlow.collect { value ->
                try {
                    if (value.event == EventManager.Event.SELECTED_TAB) {
                        binding.materialViewPager.viewPager.currentItem = value.data as Int
                    }
                } catch (ex: Exception) {
                    Log.logException(ex)
                }

            }
        }


        viewModel.flightTaskLiveData.observe(this) {
            when (it) {
                is State.Error -> {
                    Toast.makeText(
                        this,
                        ":( Không tìm thấy dữ liệu, Thử lại một lần nữa bạn nhé !",
                        Toast.LENGTH_SHORT
                    ).show()
                    onBackPressed()
                }

                State.Loading -> {
                    binding.progressBar.visibility = View.VISIBLE
                    binding.shimmerViewContainer.startShimmer()
                    binding.shimmerViewContainer.visibility = View.VISIBLE
                }

                is State.Success -> {


                    binding.progressBar.visibility = View.GONE



                    binding.fbMenu.showMenuButton(true)
                    binding.shimmerViewContainer.visibility = View.GONE
                    binding.shimmerViewContainer.stopShimmer()
                }
            }


        }
    }


    private fun setupToolbar() {

        toolbar = binding.materialViewPager.toolbar
        if (toolbar != (null)) {
            setSupportActionBar(toolbar)
            toolbar?.inflateMenu(R.menu.main)
//            supportActionBar?.setDisplayShowHomeEnabled(true)
//            toolbar?.setNavigationIcon(R.drawable.ic_action_back_home)
//            toolbar?.setTitleTextColor(Color.WHITE)
//            toolbar?.setSubtitleTextColor(Color.WHITE)
            toolbar?.setNavigationOnClickListener {
                onBackPressed()
            }

//            window.setFlags(
//                WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS,B
//                WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS
//            )
//            window.statusBarColor = Color.TRANSPARENT
        }
    }

    private fun initTabView() {


        binding.materialViewPager.viewPager.setAdapter(object : FragmentStatePagerAdapter(
            supportFragmentManager
        ) {
            override fun getItem(position: Int): Fragment {


                return when (position % totalViewPaper) {
                    0 -> FlightListFragmentV2.newInstance(FlightListFragmentV2.FlightStatus.DEPARTURE.name)
                    1 -> FlightListFragmentV2.newInstance(FlightListFragmentV2.FlightStatus.ROUND_TRIP.name)
                    else -> FlightListFragmentV2.newInstance(FlightListFragmentV2.FlightStatus.DEPARTURE.name)
                }
            }

            override fun getCount(): Int {
                return totalViewPaper
            }

            override fun getPageTitle(position: Int): CharSequence {
                when (position % totalViewPaper) {
                    0 -> return "Lượt đi"
                    1 -> return "Lượt về"
                }
                return ""
            }
        })

        MaterialViewPagerHelper.getAnimator(this)?.let {
            viewModel.offset = it.headerHeight
        } ?: run {
            viewModel.offset = Utils.dpToPx(150f, this).toInt()
        }

        binding.materialViewPager.viewPager.viewTreeObserver.addOnGlobalLayoutListener {
            Log.d("viewPager",  binding.materialViewPager.viewPager.height)
        }


        binding.materialViewPager.setMaterialViewPagerListener(object : MaterialViewPager.Listener {
            override fun getHeaderDesign(page: Int): HeaderDesign? {
                when (page) {
                    0 -> {
                        toolbar?.subtitle = viewModel.bookingDetail.departure_date

                        val view = toolbar?.getChildAt(3)
                        if (view is TextView) {
                            view.setTypeface(fontAwesome)
                        }

                        supportActionBar?.title = Common.getAirPortName(
                            viewModel.bookingDetail.origin_code,
                            true
                        ) + " → " + Common.getAirPortName(
                            viewModel.bookingDetail.destination_code,
                            true
                        )
                        toolbar?.subtitle = viewModel.bookingDetail.departure_date + "  " + viewModel.bookingDetail.adult + getString(
                                R.string.fa_male
                            ) + " " + viewModel.bookingDetail.child + getString(
                                R.string.fa_child
                            ) + " " + viewModel.bookingDetail.infant + getString(R.string.fa_female)
                        return HeaderDesign.fromColorResAndUrl(
                            R.color.primary,
                            AppConfigs.getInstance().config.getString("root_api") + "/api/v1/AirLines/Image/" + viewModel.bookingDetail.destination_code
                        )
                    }

                    1 -> {
                        toolbar?.subtitle = viewModel.bookingDetail.return_date
                        toolbar?.subtitle =
                            viewModel.bookingDetail.return_date + "  " + viewModel.bookingDetail.adult + getString(
                                R.string.fa_male
                            ) + " " + viewModel.bookingDetail.child + getString(
                                R.string.fa_child
                            ) + " " + viewModel.bookingDetail.infant + getString(R.string.fa_female)
                        supportActionBar?.title = Common.getAirPortName(
                            viewModel.bookingDetail.destination_code,
                            true
                        ) + " → " + Common.getAirPortName(viewModel.bookingDetail.origin_code, true)

                        return HeaderDesign.fromColorResAndUrl(
                            R.color.primary,
                            AppConfigs.getInstance().config.getString("root_api") + "/api/v1/AirLines/Image/" + viewModel.bookingDetail.destination_code
                        )
                    }
                }
                return null
            }
        }

        )
//
//        binding.materialViewPager.viewPager.offscreenPageLimit = binding.materialViewPager.viewPager.adapter?.count ?: 0
//        binding.materialViewPager.pagerTitleStrip.setViewPager(binding.materialViewPager.viewPager)


        binding.materialViewPager.viewPager.setOffscreenPageLimit(binding.materialViewPager.viewPager.adapter?.count ?: 0)
        binding.materialViewPager.pagerTitleStrip.setViewPager(binding.materialViewPager.viewPager)


        viewModel.offset = MaterialViewPagerHelper.getAnimator(this).headerHeight


    }

    val api: Unit
        get() {

            emptyState?.visibility = View.GONE


            viewModel.getAllFlightTasks()

            val params = Bundle()
            params.putString(
                FirebaseAnalytics.Param.ORIGIN,
                Common.getAirPortCode(viewModel.bookingDetail.origin_code)
            )
            params.putString(
                FirebaseAnalytics.Param.DESTINATION,
                Common.getAirPortCode(viewModel.bookingDetail.destination_code)
            )
            params.putString(
                FirebaseAnalytics.Param.START_DATE,
                viewModel.bookingDetail.departure_date
            )
            params.putString(FirebaseAnalytics.Param.END_DATE, viewModel.bookingDetail.return_date)
            params.putString(
                FirebaseAnalytics.Param.NUMBER_OF_PASSENGERS,
                (viewModel.bookingDetail.adult + viewModel.bookingDetail.child + viewModel.bookingDetail.infant).toString() + ""
            )
            FirebaseAnalytics.getInstance(this)
                .logEvent(FirebaseAnalytics.Event.VIEW_SEARCH_RESULTS, params)
        }


    override fun onBackPressed() {
        try {
        } catch (e: Exception) {
            AppConfigs.logException(e)
        }


        super.onBackPressed()
        overridePendingTransition(R.anim.left_to_right, R.anim.right_to_left)
    }


    fun showAlertDialog(context: Context?, title: String?, message: String?) {
        try {
            val alertDialog = AlertDialog.Builder(
                (context)!!
            ).create()
            alertDialog.setTitle(title)
            alertDialog.setMessage(message)
            alertDialog.setOnCancelListener {
                finish()
            }
            alertDialog.setIcon(R.drawable.ic_bell_alert)
            alertDialog.setButton(
                DialogInterface.BUTTON_POSITIVE,
                "ĐẶT LẠI"
            ) { dialog, which ->
                finish()
            }
            alertDialog.setButton(
                DialogInterface.BUTTON_NEGATIVE,
                "GỌI PHÒNG VÉ"
            ) { dialog, which ->
                finish()
                val i = Intent(Intent.ACTION_DIAL)
                val p = "tel:" + AppConfigs.getInstance().config.getString("hotline")
                i.setData(Uri.parse(p))
                startActivity(i)
            }

            if (!<EMAIL>) {
                alertDialog.show()
            }
        } catch (e: Exception) {
            AppConfigs.logException(e)
        }
    }





    companion object {
        fun hideKeyboard(act: AppCompatActivity?) {
            if (act != null && act.currentFocus != null) {
                val inputMethodManager =
                    act.getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
                inputMethodManager.hideSoftInputFromWindow(act.currentFocus?.windowToken, 0)
            }
        }


        val SORT_BY_DATE = "SORT_BY_DATE"
        val SORT_BY_PRICE = "SORT_BY_PRICE"


    }

}