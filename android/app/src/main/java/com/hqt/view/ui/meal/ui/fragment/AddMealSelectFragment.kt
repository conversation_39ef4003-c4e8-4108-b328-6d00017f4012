package com.hqt.view.ui.meal.ui.fragment

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.behavior.HideBottomViewOnScrollBehavior
import com.hqt.base.BaseFragment
import com.hqt.base.model.State
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.FragmentSelectAddonLayoutBinding
import com.hqt.util.AppConfigs
import com.hqt.view.ui.meal.ui.MealManagerViewModel
import com.hqt.view.ui.meal.ui.MealViewModel
import com.hqt.view.ui.meal.ui.adapter.MealItemAdapter
import dagger.hilt.android.AndroidEntryPoint


@AndroidEntryPoint
class AddMealSelectFragment : BaseFragment<FragmentSelectAddonLayoutBinding>() {

    private val viewModel : MealViewModel by viewModels()
    private val viewModelManager : MealManagerViewModel by activityViewModels()
    var isReturnTrip = false

    private val mealAdapter by lazy {
        MealItemAdapter {

        }
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_select_addon_layout
    }


    companion object {
        fun newInstance(flightK: String, isReturn: Boolean, provider: String): AddMealSelectFragment {


            val bundle = Bundle().apply {
                putBoolean("isReturnTrip", isReturn)
                putString("provider", provider)
                putString("flightK", flightK)
            }
            return AddMealSelectFragment().apply {
                arguments = bundle
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)




        arguments?.let {


            isReturnTrip = it.getBoolean("isReturnTrip", false)
            viewModel.flightK = it.getString("flightK", "")
            mealAdapter.totalPax = viewModelManager.totalPax
            val provider = it.getString("provider")


            initRcv()


            Handler(Looper.getMainLooper()).postDelayed({
                viewModel.getMeal(provider)
            }, 1000)
        }


        binding.selectBottomLayout.btnNext.setOnClickListener {

            viewModelManager.updateSeatSelected(mealAdapter.selectedAddOnListSafe, isReturnTrip)
            viewModelManager.doneClick.postValue(true)
        }
        binding.selectBottomLayout.btnBack.setOnClickListener {
            activity?.finish()

        }
        binding.btnBack.setOnClickListener {
            activity?.finish()
        }

        mealAdapter.selectedAddOnListSafe = viewModelManager.getCurrentAddOn(isReturnTrip)
        updateAddOnSelected()



    }

    private fun initRcv() {

        binding.rcvMeal.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = mealAdapter
        }

        mealAdapter.updateSelected = {
            updateAddOnSelected()
        }

        observe()

    }


    private fun updateAddOnSelected() {
        try {

            var totalPrice = 0
            var seatSummaryText = ""
            val fillerList = mealAdapter.selectedAddOnListSafe.filter {
                it.price > 0

            }.toList()

            totalPrice = mealAdapter.selectedAddOnListSafe.sumOf { addOnInfo -> addOnInfo.price }

            val group = fillerList.distinct()

            group.forEach { addOn ->

                seatSummaryText += addOn.text + " x " + mealAdapter.selectedAddOnListSafe.filter { t -> t.value == addOn.value }.count() + "\n"
            }
            seatSummaryText = seatSummaryText.removeSuffix("\n")

            seatSummaryText = "" + mealAdapter.selectedAddOnListSafe.size + " Món"


            if (totalPrice > 0) {
                binding.selectBottomLayout.txtGrandTotalPrice.text = Common.dinhDangTien(totalPrice)
                binding.selectBottomLayout.txtSeatSelect.text = seatSummaryText
            } else {
                binding.selectBottomLayout.txtGrandTotalPrice.text = Common.dinhDangTien(0)
                binding.selectBottomLayout.txtSeatSelect.text = "Vui lòng chọn"


            }


            val params = binding.bottomLayout.layoutParams as CoordinatorLayout.LayoutParams
            (params.behavior as HideBottomViewOnScrollBehavior).slideUp(binding.bottomLayout)

            viewModelManager.updateSeatSelected(ArrayList(fillerList), isReturnTrip)

        } catch (e: Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }

    }





    private fun observe(){
        viewModel.mealListLiveData.observe(viewLifecycleOwner){
            when(it){
                is State.Error -> {
                    binding.emptyState.visibility = View.VISIBLE
                    binding.shimmerViewContainer.stopShimmer()
                    binding.shimmerViewContainer.visibility = View.GONE

                    activity?.finish()

                }
                State.Loading -> {
                    binding.emptyState.visibility = View.GONE
                    binding.shimmerViewContainer.startShimmer()
                    binding.shimmerViewContainer.visibility = View.VISIBLE
                }
                is State.Success -> {


                    mealAdapter.setData(it.data)

                    binding.emptyState.visibility = View.GONE
                    binding.shimmerViewContainer.stopShimmer()
                    binding.shimmerViewContainer.visibility = View.GONE
                    binding.bottomSheet.visibility = View.VISIBLE

                }
            }
        }
    }



}