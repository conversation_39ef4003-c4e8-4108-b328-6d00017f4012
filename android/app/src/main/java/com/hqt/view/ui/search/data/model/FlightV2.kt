package com.hqt.view.ui.search.data.model

import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.hqt.datvemaybay.Common
import java.io.Serializable
import java.util.Calendar
import java.util.Date
import java.util.Locale

data class FlightV2(
    var originCode: String? = null,
    var destinationCode: String? = null,
    var departureDateTime: String? = null,
    var arriverDateTime: String? = null,
    var flightNumber: String? = null,
    var provider: String? = null,
    var seatCount: Int? = null,
    var promo: Boolean = false,
    var uuid: String? = null,
    var infant: Int? = null,
    var adult: Int? = null,
    var child: Int? = null,
    var netPrice: Int? = null,
    var tax: Int? = null,
    var airPortFee: Int? = null,
    var stops: Int? = null,
    var fareBasis: String? = null,
    var duration: String? = null,
    var flightKey: String? = null,
    var seatClass: String? = null,
    var description: String = "",
    var rewardPoint: Int? = null
) : Serializable {

    val departureDate: Date get() = Common.getDateTimeFromFormat(departureDateTime)

    val arriverDate: Date
        get() = Common.getDateTimeFromFormat(arriverDateTime)

    val providerText: String?
        get() = when (provider) {
            "3K", "GK" -> "Jetstar"
            "BL" -> "Pacific Airlines"
            "VJ" -> "Vietjet Air"
            "VN" -> "Vietnam Airlines"
            "QH" -> "Bamboo Airways"
            "VU" -> "Vietravel Airlines"
            else -> provider
        }

    val stopsText: String
        get() = if (stops == 0) "Bay Thẳng" else "$stops điểm dừng"

    val logo: String
        get() = "logo_${provider?.lowercase(Locale.getDefault()) ?: ""}"

    val airlinesLogo: String
        get() = FirebaseRemoteConfig.getInstance()
            .getString("AIRLINES_LOGO")
            .replace("{provider}", provider ?: "")

    val isNextDay: Boolean
        get() {
            val dep = Common.stringToDate(departureDateTime, "HH:mm dd/MM/yyyy")
            val arr = Common.stringToDate(arriverDateTime, "HH:mm dd/MM/yyyy")
            return dep[Calendar.DAY_OF_MONTH] != arr[Calendar.DAY_OF_MONTH]
        }

    val quickDep: Boolean
        get() {
            val depTime = Common.stringToDate(departureDateTime, "HH:mm dd/MM/yyyy")
            val diff = Common.hoursBetween(Calendar.getInstance(), depTime)
            return diff < 24.5
        }


}