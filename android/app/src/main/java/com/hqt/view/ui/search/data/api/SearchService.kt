package com.hqt.view.ui.search.data.api

import com.hqt.base.model.HttpData
import com.hqt.view.ui.search.data.model.AirportGroup
import com.hqt.view.ui.search.data.model.FightTask
import com.hqt.view.ui.search.data.model.FlightV2
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path
import retrofit2.http.Query

interface SearchService {

    @GET("/api/v1/AirLines/i/AirPort")
    suspend fun getAirport(
        @Query("q") q : String? = null
    ): HttpData<ArrayList<AirportGroup>>




    @GET("/api/v1/AirLines/AirPort")
    suspend fun getAirportList(
    ): HttpData<Any>



    @POST("/api/v1/{airline}/iSearch/{originCode}/{destinationCode}")
    suspend fun getFlightTask(
        @Path("airline") airline : String? = null,
        @Path("originCode") originCode : String? = null,
        @Path("destinationCode") destinationCode : String? = null,
        @Body request : Any? = null
    ): HttpData<FightTask>


}