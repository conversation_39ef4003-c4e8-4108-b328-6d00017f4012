package com.hqt.view.adapter;


import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Color;
import android.net.Uri;
import android.text.Html;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.hqt.datvemaybay.Common;
import com.hqt.datvemaybay.R;
import com.hqt.data.model.Notification;
import com.hqt.view.ui.NotificationFragment;
import com.mikepenz.iconics.typeface.library.googlematerial.GoogleMaterial;

import java.util.Date;
import java.util.List;

/**
 * Created by TN on 12/1/2016.
 */
public class NotificationViewAdapter extends RecyclerView.Adapter<NotificationViewAdapter.ViewHolder> {

    List<Notification> contents;
    Context mContext;
    NotificationFragment notificationFragment;
    AppCompatActivity mActivity;
    static final int TYPE_HEADER = 0;
    static final int TYPE_CELL = 1;

    public class ViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener {
        public TextView txtTitle, txtBody, txtCreatedDate;
        public TextView notificationType, seen;

        public ViewHolder(View v) {
            super(v);
            txtTitle = v.findViewById(R.id.txtTitle);
            txtBody = v.findViewById(R.id.txtBody);
            seen = v.findViewById(R.id.seen);
            txtCreatedDate = v.findViewById(R.id.txtCreatedDate);
            notificationType = v.findViewById(R.id.notificationType);
            v.setOnClickListener(this);

        }

        @Override
        public void onClick(View v) {
            try {


                String link = contents.get(getAdapterPosition()).getAction_link();

                if (link.length() < 18) {
                    new AlertDialog.Builder(mContext)
                            .setIcon(R.drawable.ic_bell_alert)
                            .setTitle(contents.get(getAdapterPosition()).getTitle())
                            .setMessage(Html.fromHtml(contents.get(getAdapterPosition()).getBody()))
                            .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                                @Override
                                public void onClick(DialogInterface dialog, int which) {

                                }

                            }).show();
                } else {
                    Uri url = Uri.parse(link);
                    Intent action = Common.ConvertLinkAction(mContext, url);
                    if (action != null) {
                        mContext.startActivity(action);
                    }

                }

                notificationFragment.seenNofitication(getAdapterPosition(), contents.get(getAdapterPosition()).getId());
                seen.setText("");
            }catch (Exception e){
                
            }
        }
    }

    public NotificationViewAdapter(Context context, List<Notification> contents, NotificationFragment notificationFragment) {
        this.mContext = context;
        this.contents = contents;
        this.notificationFragment = notificationFragment;
    }

    @Override
    public int getItemCount() {
        return contents.size();
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = null;
        view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.notification_item_layout, parent, false);

        ViewHolder v = new ViewHolder(view);
        return v;
    }


    @Override
    public void onBindViewHolder(ViewHolder holder, int position) {
        holder.txtTitle.setText(contents.get(position).getTitle());
        holder.txtBody.setText(Html.fromHtml(contents.get(position).getBody()));
        Date d = Common.stringToDate(contents.get(position).getCreated_at().toString(), "yyyy-MM-dd HH:mm").getTime();

        holder.txtCreatedDate.setText(Common.dateToString(d, "dd/MM/yyyy"));

        String types = contents.get(position).getType();

        //if(type.equalsIgnoreCase( * TYPE 'order','point','voucher','promo','price'))
        holder.notificationType.setTextSize(20);
        if (types.equals("order")) {
            holder.notificationType.setText(GoogleMaterial.Icon.gmd_shopping_cart.getFormattedName());
        } else if (types.equals("point")) {
            holder.notificationType.setText(GoogleMaterial.Icon.gmd_control_point_duplicate.getFormattedName());
        } else if (types.equals("voucher")) {
            holder.notificationType.setText(GoogleMaterial.Icon.gmd_local_play.getFormattedName());
        } else if (types.equals("promo")) {
            holder.notificationType.setText(GoogleMaterial.Icon.gmd_redeem.getFormattedName());
        } else if (types.equals("price")) {
            holder.notificationType.setText(GoogleMaterial.Icon.gmd_add_alert.getFormattedName());
        }
        holder.seen.setTextSize(20);
        holder.seen.setText(GoogleMaterial.Icon.gmd_notifications.getFormattedName());
        if (contents.get(position).getRead()) {
            holder.seen.setText("");
        } else {
            holder.seen.setTextColor(mContext.getResources().getColor(R.color.red));
            holder.notificationType.setTextColor(Color.parseColor("#03a1e4"));
            holder.txtTitle.setTextColor(mContext.getResources().getColor(R.color.textDark));
        }
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemViewType(int position) {
        return position;
    }

}