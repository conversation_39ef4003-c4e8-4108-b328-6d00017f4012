package com.hqt.view.ui.reward.ui.dialog

import android.os.Bundle
import android.text.Html
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.TextView
import androidx.fragment.app.activityViewModels
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.hqt.base.model.State
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.BottomSheetPromotionViewBinding
import com.hqt.view.adapter.SliderAdapterCustom
import com.hqt.view.ui.flighthistory.ui.FlightHistoryViewModel
import com.hqt.view.ui.reward.ui.RewardViewModel
import com.hqt.view.ui.reward.ui.state.PromotionItemState
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PromotionViewDialog : BottomSheetDialogFragment() {

    private val viewModel : RewardViewModel by activityViewModels()


    private var binding: BottomSheetPromotionViewBinding? = null


    var onSignIn: () -> Unit = {}
    var redeemVoucher: () -> Unit = {}


    companion object {
        const val TAG = "ModalBottomSheet"
    }


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = BottomSheetPromotionViewBinding.inflate(layoutInflater, container, false)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding?.apply {
            itemViewState = PromotionItemState(viewModel.promotion)


            btnLogin.setOnClickListener {
                onSignIn.invoke()
            }

            if (viewModel.isUserSigned.value == true) {
                btnLogin.visibility = View.GONE
                btnRedeemVoucher.visibility = View.VISIBLE
            } else {
                btnRedeemVoucher.visibility = View.GONE
                btnLogin.visibility = View.VISIBLE
            }


            btnRedeemVoucher.setOnClickListener { // btnRedeemVoucher.startAnimation(() -> null);
//                redeemVoucher(promotion)
            }


        }




        dialog?.let {
            val sheet = it as BottomSheetDialog
            sheet.behavior.state = BottomSheetBehavior.STATE_EXPANDED
        }


    }





}