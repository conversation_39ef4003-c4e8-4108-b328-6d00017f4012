package com.hqt.view.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Color;
import android.text.style.BackgroundColorSpan;
import android.text.style.ForegroundColorSpan;
import android.text.style.RelativeSizeSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.ScaleAnimation;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.hqt.data.model.Flight;
import com.hqt.datvemaybay.Common;
import com.hqt.datvemaybay.R;
import com.hqt.datvemaybay.SearchResult;
import com.hqt.view.ui.flighthistory.ui.activity.FlightHistoryActivityV2;
import com.hqt.view.ui.flightwaches.FlightWachesViewActivity;
import com.mikepenz.iconics.Iconics;

import java.util.List;

public class FlightViewAdapter extends RecyclerView.Adapter<FlightViewAdapter.ViewHolder> {

    List<Flight> contents;
    Context mContext;
    Boolean onFlightWatchesShow = false;
    AppCompatActivity mActivity;
    static final int TYPE_HEADER = 0;
    static final int TYPE_CELL = 1;
    String selected_position = "";

    public class ViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener {

        public TextView txtGiaVe, txtNgayBay, txtThoiGianDen, txtThoiGianBay, txtGiaPhi, txtThuePhi, txtThueSanBay, txtHangBay, txtHangGhe, txtPos, txtSoHieu, txtDiemDung, txtPromo, txtDuration;
        public ImageView imgLogo;

        public ViewHolder(View v) {
            super(v);
            txtSoHieu = v.findViewById(R.id.soHieu);
            txtGiaVe = v.findViewById(R.id.giaVe);
            txtThoiGianDen = v.findViewById(R.id.thoiGianDen);
            txtThoiGianBay = v.findViewById(R.id.thoiGianBay);
            txtGiaPhi = v.findViewById(R.id.giaPhi);
            txtThuePhi = v.findViewById(R.id.thuePhi);
            txtThueSanBay = v.findViewById(R.id.thueSanBay);
            txtHangBay = v.findViewById(R.id.hangBay);
            txtHangGhe = v.findViewById(R.id.hangGhe);
            txtNgayBay = v.findViewById(R.id.gioBay);
            txtThoiGianDen = v.findViewById(R.id.thoiGianDen);
            txtPos = v.findViewById(R.id.pos);
            imgLogo = v.findViewById(R.id.logo);
            txtDiemDung = v.findViewById(R.id.stop);
            txtPromo = v.findViewById(R.id.promo);
            txtDuration = v.findViewById(R.id.txtDuration);
            v.setOnClickListener(this);

        }

        @Override
        public void onClick(View v) {
            int viewPosition = getLayoutPosition();

            showInfo(contents.get(viewPosition));
            if (mContext instanceof SearchResult) {

                ((SearchResult) mContext).setSelectedFlight(contents.get(viewPosition));
                ((SearchResult) mContext).clearSelect();
                selected_position = contents.get(viewPosition).getUuid();

            }

        }
    }


    public FlightViewAdapter(Context context, List<Flight> contents) {
        this.mContext = context;
        this.contents = contents;
    }

    public FlightViewAdapter(Context context, List<Flight> contents, boolean onFlightWatchesShow) {
        this.mContext = context;
        this.contents = contents;
        this.onFlightWatchesShow = onFlightWatchesShow;
    }

    @Override
    public int getItemViewType(int position) {
        switch (position) {
            case 0:
                return TYPE_HEADER;
            default:
                return TYPE_CELL;
        }
    }

    @Override
    public int getItemCount() {
        return contents.size();
    }

    @Override
    public FlightViewAdapter.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = null;
        view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.list_flight_item, parent, false);

        FlightViewAdapter.ViewHolder v = new FlightViewAdapter.ViewHolder(view);
        return v;
    }


    @Override
    public void onBindViewHolder(FlightViewAdapter.ViewHolder holder, int position) {

        holder.itemView.setSelected(selected_position.equals(contents.get(position).getUuid()));
        holder.txtSoHieu.setText(contents.get(position).getFlightNumber());
        if (Common.SHOWFULLPRICE) {
            holder.txtGiaVe.setText(Common.dinhDangTien(contents.get(position).getAdult()));
        } else {
            holder.txtGiaVe.setText(Common.dinhDangTien(contents.get(position).getNetPrice()));
        }
        holder.txtThoiGianDen.setText(contents.get(position).getArriverDateTime().substring(0, 5));
        holder.txtThoiGianBay.setText(contents.get(position).getDepartureDateTime().substring(0, 5));
        holder.txtGiaPhi.setText(Common.dinhDangTien(contents.get(position).getAdult()));
        holder.txtThuePhi.setText(Common.dinhDangTien(contents.get(position).getTax()));
        holder.txtThueSanBay.setText(Common.dinhDangTien(contents.get(position).getAirPortFee()));
        holder.txtHangBay.setText(contents.get(position).getProviderText());
        holder.txtHangGhe.setText(contents.get(position).getSeatClass());
        holder.txtNgayBay.setText(contents.get(position).getDepartureDateTime().substring(6, 16));
        holder.txtDuration.setText(contents.get(position).getDuration());

        if (contents.get(position).getQuickDep()) {
            holder.txtSoHieu.setText(("{faw_history} " + contents.get(position).getFlightNumber()));
            new Iconics.Builder().style(new ForegroundColorSpan(mContext.getResources().getColor(R.color.google_yellow)), new BackgroundColorSpan(Color.TRANSPARENT), new RelativeSizeSpan(1f)).on(holder.txtSoHieu).build();

        } else if (contents.get(position).getPromo()) {
            holder.txtSoHieu.setText(("{faw_star} " + contents.get(position).getFlightNumber()));

            new Iconics.Builder().style(new ForegroundColorSpan(mContext.getResources().getColor(R.color.google_yellow)), new BackgroundColorSpan(Color.TRANSPARENT), new RelativeSizeSpan(1f)).on(holder.txtSoHieu).build();

        } else {
            holder.txtSoHieu.setText((contents.get(position).getFlightNumber()));
        }

        holder.txtPos.setText((position + ""));
        holder.txtDiemDung.setText(("{faw_circle1} " + contents.get(position).getStopsText()));
        new Iconics.Builder().style(new ForegroundColorSpan(contents.get(position).getStopsText().toLowerCase().equals("bay thẳng") ? mContext.getResources().getColor(R.color.primary) : Color.RED), new BackgroundColorSpan(Color.TRANSPARENT), new RelativeSizeSpan(0.5f)).on(holder.txtDiemDung).build();


        Glide.with(mContext).load(contents.get(position).getAirlinesLogo()).into(holder.imgLogo);
        //holder.imgLogo.setImageResource(Integer.valueOf(mContext.getResources().getIdentifier(contents.get(position).getLogo(), "drawable", mContext.getPackageName())));

        //notifyItemChanged(holder.getAdapterPosition());


    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @SuppressLint("RestrictedApi")
    private void showInfo(Flight flight) {


        if (mContext instanceof SearchResult) {
            ((SearchResult) mContext).showBottomSheetFlightInfo(flight);
        } else {
            AlertDialog.Builder builder = new AlertDialog.Builder(mContext);

            LayoutInflater inflater = LayoutInflater.from(mContext);
            View alertView = inflater.inflate(R.layout.ticket_view_layout, null);
            builder.setView(alertView, 0, 0, 0, 0);
            builder.setTitle("Thông tin chuyến bay");
            builder.setIcon(R.drawable.ic_dep);

            TextView txtTo = alertView.findViewById(R.id.to);
            TextView txtFrom = alertView.findViewById(R.id.from);
            TextView txtDepatureTime = alertView.findViewById(R.id.thoiGianBay);
            TextView txtArrivalTime = alertView.findViewById(R.id.thoiGianDen);
            TextView txtDuration = alertView.findViewById(R.id.duration);
            TextView txtHangBay = alertView.findViewById(R.id.hangBay);
            TextView txtChuyenBay = alertView.findViewById(R.id.chuyenBay);
            TextView txtLoaiVe = alertView.findViewById(R.id.loaiVe);
            TextView txtgia = alertView.findViewById(R.id.gia);
            TextView txtgiaPhi = alertView.findViewById(R.id.giaPhi);
            TextView txtthuePhi = alertView.findViewById(R.id.thuePhi);
            TextView txtthueSanBay = alertView.findViewById(R.id.thueSanBay);
            ImageView imgLogo = alertView.findViewById(R.id.logo);
            TextView txtDiemDung = alertView.findViewById(R.id.diemDung);

            txtTo.setText(flight.getDestinationCode());
            txtFrom.setText(flight.getOriginCode());
            txtDepatureTime.setText(flight.getDepartureDateTime().substring(0, 5));
            if (flight.isNextDay()) {
                txtArrivalTime.setText((flight.getArriverDateTime().substring(0, 5) + " (+1 ngày)"));
            } else {
                txtArrivalTime.setText(flight.getArriverDateTime().substring(0, 5));
            }

            txtDuration.setText(flight.getDuration());
            txtHangBay.setText(flight.getProviderText());
            txtChuyenBay.setText(flight.getFlightNumber());

            if (flight.getQuickDep()) {
                txtLoaiVe.setText(("{faw_history} " + flight.getSeatClass()));
                new Iconics.Builder().style(new ForegroundColorSpan(mContext.getResources().getColor(R.color.google_yellow)), new BackgroundColorSpan(Color.TRANSPARENT), new RelativeSizeSpan(0.8f)).on(txtLoaiVe).build();
            } else if (flight.getPromo()) {


                txtLoaiVe.setText(("{faw_star} " + flight.getSeatClass()));

                new Iconics.Builder().style(new ForegroundColorSpan(mContext.getResources().getColor(R.color.google_yellow)), new BackgroundColorSpan(Color.TRANSPARENT), new RelativeSizeSpan(0.8f)).on(txtLoaiVe).build();
            } else {
                txtLoaiVe.setText(flight.getSeatClass());
            }

            txtgia.setText(Common.dinhDangTien(flight.getNetPrice()));
            txtgiaPhi.setText(Common.dinhDangTien(flight.getAdult()));
            txtthuePhi.setText(Common.dinhDangTien(flight.getTax() + flight.getAirPortFee()));
            txtthueSanBay.setText((Common.dinhDangTien(flight.getAirPortFee()) + ""));
            txtDiemDung.setText(("{faw_circle1} " + flight.getStopsText()));
            new Iconics.Builder().style(new ForegroundColorSpan(flight.getStopsText().toLowerCase().equals("bay thẳng") ? mContext.getResources().getColor(R.color.primary) : Color.RED), new BackgroundColorSpan(Color.TRANSPARENT), new RelativeSizeSpan(0.8f)).on(txtDiemDung).build();

            imgLogo.setImageResource(Integer.valueOf(mContext.getResources().getIdentifier(flight.getLogo(), "drawable", mContext.getPackageName())));

            builder.setPositiveButton("Đặt vé", new DialogInterface.OnClickListener() {
                public void onClick(DialogInterface dialog, int id) {
                    if (mContext instanceof SearchResult) {
                        ((SearchResult) mContext).clickNext();
                    } else if (mContext instanceof FlightWachesViewActivity) {
                        ((FlightWachesViewActivity) mContext).clickNext();
                    }
                }
            });
            builder.setNegativeButton("Trở về", new DialogInterface.OnClickListener() {
                public void onClick(DialogInterface dialog, int id) {

                }
            });
            builder.setNeutralButton("Lịch sử bay", new DialogInterface.OnClickListener() {
                public void onClick(DialogInterface dialog, int id) {
                    Intent in = new Intent(mContext, FlightHistoryActivityV2.class);
                    in.putExtra("flightNumber", flight.getFlightNumber());
                    mContext.startActivity(in);
                }
            });


            builder.setCancelable(true);
            AlertDialog myDialog = builder.create();
            myDialog.show();
        }


    }

    private void setScaleAnimation(View view) {
        ScaleAnimation anim = new ScaleAnimation(0.0f, 1.0f, 0.0f, 1.0f, Animation.RELATIVE_TO_SELF, 0.5f, Animation.RELATIVE_TO_SELF, 0.5f);
        anim.setDuration(1000);
        view.startAnimation(anim);
    }
}