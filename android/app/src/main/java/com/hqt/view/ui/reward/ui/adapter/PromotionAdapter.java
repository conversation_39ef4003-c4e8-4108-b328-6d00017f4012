package com.hqt.view.ui.reward.ui.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.AppCompatButton;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;

import com.hqt.datvemaybay.R;
import com.hqt.view.ui.reward.data.model.Promotion;
import com.hqt.util.AppConfigs;
import com.hqt.view.ui.reward.ui.activity.RewardActivity;

import java.util.List;

public class PromotionAdapter extends RecyclerView.Adapter<PromotionAdapter.ViewHolder> {

    List<Promotion> contents;
    Context mContext;
    AppCompatActivity mActivity;
    AppCompatButton mBtnGetvoucher;
    static final int TYPE_HEADER = 0;
    static final int TYPE_CELL = 1;
    String selected_position = "";

    public class ViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener {

        public TextView txtName, txtPoint;
        public ImageView banner, logo;

        public ViewHolder(View v) {
            super(v);
            txtName = v.findViewById(R.id.txtName);
            txtPoint = v.findViewById(R.id.txtPoint);
            banner = v.findViewById(R.id.banner);
            logo = v.findViewById(R.id.logo);
            mBtnGetvoucher = v.findViewById(R.id.getVoucher);
            mBtnGetvoucher.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    v.performClick();
                }
            });

            v.setOnClickListener(this);

        }

        @Override
        public void onClick(View v) {

            int viewPosition = getLayoutPosition();
            if (mContext instanceof RewardActivity) {
                ((RewardActivity) mContext).showPromotionView(contents.get(viewPosition));

            }
        }
    }


    public PromotionAdapter(Context context, List<Promotion> contents) {
        this.mContext = context;
        this.contents = contents;
    }

    @Override
    public int getItemViewType(int position) {
        switch (position) {
            case 0:
                return TYPE_HEADER;
            default:
                return TYPE_CELL;
        }
    }

    @Override
    public int getItemCount() {
        return contents.size();
    }

    @Override
    public PromotionAdapter.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = null;

        view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.adapter_promotion, parent, false);

        PromotionAdapter.ViewHolder v = new PromotionAdapter.ViewHolder(view);
        return v;
    }

    @Override
    public void onBindViewHolder(PromotionAdapter.ViewHolder holder, int position) {
        try {


            holder.txtName.setText(contents.get(position).getName().toString());
            holder.txtPoint.setText(contents.get(position).getPoint() + " điểm");

            Glide.with(mContext).load(contents.get(position).getLogo()).override(120, 120)
                    .placeholder(R.mipmap.ic_launcher).skipMemoryCache(true)
                    .into(holder.logo);

            Glide.with(mContext)
                    .load(contents.get(position).getBanner()).skipMemoryCache(true)
                    .placeholder(R.drawable.top_banner)
                    .into(holder.banner);
        } catch (Exception e) {
            AppConfigs.logException(e);
            e.printStackTrace();
        }

    }

    @Override
    public long getItemId(int position) {
        return position;
    }


}