package com.hqt.view.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.core.content.ContextCompat
import androidx.core.view.children
import androidx.core.view.get
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.google.android.flexbox.FlexboxLayout
import com.hqt.data.model.PaymentType
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.AdapterPaymentMethodItemBinding
import com.hqt.util.ViewUtil
import com.hqt.view.ui.payment.NewPaymentActivity
import vn.payoo.core.util.dpToPx


class PaymentMethodAdapter(private val paymentMethodList: List<PaymentType>) :
    RecyclerView.Adapter<PaymentMethodAdapter.ViewHolder>() {
    lateinit var mBinding: AdapterPaymentMethodItemBinding

    class ViewHolder(var binding: AdapterPaymentMethodItemBinding, var mContext: Context) :
        RecyclerView.ViewHolder(binding.root), View.OnClickListener {


        override fun onClick(v: View) {

        }

        @SuppressLint("WrongConstant")
        fun bind(paymentMethod: PaymentType) {
            binding.paymentType = paymentMethod
            binding.root.tag = paymentMethod.PaymentType

            binding.flexBox.removeAllViews()
            if (paymentMethod.list.size > 0) {
                paymentMethod.list.forEach { it ->
                    val bankButton = ImageView(mContext)
                    val lpFlexItem = FlexboxLayout.LayoutParams(
                        dpToPx(120f).toInt(),
                        dpToPx(40f).toInt()
                    )

                    lpFlexItem.flexBasisPercent = 0.25f
                    bankButton.setPadding(0, 10, 0, 10)
                    bankButton.layoutParams = lpFlexItem
                    bankButton.background =
                        ContextCompat.getDrawable(mContext, R.drawable.background_bank_selector)
                    bankButton.tag = it.Bank

                    Glide.with(mContext)
                        .load(it.Logo).apply(RequestOptions.centerInsideTransform())
                        .placeholder(R.drawable.logo_gray)
                        .into(bankButton)

                    binding.flexBox.addView(bankButton)
                    val bank = it
                    val method = paymentMethod
                    bankButton.setOnClickListener {
                        (mContext as NewPaymentActivity).onSelectBank(bank, method)
                        binding.flexBox.children.forEach { v ->
                            v.isSelected = (v.tag == bank.Bank)
                        }
                    }

                }


            }
        }

        init {
            itemView.setOnClickListener(this)

        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val layoutInflater = LayoutInflater.from(parent.context)
        val binding: AdapterPaymentMethodItemBinding = DataBindingUtil.inflate(
            layoutInflater,
            R.layout.adapter_payment_method_item,
            parent,
            false
        )
        mBinding = binding

        binding.paymentTypeView.setOnClickListener {

            if (binding.methodItem.isVisible) {
                ViewUtil.collapse(binding.methodItem)
                //binding.paymentTypeView.showIcon.animate().rotation(-0f).start()


            } else {
                ViewUtil.expand(binding.methodItem)
                //binding.paymentTypeView.showIcon.animate().rotation(180f).start()
                (parent.context as NewPaymentActivity).clearSelectBank(binding.root.tag.toString())

                if (binding.flexBox.childCount > 0)
                    binding.flexBox[0].performClick()


            }

        }
        binding.paymentTypeViewHideAll.setOnClickListener {

            ViewUtil.collapse(binding.methodItem)
            //binding.paymentTypeView.showIcon.animate().rotation(-0f).start()
            binding.flexBox.children.forEach { v -> v.isSelected = false }

        }


        return ViewHolder(binding, parent.context)

    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val paymentMethod = paymentMethodList[position]

        holder.bind(paymentMethod)
    }

    override fun getItemCount(): Int {
        return paymentMethodList.size
    }


}