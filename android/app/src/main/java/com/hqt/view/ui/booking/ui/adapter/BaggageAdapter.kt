package com.hqt.view.ui.booking.ui.adapter

import com.hqt.base.BaseAdapter
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ListBagItemBinding
import com.hqt.view.ui.booking.data.model.Baggage


class BaggageAdapter(private val listener: (Baggage) -> Unit) :
    BaseAdapter<Baggage, ListBagItemBinding>(listener) {

    private var selectItem: String? = ""


    override fun getLayoutRes(): Int {
        return R.layout.list_bag_item
    }



    override fun bind(binding: ListBagItemBinding, position: Int, model: Baggage) {
        binding.apply {
//            binding.viewmodel = model

            bagViewBg.isSelected = selectItem == model.value


            var priceText = Common.dinhDangTien(model.price?.toIntOrNull() ?: 0)
            if ((model.price?.toIntOrNull() ?:0) < 1000) {
                priceText = "Miễn phí"
            }
            if (model.value?.toIntOrNull() == 0) {
                priceText = "Không thêm"
            }

            txtBagValue.text = "${model.value} kg"
//            pos.text = pos.toString() + ""
            txtBagPrice.text = priceText

        }

    }





    override fun onItemClickListener(model: Baggage) {
        listener(model)

        val uuidOld = selectItem
        selectItem = model.value ?: ""

        val indexOld = getData().indexOfFirst { it.value == uuidOld }
        if (indexOld != -1) notifyItemChanged(indexOld)

        val currentIndex = getData().indexOfFirst { it.value == selectItem }
        if (currentIndex != -1) notifyItemChanged(currentIndex)


    }

}