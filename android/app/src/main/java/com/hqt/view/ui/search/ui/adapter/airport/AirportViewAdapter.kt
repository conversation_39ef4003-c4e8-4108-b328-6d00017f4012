package com.hqt.view.ui.search.ui.adapter.airport


import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.hqt.view.ui.search.data.model.AirportGroup
import com.hqt.datvemaybay.R

class AirportViewAdapter(private val mList: List<AirportGroup>) :
    RecyclerView.Adapter<AirportViewAdapter.ViewHolder>() {

    // create new views
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.list_airport_group_item, parent, false)

        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {

        val group = mList[position]
        holder.groupName.text = group.title
        val adapter = AirportItemAdapter(group.airPorts)
        holder.recyclerView.adapter = adapter
        val layoutManager: RecyclerView.LayoutManager = LinearLayoutManager(holder.groupName.context)
        holder.recyclerView.layoutManager = layoutManager

    }

    // return the number of the items in the list
    override fun getItemCount(): Int {
        return mList.size
    }

    class ViewHolder(ItemView: View) : RecyclerView.ViewHolder(ItemView) {
        val groupName: TextView = itemView.findViewById(R.id.group_name)
        var recyclerView: RecyclerView = itemView.findViewById(R.id.list_airport)


    }
}