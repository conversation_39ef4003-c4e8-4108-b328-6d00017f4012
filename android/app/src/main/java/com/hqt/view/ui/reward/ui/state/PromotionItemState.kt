package com.hqt.view.ui.reward.ui.state

import android.text.Html
import android.text.Spanned
import com.hqt.datvemaybay.Common
import com.hqt.view.ui.reward.data.model.Promotion

data class PromotionItemState(val item: Promotion){

    fun getPoint() = item.point.toString() + " điểm"
    fun getDescriptionText(): Spanned = Html.fromHtml(item.description)

    fun getExpiredTimeText(): String {
        val expiredTime = Common.dateToString(item.expiredTime, "dd/MM/yyyy")
        return "Hạn sử dụng: $expiredTime"
    }
}
