package com.hqt.view.ui.flighthistory.ui.dialog

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import com.android.volley.VolleyError
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.gson.reflect.TypeToken
import com.hqt.base.model.State
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.FlightHistoryDetailBinding
import com.hqt.util.AppController
import com.hqt.util.Helper.clickWithDebounce
import com.hqt.util.SSLSendRequest
import com.hqt.util.Widget.getListSlider
import com.hqt.util.helper.ProgressBarAnimation
import com.hqt.view.adapter.SliderAdapterCustom
import com.hqt.view.ui.flighthistory.data.model.FlightHistoryItem
import com.hqt.view.ui.flighthistory.ui.FlightHistoryViewModel
import com.hqt.view.ui.flighthistory.ui.activity.MapViewActivity
import dagger.hilt.android.AndroidEntryPoint
import org.json.JSONObject

@AndroidEntryPoint
class FlightHistoryDetailDialog(private val flightHistoryItem : FlightHistoryItem, private val fromShortView : Boolean) : BottomSheetDialogFragment() {

    val viewModel: FlightHistoryViewModel by activityViewModels()


    var binding: FlightHistoryDetailBinding? = null


    var onActionDone: (Boolean) -> Unit = {}

    var adapter : SliderAdapterCustom? = null

    companion object {
        const val TAG = "ModalBottomSheet"
    }


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FlightHistoryDetailBinding.inflate(layoutInflater, container, false)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding?.apply {




            flightHistory = flightHistoryItem

            adapter = SliderAdapterCustom(requireContext(), true)
            adapter?.let {
                imageSlider.setSliderAdapter(it)
            }

            val mBehavior = BottomSheetBehavior.from(root.parent as View)
            mBehavior.state = BottomSheetBehavior.STATE_EXPANDED
            mBehavior.isHideable = true
            mBehavior.isDraggable = true


            flightHistoryItem.aircraft?.registration?.let {
                viewModel.getFlightImages(it)
            }

            if (!fromShortView && flightHistoryItem.aircraft?.registration != null) {
                btnViewOnMap.visibility = View.VISIBLE
                btnViewOnMap.clickWithDebounce(500) {
                    if (flightHistoryItem.aircraft?.registration != null) {
                        val intent = Intent(requireContext(), MapViewActivity::class.java)
                        intent.putExtra("flightId", flightHistoryItem.id)
                        intent.putExtra("onMoveCamera", true)
                        intent.putExtra("isHistory", true)
                        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                        startActivity(intent)
                        activity?.overridePendingTransition(R.anim.enter, R.anim.exit)
                    }

                }
            }


            closeSheet.setOnClickListener {
                dismiss()
            }


            if (flightHistoryItem.status.live) {
                viewModel.getFlightTrack(flightHistoryItem.id)
            }
            observe()


        }




        dialog?.let {
            val sheet = it as BottomSheetDialog
            sheet.behavior.state = BottomSheetBehavior.STATE_EXPANDED
        }


    }



    fun observe() {

        viewModel.sliderItemLiveData.observe(viewLifecycleOwner) {
            when (it) {
                is State.Error -> {

                }

                State.Loading -> {

                }

                is State.Success -> {
                    binding?.apply {


                        adapter?.renewItems(it.data)

                    }

                }
            }
        }
        viewModel.flightTrackLiveData.observe(viewLifecycleOwner) {
            when (it) {
                is State.Error -> {

                }

                State.Loading -> {

                }

                is State.Success -> {
                    binding?.apply {



                        flightHistory = it.data
                        if (it.data.live != null) {
                            val anim = ProgressBarAnimation(
                                flightProgress,
                                0, it.data.live?.progress ?: 0
                            )
                            anim.duration = 500
                            flightProgress.startAnimation(anim)
                        }

                    }

                }
            }
        }


    }


}