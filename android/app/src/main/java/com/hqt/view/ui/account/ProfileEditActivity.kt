package com.hqt.view.ui.account

import android.app.DatePickerDialog
import android.content.res.Configuration
import android.content.res.Resources
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import com.android.volley.VolleyError
import com.bumptech.glide.Glide
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.firebase.FirebaseException
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseAuthInvalidCredentialsException
import com.google.firebase.auth.FirebaseAuthUserCollisionException
import com.google.firebase.auth.PhoneAuthCredential
import com.google.firebase.auth.PhoneAuthOptions
import com.google.firebase.auth.PhoneAuthProvider
import com.hqt.data.model.User
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ProfileEditActivityBinding
import com.hqt.datvemaybay.databinding.WidgetOtpLayoutBinding
import com.hqt.util.AppConfigs
import com.hqt.util.AppController
import com.hqt.util.AppController.Companion.instance
import com.hqt.util.SSLSendRequest
import com.hqt.util.SSLSendRequest.CallBackInterface
import com.hqt.view.ui.BaseActivityKt
import `in`.aabhasjindal.otptextview.OTPListener
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import org.json.JSONException
import org.json.JSONObject
import java.util.Calendar
import java.util.Locale
import java.util.concurrent.TimeUnit


class ProfileEditActivity : BaseActivityKt<ProfileEditActivityBinding>() {
    override val layoutId: Int = R.layout.profile_edit_activity

    private var disposable: Disposable? = null
    private lateinit var callbacks: PhoneAuthProvider.OnVerificationStateChangedCallbacks
    private lateinit var auth: FirebaseAuth
    private lateinit var newUserInfo: User
    private lateinit var dialog: BottomSheetDialog

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        auth = FirebaseAuth.getInstance()
        auth.setLanguageCode("vi")

        getToolbar().title = "Thay đổi thông tin"
        getToolbar().setNavigationIcon(R.drawable.ic_action_back_home)
        supportActionBar!!.setDisplayShowHomeEnabled(true)
        dialog = BottomSheetDialog(this)

        if (isUserSigned && instance.user != null) {
            try {
                newUserInfo = instance.user!!.copy()
                getViewBindding().user = newUserInfo

                Glide.with(applicationContext).load(newUserInfo.avatar)
                    .placeholder(R.drawable.logo_mini)
                    .skipMemoryCache(true).into(getViewBindding().profileImage)
            } catch (E: Exception) {
                getUserInfo()
            }
        }

        getViewBindding().btnSaved.setOnClickListener {
            hideKeyBoard(getViewBindding().noneEdit)
            getViewBindding().btnSaved.isEnabled = false
            showSnackBarMessage("Đang kiểm tra thông tin!", R.color.google_yellow)

            if (Common.addWorldPhoneFormat(newUserInfo.phoneNumber) != Common.addWorldPhoneFormat(
                    instance.user!!.phoneNumber
                )
            ) {
                val options = PhoneAuthOptions.newBuilder(auth)
                    .setPhoneNumber(Common.addWorldPhoneFormat(newUserInfo.phoneNumber))
                    .setTimeout(60L, TimeUnit.SECONDS).setActivity(this).setCallbacks(callbacks)
                    .build()
                PhoneAuthProvider.verifyPhoneNumber(options)
            } else {
                updateUser()
            }

        }

        initChangeBirthDate()
        initFirebaseAuth()
    }

    private fun getUserInfo() {
        SSLSendRequest(this).GET(
            true,
            "users/" + auth.uid,
            JSONObject(),
            object : CallBackInterface {

                override fun onSuccess(response: JSONObject, cached: Boolean) {
                    try {
                        val jData = response.getJSONObject("data")
                        val user = instance.gSon.fromJson(jData.toString(), User::class.java)
                        instance.user = user
                        newUserInfo = user.copy()
                        getViewBindding().user = newUserInfo

                        Glide.with(applicationContext).load(user.avatar)
                            .placeholder(R.drawable.logo_mini)
                            .skipMemoryCache(true).into(getViewBindding().profileImage)

                    } catch (e: JSONException) {
                        AppConfigs.logException(e)
                    }
                }

                override fun onFail(error: VolleyError) { // LOGIN ERROR
                }
            })
    }

    private fun updateUser() {

        try {
            disposable = AppController.instance.getService()
                .updateUser(AppController.instance.user!!.uid!!, getViewBindding().user!!)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe {
                    getViewBindding().noneEdit.requestFocus()

                }.observeOn(AndroidSchedulers.mainThread()).toObservable().doOnComplete {
                    showSnackBarMessage("Cập nhật thông tin thành công!", R.color.green)

                }.subscribe({ response ->

                    getViewBindding().user = response.data
                    instance.user = response.data
                    getViewBindding().btnSaved.isEnabled = true
                    Common.isNewData = true


                }, { throwable ->
                    getViewBindding().btnSaved.isEnabled = true
                    showSnackBarMessage(
                        "Cập nhật thông tin không thành công. Vui lòng thử lại sau",
                        R.color.black
                    )
                    AppConfigs.logException(throwable)
                })
        } catch (e: Exception) {
            AppConfigs.logException(e)
        }
    }

    private fun initFirebaseAuth() {
        try {
            callbacks = object : PhoneAuthProvider.OnVerificationStateChangedCallbacks() {

                override fun onVerificationCompleted(credential: PhoneAuthCredential) {

                    showSnackBarMessage("Xác minh thay đổi số điện thoại thành công", R.color.green)
                    if (dialog.isShowing) dialog.hide()
                    updateUser()
                }

                override fun onVerificationFailed(e: FirebaseException) {

                    showSnackBarMessage(
                        "Xác minh số điện thoại không thành công. Vui lòng thử lại sau!",
                        R.color.green
                    )
                    getViewBindding().user!!.phoneNumber =
                        AppController.instance.user!!.phoneNumber // updateUser()
                }

                override fun onCodeSent(
                    verificationId: String,
                    token: PhoneAuthProvider.ForceResendingToken
                ) {
                    showOtpVerify(getViewBindding().user!!.phoneNumber!!, verificationId)
                    showSnackBarMessage(
                        "Đã gửi mã xác minh số điện thoại. Vui lòng đợi giây lát !!",
                        R.color.google_yellow
                    )
                }
            }
        } catch (e: Exception) {
            AppConfigs.logException(e)
        }
    }

    override fun onResume() {
        super.onResume()
        val locale = Locale("vi")
        Locale.setDefault(locale)
        val resources: Resources = this.resources
        val config: Configuration = resources.getConfiguration()
        config.setLocale(locale)
        resources.updateConfiguration(config, resources.displayMetrics)
    }

    private fun initChangeBirthDate() {
        try {
            var curentBirthDay =
                Common.stringToDate(AppController.instance.user!!.birthDay, "yyyy-MM-dd")

            if (curentBirthDay == null) {
                curentBirthDay = Calendar.getInstance()

            }

            val dateSetListener =
                DatePickerDialog.OnDateSetListener { view, year, monthOfYear, dayOfMonth ->
                    curentBirthDay.set(Calendar.YEAR, year)
                    curentBirthDay.set(Calendar.MONTH, monthOfYear)
                    curentBirthDay.set(Calendar.DAY_OF_MONTH, dayOfMonth)
                    getViewBindding().inputPaxBirthday.setText(
                        Common.dateToString(
                            curentBirthDay.time,
                            "yyyy-MM-dd"
                        )
                    )

                }

            getViewBindding().inputPaxBirthday.setOnClickListener {
                val dialog = DatePickerDialog(
                    this,
                    dateSetListener,
                    curentBirthDay.get(Calendar.YEAR),
                    curentBirthDay.get(Calendar.MONTH),
                    curentBirthDay.get(Calendar.DAY_OF_MONTH)
                )
                var now = Calendar.getInstance()
                dialog.datePicker.maxDate = now.timeInMillis
                (now.add(Calendar.YEAR, -80))
                dialog.datePicker.minDate = now.timeInMillis
                dialog.show()

            }
        } catch (e: Exception) {
            AppConfigs.logException(e)
        }
    }

    override fun onDestroy() {
        disposable?.dispose()
        disposable = null

        super.onDestroy()
    }

    private fun showOtpVerify(phone: String, verificationId: String) {
        try {
            val layoutInflater = LayoutInflater.from(this)
            val binding: WidgetOtpLayoutBinding = DataBindingUtil.inflate(
                layoutInflater,
                R.layout.widget_otp_layout,
                null,
                false
            )

            binding.otpConfirmView.visibility = View.VISIBLE
            binding.otpPhoneInputView.visibility = View.GONE



            binding.otpView.otpListener = object : OTPListener {
                override fun onInteractionListener() {
                }

                override fun onOTPComplete(otp: String) {
                    binding.otpError.visibility = View.GONE
                    val credential = PhoneAuthProvider.getCredential(verificationId, otp)
                    linkWithCredential(credential)
                }
            }

            dialog.setContentView(binding.root)
            (binding.root.parent as View).setBackgroundColor(Color.TRANSPARENT)
            (binding.root.parent as View).setBackgroundResource(R.drawable.rounded_dialog)
            var mBehavior = BottomSheetBehavior.from(binding.root.parent as View)
            mBehavior.state = BottomSheetBehavior.STATE_EXPANDED
            dialog.window!!.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)

            dialog.show()

        } catch (e: java.lang.Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }

    }

    private fun linkWithCredential(credential: PhoneAuthCredential) {

        auth.currentUser!!.linkWithCredential(credential).addOnCompleteListener(this) { task ->
            if (task.isSuccessful) {
                if (dialog.isShowing) dialog.hide()
                updateUser()
                auth.currentUser!!.uid
            } else {

                if (task.exception is FirebaseAuthInvalidCredentialsException) {
                    dialog.findViewById<TextView>(R.id.otp_error)!!.visibility = View.VISIBLE
                } else if (task.exception is FirebaseAuthUserCollisionException) {
                    dialog.findViewById<TextView>(R.id.otp_error)!!.text =
                        "Số điện thoại đã được sử dụng ở tài khoản khác. Vui lòng đăng nhập lại!"
                    dialog.findViewById<TextView>(R.id.otp_error)!!.visibility = View.VISIBLE
                } else {
                    dialog.findViewById<TextView>(R.id.otp_error)!!.text =
                        "Xác minh OTP không thành công. Vui lòng thử lại sau!"
                    dialog.findViewById<TextView>(R.id.otp_error)!!.visibility = View.VISIBLE
                }
            }
        }
    }

}