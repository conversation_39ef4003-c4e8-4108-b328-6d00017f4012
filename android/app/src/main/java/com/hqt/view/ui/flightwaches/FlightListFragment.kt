package com.hqt.view.ui.flightwaches

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.Toolbar
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.hqt.data.model.Flight
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.FragmentRecyclerviewFlightWatchesBinding
import com.hqt.util.AppConfigs
import com.hqt.view.adapter.FlightViewAdapter

class FlightListFragment : Fragment() {
    lateinit var recyclerView: RecyclerView
    lateinit var mAdapter: FlightViewAdapter
    lateinit var toolbar: Toolbar
    lateinit var binding: FragmentRecyclerviewFlightWatchesBinding
    private var flightFareList: ArrayList<Flight> = ArrayList()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_recyclerview_flight_watches, container, false)

        val rootView = binding.root

        recyclerView = binding.recyclerView
        recyclerView.setHasFixedSize(true)
        recyclerView.layoutManager = LinearLayoutManager(activity)

        mAdapter = FlightViewAdapter(activity, flightFareList, true)
        recyclerView.adapter = mAdapter

        return rootView
    }

    fun addFlightData(flightFares: List<Flight>?) {
        try {
            flightFareList.clear()
            flightFareList.addAll(flightFares!!)
            if (::mAdapter.isInitialized) mAdapter.notifyDataSetChanged()
        } catch (e: Exception) {
            AppConfigs.logException(e)
        }
    }
}