package com.hqt.view.ui.search.ui.adapter.airport


import android.app.Activity
import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.hqt.view.ui.search.data.model.AirportInfo
import com.hqt.datvemaybay.R

class AirportItemAdapter(private val mList: List<AirportInfo>) : RecyclerView.Adapter<AirportItemAdapter.ViewHolder>() {
    val RESULT_OK = -1

    // create new views
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.listairport_info_item, parent, false)

        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {

        val airport = mList[position]
        holder.airportCity.text = airport.city
        holder.airportName.text = airport.name
        holder.airportCode.text = airport.code

        holder.itemView.setOnClickListener {

            val data = Intent()
            data.putExtra("code", airport.code)
            data.putExtra("name", airport.city)
            data.putExtra("isDomestic", airport.isDomestic)
            data.putExtra("airportInfo", airport)

            (holder.itemView.context as Activity).setResult(RESULT_OK, data)
            (holder.itemView.context as Activity).finish()
        }

    }

    // return the number of the items in the list
    override fun getItemCount(): Int {
        return mList.size
    }

    class ViewHolder(ItemView: View) : RecyclerView.ViewHolder(ItemView) {
        val airportName: TextView = itemView.findViewById(R.id.airPortName)
        val airportCity: TextView = itemView.findViewById(R.id.airPortCity)
        val airportCode: TextView = itemView.findViewById(R.id.airPortCode)
    }
}