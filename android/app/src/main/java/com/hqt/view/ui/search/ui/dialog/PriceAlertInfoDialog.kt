package com.hqt.view.ui.search.ui.dialog

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.fragment.app.activityViewModels
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.hqt.datvemaybay.databinding.WidgetPriceAlertInfoBinding
import com.hqt.util.Log
import com.hqt.view.ui.flighthistory.other.PermissionUtils.createNotificationChannel
import com.hqt.view.ui.flighthistory.other.PermissionUtils.requestPermission
import com.hqt.view.ui.flightwaches.NewFlightWatchesActivity
import com.hqt.view.ui.search.ui.SearchResultViewModel
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PriceAlertInfoDialog : BottomSheetDialogFragment() {

    val viewModel: SearchResultViewModel by activityViewModels()


    var binding: WidgetPriceAlertInfoBinding? = null


    var onActionDone: (Boolean) -> Unit = {}

    companion object {
        const val TAG = "ModalBottomSheet"
    }


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = WidgetPriceAlertInfoBinding.inflate(layoutInflater, container, false)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding?.apply {




            booking = viewModel.bookingDetail



            btnPriceAlertAdd.setOnClickListener {
                try {
                    if (ContextCompat.checkSelfPermission(requireContext(), Manifest.permission.POST_NOTIFICATIONS) == PackageManager.PERMISSION_GRANTED) {

                        val booking = viewModel.bookingDetail
                        val intent = Intent(requireContext(), NewFlightWatchesActivity::class.java)
                        intent.putExtra("origin", booking.origin_code)
                        intent.putExtra("destination", booking.destination_code)
                        intent.putExtra("departureDate", booking.departure_date)
                        intent.putExtra("adult", booking.adult)
                        intent.putExtra("child", booking.child)
                        intent.putExtra("infant", booking.infant)
                        intent.putExtra("autoSaved", true)

                        startActivity(intent)

                    } else {
                        createNotificationChannel(requireActivity() as AppCompatActivity)
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                            requestPermission(requireActivity() as AppCompatActivity, 9999, Manifest.permission.POST_NOTIFICATIONS, false)
                        }
                    }

                }catch (ex : Exception){
                    Log.logException(ex)
                }


            }




            observe()






        }




        dialog?.let {
            val sheet = it as BottomSheetDialog
            sheet.behavior.state = BottomSheetBehavior.STATE_EXPANDED
        }


    }


    fun observe(){




    }



}