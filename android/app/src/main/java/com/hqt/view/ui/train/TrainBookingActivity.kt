package com.hqt.view.ui.train

import android.app.ProgressDialog
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.lifecycle.ViewModelProvider
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.android.volley.VolleyError
import com.google.firebase.analytics.FirebaseAnalytics
import com.hqt.data.model.BookingTrain
import com.hqt.data.model.Passenger
import com.hqt.data.model.PassengerType
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ActivityTrainBookBinding
import com.hqt.util.*
import com.hqt.util.SSLSendRequest.CallBackInterface
import com.hqt.view.ui.BaseActivityKt
import com.hqt.viewmodel.TrainBookingViewModel
import org.json.JSONException
import org.json.JSONObject


class TrainBookingActivity : BaseActivityKt<ActivityTrainBookBinding>() {

    override val layoutId: Int = R.layout.activity_train_book
    lateinit var booking: BookingTrain
    lateinit var viewModel: TrainBookingViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        getToolbar().title = "Điền thông tin"
        getToolbar().setNavigationIcon(R.drawable.ic_action_back_home)
        supportActionBar!!.setDisplayShowHomeEnabled(true)

        booking = intent.getSerializableExtra("trainBookingInfo") as BookingTrain

        if (isUserSigned && firebaseUser != null) {
            booking.contact_name = firebaseUser!!.displayName.toString()
            booking.contact_email = firebaseUser!!.email.toString()
        }

        initAnalytics(booking, FirebaseAnalytics.Event.BEGIN_CHECKOUT)

        viewModel = ViewModelProvider.AndroidViewModelFactory(application).create(TrainBookingViewModel::class.java)

        viewModel.mBooking.value = (booking)
        viewModel.isUserSigned.value = (isUserSigned)
        viewModel.getBooking().observe(this, { bk -> booking = bk })

        getViewBindding().viewModel = viewModel
        getViewBindding().lifecycleOwner = this

        showBookingTripDetail(booking)
        showPaxInput()
        setClickHandlers() //START SHOW SHOWCASE
        Widget.retrievePassenger(this, firebaseUser?.uid)
        initDefaultInfo()

    }

    private fun setClickHandlers() {
        getViewBindding().content.btnGetVoucher.setOnClickListener {
            viewModel.onChange()

            if (booking.voucher.isNullOrEmpty()) {
                Toast.makeText(applicationContext, "Vui lòng nhập mã giảm giá!", Toast.LENGTH_SHORT).show()
                getViewBindding().content.txtVoucherCode.requestFocus()
            } else {
                useVoucher()
            }
        }
        getViewBindding().content.btnBookVe.setOnClickListener {

            if (!booking.pax_info.isValidated()) {
                Toast.makeText(applicationContext, "Vui lòng nhập đầy đủ thông tin hành khách", Toast.LENGTH_SHORT)
                    .show()
                getViewBindding().content.scrollView.smoothScrollTo(0,
                    (getViewBindding().content.horizalScroll.bottom) - 200)
            } else if (isValidatedContact()) {
                AppConfigs.Log("BOOKING JSON", AppController.instance.gSon.toJson(booking, BookingTrain::class.java))
                sendBooking()
                initAnalytics(booking, FirebaseAnalytics.Event.PURCHASE)
            }
        }
        getViewBindding().content.loginLayout.setOnClickListener {
            signIn()
        }
    }

    private fun isValidatedContact(): Boolean {
        if (booking.contact_name.isEmpty()) {
            Toast.makeText(applicationContext, "Vui lòng điền tên liên hệ", Toast.LENGTH_SHORT).show()
            getViewBindding().content.txtContactName.requestFocus()
            return false
        } else if (booking.contact_phone.isEmpty()) {
            Toast.makeText(applicationContext, "Vui lòng điền số điện thoại liên hệ", Toast.LENGTH_SHORT).show()
            getViewBindding().content.txtContactPhone.requestFocus()
            return false
        }
        return true
    }

    private fun sendBooking() {


        val dialog = ProgressDialog(this)
        dialog.setMessage("Đang thực hiện đặt chỗ ...\nVui lòng đợi giây lát!")
        dialog.isIndeterminate = false
        dialog.max = 100
        dialog.setCanceledOnTouchOutside(false)
        dialog.setCancelable(false)
        dialog.setProgressStyle(ProgressDialog.STYLE_SPINNER)
        dialog.show()

        var postParam = JSONObject()
        try {
            val contactInfo = JSONObject()
            contactInfo.put("fullname", booking.contact_name)
            contactInfo.put("phone", booking.contact_phone)
            contactInfo.put("email", booking.contact_email)

            postParam = JSONObject(AppController.instance.gSon.toJson(booking, BookingTrain::class.java))
            postParam.put("contact", contactInfo)
            postParam.put("gcm", Common.FCM_TOKEN)

        } catch (e: JSONException) {
            e.printStackTrace()
        }

        SSLSendRequest(this).POST(false, "Trains/Booking", postParam, object : CallBackInterface {
            override fun onSuccess(response: JSONObject, cached: Boolean) {
                try {
                    if (response.has("data")) {
                        val jsonBookingTrain = response.getJSONObject("data")
                        val bookingReturn = AppController.instance.gSon.fromJson(jsonBookingTrain.toString(),
                            BookingTrain::class.java)

                        if (bookingReturn.id.isEmpty()) {
                            Toast.makeText(applicationContext,
                                "Thật xin lỗi :( \nCó lỗi xảy ra khi kết nối máy chủ đặt chỗ \n Vui lòng thử lại hoặc gọi " + AppConfigs.getInstance().config.getString(
                                    "hotline") + " để được hỗ trợ!",
                                Toast.LENGTH_SHORT).show()
                        } else {

                            val i = Intent(applicationContext, TrainBookingViewActivity::class.java)
                            i.putExtra("BookingType", "TRAIN")
                            i.putExtra("BookingInfo", bookingReturn)
                            i.putExtra("showLoading", false)

                            startActivity(i)

                            val intent = Intent("bookingInsert")
                            LocalBroadcastManager.getInstance(applicationContext).sendBroadcast(intent)

                            finish()
                        }

                    }
                    dialog.dismiss()

                } catch (e: JSONException) {
                    dialog.dismiss()
                    Toast.makeText(applicationContext,
                        "Thật xin lỗi :( \nCó lỗi xảy ra khi kết nối máy chủ đặt chỗ \n Vui lòng thử lại hoặc gọi " + AppConfigs.getInstance().config.getString(
                            "hotline") + " để được hỗ trợ!",
                        Toast.LENGTH_SHORT).show()
                    AppConfigs.logException(e)
                    e.printStackTrace()
                }
            }

            override fun onFail(e: VolleyError) {
                dialog.dismiss()
                Toast.makeText(applicationContext,
                    "Thật xin lỗi :( \nCó lỗi xảy ra khi kết nối máy chủ đặt chỗ \n Vui lòng thử lại hoặc gọi " + AppConfigs.getInstance().config.getString(
                        "hotline") + " để được hỗ trợ!",
                    Toast.LENGTH_SHORT).show()
                AppConfigs.logException(e)
                e.printStackTrace()
            }
        })
    }

    private fun useVoucher() {
        val dialog = ProgressDialog(this)
        dialog.setMessage("Đang tải dữ liệu ...")
        dialog.isIndeterminate = false
        dialog.max = 100
        dialog.setCanceledOnTouchOutside(true)
        dialog.setCancelable(true)
        dialog.setProgressStyle(ProgressDialog.STYLE_SPINNER)
        dialog.show()

        val postParam = JSONObject()
        postParam.put("voucher", booking.voucher)

        SSLSendRequest(this).POST(false, "AirLines/Voucher/Use", postParam, object : CallBackInterface {
            override fun onSuccess(response: JSONObject, cached: Boolean) {
                try {
                    if (!response.isNull("data")) {
                        val data = response.getJSONObject("data")
                        val rtCode = data.getString("voucher")
                        val rtDiscount = data.getInt("discount")
                        val rtresult = data.getString("text")
                        if (rtDiscount > 0) {
                            booking.discount = rtDiscount
                            booking.voucher = rtCode

                            // getViewBindding().viewModel = viewModel
                            getViewBindding().content.btnBookVe.requestFocus()
                            viewModel.onChange()
                        }
                        Toast.makeText(this@TrainBookingActivity, rtresult, Toast.LENGTH_SHORT).show()

                    } else {
                        Common.showAlertDialog(this@TrainBookingActivity,
                            "Thật tiếc",
                            "Mã giảm giá không đúng!",
                            false,
                            false)
                    }
                    if (dialog.isShowing) dialog.dismiss()
                } catch (e: JSONException) {
                    AppConfigs.logException(e)
                    e.printStackTrace()
                }
            }

            override fun onFail(e: VolleyError) {
                if (dialog.isShowing) dialog.dismiss()
                Common.showAlertDialog(this@TrainBookingActivity,
                    "Thông báo !",
                    "Không tìm thấy voucher \nVui lòng liên lạc chúng tôi để được hỗ trợ",
                    false,
                    false)
            }
        })
    }

    private fun showPaxInput() {

        getViewBindding().content.paxInPut.removeAllViews()
        var i = 0
        booking.pax_info.adult.forEach { pax ->
            pax.startInput(PassengerType.ADULT, i++)
            Widget.showPaxList(true, pax, false, this, getViewBindding().content.paxInPut, true)

        }

        booking.pax_info.child.forEach { pax ->
            pax.startInput(PassengerType.CHILD, i++)
            Widget.showPaxList(true, pax, false, this, getViewBindding().content.paxInPut, true)

        }

        booking.pax_info.student.forEach { pax ->
            pax.startInput(PassengerType.STUDENT, i++)
            Widget.showPaxList(true, pax, false, this, getViewBindding().content.paxInPut, true)
        }

        booking.pax_info.older.forEach { pax ->
            pax.startInput(PassengerType.OLDER, i++)
            Widget.showPaxList(true, pax, false, this, getViewBindding().content.paxInPut, true)

        }

    }

    fun initAnalytics(booking: BookingTrain, event: String) {

        val params = Bundle()
        params.putString(FirebaseAnalytics.Param.ORIGIN, booking.departure_f.originCode)
        params.putString(FirebaseAnalytics.Param.DESTINATION, booking.departure_f.destinationCode)
        params.putString(FirebaseAnalytics.Param.START_DATE, booking.departure_f.arrivalDateTime.toString())
        params.putString(FirebaseAnalytics.Param.END_DATE, booking.departure_f.arrivalDateTime.toString())
        params.putString(FirebaseAnalytics.Param.FLIGHT_NUMBER, booking.departure_f.trainNumber)
        params.putString(FirebaseAnalytics.Param.ITEM_NAME,
            booking.departure_f.originCode + booking.departure_f.destinationCode + booking.departure_f.trainNumber)
        params.putString(FirebaseAnalytics.Param.NUMBER_OF_PASSENGERS,
            (booking.departure_f.adultCount + booking.departure_f.student + booking.departure_f.childCount + booking.departure_f.older).toString() + "")
        firebaseAnalytics.logEvent(event, params)
        firebaseAnalytics.setCurrentScreen(this, "train_booking_input", null)
    }

    private fun showBookingTripDetail(booking: BookingTrain) {
        getViewBindding().swipeRefreshLayout.isEnabled = false
        getViewBindding().shimmerViewContainer.visibility = View.GONE


        getViewBindding().content.tripContainer.removeAllViews()
        val trainInfo = Widget.trainTripView(booking.departure_f, this, getViewBindding().content.tripContainer)

        trainInfo.setOnClickListener {
            getViewBindding().content.horizalScroll.fullScroll(View.FOCUS_LEFT)
        }

        if (booking.is_round_trip) {
            getViewBindding().content.tripContainerRt.removeAllViews()
            val trainInfoRt: View = Widget.trainTripView(booking.return_f,
                this,
                getViewBindding().content.tripContainerRt)
            trainInfoRt.setOnClickListener {
                getViewBindding().content.horizalScroll.fullScroll(View.FOCUS_RIGHT)
            }
        }
    }

    private fun initDefaultInfo() {
        try {
            if (isUserSigned && AppController.instance.user != null) {
                booking.contact_name = AppController.instance.user?.userName!!
                booking.contact_email = AppController.instance.user?.userEmail!!
                booking.contact_phone = AppController.instance.user?.phoneNumber!!
            }
        } catch (e: Exception) {

        }

        val settings = getSharedPreferences("12BAY-APP-CONFIG", 0)
        if (!isUserSigned) {
            getViewBindding().content.txtContactPhone.setText(settings.getString("phone", "").toString()
                .replace("+84", "0"))
            getViewBindding().content.txtContactName.setText(settings.getString("name", "").toString())
            val Umail = settings.getString("Uemail", "").toString()
            getViewBindding().content.txtContactEmail.setText(settings.getString("email", Umail).toString())
        } else {
            refreshLayout()
        }
    }

    override fun refreshLayout() {
        try {
            if (isUserSigned && AppController.instance.user != null) {
                Widget.retrievePassenger(this, firebaseUser?.uid)
                getViewBindding().content.loginLayout.visibility = (View.GONE)
                getViewBindding().content.layoutPointReward.visibility = (View.VISIBLE)
                getViewBindding().content.txtContactName.setText(Common.unAccent(AppController.instance.user?.userName)
                    .toUpperCase())

                val phone = if (firebaseUser?.phoneNumber != null && !firebaseUser?.phoneNumber.equals("",
                        ignoreCase = true)) AppController.instance.user?.phoneNumber else SharedPrefs.getInstance()
                    .get("phone", String::class.java).toString()


                getViewBindding().content.txtContactPhone.setText(phone?.replace("+84", "0"))
                getViewBindding().content.txtContactEmail.setText(AppController.instance.user?.userEmail)
            } else {
                getViewBindding().content.loginLayout.visibility = View.VISIBLE
                getViewBindding().content.layoutPointReward.visibility = (View.GONE)
            }
        } catch (e: java.lang.Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }
    }

    override fun onSystemSettingChange(type: AppConfigs.SystemSettingType) {

        if (type == AppConfigs.SystemSettingType.INTERNET) {
            if (this::viewModel.isInitialized) {
                viewModel.updateInternetStatus(isInternetConnected)
            }
        }
        if (type == AppConfigs.SystemSettingType.USER) {
            if (this::viewModel.isInitialized) {
                viewModel.updateUserStatus(isUserSigned)
                getViewBindding().viewModel = viewModel
                refreshLayout()
            }
        }
    }
}
