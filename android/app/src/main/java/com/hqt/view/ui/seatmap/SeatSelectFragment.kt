package com.hqt.view.ui.seatmap

import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.TextView
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.fragment.app.Fragment
import com.google.android.flexbox.FlexboxLayout
import com.google.android.material.behavior.HideBottomViewOnScrollBehavior
import com.hqt.data.model.request.GetSeatMap
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.FragmentSelectSeatLayoutBinding
import com.hqt.datvemaybay.databinding.SeatGroupTitleViewBinding
import com.hqt.datvemaybay.databinding.SeatViewTitleItemBinding
import com.hqt.util.AppConfigs
import com.hqt.util.AppController
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers

class SeatSelectFragment : Fragment() {
    private var _binding: FragmentSelectSeatLayoutBinding? = null
    private val binding get() = _binding!!
    private var disposable: Disposable? = null
    private var arraylistSeatGroup: ArrayList<SeatGroup> = ArrayList()
    private val seatViewList: ArrayList<SeatView> = ArrayList()
    var startRow = 1
    var isReturnTrip = false


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentSelectSeatLayoutBinding.inflate(inflater, container, false)

        getViewBindding().bottomSheet.findViewById<Button>(R.id.btnNext).setOnClickListener {
            (activity as SelectSeatActivity).doneClick()
        }
        getViewBindding().bottomSheet.findViewById<Button>(R.id.btnBack).setOnClickListener {
            (activity as SelectSeatActivity).backClick()
        }
        getViewBindding().btnBack.setOnClickListener {
            (activity as SelectSeatActivity).backClick()
        }

        return binding.root
    }

    private fun getViewBindding(): FragmentSelectSeatLayoutBinding {
        return _binding!!
    }

    private fun updateSeatSelected() {
        try {


            var totalPrice = 0
            var seatSummaryText = ""
            val fillerList = seatViewList.filter { st -> st.isSelect }


            fillerList.forEach { seat ->
                totalPrice += seat.seatOption!!.seatCharges
                seatSummaryText += seat.seatOption!!.rowIdentifier + seat.seatOption!!.seatIdentifier + ", "
            }
            seatSummaryText = seatSummaryText.removeSuffix(", ")


            if (totalPrice > 0) {
                getViewBindding().bottomSheet.findViewById<TextView>(R.id.txtGrandTotalPrice).text =
                    Common.dinhDangTien(
                        totalPrice
                    )
                getViewBindding().bottomSheet.findViewById<TextView>(R.id.txt_seat_select).text =
                    seatSummaryText
            } else {
                getViewBindding().bottomSheet.findViewById<TextView>(R.id.txtGrandTotalPrice).text =
                    Common.dinhDangTien(
                        0
                    )
                getViewBindding().bottomSheet.findViewById<TextView>(R.id.txt_seat_select).text =
                    "Vui lòng chọn"

                getViewBindding().currentSeatSelecte.text = ""
                getViewBindding().seatPrice.text = ""
            }


            val params = getViewBindding().bottomLayout.layoutParams as CoordinatorLayout.LayoutParams
            (params.behavior as HideBottomViewOnScrollBehavior).slideUp(getViewBindding().bottomLayout)


            (activity as SelectSeatActivity).updateSeatSelected(ArrayList(fillerList), isReturnTrip)

        } catch (e: Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }

    }

    fun getAllowSelect(): Boolean {
        val totalSelected = seatViewList.count { seatView -> seatView.isSelect }

        if (totalSelected < (activity as SelectSeatActivity).totalPax || (activity as SelectSeatActivity).totalPax == 1) {
            return true
        }

        return false
    }

    fun clearSelect(seat: SeatView) {

        if (seat.seatOption != null) {
            (seat.seatOption?.rowIdentifier + seat.seatOption?.seatIdentifier).also {
                getViewBindding().currentSeatSelecte.text = it
            }
            getViewBindding().seatPrice.text = Common.dinhDangTien(seat.seatOption!!.seatCharges)
        }

        seatViewList.forEach { it ->
            if (it.seatId != "") {
                if (it.seatId != seat.seatId) { //clear select
                    if ((activity as SelectSeatActivity).totalPax == 1) {
                        it.selectSeat(false)
                    }
                } else {
                    if (it.isSelect) {

                        seat.binding.seatGroupView.setCardBackgroundColor(Color.parseColor("#" + seat.seatOption?.color))
                        seat.isSelect = false
                    } else {
                        if (getAllowSelect())
                            seat.isSelect = true
                    }
                }
            }
        }
        updateSeatSelected()
    }

    fun genSeatMap(
        seatOptions: ArrayList<SeatOption>,
        seatIdentifierTitles: ArrayList<SeatIdentifierTitle>
    ) {

        val maxRow = seatOptions.maxOf { t -> t.rowIndex }

        val percent = (1 / (seatIdentifierTitles.count() + 0f)) - 0.005f
        for (i in startRow until maxRow + 1) {
            if (seatIdentifierTitles.size > 0) {
                val seatRowIndex = startRow
                seatIdentifierTitles.forEach { it ->
                    val seatView = SeatView(
                        requireContext(),
                        it,
                        seatRowIndex.toString(),
                        percent,
                        isReturnTrip,
                        false
                    )
                    seatView.seatId = "" + seatRowIndex + "-" + it.value
                    seatViewList.add(seatView)
                    getViewBindding().flexBoxContainer.addView(seatView)
                }
                startRow++
            }
        }
    }

    private fun mappingSeatInfo(seatGroup: SeatGroup) {
        val paxSeatStr = (activity as SelectSeatActivity).getPaxSeatStr(isReturnTrip)
        seatGroup.seatOptions.forEach { seat ->
            val seatId = "" + seat.rowIndex + "-" + seat.seatIdentifier
            for (i in 0 until seatViewList.size) {
                val seatView = seatViewList[i]
                if (seatView.seatId == seatId) {
                    seatView.setSeatInfo(seat)

                    if (i > 1 && seatViewList[i - 1].isIndex) {
                        seatViewList[i - 1].binding.rowNumber.text = seat.rowIdentifier
                    }

                    val seatIdentifier = "-" + seat.rowIdentifier + seat.seatIdentifier
                    if (paxSeatStr.contains(seatIdentifier)) {
                        seatView.selectSeat(true)
                    }
                }

            }


        }
    }


    fun initSeatMap(provider: String, flightKey: String) {
        val seatRequest = GetSeatMap()

        seatRequest.flightKey = flightKey
        if (isReturnTrip) Thread.sleep(500)
        disposable = AppController.instance.getService().postSeatMap(provider, seatRequest)
            .subscribeOn(Schedulers.io())
            .doOnSubscribe {

            }.observeOn(AndroidSchedulers.mainThread()).subscribe({ response ->

                if (response.status != null && response.status) {
                    arraylistSeatGroup.clear()
                    arraylistSeatGroup.addAll(response.data!!.seatGroups)


                    getViewBindding().originCode.text = flightKey.substring(0, 3)
                    getViewBindding().destinationCode.text = flightKey.substring(3, 6)
                    getViewBindding().airCraft.text = response.data.airCraft

                    response.data.seatType.forEach { seatType ->
                        try {
                            val seatTypeView = SeatViewTitleItemBinding.inflate(
                                LayoutInflater.from(requireContext()),
                                null,
                                false
                            )

                            seatTypeView.seatTitle.text = seatType.text
                            seatTypeView.seatColor.setCardBackgroundColor(Color.parseColor("#" + seatType.color))
                            val lpPlexItem = FlexboxLayout.LayoutParams(
                                FlexboxLayout.LayoutParams.MATCH_PARENT,
                                FlexboxLayout.LayoutParams.MATCH_PARENT
                            )
                            lpPlexItem.flexBasisPercent = 0.5f
                            seatTypeView.root.layoutParams = lpPlexItem
                            getViewBindding().flexBoxSeatTitle.addView(seatTypeView.root)
                        } catch (e: Exception) {
                            e.printStackTrace()
                            AppConfigs.logException(e)
                        }

                    }


                    response.data.seatGroups.forEach { sg ->
                        try {
                            val seatTitleView = SeatGroupTitleViewBinding.inflate(
                                LayoutInflater.from(requireContext()),
                                null,
                                false
                            )
                            seatTitleView.seatGroupTitle.text = sg.title
                            val lpPlexItem = FlexboxLayout.LayoutParams(
                                FlexboxLayout.LayoutParams.MATCH_PARENT,
                                FlexboxLayout.LayoutParams.MATCH_PARENT
                            )
                            lpPlexItem.flexBasisPercent = 1f
                            seatTitleView.root.layoutParams = lpPlexItem

                            getViewBindding().flexBoxContainer.addView(seatTitleView.root)
                            genSeatMap(sg.seatOptions, response.data.seatIdentifierTitles)

                            mappingSeatInfo(sg)
                        } catch (e: Exception) {
                            e.printStackTrace()
                            AppConfigs.logException(e)

                        }
                    }

                    getViewBindding().shimmerViewContainer.stopShimmer()
                    getViewBindding().shimmerViewContainer.visibility = View.GONE

                    getViewBindding().emptyState.visibility = View.GONE
                    getViewBindding().bottomSheet.visibility = View.VISIBLE
                } else {
                    getViewBindding().emptyState.visibility = View.VISIBLE
                    getViewBindding().shimmerViewContainer.stopShimmer()
                    getViewBindding().shimmerViewContainer.visibility = View.GONE
                }

            }, { throwable ->

                getViewBindding().emptyState.visibility = View.VISIBLE
                getViewBindding().shimmerViewContainer.stopShimmer()
                getViewBindding().shimmerViewContainer.visibility = View.GONE

                throwable.printStackTrace()
            })
    }

    companion object {
        fun newInstance(provider: String, flightK: String, isReturn: Boolean): SeatSelectFragment {
            val x = SeatSelectFragment()
            x.isReturnTrip = isReturn

            Handler(Looper.getMainLooper()).postDelayed({
                x.initSeatMap(provider, flightK)
            }, 1000)

            return x
        }
    }
}