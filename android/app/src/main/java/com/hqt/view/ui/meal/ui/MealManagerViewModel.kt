package com.hqt.view.ui.meal.ui

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.hqt.data.model.AddOnInfo
import com.hqt.data.model.AddOnType
import com.hqt.data.model.PaxInfoList
import com.hqt.util.AppConfigs
import com.hqt.view.ui.booking.data.model.BookingV3
import com.hqt.view.ui.booking.data.model.PaxInfoListV2
import com.hqt.view.ui.meal.data.api.MealApiHelper
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject


@HiltViewModel
class MealManagerViewModel @Inject constructor(
    private val seatApiHelper: MealApiHelper
) : ViewModel() {



    var totalPax = 1
    var addOnList: ArrayList<AddOnInfo> = ArrayList()
    var addOnListReturn: ArrayList<AddOnInfo> = ArrayList()
    lateinit var booking: BookingV3
    var doneClick  = MutableLiveData(false)





    fun updateSeatSelected(list: ArrayList<AddOnInfo>, isReturn: Boolean) {
        try {
            if (isReturn) {
                addOnListReturn = list
                booking.pax_info = addAddOnToPax(booking.pax_info, list, true)
            } else {
                addOnList = list
                booking.pax_info = addAddOnToPax(booking.pax_info, list, false)

            }
        } catch (e: Exception) {
            e.printStackTrace()
            AppConfigs.logException(e)
        }
    }
    private fun addAddOnToPax(pax_info: PaxInfoListV2, addOnList: ArrayList<AddOnInfo>, isReturn: Boolean): PaxInfoListV2 {
        try {
            pax_info.adult[0].updateAddOn(AddOnType.MEAL, addOnList, isReturn)

            return pax_info

        } catch (e: Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }
        return pax_info

    }

    fun getCurrentAddOn(isReturn: Boolean): ArrayList<AddOnInfo> {

        val addOnListPax: ArrayList<AddOnInfo> = ArrayList()
        booking.pax_info.adult.forEach { it ->
            if (isReturn) {
                it.addOnReturn.forEach {
                    if (it.type == AddOnType.MEAL) {
                        addOnListPax.add(it)
                    }

                }
            } else {
                it.addOn.forEach {
                    if (it.type == AddOnType.MEAL) {
                        addOnListPax.add(it)
                    }

                }
            }

        }
        booking.pax_info.child.forEach { it ->
            if (isReturn) {
                it.addOnReturn.forEach {
                    if (it.type == AddOnType.MEAL) {
                        addOnListPax.add(it)
                    }

                }
            } else {
                it.addOn.forEach {
                    if (it.type == AddOnType.MEAL) {
                        addOnListPax.add(it)
                    }

                }
            }

        }


        return addOnListPax

    }





}