package com.hqt.view.ui.priceboard

import android.app.Activity
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.os.CountDownTimer
import android.view.LayoutInflater
import android.view.View
import android.view.animation.AnimationUtils
import android.widget.ArrayAdapter
import android.widget.Button
import android.widget.LinearLayout
import android.widget.Spinner
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.AppCompatButton
import com.android.volley.VolleyError
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.hqt.data.model.DatePrice
import com.hqt.data.model.PriceBoardTable
import com.hqt.datvemaybay.AirportSearch
import com.hqt.datvemaybay.BuildConfig
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.SearchResult
import com.hqt.datvemaybay.databinding.ActivityMonthViewBinding
import com.hqt.datvemaybay.databinding.CalendarFooterBinding
import com.hqt.datvemaybay.databinding.CalendarHeaderBinding
import com.hqt.util.AppConfigs
import com.hqt.util.SSLSendRequest
import com.hqt.util.SSLSendRequest.CallBackInterface
import com.hqt.util.amlich.LunarCalendarUtil
import com.hqt.view.ui.BaseActivityKt
import com.kizitonwose.calendarview.model.CalendarDay
import com.kizitonwose.calendarview.model.CalendarMonth
import com.kizitonwose.calendarview.model.DayOwner
import com.kizitonwose.calendarview.ui.DayBinder
import com.kizitonwose.calendarview.ui.MonthHeaderFooterBinder
import com.kizitonwose.calendarview.ui.ViewContainer
import org.json.JSONArray
import org.json.JSONException
import org.json.JSONObject
import java.time.DayOfWeek
import java.time.YearMonth
import java.time.format.DateTimeFormatter
import java.util.Calendar
import java.util.Locale
import java.util.concurrent.TimeUnit

class MonthViewActivity : BaseActivityKt<ActivityMonthViewBinding>() {
    override val layoutId: Int = R.layout.activity_month_view
    var monthNow = Calendar.getInstance()
    private val titleFormatter = DateTimeFormatter.ofPattern("MM, yyyy")
    var priceTableData: ArrayList<DatePrice> = ArrayList()
    private val priceBoardTool = PriceBoardTable()
    var timer: CountDownTimer? = null
    val loadedMonth: ArrayList<String> = ArrayList()
    var originTxt = ""
    var destinationTxt = ""
    var monthTxt = ""
    val REQUEST_AIRPORT_ORIGIN = 2
    val REQUEST_AIRPORT_DESTINATION = 3
    var onFetch = true

    var showSelectReturnDate = true
    var departureDateTime = ""


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        AppConfigs.analyticScreenView("price_board_month_view")
        firebaseAnalytics.logEvent("price_board_month_click", Bundle())

        getToolbar().title = "Chi tiết giá trong tháng"
        getToolbar().setNavigationIcon(R.drawable.ic_action_back_home)
        supportActionBar!!.setDisplayShowHomeEnabled(true)


        if (intent.hasExtra("origin")) {

            originTxt = intent.getStringExtra("origin")!!.toUpperCase(Locale.ROOT)
            destinationTxt = intent.getStringExtra("destination")!!.toUpperCase(Locale.ROOT)
            monthTxt = intent.getStringExtra("monthName").toString()
            getViewBindding().txtOrigin.text = Common.getAirPortName(originTxt, false)
            getViewBindding().txtDestination.text = Common.getAirPortName(destinationTxt, false)

        } else {
            monthTxt = YearMonth.now().format(DateTimeFormatter.ofPattern("yyyyMM"))
        }


        if (intent.hasExtra("departureDateTime")) {
            departureDateTime = intent.getStringExtra("departureDateTime")!!
            showSelectReturnDate = false
            getViewBindding().toolbar.subtitle = "Lượt đi: " + departureDateTime
        }

        val currentMonth = YearMonth.parse(monthTxt, DateTimeFormatter.ofPattern("yyyyMM"))
        val startMonth = YearMonth.now().minusMonths(0)
        val endMonth = YearMonth.now().plusMonths(10)
        getViewBindding().calendarView.setup(startMonth, endMonth, DayOfWeek.MONDAY)

        getViewBindding().calendarView.scrollToMonth(currentMonth)
        bindPriceToCalendar()

        getViewBindding().calendarView.monthScrollListener = { month ->
            val title = "${month.year} -  ${month.month}"
            monthNow.set(month.year, month.month - 1, 1)
            getMonthPrices(originTxt, destinationTxt)
        }
        autoReloadTimer()

    }

    fun bindPriceToCalendar() {

        getViewBindding().calendarView.dayBinder = object : DayBinder<DayViewContainer> {
            override fun create(view: View) = DayViewContainer(view)
            override fun bind(container: DayViewContainer, day: CalendarDay) {

                if (day.date.dayOfWeek.value % 2 == 0) {
                    container.dayBackground.setBackgroundResource(R.drawable.cell_background_hight_light)
                }
                val key = originTxt + destinationTxt + "_" + getDateKeyFormat(day, "yyyyMMdd")
                val datePrice = priceTableData.find { p -> p.code == key }

                container.textView.text = day.date.dayOfMonth.toString()
                addLunarDate(day, container.lunarText)

                if (datePrice != null && day.owner == DayOwner.THIS_MONTH) {
                    container.cellData.text = datePrice.getShortPrice()
                    container.cellPriceBackround.setBackgroundResource(datePrice.getPriceColor())
                }

                if (day.owner == DayOwner.THIS_MONTH) {
                    container.textView.setTextColor(Color.BLACK)
                } else {
                    container.textView.setTextColor(Color.GRAY)
                }

                container.dayBackground.setOnClickListener {
                    var selectTxtDate = getDateKeyFormat(day, "yyyy-MM-dd")
                    if (showSelectReturnDate) {
                        showReturnDateSelect(selectTxtDate)
                    } else {
                        if (departureDateTime > selectTxtDate) {
                            Toast.makeText(
                                applicationContext,
                                "Ngày về phải sau hoặc bằng ngày khởi hành",
                                Toast.LENGTH_SHORT
                            ).show()
                        } else showPaxInput(selectTxtDate)
                    }

                }
            }
        }
        getViewBindding().calendarView.monthHeaderBinder =
            object : MonthHeaderFooterBinder<MonthViewContainer> {
                override fun create(view: View) = MonthViewContainer(view)
                override fun bind(container: MonthViewContainer, month: CalendarMonth) {
                    container.textView.text = "Tháng " + titleFormatter.format(month.yearMonth)

                    container.nextWeek.setOnClickListener {
                        getViewBindding().calendarView.scrollToMonth(month.yearMonth.plusMonths(1))
                    }
                    container.previousWeek.setOnClickListener {
                        getViewBindding().calendarView.scrollToMonth(month.yearMonth.plusMonths(-1))
                    }
                }
            }
        getViewBindding().calendarView.monthFooterBinder =
            object : MonthHeaderFooterBinder<MonthViewFooterContainer> {
                override fun create(view: View) = MonthViewFooterContainer(view)
                override fun bind(container: MonthViewFooterContainer, month: CalendarMonth) {

                }
            }
        initClick()


    }

    fun showReturnDateSelect(departureDateSelect: String) {
        val alertDialog = AlertDialog.Builder(this).create()
        alertDialog.setTitle("Chú ý")
        alertDialog.setMessage("Lượt đi ngày " + departureDateSelect + ", Bạn có muốn chọn ngày về cho chuyến đi của mình?")
        alertDialog.setIcon(R.drawable.ic_bell_alert)
        alertDialog.setButton(DialogInterface.BUTTON_POSITIVE, "Chọn lượt về") { dialog, which ->

            val i3 = Intent(this, MonthViewActivity::class.java)
            i3.putExtra("origin", destinationTxt)
            i3.putExtra("destination", originTxt)
            i3.putExtra("monthName", monthTxt)
            i3.putExtra("departureDateTime", departureDateSelect)
            startActivity(i3)

        }
        alertDialog.setButton(
            DialogInterface.BUTTON_NEGATIVE,
            "Tôi chỉ đi 1 chiều"
        ) { dialog, which ->
            showPaxInput(departureDateSelect)
        }

        alertDialog.show()

    }

    fun initClick() {
        getViewBindding().txtOrigin.setOnClickListener {
            val i = Intent(this, AirportSearch::class.java)
            i.putExtra("PRICEBOARDMONTH_ORIGIN", true)
            startActivityForResult(i, REQUEST_AIRPORT_ORIGIN)
        }
        getViewBindding().txtDestination.setOnClickListener {
            val i = Intent(this, AirportSearch::class.java)
            i.putExtra("PRICEBOARDMONTH_ORIGIN", true)
            i.putExtra("dep", originTxt)
            startActivityForResult(i, REQUEST_AIRPORT_DESTINATION)
        }
    }

    fun getDateKeyFormat(day: CalendarDay, format: String): String {

        val date = Calendar.getInstance()
        date.set(day.date.year, day.date.monthValue - 1, day.date.dayOfMonth)

        return Common.dateToString(date.time, format)

    }

    @Override
    override fun onPause() {
        onFetch = false
        super.onPause()
    }

    override fun onResume() {
        onFetch = true
        super.onResume()
    }

    fun addLunarDate(day: CalendarDay, lunarText: TextView) {
        val cal = Calendar.getInstance()
        cal.set(day.date.year, day.date.monthValue, day.date.dayOfMonth)

        val tz =
            TimeUnit.HOURS.convert(cal.timeZone.rawOffset.toLong(), TimeUnit.MILLISECONDS).toFloat()
        val lunarDate = LunarCalendarUtil.convertSolar2Lunar(
            cal[Calendar.YEAR],
            cal[Calendar.MONTH],
            cal[Calendar.DAY_OF_MONTH],
            tz
        )
        var ngayAm = lunarDate.day.toString()
        if (lunarDate.day == 1 || lunarDate.day == 15) {
            ngayAm = lunarDate.day.toString() + "/" + lunarDate.month
            lunarText.setTextColor(Color.RED)
        }

        lunarText.text = ngayAm
    }

    class DayViewContainer(view: View) : ViewContainer(view) {
        val textView = view.findViewById<TextView>(R.id.calendarDayText)
        val lunarText = view.findViewById<TextView>(R.id.calendarDayLunarText)
        val dayBackground = view.findViewById<LinearLayout>(R.id.day_background)
        val cellData = view.findViewById<TextView>(R.id.cell_data)
        val cellPriceBackround = view.findViewById<LinearLayout>(R.id.cell_price_background)

    }

    class MonthViewContainer(view: View) : ViewContainer(view) {
        val binding = CalendarHeaderBinding.bind(view)
        val textView = binding.exSixMonthText
        val nextWeek = binding.nextWeek
        val previousWeek = binding.previousWeek

    }

    class MonthViewFooterContainer(view: View) : ViewContainer(view) {
        val binding = CalendarFooterBinding.bind(view)
    }

    private fun autoReloadTimer(): Boolean {
        if (timer == null) {

            var endDate = Calendar.getInstance()
            endDate.add(Calendar.DATE, 1)
            var live = 30
            timer = object : CountDownTimer(endDate.timeInMillis, 1000) {
                override fun onTick(millisUntilFinished: Long) { //   if (isVisible) {
                    getViewBindding().liveText.text = "Live " + live
                    if (live == 0) {
                        live = 30

                        priceTableData.clear()
                        loadedMonth.clear()
                        getMonthPrices(originTxt, destinationTxt)

                    }
                    live-- //}
                }

                override fun onFinish() {

                }
            }.start()
            return true
        } else {
            return false
        }
    }

    private fun getMonthPrices(txtOrigin: String, txtDestination: String) {

        val month = Common.dateToString(monthNow.time, "yyyyMM")
        val keyRoute = txtOrigin + txtDestination
        val keyMonth = month + "_" + keyRoute
        if (!loadedMonth.contains(keyMonth) && onFetch) {

            loadedMonth.add(keyMonth)
            val postParam = JSONObject()
            val route = JSONArray()
            route.put(txtOrigin + txtDestination)

            postParam.put("startDate", month + "01")



            postParam.put("endDate", month + "31")
            postParam.put("maxPrice", "0")
            postParam.put("minPrice", "10000")
            postParam.put("routes", route)
            postParam.put("source", "ANDROID")
            postParam.put("type", "date")
            postParam.put("key", Common.getKeyHash())
            postParam.put("ver", BuildConfig.VERSION_CODE.toString() + "")


            SSLSendRequest(this).POST(
                false,
                "AirLines/PricesBoard",
                postParam,
                object : CallBackInterface {
                    override fun onSuccess(response: JSONObject, cached: Boolean) {
                        try {
                            val data = response.getJSONObject("data")
                            val fullData = data.getJSONObject("full")

                            if (!fullData.isNull(keyRoute) && fullData.length() > 0) {
                                priceTableData.addAll(
                                    priceBoardTool.getDayListMonth(
                                        fullData.getJSONArray(keyRoute),
                                        keyRoute
                                    )
                                )
                                bindPriceToCalendar()
                            }
                        } catch (e: JSONException) {
                            AppConfigs.logException(e)
                            e.printStackTrace()
                        }
                    }

                    override fun onFail(e: VolleyError) {
                        AppConfigs.logException(e)
                        e.printStackTrace()

                    }
                })
        }

    }

    fun showPaxInput(departureTime: String) {
        try {

            var adultCount = 1
            var childCount: Int = 0
            var infantCount: Int = 0

            var dialog: BottomSheetDialog? = null
            dialog = BottomSheetDialog(this)
            val selectPassengerView = layoutInflater.inflate(R.layout.select_passenger_layout, null)
            dialog.setContentView(selectPassengerView)
            val txtAdult: TextView = selectPassengerView.findViewById(R.id.sheet_adult_number)
            val txtChild: TextView = selectPassengerView.findViewById(R.id.sheet_child_number)
            val txtInfant: TextView = selectPassengerView.findViewById(R.id.sheet_infant_number)
            val inAdult: LinearLayout = selectPassengerView.findViewById(R.id.btn_in_adult)
            val deAdult: LinearLayout = selectPassengerView.findViewById(R.id.btn_de_adult)
            val inChild: LinearLayout = selectPassengerView.findViewById(R.id.btn_in_child)
            val deChild: LinearLayout = selectPassengerView.findViewById(R.id.btn_de_child)
            val inInfant: LinearLayout = selectPassengerView.findViewById(R.id.btn_in_infant)
            val deInfant: LinearLayout = selectPassengerView.findViewById(R.id.btn_de_infant)
            val choicePassengerBtn: AppCompatButton =
                selectPassengerView.findViewById(R.id.select_passenger_button)
            choicePassengerBtn.setOnClickListener { if (dialog.isShowing()) dialog.dismiss() }
            val inAnim = AnimationUtils.loadAnimation(this, android.R.anim.fade_in)
            inAnim.duration = 250
            inAdult.setOnClickListener {
                if (adultCount < 9 && adultCount + childCount < 9) {
                    adultCount++
                    txtAdult.setText(adultCount.toString() + "")
                    txtAdult.startAnimation(inAnim)
                } else {
                    Toast.makeText(
                        applicationContext,
                        "Tối đa 9 hành khách (người lớn và trẻ em)",
                        Toast.LENGTH_SHORT
                    )
                        .show()
                }
            }
            deAdult.setOnClickListener {
                if (adultCount > 1) {
                    adultCount = adultCount - 1
                    txtAdult.setText(adultCount.toString() + "")
                    txtAdult.startAnimation(inAnim)
                }
            }
            inChild.setOnClickListener {
                if (childCount < 9 && adultCount + childCount < 9) {
                    childCount++
                    txtChild.setText(childCount.toString() + "")
                    txtChild.startAnimation(inAnim)
                } else {
                    Toast.makeText(
                        applicationContext,
                        "Tối đa 9 hành khách (người lớn và trẻ em)",
                        Toast.LENGTH_SHORT
                    )
                        .show()
                }
            }
            deChild.setOnClickListener {
                if (childCount > 0) {
                    childCount = childCount - 1
                    txtChild.setText(childCount.toString() + "")
                    txtChild.startAnimation(inAnim)
                }
            }
            inInfant.setOnClickListener {
                if (infantCount < adultCount) {
                    infantCount++
                    txtInfant.setText(infantCount.toString() + "")
                    txtInfant.startAnimation(inAnim)
                } else {
                    Toast.makeText(
                        applicationContext,
                        "Số lượng em bé phải nhỏ hơn hoặc bằng người lớn",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }
            deInfant.setOnClickListener {
                if (infantCount > 0) {
                    infantCount = infantCount - 1
                    txtInfant.setText(infantCount.toString() + "")
                    txtInfant.startAnimation(inAnim)
                }
            }
            selectPassengerView.findViewById<Button>(R.id.select_passenger_button)
                .setOnClickListener {

                    try {
                        val bundle = Bundle()
                        bundle.putString(FirebaseAnalytics.Param.ORIGIN, originTxt)
                        bundle.putString(FirebaseAnalytics.Param.DESTINATION, destinationTxt)
                        bundle.putString(FirebaseAnalytics.Param.START_DATE, departureTime)
                        firebaseAnalytics.logEvent("price_board_month_click", bundle)
                    } catch (e: java.lang.Exception) {
                        FirebaseCrashlytics.getInstance().recordException(e)
                    }

                    val book = Intent(applicationContext, SearchResult::class.java)




                    if (departureDateTime.isNotEmpty()) {
                        book.putExtra("isRoundTrip", true)
                        book.putExtra("originCode", destinationTxt)
                        book.putExtra("destinationCode", originTxt)
                        book.putExtra("departureTime", departureDateTime)
                        book.putExtra("returnTime", departureTime)
                    } else {
                        book.putExtra("departureTime", departureTime)
                        book.putExtra("isRoundTrip", false)
                        book.putExtra("originCode", originTxt)
                        book.putExtra("destinationCode", destinationTxt)
                        book.putExtra("returnTime", "")

                    }


                    book.putExtra("adult", Integer.valueOf(adultCount))
                    book.putExtra("child", Integer.valueOf(childCount))
                    book.putExtra("infant", Integer.valueOf(infantCount))

                    startActivity(book)
                    overridePendingTransition(R.anim.enter, R.anim.exit)
                    if (dialog.isShowing) dialog.dismiss()
                }
            dialog.show()
        } catch (E: java.lang.Exception) {
            AppConfigs.logException(E)
            showPaxInputx(departureTime)
        }
    }

    fun showPaxInputx(departureTime: String) {
        try {


            val listSoLuongHanhKhachAdapter = ArrayAdapter.createFromResource(
                this,
                R.array.soHanhKhachNguoiLon,
                R.layout.spinner_layout
            )
            listSoLuongHanhKhachAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
            val listSoLuongHanhKhachTreemAdapter = ArrayAdapter.createFromResource(
                this,
                R.array.soHanhKhachTreEm,
                R.layout.spinner_layout
            )
            listSoLuongHanhKhachTreemAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)

            val builder = AlertDialog.Builder(this)
            val inflater =
                baseContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
            val alertView = inflater.inflate(R.layout.pax_number_layout, null)
            val adult: Spinner = alertView.findViewById(R.id.nguoiLon)
            val child: Spinner = alertView.findViewById(R.id.treEm)
            val infant: Spinner = alertView.findViewById(R.id.emBe)
            adult.adapter = listSoLuongHanhKhachAdapter
            child.adapter = listSoLuongHanhKhachTreemAdapter
            infant.adapter = listSoLuongHanhKhachTreemAdapter
            builder.setView(alertView)
            builder.setTitle("Chọn số lượng hành khách")
            builder.setIcon(R.drawable.ic_passenger)
            builder.setPositiveButton("Đặt vé") { dialog, id ->

                val book = Intent(applicationContext, SearchResult::class.java)
                book.putExtra("departureTime", departureTime)
                book.putExtra("originCode", originTxt)
                book.putExtra("destinationCode", destinationTxt)
                book.putExtra("returnTime", "")
                book.putExtra("adult", Integer.valueOf(adult.selectedItem.toString()))
                book.putExtra("child", Integer.valueOf(child.selectedItem.toString()))
                book.putExtra("infant", Integer.valueOf(infant.selectedItem.toString()))
                book.putExtra("isRoundTrip", false)
                startActivity(book)
                overridePendingTransition(R.anim.enter, R.anim.exit)
            }.setNegativeButton("Làm lại") { dialog, id -> }
            builder.setCancelable(true)
            val myDialog = builder.create()
            if (!myDialog.isShowing) myDialog.show()

        } catch (e: Exception) {
            AppConfigs.logException(e)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (resultCode == Activity.RESULT_OK && requestCode == REQUEST_AIRPORT_ORIGIN) {
            if (data != null && data.hasExtra("code")) {
                originTxt = data.getStringExtra("code").toString()
                getViewBindding().txtOrigin.text = Common.getAirPortName(originTxt, false)

                priceTableData.clear()
                loadedMonth.clear()
                getMonthPrices(originTxt, destinationTxt)

            }

        }
        if (resultCode == Activity.RESULT_OK && requestCode == REQUEST_AIRPORT_DESTINATION) {
            if (data != null && data.hasExtra("code")) {

                priceTableData.clear()
                loadedMonth.clear()
                destinationTxt = data.getStringExtra("code").toString()
                getViewBindding().txtDestination.text = Common.getAirPortName(destinationTxt, false)
                getMonthPrices(originTxt, destinationTxt)
            }

        }
        super.onActivityResult(requestCode, resultCode, data)
    }
}