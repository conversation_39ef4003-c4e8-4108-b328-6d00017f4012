package com.hqt.view.ui.bus

import android.annotation.SuppressLint
import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationSet
import android.view.animation.DecelerateInterpolator
import android.view.animation.RotateAnimation
import android.widget.Toast
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.databinding.DataBindingUtil
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.android.volley.VolleyError
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.snackbar.Snackbar
import com.google.gson.reflect.TypeToken
import com.hqt.data.model.SuggestTrip
import com.hqt.datvemaybay.*
import com.hqt.datvemaybay.databinding.ActivityBusSearchBinding
import com.hqt.datvemaybay.databinding.SuggestTripViewBinding
import com.hqt.util.AppConfigs
import com.hqt.util.AppController
import com.hqt.util.SSLSendRequest
import com.hqt.util.amlich.AmLich
import com.hqt.view.ui.BaseActivityKt
import com.mikepenz.iconics.context.IconicsContextWrapper
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.*

class BusSearchActivity : BaseActivityKt<ActivityBusSearchBinding>() {

    private lateinit var depDate: Calendar
    private lateinit var retDate: Calendar
    override val layoutId: Int = R.layout.activity_bus_search
    lateinit var dialog: BottomSheetDialog
    lateinit var coordinatorLayout: CoordinatorLayout
    private lateinit var selectPassengerView: View

    private var fromId = -1
    private var toId = -1

    private val REQUEST_CODE_FROM = 0
    private val REQUEST_CODE_TO = 1
    private val REQUEST_CODE_DEP_DATE = 2
    private val REQUEST_CODE_RE_DATE = 3
    val dF = SimpleDateFormat("dd/MM/yyyy", Locale.US)

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        getViewBindding().search.setOnClickListener {
            setSnackbarMessage("xxxx", true)
        }

        firebaseAnalytics.setCurrentScreen(this, "bus_booking_search", null)

        depDate = Calendar.getInstance()
        retDate = Calendar.getInstance()

        depDate.add(Calendar.DAY_OF_MONTH, 3)
        retDate.add(Calendar.DAY_OF_MONTH, 5)

        //binding.originName.text = "xxxxxx";
        getToolbar().title = "Đặt vé xe khách"
        getToolbar().setNavigationIcon(R.drawable.ic_action_back_home)
        supportActionBar!!.setDisplayShowHomeEnabled(true)
        initClickAction()
        initAnalytics()

    }

    private fun initClickAction() {

        val i = Intent(applicationContext, AirportSearch::class.java)
        i.putExtra("isSearchBus", true)

        getViewBindding().selectOrigin.setOnClickListener {
            startActivityForResult(i, REQUEST_CODE_FROM)
        }
        getViewBindding().selectDestination.setOnClickListener {
            startActivityForResult(i, REQUEST_CODE_TO)
        }

        setDateToView(depDate.time, false)



        getViewBindding().selectDepDate.setOnClickListener {
            try {
                val i = Intent(applicationContext, AmLich::class.java)
                i.putExtra("isSearchTrain", true)
                i.putExtra("depDate", dF.format(depDate.time))
                i.putExtra("origin", getViewBindding().originCode.text)
                i.putExtra("destination", getViewBindding().destinationCode.text)
                startActivityForResult(i, REQUEST_CODE_DEP_DATE)
                AppConfigs.Log("depDate", dF.format(depDate.time))

            } catch (except: Exception) {

            }
        }

        getViewBindding().swapRouteLayout.setOnClickListener { getViewBindding().swapRoute.performClick() }

        getViewBindding().swapRoute.setOnClickListener {

            val tem = getViewBindding().originCode.text
            val temName = getViewBindding().originName.text

            getViewBindding().originName.text = getViewBindding().destinationName.text
            getViewBindding().originCode.text = getViewBindding().destinationCode.text

            getViewBindding().destinationCode.text = (tem)
            getViewBindding().destinationName.text = (temName)

            val animSet = AnimationSet(true)
            animSet.interpolator = DecelerateInterpolator()
            animSet.fillAfter = true
            animSet.isFillEnabled = true

            val animRotate = RotateAnimation(0.0f,
                360.0f,
                RotateAnimation.RELATIVE_TO_SELF,
                0.5f,
                RotateAnimation.RELATIVE_TO_SELF,
                0.5f)

            animRotate.duration = 200
            animRotate.fillAfter = true
            animSet.addAnimation(animRotate)
            getViewBindding().swapRoute.startAnimation(animSet)
            var temId = toId
            toId = fromId
            fromId = temId
        }
        showSuggestTrip()
        getViewBindding().search.setOnClickListener {

            if (isInternetConnected) {

                if (fromId == -1) {
                    Toast.makeText(this, "Vui lòng chọn điểm đi !", Toast.LENGTH_SHORT).show()

                } else if (toId == -1) {
                    Toast.makeText(this, "Vui lòng chọn điểm đến !", Toast.LENGTH_SHORT).show()

                } else if (getViewBindding().originCode.text.trim() == getViewBindding().destinationCode.text.trim()) {
                    Toast.makeText(this, "Vui lòng chọn điểm đến và điểm đi khác nhau !", Toast.LENGTH_SHORT).show()

                } else {

                    val intentSearch = Intent(this, BusSelectActivity::class.java)

                    intentSearch.putExtra("webLink",
                        SSLSendRequest.getAPILINK() + "/api/v1/Bus/Book/?fromId=$fromId&toId=$toId&departDate=" + Common.dateToString(
                            depDate.time,
                            "dd-MM-yyyy") + "&uid=" + firebaseUser?.uid + "&gcm=" + Common.FCM_TOKEN)
                    intentSearch.putExtra("webTitle",
                        "" + getViewBindding().originCode.text + " - " + getViewBindding().destinationCode.text)
                    intentSearch.putExtra("webSubTitle", Common.dateToString(depDate.time, "dd-MM-yyyy"))
                    intentSearch.putExtra("departureTime", Common.dateToString(depDate.time, "yyyy-MM-dd"))
                    intentSearch.putExtra("originCode", fromId)
                    intentSearch.putExtra("destinationCode", toId)

                    startActivity(intentSearch)
                    overridePendingTransition(R.anim.enter, R.anim.exit)

                }
            } else {

                Common.showAlertDialog(this,
                    "Không có Internet",
                    "Xin vui lòng kiểm tra lại Wifi/3G để tiếp tục",
                    false,
                    true)
            }
        }

    }

    //    override fun attachBaseContext(newBase: Context) {
    //        super.attachBaseContext(IconicsContextWrapper.wrap(newBase))
    //    }


    @SuppressLint("MissingSuperCall") override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {


        if (resultCode == Activity.RESULT_OK && requestCode == REQUEST_CODE_FROM) {
            if (data!!.hasExtra("name")) {
                fromId = (data.extras!!.getInt("id"))
                getViewBindding().originName.text = (data.extras!!.getString("base"))
                getViewBindding().originCode.text = (data.extras!!.getString("name"))
            }
        } else if (resultCode == Activity.RESULT_OK && requestCode == REQUEST_CODE_TO) {
            if (data!!.hasExtra("name")) {
                toId = (data.extras!!.getInt("id"))
                getViewBindding().destinationName.text = (data.extras!!.getString("base"))
                getViewBindding().destinationCode.text = (data.extras!!.getString("name"))
            }
        } else if (resultCode == Activity.RESULT_OK && requestCode == REQUEST_CODE_DEP_DATE) {
            if (data!!.hasExtra("date")) {
                depDate.time = Common.getDateFromString(data.extras!!.getString("date")).time
                setDateToView(depDate.time, false)

            }
        }


    }

    private fun setDateToView(date: Date?, isReturn: Boolean?) {
        try {
            if (date == null) {
                getViewBindding().txtDepDate.text = ""
            } else {
                getViewBindding().txtDepDate.text = Common.dateToString(date, "DOWF, dd/MM/yyyy")
            }
        } catch (e: Exception) {
            AppConfigs.Log("setDepDateString ", e.toString())
        }

    }


    override fun onStart() {
        super.onStart()

    }

    private fun showSuggestTrip() {
        if (isInternetConnected) {
            SSLSendRequest(this).GET(false, "Bus/SuggestTrip", JSONObject(), object : SSLSendRequest.CallBackInterface {
                override fun onSuccess(response: JSONObject, cached: Boolean) {
                    if (!response.isNull("data")) {
                        val listType = object : TypeToken<ArrayList<SuggestTrip>>() {}.type
                        var suggestList = AppController.instance.gSon.fromJson<List<SuggestTrip>>(response.getJSONArray(
                            "data").toString(), listType)

                        suggestList.onEach { trip ->

                            genTripView(trip, getViewBindding().suggestTripContainer)

                        }

                    }
                }

                override fun onFail(er: VolleyError) {

                }
            })
        }
    }

    fun genTripView(trip: SuggestTrip, parent: ViewGroup) {
        try {
            val layoutInflater = LayoutInflater.from(this)
            var binding: SuggestTripViewBinding = DataBindingUtil.inflate(layoutInflater,
                R.layout.suggest_trip_view,
                parent,
                true)
            binding.trip = trip
            binding.root.setOnClickListener {
                fromId = trip.origin!!.id
                getViewBindding().originName.text = trip.origin!!.base
                getViewBindding().originCode.text = trip.origin!!.name

                toId = trip.destination!!.id
                getViewBindding().destinationName.text = trip.destination!!.base
                getViewBindding().destinationCode.text = trip.destination!!.name

                depDate.time = trip.depatureDate
                setDateToView(depDate.time, false)

            }

        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }

    private fun initAnalytics() {

        val params = Bundle()
        firebaseAnalytics.logEvent("bus_search_view", params)
        firebaseAnalytics.setCurrentScreen(this, "bus_search_view", null)
    }
}

