package com.hqt.view.ui.flightSearch

import android.content.Context
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.PopupMenu
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.SearchResult
import com.hqt.datvemaybay.databinding.ListFareDataItemBinding
import com.hqt.view.ui.flightSearch.model.FareData
import com.hqt.view.ui.flightwaches.FlightWachesList


class FareDataItemAdapter(var mContext: Context, var contents: List<FareData>) :
    RecyclerView.Adapter<FareDataItemAdapter.ViewHolder>() {
    class ViewHolder(var context: Context, val binding: ListFareDataItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(fareData: FareData) {
            try {

                binding.viewmodel = fareData.filterFlight()

                binding.departureFlight.durationView.drawDuration(fareData.getDepartureFlight()!!.getStopDuration())
                if (fareData.isRoundTrip) {
                    binding.returnFlight.durationView.drawDuration(fareData.getReturnFlight()!!.getStopDuration())
                }

                binding.executePendingBindings()
                binding.item.setOnClickListener {

                    if (context is FlightSearchActivity) {
                        (context as FlightSearchActivity).showBottomSheetFlightInfo(fareData)
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }

        }

    }

    override fun getItemCount(): Int {
        return contents.size
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {

        val layoutInflater = LayoutInflater.from(parent.context)
        val binding: ListFareDataItemBinding = DataBindingUtil.inflate(layoutInflater,
            R.layout.list_fare_data_item,
            parent,
            false)
        return ViewHolder(mContext, binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(contents[position])
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getItemViewType(position: Int): Int {
        return position
    }


}