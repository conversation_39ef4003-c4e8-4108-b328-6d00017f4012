package com.hqt.view.ui.chart

import android.content.Context
import android.widget.TextView
import com.github.mikephil.charting.components.MarkerView
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.highlight.Highlight
import com.github.mikephil.charting.utils.MPPointF
import com.hqt.data.model.WeeklyPriceData
import com.hqt.datvemaybay.R

/**
 * Custom marker view for displaying detailed information when user taps on chart bars
 */
class WeeklyPriceMarkerView(
    context: Context,
    private val weeklyData: List<WeeklyPriceData>
) : MarkerView(context, R.layout.marker_weekly_price) {

    private val tvContent: TextView = findViewById(R.id.tvContent)

    override fun refreshContent(e: Entry?, highlight: Highlight?) {
        e?.let { entry ->
            val index = entry.x.toInt()
            if (index < weeklyData.size) {
                val weekData = weeklyData[index]
                val content = """
                    Tuần: ${weekData.weekLabel}
                    Giá rẻ nhất: ${weekData.getFormattedPrice()}
                    ${weekData.origin} → ${weekData.destination}
                """.trimIndent()
                
                tvContent.text = content
            }
        }
        super.refreshContent(e, highlight)
    }

    override fun getOffset(): MPPointF {
        return MPPointF(-(width / 2).toFloat(), -height.toFloat())
    }
}
