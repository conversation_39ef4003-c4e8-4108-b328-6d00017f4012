package com.hqt.view.ui.search.ui.fragment

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.github.florent37.materialviewpager.MaterialViewPagerHelper
import com.github.florent37.materialviewpager.Utils
import com.github.florent37.materialviewpager.header.MaterialViewPagerHeaderDecorator
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.gson.Gson
import com.hqt.base.BaseFragment
import com.hqt.base.model.State
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.FragmentRecyclerviewBinding
import com.hqt.util.EventManager
import com.hqt.view.ui.booking.ui.activity.BookingActivityV2
import com.hqt.view.ui.search.data.model.FlightV2
import com.hqt.view.ui.search.ui.SearchResultViewModel
import com.hqt.view.ui.search.ui.activity.SearchResultActivityV2.Companion.SORT_BY_DATE
import com.hqt.view.ui.search.ui.activity.SearchResultActivityV2.Companion.SORT_BY_PRICE
import com.hqt.view.ui.search.ui.adapter.airport.v2.FlightViewAdapterV2
import com.hqt.view.ui.search.ui.dialog.SelectFlightDialog
import com.hqt.view.ui.search.ui.helper.MaterialViewPagerHeaderDecoratorV2
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


@AndroidEntryPoint
class FlightListFragmentV2 : BaseFragment<FragmentRecyclerviewBinding>() {

    val viewModel: SearchResultViewModel by activityViewModels()

    private var status = ""


    private val mAdapter by lazy {
        FlightViewAdapterV2 {


            viewModel.selectFlight = it
            val dialog = SelectFlightDialog()
            dialog.onActionDone = {
                if (!viewModel.bookingDetail.is_round_trip && (viewModel.bookingDetail.departure_f != (null))) {
                    val intentBooking = Intent(requireContext(), BookingActivityV2::class.java)
                    intentBooking.putExtra("bookingDetail", Gson().toJson(viewModel.bookingDetail))
                    startActivity(intentBooking)
                    activity?.overridePendingTransition(R.anim.enter, R.anim.exit)
                    FirebaseAnalytics.getInstance(requireContext()).setUserProperty(
                        "favorite_route",
                        viewModel.bookingDetail.origin_code + viewModel.bookingDetail.destination_code
                    )

                } else {
                    if (viewModel.bookingDetail.departure_f == (null)) {
                        Toast.makeText(requireContext(), "Vui lòng chọn chuyến bay chiều đi !", Toast.LENGTH_SHORT).show()

                        CoroutineScope(Dispatchers.IO).launch {
                            EventManager.emitEvent(EventManager.EventFlow(EventManager.Event.SELECTED_TAB, 0))
                        }
                    } else if (viewModel.bookingDetail.return_f == (null)) {
                        Toast.makeText(requireContext(), "Vui lòng chọn chuyến bay chiều về !", Toast.LENGTH_SHORT).show()
                        CoroutineScope(Dispatchers.IO).launch {
                            EventManager.emitEvent(EventManager.EventFlow(EventManager.Event.SELECTED_TAB, 1))
                        }
                    } else {
                        val intentBooking = Intent(requireContext(), BookingActivityV2::class.java)
                        intentBooking.putExtra("bookingDetail", Gson().toJson(viewModel.bookingDetail))
                        startActivity(intentBooking)
                        activity?.overridePendingTransition(R.anim.enter, R.anim.exit)
                        FirebaseAnalytics.getInstance(requireContext()).setUserProperty(
                            "favorite_route",
                            viewModel.bookingDetail.origin_code + viewModel.bookingDetail.destination_code
                        )
                    }
                }
            }
                
            dialog.show(childFragmentManager, "")

        }
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_recyclerview
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val STATUS = "STATUS"


        arguments?.takeIf { it.containsKey(STATUS) }?.apply {
            MaterialViewPagerHelper.registerRecyclerView(activity, binding.recyclerView)
            status = getString(STATUS, "")
            initRcv()
            observe()
        }


    }

    private fun initRcv() {


        binding.recyclerView.apply {
            adapter = mAdapter
            layoutManager = LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false)
            setHasFixedSize(true)
            addItemDecoration(MaterialViewPagerHeaderDecoratorV2(viewModel.offset))
        }

        mAdapter.offset = viewModel.offset


    }

    fun addFlight(listFlight: ArrayList<FlightV2>?, sortBy: String) {

        if (sortBy == "price") {
            listFlight?.sortWith { s1, s2 -> (s1.netPrice ?: 0) - (s2.netPrice ?: 0) }
        } else {
            listFlight?.sortWith { s1, s2 -> //TO DO
                s1.departureDate.compareTo(s2.departureDate)
            }
        }

        mAdapter.setData(listFlight)
    }


    @SuppressLint("NotifyDataSetChanged")
    private fun observe() {
        viewModel.flightTaskLiveData.observe(viewLifecycleOwner) {
            when (it) {
                is State.Error -> {
                    binding.recyclerView.isVisible = false
                }

                State.Loading -> {

                    binding.recyclerView.isVisible = false
                }

                is State.Success -> {
                    binding.recyclerView.isVisible = true


                    if (status == FlightStatus.DEPARTURE.name) {
                        mAdapter.addData(it.data.departure)
                    } else {
                        mAdapter.addData(it.data.returnList)
                    }
                }

            }
            viewModel.sortBy.observe(viewLifecycleOwner) {
                when (viewModel.sortBy.value) {
                    SORT_BY_PRICE -> {
                        mAdapter.getData().sortBy { item -> item.netPrice ?: 0 }
                    }

                    SORT_BY_DATE -> {
                        mAdapter.getData().sortBy { item -> item.departureDate }
                    }

                }
                mAdapter.notifyDataSetChanged()
            }


        }
    }


    companion object {
        fun newInstance(status: String): FlightListFragmentV2 {
            val STATUS = "STATUS"

            val bundle = Bundle()
            bundle.putString(STATUS, status)

            return FlightListFragmentV2().apply {
                arguments = bundle
            }
        }
    }

    enum class FlightStatus {
        DEPARTURE,
        ROUND_TRIP
    }


}



