package com.hqt.view.ui.search.data.model.request


import com.google.gson.annotations.SerializedName

data class FlightTaskBody(
    @SerializedName("adultCount")
    var adultCount: String? = null,
    @SerializedName("childCount")
    var childCount: String? = null,
    @SerializedName("departureDate")
    var departureDate: String? = null,
    @SerializedName("device_id")
    var deviceId: String? = null,
    @SerializedName("infantCount")
    var infantCount: String? = null,
    @SerializedName("isRoundTrip")
    var isRoundTrip: String? = null,
    @SerializedName("key")
    var key: String? = null,
    @SerializedName("returnDate")
    var returnDate: String? = null,
    @SerializedName("source")
    var source: String? = null,
    @SerializedName("track")
    var track: String? = null,
    @SerializedName("uid")
    var uid: String? = null,
    @SerializedName("ver")
    var ver: String? = null
)