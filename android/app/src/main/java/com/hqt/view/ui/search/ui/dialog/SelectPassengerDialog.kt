package com.hqt.view.ui.search.ui.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import android.widget.Toast
import androidx.fragment.app.activityViewModels
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.hqt.datvemaybay.databinding.SelectPassengerLayoutBinding
import com.hqt.view.ui.search.ui.SearchViewModel
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SelectPassengerDialog : BottomSheetDialogFragment() {

    val viewModel: SearchViewModel by activityViewModels()


    var binding: SelectPassengerLayoutBinding? = null


    var onActionDone: () -> Unit = {}

    companion object {
        const val TAG = "ModalBottomSheet"
    }


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = SelectPassengerLayoutBinding.inflate(layoutInflater, container, false)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding?.apply {



            observe()

            selectPassengerButton.setOnClickListener {
                onActionDone.invoke()
                dismiss()
            }



            btnInAdult.setOnClickListener {
                viewModel.clickInAdult()
            }
            btnDeAdult.setOnClickListener {
                viewModel.clickDeInAdult()

            }

            btnInChild.setOnClickListener {
                viewModel.clickInChild()
            }

            btnDeChild.setOnClickListener {
                viewModel.clickDeInChild()

            }

            btnInInfant.setOnClickListener {
               viewModel.clickInInfant()
            }

            btnDeInfant.setOnClickListener {
                viewModel.clickDeInInfant()

            }





        }




        dialog?.let {
            val sheet = it as BottomSheetDialog
            sheet.behavior.state = BottomSheetBehavior.STATE_EXPANDED
        }


    }


    fun observe(){

        val inAnim = AnimationUtils.loadAnimation(
            requireContext(),
            android.R.anim.fade_in
        )
        inAnim.duration = 250

        binding?.apply {
            viewModel.adultCount.observe(viewLifecycleOwner){
                sheetAdultNumber.text = it.toString()
                sheetAdultNumber.startAnimation(inAnim)
            }
            viewModel.childCount.observe(viewLifecycleOwner){
                sheetChildNumber.text = it.toString()
                sheetChildNumber.startAnimation(inAnim)

            }
            viewModel.infantCount.observe(viewLifecycleOwner){
                sheetInfantNumber.text = it.toString()
                sheetInfantNumber.startAnimation(inAnim)

            }

        }

        viewModel.messageAdult.observe(viewLifecycleOwner){
            if (it.isNotEmpty())
                Toast.makeText(requireContext(), it.toString(), Toast.LENGTH_SHORT).show()
        }



    }



}