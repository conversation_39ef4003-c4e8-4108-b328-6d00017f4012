package com.hqt.view.ui.reward.data.model

import com.google.gson.annotations.SerializedName
import java.io.Serializable
import java.util.Date

data class Voucher(

    @SerializedName("id")
    var id: Int? = null,

    @SerializedName("type")
    var type: String? = null,

    @SerializedName("promotion_id")
    var promotionId: Int? = null,

    @SerializedName("voucher")
    var voucher: String? = null,

    @SerializedName("discount")
    var discount: Int? = null,

    @SerializedName("status")
    var status: String? = null,

    @SerializedName("note")
    var note: String? = null,

    @SerializedName("expired_at")
    var expiredAt: Date? = null,

    @SerializedName("promotion_detail")
    var promotionDetail: Promotion? = null,
) : Serializable