package com.hqt.view.ui.flighthistory.ui

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.hqt.base.model.State
import com.hqt.data.model.SliderItem
import com.hqt.data.prefs.SharedPrefsHelper
import com.hqt.util.Log
import com.hqt.view.ui.calender.data.model.FlightHistoryBody
import com.hqt.view.ui.flighthistory.data.api.FlightHistoryApiHelper
import com.hqt.view.ui.flighthistory.data.model.FlightHistory
import com.hqt.view.ui.flighthistory.data.model.FlightHistoryItem
import com.hqt.view.ui.search.data.model.FlightV2
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import javax.inject.Inject


@HiltViewModel
class FlightHistoryViewModel @Inject constructor(

    private val flightHistoryApiHelper: FlightHistoryApiHelper,
    private val sharedPrefsHelper: SharedPrefsHelper
) : ViewModel() {

    var flightHistory = MutableLiveData<FlightHistory>()
    var flightNumber = MutableLiveData<String>()
    fun getDays(): ArrayList<String> {
        var days: ArrayList<String> = ArrayList()

        days.add("14 ngày")
        days.add("30 ngày")
        days.add("60 ngày")
        days.add("90 ngày")
        return days
    }

    fun getSelectedDay(): String {
        return "14 ngày"
    }






    private val _flightHistoryLiveData: MutableLiveData<State<FlightHistory>> = MutableLiveData()
    val flightHistoryLiveData: LiveData<State<FlightHistory>> get() = _flightHistoryLiveData

    private val _sliderItemLiveData: MutableLiveData<State<ArrayList<SliderItem>>> = MutableLiveData()
    val sliderItemLiveData: LiveData<State<ArrayList<SliderItem>>> get() = _sliderItemLiveData


    private val _flightTrackLiveData: MutableLiveData<State<FlightHistoryItem>> = MutableLiveData()
    val flightTrackLiveData: LiveData<State<FlightHistoryItem>> get() = _flightTrackLiveData



    var selectFlight: FlightV2? = null

    init {
        flightHistory.postValue(FlightHistory())
    }



    fun getFlightHistoryList(flightNumber : String?, days : String) {
        viewModelScope.launch(Dispatchers.IO) {

            try {

                _flightHistoryLiveData.postValue(State.Loading)

                val body = FlightHistoryBody(
                    flightNumber = flightNumber,
                    days = days.toIntOrNull(),
                )
                val result = flightHistoryApiHelper.getFlightHistory(body)

                _flightHistoryLiveData.postValue(State.Success(result.data ?: FlightHistory()))


            }catch (ex : Exception){
                _flightHistoryLiveData.postValue(State.Error(ex))
                Log.logException(ex)
            }



        }


    }
    fun getFlightImages(registration : String?) {
        viewModelScope.launch(Dispatchers.IO) {

            try {

                _sliderItemLiveData.postValue(State.Loading)


                val result = flightHistoryApiHelper.getFlightImages(registration)

                _sliderItemLiveData.postValue(State.Success(result.data ?: arrayListOf() ))


            }catch (ex : Exception){
                _sliderItemLiveData.postValue(State.Error(ex))
                Log.logException(ex)
            }



        }


    }
    fun getFlightTrack(id : String?) {
        viewModelScope.launch(Dispatchers.IO) {

            try {

                _flightTrackLiveData.postValue(State.Loading)


                val result = flightHistoryApiHelper.getFlightTrack(id)

                _flightTrackLiveData.postValue(State.Success(result.data ?: FlightHistoryItem()))


            }catch (ex : Exception){
                _sliderItemLiveData.postValue(State.Error(ex))
                Log.logException(ex)
            }



        }


    }





}