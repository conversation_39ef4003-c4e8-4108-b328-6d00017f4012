package com.hqt.view.ui.payment.ui.main

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.get
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.hqt.data.model.Booking
import com.hqt.data.model.BookingTrain
import com.hqt.data.model.OrderInfo
import com.hqt.data.model.Payment
import com.hqt.data.model.PaymentType
import com.hqt.data.model.response.GetListPaymentRespone
import com.hqt.datvemaybay.PnrActivity
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.PaymentMainFragmentBinding
import com.hqt.util.AppConfigs
import com.hqt.view.adapter.PaymentMethodAdapter
import com.hqt.view.ui.bus.BusBookingViewActivity
import com.hqt.view.ui.payment.NewPaymentActivity
import com.hqt.view.ui.train.TrainBookingViewActivity

class NewPaymentFragment : Fragment() {

    companion object {
        fun newInstance() = NewPaymentFragment()
    }

    private var mRecyclerView: RecyclerView? = null
    private lateinit var adapter: PaymentMethodAdapter
    lateinit var binding: PaymentMainFragmentBinding
    private lateinit var viewModel: MainViewModel
    var orderInfo: OrderInfo? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {


        binding = DataBindingUtil.inflate(inflater, R.layout.payment_main_fragment, container, false)
        viewModel = ViewModelProvider(this).get(MainViewModel::class.java)

        mRecyclerView = binding.content.recyclerView
        val layoutManager: RecyclerView.LayoutManager = LinearLayoutManager(activity)
        mRecyclerView?.layoutManager = layoutManager
        mRecyclerView?.setHasFixedSize(true)

        binding.selectBankLayout.btnPayment.setOnClickListener {
            (activity as NewPaymentActivity).onPaymentCall()
        }


        binding.swipeRefreshLayout.setOnRefreshListener {
            (activity as NewPaymentActivity).getOrderInfo()
            Handler(Looper.getMainLooper()).postDelayed({
                binding.swipeRefreshLayout.isRefreshing = false
            }, 1500)

        }
        binding.content.viewBookingInfo.setOnClickListener {
            try {

                val bookingReturn = Booking()
                bookingReturn.token = orderInfo!!.sessionId!!
                bookingReturn.contact_email = orderInfo!!.contact!!.contactEmail!!
                bookingReturn.id = orderInfo!!.bookingId


                var i = Intent(context, PnrActivity::class.java)
                if (orderInfo!!.type == "TRAIN") {
                    i = Intent(context, TrainBookingViewActivity::class.java)
                    i.putExtra("BookingInfo", bookingReturn);
                } else if (orderInfo!!.type == "BUS") {
                    i = Intent(context, BusBookingViewActivity::class.java)
                    i.putExtra("BookingInfo", bookingReturn);

                }
                i.putExtra("token", orderInfo!!.sessionId)
                i.putExtra("bookingId", orderInfo!!.bookingId)
                i.putExtra("email", orderInfo!!.contact!!.contactEmail)
                i.putExtra("onyInfoView", true)
                startActivity(i)

            } catch (e: Exception) {
                e.printStackTrace()
            }

        }
        binding.lifecycleOwner = this
        binding.viewModel = viewModel
        setupToolbar()
        return binding.root
    }

    fun updateViewData(response: GetListPaymentRespone) {
        orderInfo = response.data!!.order
        binding.viewModel!!.orderInfo.value = response.data.order
        setTimeLimit(response.data.order!!)
        val data = response.data.paymentMethod.filter { it.On == true }
        adapter = PaymentMethodAdapter(data)

        mRecyclerView?.adapter = adapter
        adapter.notifyDataSetChanged()

        binding.shimmerViewContainer.visibility = View.GONE


    }

    private fun setTimeLimit(orderInfo: OrderInfo) {

        binding.toolbar.subtitle = "Đơn hàng #" + orderInfo.bookingId
        (activity as NewPaymentActivity).setTimeLimit(orderInfo.timeLimit!!, binding.headStatus)
    }

    fun clearSelectBank(tag: String) {
        try {
            for (i in 0 until adapter.itemCount) {
                val v = mRecyclerView!![i]
                if (!v.tag.equals(tag)) {
                    v.findViewById<LinearLayout>(R.id.payment_type_view_hide_all).performClick()
                }
            }
        } catch (e: Exception) {
            AppConfigs.logException(e)
        }

    }

    fun selectBank(selectPayment: Payment, selectMethod: PaymentType) {

        binding.selectBankLayout.payment = selectPayment
        binding.selectBankLayout.paymentMethod = selectMethod


    }

    private fun setupToolbar() {
        try {
            binding.toolbar.title = "Thanh toán"

            (activity as AppCompatActivity).setSupportActionBar(binding.toolbar)
            (activity as AppCompatActivity).supportActionBar!!.setDisplayHomeAsUpEnabled(true)
            binding.toolbar.bringToFront()
            binding.toolbar.setNavigationOnClickListener {
                (activity as AppCompatActivity).onBackPressed()
            }
        } catch (e: Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }

    }


    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        viewModel = ViewModelProvider(this).get(MainViewModel::class.java) // TODO: Use the ViewModel
    }

}