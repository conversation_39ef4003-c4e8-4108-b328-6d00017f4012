package com.hqt.view.ui.search.ui

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.hqt.view.ui.search.data.model.AirportInfo
import com.hqt.data.prefs.SharedPrefsHelper
import com.hqt.datvemaybay.Common
import dagger.hilt.android.lifecycle.HiltViewModel
import java.util.Calendar
import javax.inject.Inject


@HiltViewModel
class SearchViewModel @Inject constructor(

    val sharedPrefsHelper: SharedPrefsHelper
) : ViewModel() {


    val isOriginDomestic = MutableLiveData(true)
    val isDestinationDomestic = MutableLiveData(true)

    val originCode = MutableLiveData<String>()
    val originName = MutableLiveData<String>()
    val destinationCode = MutableLiveData<String>()
    val destinationName = MutableLiveData<String>()

    val adultCount = MutableLiveData(1)
    val childCount = MutableLiveData(0)
    val infantCount = MutableLiveData(0)


    var depDate =  MutableLiveData(Calendar.getInstance())
    var retDate =  MutableLiveData(Calendar.getInstance())




    var messageAdult = MutableLiveData("")

    init {


        depDate.value?.add(Calendar.DAY_OF_MONTH, 3)
        retDate.value?.add(Calendar.DAY_OF_MONTH, 5)



        initAddress()

    }

    private fun initAddress(){
        val originHistory = sharedPrefsHelper.getData("from", String::class.java) ?: "SGN"
        if (originHistory.length == 3) {
            originCode.value = originHistory
            originName.value = Common.getAirPortName(originHistory, false)
        }


        val destinationHistory = sharedPrefsHelper.getData("to", String::class.java) ?: "HAN"
        if (destinationHistory.length == 3) {
            destinationCode.value = destinationHistory
            destinationName.value = Common.getAirPortName(
                destinationHistory,
                false
            )
        }
    }

    fun setOrigin(airport : AirportInfo?){
        originCode.value = airport?.code ?: ""
        originName.value = airport?.name ?: ""
        isOriginDomestic.value = airport?.isDomestic



    }
    fun setDestination(airport : AirportInfo?){
        destinationCode.value = airport?.code ?: ""
        destinationName.value = airport?.name ?: ""
        isDestinationDomestic.value = airport?.isDomestic

    }


    fun getNumberOfPassenger() = (adultCount.value ?: 1) +( childCount.value ?: 0) + (infantCount.value ?: 0)


    fun swapLocations() {
        // Swap domestic flags
        val tempDomestic = isOriginDomestic.value
        isOriginDomestic.value = isDestinationDomestic.value
        isDestinationDomestic.value = tempDomestic ?: false

        // Swap codes and names
        val tempCode = originCode.value
        val tempName = originName.value

        originCode.value = destinationCode.value
        originName.value = destinationName.value

        destinationCode.value = tempCode ?: ""
        destinationName.value = tempName ?: ""
    }



    fun clickInAdult(){
        val adult = adultCount.value ?: 1
        val child = childCount.value ?: 0

        if (adult < 9 && (adult + child) < 9) {
            adultCount.value = adult + 1
        } else {
            messageAdult.value = "Tối đa 9 hành khách (người lớn và trẻ em)"
        }
    }
    fun clickDeInAdult(){
        val adult = adultCount.value ?: 1
        if (adult > 1) {
            adultCount.value = adult - 1
        }
    }
    fun clickInChild(){
        val adult = adultCount.value ?: 1
        val child = childCount.value ?: 0

        if (child < 9 && (adult + child) < 9) {
            childCount.value = child + 1
        } else {
            messageAdult.value =  "Tối đa 9 hành khách (người lớn và trẻ em)"
        }
    }
    fun clickDeInChild(){
        val child = childCount.value ?: 0
        if (child > 0) {
            childCount.value = child - 1
        }
    }
    fun clickInInfant() {
        val infant = infantCount.value ?: 0
        val adult = adultCount.value ?: 1
        if (infant < adult) {
            infantCount.value = infant + 1

        } else {
            messageAdult.value = "Số lượng em bé phải nhỏ hơn hoặc bằng người lớn"
        }
    }
    fun clickDeInInfant() {
        val infant = infantCount.value ?: 0
        if (infant > 0) {
            infantCount.value = infant - 1
        }
    }
}