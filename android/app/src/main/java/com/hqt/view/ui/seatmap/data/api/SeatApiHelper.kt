package com.hqt.view.ui.seatmap.data.api

import com.hqt.base.model.HttpData
import com.hqt.data.model.request.GetSeatMap
import com.hqt.data.prefs.SharedPrefsHelper
import com.hqt.util.ApiUtil
import com.hqt.view.ui.seatmap.SeatMap
import javax.inject.Inject
import javax.inject.Singleton


@Singleton
class SeatApiHelper @Inject constructor(
    private val api : SeatService,
    private val sharedPrefsHelper: SharedPrefsHelper
) {

    suspend fun postSeatMap(provider : String?, request : GetSeatMap?) : HttpData<SeatMap>{
        return api.postSeatMap(provider, request)
    }


}