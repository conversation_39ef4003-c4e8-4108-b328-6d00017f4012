package com.hqt.view.ui.chart

import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.lifecycle.lifecycleScope
import com.android.volley.AuthFailureError
import com.android.volley.Response
import com.android.volley.toolbox.JsonObjectRequest
import com.hqt.data.model.WeeklyPriceData
import com.hqt.datvemaybay.BuildConfig
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ActivityWeeklyPriceChartBinding
import com.hqt.util.AppConfigs
import com.hqt.util.AppController.Companion.instance
import com.hqt.util.WeeklyPriceProcessor
import com.hqt.view.ui.BaseActivityKt
import com.hqt.view.ui.airport.AirportSearchActivity
import kotlinx.coroutines.launch
import org.json.JSONArray
import org.json.JSONException
import org.json.JSONObject
import java.util.*

/**
 * Dedicated activity for displaying Weekly Cheapest Ticket Price Chart
 * Allows users to search for routes and view weekly price trends
 */
class WeeklyPriceChartActivity : BaseActivityKt<ActivityWeeklyPriceChartBinding>() {

    override val layoutId: Int = R.layout.activity_weekly_price_chart

    private lateinit var weeklyPriceChart: WeeklyPriceChartView
    private var originCode = "SGN"
    private var destinationCode = "HAN"
    private var originName = "Hồ Chí Minh"
    private var destinationName = "Hà Nội"
    private var loadedMonths: MutableSet<String> = mutableSetOf()

    private val REQUEST_CODE_ORIGIN = 100
    private val REQUEST_CODE_DESTINATION = 101

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setupToolbarx()
        initViews()
        initClickListeners()
        loadDefaultRoute()
        initAnalytics()
    }

    fun setupToolbarx() {
        setupToolbar()
        getToolbar().title = "Biểu đồ giá vé theo tuần"
        getToolbar().setNavigationIcon(R.drawable.ic_action_back_home)
        supportActionBar?.setDisplayShowHomeEnabled(true)
    }

    private fun initViews() {
        weeklyPriceChart = getViewBindding().weeklyPriceChart

        // Set initial route display
        updateRouteDisplay()

        // Set chart click listener
        weeklyPriceChart.setOnBarClickListener { weekData ->
            showWeekDetails(weekData)
        }
    }

    private fun initClickListeners() {
        // Origin selection
        getViewBindding().layoutOrigin.setOnClickListener {
            val intent = Intent(this, AirportSearchActivity::class.java)
            intent.putExtra("type", "origin")
            startActivityForResult(intent, REQUEST_CODE_ORIGIN)
        }

        // Destination selection
        getViewBindding().layoutDestination.setOnClickListener {
            val intent = Intent(this, AirportSearchActivity::class.java)
            intent.putExtra("type", "destination")
            startActivityForResult(intent, REQUEST_CODE_DESTINATION)
        }

        // Swap origin and destination
        getViewBindding().btnSwapRoute.setOnClickListener {
            swapRoute()
        }

        // Refresh data
        getViewBindding().btnRefresh.setOnClickListener {
            refreshData()
        }
    }

    private fun updateRouteDisplay() {
        getViewBindding().txtOrigin.text = originName
        getViewBindding().txtDestination.text = destinationName
        getViewBindding().txtOriginCode.text = originCode
        getViewBindding().txtDestinationCode.text = destinationCode
    }

    private fun loadDefaultRoute() {
        // Load data for default route (SGN -> HAN)
        loadWeeklyPriceData()
    }

    private fun swapRoute() {
        val tempCode = originCode
        val tempName = originName

        originCode = destinationCode
        originName = destinationName
        destinationCode = tempCode
        destinationName = tempName

        updateRouteDisplay()
        refreshData()
    }

    private fun refreshData() {
        loadedMonths.clear()
        loadWeeklyPriceData()
    }

    /**
     * Loads weekly price data for the current route
     */
    private fun loadWeeklyPriceData() {
        weeklyPriceChart.showLoading()

        // Load data for current month and next 2 months to get enough weekly data
        val calendar = Calendar.getInstance()
        val monthsToLoad = 3

        lifecycleScope.launch {
            try {
                for (i in 0 until monthsToLoad) {
                    val monthKey = Common.dateToString(calendar.time, "yyyyMM")
                    if (!loadedMonths.contains(monthKey)) {
                        loadMonthData(calendar.time)
                        loadedMonths.add(monthKey)
                    }
                    calendar.add(Calendar.MONTH, 1)
                }
            } catch (e: Exception) {
                AppConfigs.logException(e)
                weeklyPriceChart.showError("Không thể tải dữ liệu")
            }
        }
    }

    /**
     * Loads price data for a specific month
     */
    private fun loadMonthData(date: Date) {
        val month = Common.dateToString(date, "yyyyMM")
        val url =
            AppConfigs.getInstance().config.getString("root_api") + "/api/v1/AirLines/PricesBoard"

        val postParam = JSONObject()
        val route = JSONArray()
        route.put(originCode + destinationCode)

        try {
            postParam.put("startDate", month + "01")
            postParam.put("endDate", month + "31")
            postParam.put("maxPrice", "0")
            postParam.put("minPrice", "10000")
            postParam.put("routes", route)
            postParam.put("source", "ANDROID")
            postParam.put("type", "date")
            postParam.put("key", Common.getKeyHash())
            postParam.put("ver", BuildConfig.VERSION_CODE.toString())
        } catch (e: JSONException) {
            AppConfigs.logException(e)
            return
        }

        val jsonObjReq: JsonObjectRequest = object : JsonObjectRequest(
            Method.POST, url, postParam,
            Response.Listener<JSONObject> { response ->
                try {
                    if (!response.isNull("data")) {
                        val data = response.getJSONObject("data")
                        val fullData = data.getJSONObject("full")
                        val routeKey = originCode + destinationCode

                        if (!fullData.isNull(routeKey) && fullData.length() > 0) {
                            processMonthlyData(fullData.getJSONArray(routeKey))
                        }
                    }
                } catch (e: JSONException) {
                    AppConfigs.logException(e)
                    weeklyPriceChart.showError("Lỗi xử lý dữ liệu")
                }
            },
            Response.ErrorListener { e ->
                AppConfigs.logException(e)
                weeklyPriceChart.showError("Không thể tải dữ liệu từ server")
            }) {

            @Throws(AuthFailureError::class)
            override fun getHeaders(): HashMap<String, String> {
                return getHeaderRequest()
            }
        }

        instance.addToRequestQueue(jsonObjReq, "weekly_price_chart")
    }

    /**
     * Processes monthly data and updates the weekly chart
     */
    private fun processMonthlyData(pricesArray: JSONArray) {
        try {
            val weeklyData = WeeklyPriceProcessor.processMonthlyDataToWeekly(
                pricesArray,
                originCode,
                destinationCode
            )

            if (weeklyData.isNotEmpty()) {
                weeklyPriceChart.setWeeklyData(weeklyData, originCode, destinationCode)
                AppConfigs.Log("WeeklyChart", "Loaded ${weeklyData.size} weeks of data")
            } else {
                weeklyPriceChart.showError("Không có dữ liệu giá vé")
            }

        } catch (e: Exception) {
            AppConfigs.logException(e)
            weeklyPriceChart.showError("Lỗi xử lý dữ liệu")
        }
    }

    private fun getHeaderRequest(): HashMap<String, String> {
        val headers: HashMap<String, String> = HashMap()
        headers["Content-Type"] = "application/json"
        headers["X-Auth-ID"] = "9B1B13952BD9FF446AB569BBB49B3"
        headers["Origin"] = "https://12bay.vn"
        headers["Referer"] = "https://12bay.vn"
        headers["X-Requested-With"] = "XMLHttpRequest"
        return headers
    }

    private fun showWeekDetails(weekData: WeeklyPriceData) {
        val message = """
            Tuần: ${weekData.weekLabel}
            Giá rẻ nhất: ${weekData.getFormattedPrice()}
            Tuyến: ${weekData.origin} → ${weekData.destination}
        """.trimIndent()

        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (resultCode == RESULT_OK && data != null) {
            val airportCode = data.getStringExtra("airportCode") ?: return
            val airportName = data.getStringExtra("airportName") ?: return

            when (requestCode) {
                REQUEST_CODE_ORIGIN -> {
                    originCode = airportCode
                    originName = airportName
                }

                REQUEST_CODE_DESTINATION -> {
                    destinationCode = airportCode
                    destinationName = airportName
                }
            }

            updateRouteDisplay()
            refreshData()
        }
    }

    private fun initAnalytics() {
        val params = Bundle()
        params.putString("route", "$originCode-$destinationCode")
        firebaseAnalytics.logEvent("weekly_price_chart_view", params)
        firebaseAnalytics.setCurrentScreen(this, "weekly_price_chart", null)
    }
}
