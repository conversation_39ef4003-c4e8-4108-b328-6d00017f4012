package com.hqt.view.ui.booking.ui

import android.graphics.Color
import android.os.Build
import androidx.annotation.RequiresApi
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.hqt.base.model.State
import com.hqt.data.model.AddOnType
import com.hqt.datvemaybay.Common
import com.hqt.util.AppConfigs
import com.hqt.util.Log
import com.hqt.view.ui.booking.data.api.BookingHelper
import com.hqt.view.ui.booking.data.model.Baggage
import com.hqt.view.ui.booking.data.model.BaseBooking
import com.hqt.view.ui.booking.data.model.BookingV3
import com.hqt.view.ui.booking.data.model.PassengerType
import com.hqt.view.ui.booking.data.model.PassengerV2
import com.hqt.view.ui.booking.data.model.PaxInfoListV2
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject


@HiltViewModel
class BookingViewModelV2 @Inject constructor(
    private val bookingHelper: BookingHelper
) : ViewModel() {

    var flightK = ""

    val isUserSigned = MutableLiveData<Boolean>()
    var mBooking = MutableLiveData<BookingV3>()
    var isLoading = MutableLiveData<Boolean>()

    var mealCount = MutableLiveData(0)
    var seatCount = MutableLiveData(0)

    var mealCountText = MutableLiveData("")
    var seatCountText = MutableLiveData("")
    var mealCountDetailText = MutableLiveData("")
    var seatCountDetailText = MutableLiveData("")

    private val _baggageListLiveData: MutableLiveData<State<ArrayList<Baggage>>> = MutableLiveData()
    val baggageListLiveData: LiveData<State<ArrayList<Baggage>>> get() = _baggageListLiveData


    private val _baggageListReturnLiveData: MutableLiveData<State<ArrayList<Baggage>>> = MutableLiveData()
    val baggageListReturnLiveData: LiveData<State<ArrayList<Baggage>>> get() = _baggageListReturnLiveData


    private val _bookingLiveData: MutableLiveData<State<BaseBooking>> = MutableLiveData()
    val bookingLiveData: LiveData<State<BaseBooking>> get() = _bookingLiveData

    var clickPassenger = PassengerV2()

    fun getBaggageSequentially() {
        viewModelScope.launch {
            getBaggageInternal(false)
            getBaggageInternal(true)
        }
    }

    private suspend fun getBaggageInternal(isReturnTrip: Boolean) {
        withContext(Dispatchers.IO) {
            try {
                var provider = mBooking.value?.departure_f?.provider + "/" + mBooking.value?.origin_code + "/" + mBooking.value?.destination_code
                var fareBasis = mBooking.value?.departure_f?.fareBasis
                var stops = mBooking.value?.departure_f?.stops
                var flightKey = mBooking.value?.departure_f?.flightKey

                if (isReturnTrip) {
                    provider = mBooking.value?.return_f?.provider + "/" + mBooking.value?.destination_code + "/" + mBooking.value?.origin_code
                    fareBasis = mBooking.value?.return_f?.fareBasis
                    stops = mBooking.value?.return_f?.stops
                    flightKey = mBooking.value?.return_f?.flightKey
                }

                _baggageListLiveData.postValue(State.Loading)
                val result = bookingHelper.getBagFeeApi(provider, fareBasis, stops.toString(), flightKey)

                if (isReturnTrip) {
                    _baggageListReturnLiveData.postValue(State.Success(result.data ?: arrayListOf()))
                    Log.d("isReturnTrip", "Lượt về")
                } else {
                    _baggageListLiveData.postValue(State.Success(result.data ?: arrayListOf()))
                    Log.d("isReturnTrip", "Lượt đi")
                }

            } catch (ex: Exception) {
                _baggageListLiveData.postValue(State.Error(ex))
                Log.logException(ex)
            }
        }
    }
    fun sendBooking() {
        viewModelScope.launch {
            try {


                _bookingLiveData.postValue(State.Loading)


                mBooking.value?.contact?.fullname = mBooking.value?.contact_name
                mBooking.value?.contact?.phone = mBooking.value?.contact_phone
                mBooking.value?.contact?.email = mBooking.value?.contact_email

                val result = bookingHelper.sendBooking(mBooking.value)
                result.data?.let {
                    _bookingLiveData.postValue(State.Success(it))
                }



            } catch (ex: Exception) {
                _bookingLiveData.postValue(State.Error(ex))
                Log.logException(ex)
            }
        }

    }

    @RequiresApi(Build.VERSION_CODES.VANILLA_ICE_CREAM)
    fun initAllPassengers() {
        mBooking.value?.let {
            initPassengerList(it.pax_info.adult, it.adult, PassengerType.ADULT)
            initPassengerList(it.pax_info.child, it.child, PassengerType.CHILD)
            initPassengerList(it.pax_info.student, it.student, PassengerType.STUDENT)
            initPassengerList(it.pax_info.infant, it.infant, PassengerType.INFANT)
        }
    }


    @RequiresApi(Build.VERSION_CODES.VANILLA_ICE_CREAM)
    private fun initPassengerList(passengerList: MutableList<PassengerV2>, count: Int, type: PassengerType) {
        if (passengerList.isEmpty()) {
            for (i in 0 until count) {
                passengerList.addLast(PassengerV2().apply { startInput(type, i) })
            }
        }
    }






    fun isHaveVoucher(): Boolean {
        return mBooking.value!!.discount > 0
    }

    fun discountText(): String {
        return if (mBooking.value!!.discount > 0) Common.dinhDangTien(mBooking.value!!.discount)
        else {
            ""
        }
    }

    fun isDomesticBooking(): Boolean {
        if (mBooking.value?.type == BaseBooking.BookingType.FLIGHT) return true
        return false
    }

    fun isCreatedBooking(): Boolean {
        if (mBooking.value!!.status.isNullOrEmpty() || mBooking.value!!.status == "created") {
            return true
        }
        return false
    }

    fun getInputPaxTitle(): String {
        if (isShowBookingView()) {
            return "Thông tin hành khách"
        }
        return "Nhập thông tin hành khách"
    }

    fun getInputContactTitle(): String {
        if (isShowBookingView()) {
            return "Thông tin đơn hàng"
        }
        return "Nhập thông tin liên hệ"
    }

    fun isShowBookingView(): Boolean {
        if ((!mBooking.value?.status.isNullOrEmpty() || !mBooking.value?.id.isNullOrEmpty())) {
            return true
        }
        return false
    }

    fun isShowPayment(): Boolean {
        if (!mBooking.value?.status.isNullOrEmpty()) {
            return false
        }
        if (mBooking.value?.status.contentEquals("done")) {
            return false
        }
        return true
    }

    fun getTimeLimitText(): String {
        if (mBooking.value?.payment?.status == true) {
            var timeLimit = Common.getDateTimeFromFormat(mBooking.value!!.expired_date)
            return Common.dateToString(timeLimit, "HH:mm dd/MM/yyyy")
        }
        return "Hết hạn thanh toán"

    }

    fun formatTotal(value: Int): String {
        return Common.dinhDangTien(value)
    }

    fun getBookButtonText(): String {
        if (mBooking.value!!.payment.status) {
            return "THANH TOÁN NGAY"
        }
        if (!mBooking.value?.status.isNullOrEmpty()) {
            return "LIÊN LẠC HỖ TRỢ"
        }

        return "Đặt vé"
    }

    fun getStatusBackgoundColor(): Int {
        if (mBooking.value!!.status == "created") {
            return Color.parseColor("#FB953B")
        } else if (mBooking.value!!.status == "fail" || mBooking.value!!.status == "expired") {
            return Color.parseColor("#B1B1B1")
        } else if (mBooking.value!!.status == "done") {
            return Color.parseColor("#4CAF50")
        } else if (mBooking.value!!.status == "waiting_payment") {
            return Color.parseColor("#ff0088cd")
        } else if (mBooking.value!!.status == "payment_success") {
            return Color.parseColor("#FB953B")
        }
        return Color.parseColor("#FB953B")
    }

    fun isShowLogin(): Boolean {
        if (isUserSigned.value!!) {
            return false
        }
        return true
    }

    fun onChange() {
        mBooking.postValue(mBooking.value) // force postValue to notify Observers
        // can also use user.postValue()
    }

    fun getBooking(): LiveData<BookingV3> {
        return mBooking
    }



    fun updateAdult(){
//        mBooking.value?.contact?.fullname = clickPassenger.fullname
        when(clickPassenger.type){
            PassengerType.ADULT -> {
                mBooking.value?.pax_info?.adult?.set(clickPassenger.order, clickPassenger)
            }
            PassengerType.CHILD -> {
                mBooking.value?.pax_info?.child?.set(clickPassenger.order, clickPassenger)
            }
            PassengerType.INFANT -> {
                mBooking.value?.pax_info?.infant?.set(clickPassenger.order, clickPassenger)
            }
            PassengerType.STUDENT -> {
                mBooking.value?.pax_info?.student?.set(clickPassenger.order, clickPassenger)
            }
            PassengerType.OLDER -> {
                mBooking.value?.pax_info?.older?.set(clickPassenger.order, clickPassenger)
            }
            null -> {}
        }


        mBooking.postValue(mBooking.value)
    }


    fun addAddOnInfo(paxInfo: PaxInfoListV2) {
        viewModelScope.launch {
            try {

                val bookingPaxInfo = mBooking.value?.pax_info

                processPaxList(bookingPaxInfo?.adult, paxInfo.adult)
                processPaxList(bookingPaxInfo?.child, paxInfo.child)

                withContext(Dispatchers.Main){
                    seatCountText.value = (if ((seatCount.value ?: 0 )> 0) {
                        "Đã chọn $seatCount ghế"
                    } else {
                        "Chọn ghế ngồi"
                    })

                    seatCountDetailText.value = (if ((seatCount.value ?: 0) > 0) {
                        "Nhấn để xem lại hoặc thay đổi"
                    } else {
                        "Bạn có thể chọn vị trí chỗ ngồi trên máy bay"
                    })

                    mealCountText.value = (if ((mealCount.value ?: 0) > 0) {
                        "Đã chọn $mealCount món"
                    } else {
                        "Chọn bữa ăn"
                    })

                    mealCountDetailText.value = (
                            if ((mealCount.value ?: 0) > 0) {
                                "Nhấn để xem lại hoặc thay đổi"
                            } else {
                                "Đã chọn bữa ăn"
                            }
                            )

                }


                mBooking.postValue(mBooking.value)
            } catch (e: Exception) {
                AppConfigs.logException(e)
                e.printStackTrace()
            }
        }

    }



    private fun processPaxList(
        paxList: List<PassengerV2>?,
        sourceList: List<PassengerV2>?
    ) {
        paxList?.forEachIndexed { index, passenger ->
            val source = sourceList?.getOrNull(index)
            passenger.addOn = source?.addOn ?: arrayListOf()
            passenger.addOnReturn = source?.addOnReturn ?: arrayListOf()

            mealCount.value = (mealCount.value ?: 0) + (passenger.addOn + passenger.addOnReturn).count { it.type == AddOnType.MEAL }
            seatCount.value = (seatCount.value ?: 0) + (passenger.addOn + passenger.addOnReturn).count { it.type == AddOnType.SEAT }

        }
    }


}