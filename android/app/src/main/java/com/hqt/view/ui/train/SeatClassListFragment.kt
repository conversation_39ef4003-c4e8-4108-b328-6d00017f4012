package com.hqt.view.ui.train

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProviders
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.firebase.analytics.FirebaseAnalytics
import com.hqt.data.model.Train
import com.hqt.data.model.TrainSeatFare
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.FragmentTrainListBinding
import com.hqt.util.Widget
import com.hqt.view.adapter.TrainSeatClassAdapter
import com.hqt.viewmodel.WidgetFilterButtonViewModel
import java.util.*

class SeatClassListFragment(var isRoundTrip: Boolean) : Fragment() {
    lateinit var recyclerView: RecyclerView
    private lateinit var mAdapter: TrainSeatClassAdapter
    private var arraylistTrainSeat: ArrayList<TrainSeatFare> = ArrayList()
    private var listRawSeatFare: List<TrainSeatFare> = ArrayList()

    lateinit var toolbar: Toolbar
    lateinit var binding: FragmentTrainListBinding

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {

        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_train_list, container, false)

        var rootView = binding.root
        toolbar = binding.toolbar
        toolbar.inflateMenu(R.menu.main)

        if (isRoundTrip) toolbar.setTitle("Chọn loại ghế lượt về") else toolbar.setTitle("Chọn loại ghế")
        toolbar.setNavigationIcon(R.drawable.ic_action_back_home)
        (activity as AppCompatActivity).setSupportActionBar(toolbar)
        (activity as AppCompatActivity).supportActionBar!!.setDisplayShowHomeEnabled(true)
        toolbar.setNavigationOnClickListener {
            (activity as TrainSelectActivity).onBackPressed()
        }


        recyclerView = binding.recyclerView
        recyclerView.setHasFixedSize(true)
        recyclerView.layoutManager = LinearLayoutManager(activity)
        mAdapter = TrainSeatClassAdapter((activity as AppCompatActivity), arraylistTrainSeat)
        recyclerView.adapter = mAdapter
        initSortListen()

        return rootView
    }

    private fun initSortListen() {

        val viewModel = ViewModelProviders.of(this).get(WidgetFilterButtonViewModel::class.java)
        viewModel.initTow()
        binding.viewModel = viewModel
        viewModel.onSort.observe(requireActivity(), androidx.lifecycle.Observer {
            sortListTrain(viewModel.sortKey)
        })

    }

    private fun sortListTrain(byKey: String) {
        var listSortTrainSeat: List<TrainSeatFare> = listRawSeatFare

        when (byKey.replace("Desc", "")) {
            "seatClass" -> {
                listSortTrainSeat = arraylistTrainSeat.sortedWith(compareBy { it.seatClass })
            }

            "fare" -> {
                listSortTrainSeat = arraylistTrainSeat.sortedWith(compareBy { it.adult })
            }

            "allTrain" -> {
                listSortTrainSeat = listRawSeatFare.filter { trainSeat -> trainSeat.destinationKm > 0 }
            }

            "availableTrain" -> {
                listSortTrainSeat = listRawSeatFare.filter { trainSeat -> trainSeat.seatCount > 0 }
            }
        }

        if (byKey.toLowerCase(Locale.ROOT).contains("desc")) {
            listSortTrainSeat = listSortTrainSeat.reversed()
        }
        arraylistTrainSeat.clear()
        arraylistTrainSeat.addAll(listSortTrainSeat)
        mAdapter.notifyDataSetChanged()
    }

    fun genListTrainSeat(train: Train) {
        listRawSeatFare = train.fareOptions
        binding.layoutTrainInfo.removeAllViews()
        var trainInfo: View = Widget.trainView(train, binding.layoutTrainInfo, requireContext().applicationContext)
        binding.layoutTrainInfo.visibility = View.VISIBLE
        arraylistTrainSeat.clear()

        arraylistTrainSeat.addAll(train.fareOptions.filter { trainFare -> trainFare.seatCount > 0 })
        if (arraylistTrainSeat.isEmpty()) {
            arraylistTrainSeat.addAll(listRawSeatFare)
        }

        binding.filter.btnOne.performClick()
        binding.shimmerViewContainer.stopShimmer()
        binding.shimmerViewContainer.visibility = View.GONE
        binding.filter.root.visibility = View.VISIBLE

        initAnalytics(train)


    }

    private fun initAnalytics(train: Train) {

        val params = Bundle()
        params.putString(FirebaseAnalytics.Param.ORIGIN, train.originCode)
        params.putString(FirebaseAnalytics.Param.DESTINATION, train.destinationCode)
        params.putString(FirebaseAnalytics.Param.START_DATE, train.arrivalDateTime.toString())
        params.putString(FirebaseAnalytics.Param.END_DATE, train.arrivalDateTime.toString())
        params.putString(FirebaseAnalytics.Param.FLIGHT_NUMBER, train.trainNumber)
        params.putString(FirebaseAnalytics.Param.ITEM_NAME,
            train.originCode + train.destinationCode + train.trainNumber)
        if (train.fareOptions.isNotEmpty()) {
            params.putString(FirebaseAnalytics.Param.NUMBER_OF_PASSENGERS,
                (train.fareOptions[0].adultCount + train.fareOptions[0].student + train.fareOptions[0].childCount + train.fareOptions[0].older).toString() + "")
        }
        (activity as TrainSelectActivity).firebaseAnalytics.logEvent(FirebaseAnalytics.Event.VIEW_ITEM, params)
        (activity as TrainSelectActivity).firebaseAnalytics.setCurrentScreen((activity as TrainSelectActivity),
            "train_select_seat_class",
            null)
    }

}
