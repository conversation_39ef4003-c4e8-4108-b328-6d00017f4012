package com.hqt.view.ui.flightSearch

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Color
import android.graphics.Typeface
import android.net.Uri
import android.os.*
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.Button
import android.widget.LinearLayout
import android.widget.TextView
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.snackbar.Snackbar
import com.google.firebase.analytics.FirebaseAnalytics
import com.hqt.data.model.Booking
import com.hqt.data.model.BookingV2
import com.hqt.data.model.request.FlightSearchRequest
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.PnrActivity
import com.hqt.datvemaybay.R
import com.hqt.util.AppConfigs
import com.hqt.util.AppController
import com.hqt.util.Widget.createFareDetailInfo
import com.hqt.view.ui.BaseActivity
import com.hqt.view.ui.BaseActivityKt
import com.hqt.view.ui.reward.ui.activity.RewardActivity
import com.hqt.view.ui.booking.BookingActivity
import com.hqt.view.ui.flightSearch.model.FareData
import com.hqt.viewmodel.BookingViewModel
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import q.rorbin.badgeview.QBadgeView


class FlightSearchActivity() : BaseActivityKt<com.hqt.datvemaybay.databinding.ActivityFlightSearchLayoutBinding>() {
    var searchRequest: FlightSearchRequest? = null
    private var disposable: Disposable? = null
    private lateinit var mAdapter: FareDataItemAdapter
    private var arraylistFareData: ArrayList<FareData> = ArrayList()
    var sheetDialog: BottomSheetDialog? = null
    var sheetViewLayout: View? = null
    var selectedFare: FareData? = null
    override val layoutId: Int = R.layout.activity_flight_search_layout
    lateinit var viewModel: BookingViewModel
    lateinit var emptyState: LinearLayout
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        getToolbar().title = "Kết quả tìm kiếm"
        getToolbar().setNavigationIcon(R.drawable.ic_action_back_home)
        supportActionBar!!.setDisplayShowHomeEnabled(true)


        getViewBindding().recyclerView.layoutManager = LinearLayoutManager(applicationContext)
        getViewBindding().lifecycleOwner = this
        mAdapter = FareDataItemAdapter(this, arraylistFareData)
        getViewBindding().recyclerView.adapter = mAdapter

        initDefaultInfo()
        setUpFlightInfo()
        setClickHandlers()
        initSortFab()


        emptyState = initEmptyState("Hiện tại không có chuyến bay phù hợp. <br>Bạn vui lòng chọn lại hoặc liên hệ 19002642 để được hỗ trợ ngay",
            R.drawable.no_flight,
            -1,
            object : BaseActivity.EmptyStateCallBackInterface, EmptyStateCallBackInterface {
                override fun negativeButton(button: Button) {
                    button.text = "Tìm ngày khác"
                    button.setOnClickListener { finish() }
                }

                override fun positiveButton(button: Button) {
                    button.text = "Liên hệ hỗ trợ"
                    button.setOnClickListener {
                        val i = Intent(Intent.ACTION_DIAL)
                        i.data = Uri.parse("tel:19002642")
                        i.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                        startActivity(i)
                        finish()
                    }
                }
            })

    }

    private fun getDateFormat(dateString: String?): String {
        if (dateString == null) return ""
        val newDate = Common.stringToDate(dateString, "yyyy-MM-dd")

        return Common.dateToString(newDate.time, "dd/MM")
    }

    @SuppressLint("NotifyDataSetChanged") private fun initDefaultInfo() {

        var isRoundTrip = intent.getBooleanExtra("isRoundTrip", false)
        getToolbar().subtitle = " "
        getToolbar().title = " "


        val mTitle = getToolbar().getChildAt(0)

        // (0 until getToolbar().childCount).forEach { i ->
        val view = getToolbar().getChildAt(3)
        if (view is TextView) {
            view.typeface = Typeface.createFromAsset(assets, "fonts/fontawesome-webfont.ttf")
            view.setTextColor(Color.WHITE)
            view.textSize = 16f
        } // }
        val rtIcon = if (isRoundTrip) getString(R.string.fa_icon_exchange) + " " + getDateFormat(intent.getStringExtra("returnTime")) + " " else ""
        getToolbar().title = Common.getAirPortName(intent.getStringExtra("originCode"),
            true) + " - " + Common.getAirPortName(intent.getStringExtra("destinationCode"), true)

        getToolbar().subtitle = getString(R.string.fa_calendar) + " " + getDateFormat(intent.getStringExtra("departureTime")) + "  " + rtIcon + intent.getIntExtra(
            "adult",
            1) + getString(R.string.fa_male) + " " + intent.getIntExtra("child",
            0) + getString(R.string.fa_child) + " " + intent.getIntExtra("infant", 0) + getString(R.string.fa_female)



        searchRequest = FlightSearchRequest(isRoundTrip,
            intent.getStringExtra("departureTime"),
            intent.getStringExtra("returnTime"),
            intent.getIntExtra("adult", 1),
            intent.getIntExtra("child", 0),
            intent.getIntExtra("infant", 0),
            intent.getStringExtra("originCode")!!,
            intent.getStringExtra("destinationCode")!!)



        disposable = AppController.instance.getService()
            .postFlightSearch(searchRequest!!.originCode, searchRequest!!.destinationCode, searchRequest!!)
            .subscribeOn(Schedulers.io()).doOnSubscribe {

            }.observeOn(AndroidSchedulers.mainThread()).subscribe({ response ->

                if (response.data!!.listFareData!!.size > 0) {
                    arraylistFareData.clear()
                    arraylistFareData.addAll(response.data.listFareData!!)

                    mAdapter.notifyDataSetChanged()

                    getViewBindding().shimmerViewContainer.stopShimmer()
                    getViewBindding().shimmerViewContainer.visibility = View.GONE
                    getViewBindding().fbMenu.showMenuButton(true)
                    emptyState.visibility = View.GONE
                } else {
                    emptyState.visibility = View.VISIBLE
                    getViewBindding().shimmerViewContainer.stopShimmer()
                    getViewBindding().shimmerViewContainer.visibility = View.GONE
                }

            }, { throwable ->
                throwable.printStackTrace()
            })
    }

    private fun setClickHandlers() {

        sheetViewLayout!!.findViewById<Button>(R.id.btnBack).setOnClickListener {
            if (sheetDialog!!.isShowing) sheetDialog!!.hide()
        }

        sheetViewLayout!!.findViewById<Button>(R.id.btnBookVe).setOnClickListener {
            var booking = BookingV2()
            booking.type = Booking.BookingType.INTER
            booking.fareData = selectedFare
            booking.departure_f = null

            if (selectedFare!!.isRoundTrip) {
                booking.return_f = null
                booking.return_date = searchRequest!!.returnDate!!
            }

            booking.origin_code = searchRequest!!.originCode
            booking.destination_code = searchRequest!!.destinationCode
            booking.departure_date = searchRequest!!.departureDate!!

            booking.is_round_trip = searchRequest!!.isRoundTrip!!
            booking.adult = searchRequest!!.adultCount
            booking.child = searchRequest!!.childCount!!
            booking.infant = searchRequest!!.infantCount!!
            booking.total = selectedFare!!.totalPrice!!


            val `in` = Intent(applicationContext, BookingActivity::class.java)
            `in`.putExtra("bookingDetail", booking)
            `in`.putExtra("interBooking", true)
            startActivity(`in`)
            overridePendingTransition(R.anim.enter, R.anim.exit)
            firebaseAnalytics.setUserProperty("favorite_route",
                searchRequest!!.originCode + searchRequest!!.destinationCode)

            firebaseAnalytics.logEvent("click_search_button", null)


        }
    }

    private fun setUpFlightInfo() {

        sheetDialog = BottomSheetDialog(this, R.style.SheetDialogTransparent)
        sheetViewLayout = layoutInflater.inflate(R.layout.select_flight_inter_layout, null)
        sheetDialog!!.setContentView(sheetViewLayout!!)
    }

    fun showBottomSheetFlightInfo(fare: FareData?) {
        try {
            selectedFare = fare
            createFareDetailInfo(this, fare!!, sheetViewLayout!!.findViewById(R.id.flightInfo))
            sheetDialog!!.behavior.state = BottomSheetBehavior.STATE_EXPANDED
            sheetDialog!!.behavior.isHideable = false
            sheetDialog!!.behavior.isDraggable = false
            sheetDialog!!.show()

            sheetViewLayout!!.findViewById<TextView>(R.id.txtGrandTotalPrice).text = Common.dinhDangTien(fare.totalPrice!!)
        } catch (e: java.lang.Exception) {
            AppConfigs.Log("eeee", e.toString())
            e.printStackTrace()
            if (sheetDialog!!.isShowing) sheetDialog!!.dismiss()
        }
    }

    private fun isValidatedContact(): Boolean {


        return true
    }


    private fun initAnalytics(booking: BookingV2, event: String) {
        try {

            val params = Bundle()
            params.putString(FirebaseAnalytics.Param.ORIGIN, booking.departure_f?.originCode)
            params.putString(FirebaseAnalytics.Param.DESTINATION, booking.departure_f?.destinationCode)
            params.putString(FirebaseAnalytics.Param.START_DATE, booking.departure_f?.departureDateTime.toString())
            params.putString(FirebaseAnalytics.Param.END_DATE, booking.departure_f?.arriverDateTime.toString())
            params.putString(FirebaseAnalytics.Param.FLIGHT_NUMBER, booking.departure_f?.flightNumber)
            params.putString(FirebaseAnalytics.Param.ITEM_NAME,
                booking.departure_f?.originCode + booking.departure_f?.destinationCode + booking.departure_f?.flightNumber)
            params.putString(FirebaseAnalytics.Param.NUMBER_OF_PASSENGERS,
                (booking.departure_f!!.adult + booking.departure_f!!.child + booking.departure_f!!.infant).toString() + "")
            firebaseAnalytics.logEvent(event, params)
            firebaseAnalytics.setCurrentScreen(this, "booking_input", null)

        } catch (e: Exception) {

        }
    }

    override fun onResume() {
        super.onResume()
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean { // Inflate the menu; this adds items to the action bar if it is present.
        menuInflater.inflate(R.menu.main_reward, menu)
        try {
            Handler(Looper.getMainLooper()).postDelayed({
                val rewardButton = findViewById(R.id.action_reward) as View
                QBadgeView(applicationContext).setGravityOffset(0f, 0f, true).bindTarget(rewardButton).setBadgeText("!")
                    .setOnDragStateChangedListener { dragState, badge, targetView ->
                    }
            }, 1000)
        } catch (e: java.lang.Exception) {
        }
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean { // Take appropriate action for each action item click
        when (item.itemId) {
            android.R.id.home -> return true
            R.id.action_reward -> {
                val `in` = Intent(this, RewardActivity::class.java)
                startActivity(`in`)
                return true
            }
            else -> {
            }
        }
        return false
    }


    override fun refreshLayout() {

    }


    override fun onStart() {
        super.onStart()
        LocalBroadcastManager.getInstance(this).registerReceiver(mMessageReceiver, IntentFilter("bookingupdate"))
    }

    private val mMessageReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val message = intent.getStringExtra("message")
            val snackbar = Snackbar.make(getViewBindding().coordinatorLayout, message!!, 60000)
                .setAction("XEM CHI TIẾT") {
                    val `in` = Intent(applicationContext, PnrActivity::class.java)
                    startActivity(`in`)
                    finish()
                }
            snackbar.show()
        }
    }

    constructor(parcel: Parcel) : this() {

    }

    override fun onSystemSettingChange(type: AppConfigs.SystemSettingType) {

        if (type == AppConfigs.SystemSettingType.INTERNET) {
            if (this::viewModel.isInitialized) {
                viewModel.updateInternetStatus(isInternetConnected)
            }
        }

        if (type == AppConfigs.SystemSettingType.USER) {
            if (this::viewModel.isInitialized) {
                viewModel.updateUserStatus(isUserSigned)
                getViewBindding().viewModel = viewModel
                if (isUserSigned && AppController.instance.user != null) {


                }
            }
        }
    }


    companion object CREATOR : Parcelable.Creator<FlightSearchActivity> {
        override fun createFromParcel(parcel: Parcel): FlightSearchActivity {
            return FlightSearchActivity(parcel)
        }

        override fun newArray(size: Int): Array<FlightSearchActivity?> {
            return arrayOfNulls(size)
        }
    }

    @SuppressLint("NotifyDataSetChanged") private fun sortBy(type: String) {
        if (type == "sortArrTimeDesc" || type == "sortDepTimeDesc") {
            arraylistFareData.sortByDescending {
                when (type) {
                    "sortArrTimeDesc" -> it.arrTime
                    "sortDepTimeDesc" -> it.depTime
                    else -> it.totalPrice
                }
            }
        } else {
            arraylistFareData.sortBy {
                when (type) {
                    "sortDuration" -> it.duration
                    "sortArrTime" -> it.arrTime
                    "sortDepTime" -> it.depTime
                    "fab_sortPrice" -> it.totalPrice
                    else -> it.totalPrice
                }


            }
        }
        getViewBindding().fbMenu.close(true)
        mAdapter.notifyDataSetChanged()
    }

    private fun initSortFab() {
        getViewBindding().fbMenu.isIconAnimated = false
        getViewBindding().fbMenu.hideMenuButton(false)


        getViewBindding().fabSortArrTime.setOnClickListener {
            sortBy(it.tag.toString())
        }
        getViewBindding().fabSortArrTimeDesc.setOnClickListener {
            sortBy(it.tag.toString())
        }
        getViewBindding().fabSortDuration.setOnClickListener {
            sortBy(it.tag.toString())
        }
        getViewBindding().fabSortDepTime.setOnClickListener {
            sortBy(it.tag.toString())
        }
        getViewBindding().fabSortDepTimeDesc.setOnClickListener {
            sortBy(it.tag.toString())
        }
        getViewBindding().fabSortPrice.setOnClickListener {
            sortBy(it.tag.toString())
        }
    }
}
