package com.hqt.view.ui.search.ui.adapter.airport.v2

import android.graphics.Color
import android.text.style.BackgroundColorSpan
import android.text.style.ForegroundColorSpan
import android.text.style.RelativeSizeSpan
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import com.bumptech.glide.Glide
import com.github.florent37.materialviewpager.MaterialViewPagerHelper
import com.github.florent37.materialviewpager.Utils
import com.hqt.base.BaseAdapter
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ListFlightItemBinding
import com.hqt.view.ui.search.data.model.FlightV2
import com.mikepenz.iconics.Iconics
import java.util.Locale


class FlightViewAdapterV2(private val listener: (FlightV2) -> Unit) :
    BaseAdapter<FlightV2, ListFlightItemBinding>(listener) {


    private var selected_position: String? = ""

    var offset = 0


    override fun getLayoutRes(): Int {
        return R.layout.list_flight_item
    }



    override fun bind(binding: ListFlightItemBinding, position: Int, model: FlightV2) {
        binding.apply {

//            itemViewState = AirportInfoItemState(model)

//
//            if (position == 0){
//                val layoutParams = LinearLayout.LayoutParams(
//                    LinearLayout.LayoutParams.MATCH_PARENT, // Chiều rộng
//                    LinearLayout.LayoutParams.WRAP_CONTENT // Chiều cao
//
//                )
//                layoutParams.setMargins(0, Utils.dpToPx((offset + 10).toFloat(), _context).toInt(), 0, 0)
//
//                viewItem.layoutParams = layoutParams
//            }

            container.isSelected = selected_position == model.uuid

            soHieu.text = model.flightNumber
            if (Common.SHOWFULLPRICE) {
                giaVe.text = Common.dinhDangTien(model.adult ?: 0)
            } else {
                giaVe.text = Common.dinhDangTien(model.netPrice ?: 0)
            }
            
            thoiGianDen.text = model.arriverDateTime?.substring(0, 5)
            thoiGianBay.text = model.departureDateTime?.substring(0, 5)
            giaPhi.text = Common.dinhDangTien(model.adult ?: 0)
            thuePhi.text = Common.dinhDangTien(model.tax ?: 0)
            thueSanBay.text = Common.dinhDangTien(model.airPortFee ?: 0 )
            hangBay.text = model.providerText
            hangGhe.text = model.seatClass
            gioBay.text = model.departureDateTime?.substring(6, 16)
            txtDuration.text = model.duration



            
            if (model.quickDep) {
                soHieu.text = "{faw_history} " + model.flightNumber
                Iconics.Builder().style(
                    ForegroundColorSpan(ContextCompat.getColor(_context, R.color.google_yellow)),
                    BackgroundColorSpan(
                        Color.TRANSPARENT
                    ),
                    RelativeSizeSpan(1f)
                ).on(soHieu).build()
            } else if (model.promo) {
                soHieu.text = "{faw_star} " + model.flightNumber

                Iconics.Builder().style(
                    ForegroundColorSpan(ContextCompat.getColor(_context,R.color.google_yellow)),
                    BackgroundColorSpan(
                        Color.TRANSPARENT
                    ),
                    RelativeSizeSpan(1f)
                ).on(soHieu).build()
            } else {
                soHieu.text = model.flightNumber
            }

            pos.text = position.toString() + ""
            stop.text = "{faw_circle1} " + model.stopsText
            Iconics.Builder().style(
                ForegroundColorSpan(
                    if (model.stopsText.lowercase(Locale.getDefault()) == "bay thẳng") ContextCompat.getColor(_context,
                        R.color.primary
                    ) else Color.RED
                ), BackgroundColorSpan(Color.TRANSPARENT), RelativeSizeSpan(0.5f)
            ).on(stop).build()


            Glide.with(_context).load(model.airlinesLogo).into(binding.logo)



        }

    }





    override fun onItemClickListener(model: FlightV2) {
        listener(model)

        val uuidOld = selected_position
        selected_position = model.uuid ?: ""

        val indexOld = getData().indexOfFirst { it.uuid == uuidOld }
        if (indexOld != -1) notifyItemChanged(indexOld)

        val currentIndex = getData().indexOfFirst { it.uuid == selected_position }
        if (currentIndex != -1) notifyItemChanged(currentIndex)


    }

}