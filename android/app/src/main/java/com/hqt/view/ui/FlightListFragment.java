package com.hqt.view.ui;


import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.github.florent37.materialviewpager.MaterialViewPagerHelper;
import com.github.florent37.materialviewpager.header.MaterialViewPagerHeaderDecorator;
import com.hqt.view.adapter.FlightViewAdapter;
import com.hqt.datvemaybay.R;
import com.hqt.datvemaybay.SearchResult;
import com.hqt.data.model.Flight;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;


public class FlightListFragment extends Fragment {

    private RecyclerView mRecyclerView;
    private RecyclerView.Adapter mAdapter;

    private static final int ITEM_COUNT = 5;

    private List<Flight> mContentItems = new ArrayList<>();

    public static FlightListFragment newInstance() {
        return new FlightListFragment();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_recyclerview, container, false);
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        mRecyclerView = view.findViewById(R.id.recyclerView);
        RecyclerView.LayoutManager layoutManager = new LinearLayoutManager(getActivity());
        mRecyclerView.setLayoutManager(layoutManager);
        mRecyclerView.setHasFixedSize(true);

        mAdapter = new FlightViewAdapter(getActivity(), mContentItems);
        mRecyclerView.addItemDecoration(new MaterialViewPagerHeaderDecorator());
        mRecyclerView.setAdapter(mAdapter);


    }

    public void addFlight(List<Flight> listFlight, String sortBy) {

        mContentItems.clear();
        mContentItems.addAll(listFlight);
        if (sortBy.equals("price")) {
            Collections.sort(mContentItems, new Comparator<Flight>() {
                public int compare(Flight s1, Flight s2) {
                    return s1.getNetPrice() - s2.getNetPrice();
                }

            });
        } else {
            Collections.sort(mContentItems, new Comparator<Flight>() {
                public int compare(Flight s1, Flight s2) {
                    //TO DO
                    return s1.getDepartureDate().compareTo(s2.getDepartureDate());

                }
            });
        }
        mAdapter.notifyDataSetChanged();

    }

    public void clearSelect() {

        mAdapter.notifyDataSetChanged();
    }


}


