package com.hqt.view.ui.train

import android.graphics.Color
import android.graphics.Typeface
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProviders
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.firebase.analytics.FirebaseAnalytics
import com.hqt.datvemaybay.R
import com.hqt.view.adapter.TrainAdapter
import com.hqt.datvemaybay.databinding.FragmentTrainListBinding
import com.hqt.data.model.Train
import com.hqt.util.Widget
import com.hqt.viewmodel.TrainBookingViewModel
import com.hqt.viewmodel.WidgetFilterButtonViewModel
import java.util.*
import kotlin.collections.ArrayList


class TrainListFragment(var isRoundTrip: Boolean) : Fragment() {
    lateinit var recyclerView: RecyclerView
    private lateinit var mAdapter: TrainAdapter
    private var arraylistTrain: ArrayList<Train> = ArrayList()
    private var listRawTrain: List<Train> = ArrayList()
    lateinit var toolbar: Toolbar
    lateinit var binding: FragmentTrainListBinding

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_train_list, container, false)

        val rootView = binding.root
        toolbar = binding.toolbar
        toolbar.inflateMenu(R.menu.main)

        if (isRoundTrip) toolbar.title = "Chọn tàu về" else toolbar.title = "Chọn tàu đi"

        toolbar.setNavigationIcon(R.drawable.ic_action_back_home)
        (activity as AppCompatActivity).setSupportActionBar(toolbar)
        (activity as AppCompatActivity).supportActionBar!!.setDisplayShowHomeEnabled(true)

        toolbar.setNavigationOnClickListener {
            (activity as TrainSelectActivity).onBackPressed()
        }
        initSortListen()
        setSubTitleInfo()
        initAnalytics()

        recyclerView = binding.recyclerView
        recyclerView.setHasFixedSize(true)
        recyclerView.layoutManager = LinearLayoutManager(activity)
        mAdapter = TrainAdapter((activity as AppCompatActivity), arraylistTrain)
        recyclerView.adapter = mAdapter
        iniToolbar()
        return rootView
    }

    fun iniToolbar() {
        try {
            val view = toolbar.getChildAt(1)
            if (view is TextView) {
                view.textSize = 18f
                view.ellipsize = TextUtils.TruncateAt.MARQUEE;
                view.marqueeRepeatLimit = -1;
                view.isSingleLine = true;
                view.setSelected(true);
            }
            activity?.window?.statusBarColor = Color.TRANSPARENT
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun initSortListen() {

        var viewModel = ViewModelProviders.of(this).get(WidgetFilterButtonViewModel::class.java)
        viewModel.initOne()
        binding.viewModel = viewModel
        viewModel.onSort.observe(viewLifecycleOwner, androidx.lifecycle.Observer {
            sortListTrain(viewModel.sortKey)
        })

    }

    private fun sortListTrain(byKey: String) {
        var listSortTrain: List<Train> = listRawTrain

        when (byKey.replace("Desc", "")) {
            "departureTime" -> {
                listSortTrain = arraylistTrain.sortedWith(compareBy { it.departureDateTime })
            }

            "duration" -> {
                listSortTrain = arraylistTrain.sortedWith(compareBy { it.getDuration() })
            }

            "allTrain" -> {
                listSortTrain = listRawTrain.filter { train -> train.destinationKm > 0 }
            }

            "availableTrain" -> {
                listSortTrain = listRawTrain.filter { train -> train.getRemainSeat() > 0 }
            }
        }

        if (byKey.toLowerCase(Locale.ROOT).contains("desc")) {
            listSortTrain = listSortTrain.reversed()
        }
        arraylistTrain.clear()
        arraylistTrain.addAll(listSortTrain)
        mAdapter.notifyDataSetChanged()
    }

    fun genListTrain(trains: List<Train>) {
        listRawTrain = trains

        arraylistTrain.clear()
        arraylistTrain.addAll(listRawTrain.filter { train -> train.getRemainSeat() > 0 })
        if (arraylistTrain.isEmpty()) {
            arraylistTrain.addAll(listRawTrain)
        }

        //mAdapter.notifyDataSetChanged()
        binding.filter.btnOne.performClick()
        binding.shimmerViewContainer.stopShimmer()
        binding.shimmerViewContainer.visibility = View.GONE
        binding.filter.root.visibility = View.VISIBLE


    }

    private fun setSubTitleInfo() { //        toolbar.setSubtitleTextColor(Color.WHITE)
        var subtitle = (activity as TrainSelectActivity).booking.origin_code + "-" + (activity as TrainSelectActivity).booking.destination_code + " " + (activity as TrainSelectActivity).booking.departure_date + " - "

        if ((activity as TrainSelectActivity).booking.adult > 0) {
            subtitle = subtitle + " " + (activity as TrainSelectActivity).booking.adult + getString(R.string.fa_male)
        }
        if ((activity as TrainSelectActivity).booking.child > 0) {
            subtitle = subtitle + " " + (activity as TrainSelectActivity).booking.child + getString(R.string.fa_child)
        }
        if ((activity as TrainSelectActivity).booking.student > 0) {
            subtitle = subtitle + " " + (activity as TrainSelectActivity).booking.student + getString(R.string.fa_user_graduate)
        }
        if ((activity as TrainSelectActivity).booking.older > 0) {
            subtitle = subtitle + " " + (activity as TrainSelectActivity).booking.older + getString(R.string.fa_blind)
        }
        toolbar.subtitle = subtitle
        val view = toolbar.getChildAt(3)
        if (view is TextView) {
            view.textSize = 16f
            view.typeface = Typeface.createFromAsset((activity as TrainSelectActivity).assets,
                "fonts/fontawesome-webfont.ttf")
        }
    }

    private fun initAnalytics() {

        (activity as TrainSelectActivity).firebaseAnalytics.setCurrentScreen((activity as TrainSelectActivity),
            "train_select_train",
            null)
    }


}
