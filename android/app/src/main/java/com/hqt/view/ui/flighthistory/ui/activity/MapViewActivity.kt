package com.hqt.view.ui.flighthistory.ui.activity

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.content.res.Resources
import android.graphics.Bitmap


import android.graphics.Color
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.android.volley.VolleyError
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.CustomTarget
import com.google.android.gms.maps.CameraUpdateFactory
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.OnMapReadyCallback
import com.google.android.gms.maps.SupportMapFragment
import com.google.android.gms.maps.model.*
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.gson.reflect.TypeToken
import com.hqt.view.ui.flighthistory.data.model.FPosition
import com.hqt.view.ui.flighthistory.data.model.FlightHistoryItem
import com.hqt.view.ui.flighthistory.data.model.LiveMapData
import com.hqt.datvemaybay.Common.getBitmapFromVectorDrawable
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ActivityMapViewBinding
import com.hqt.datvemaybay.databinding.FlightHistoryDetailMiniBinding
import com.hqt.util.*
import com.hqt.view.ui.BaseActivityKt
import com.hqt.view.ui.flighthistory.other.PermissionUtils.isPermissionGranted
import com.hqt.view.ui.flighthistory.other.PermissionUtils.requestPermission
import io.nlopez.smartlocation.OnLocationUpdatedListener
import io.nlopez.smartlocation.SmartLocation
import org.json.JSONObject
import java.util.*
import kotlin.collections.ArrayList


class MapViewActivity : BaseActivityKt<ActivityMapViewBinding>(), OnMapReadyCallback,
    ActivityCompat.OnRequestPermissionsResultCallback {
    override val layoutId: Int = R.layout.activity_map_view
    lateinit var googleMapInit: GoogleMap
    lateinit var airport: Bitmap
    var curentPosition: FPosition = FPosition()
    var selectedFlight: FlightHistoryItem? = null
    private var nextMarker: Marker? = null
    private var nextPolyline: Polyline? = null
    private var nextPolylineReturn: Polyline? = null
    var countDownTime = 20
    private var flightDetailBinding: FlightHistoryDetailMiniBinding? = null
    val handler = Handler(Looper.getMainLooper())
    val handlerAuto = Handler(Looper.getMainLooper())
    lateinit var runnable: Runnable
    lateinit var runnableAuto: Runnable
    var onLiveMapLoading = false
    var onGetTrack = false
    var onMoveCamera = false
    var permissionDenied = false
    var isHistory = false;

    var markerList: ArrayList<Marker> = ArrayList()
    private var airportList: ArrayList<Marker> = ArrayList()
    private var polylineList: ArrayList<Polyline> = ArrayList()


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        airport = Bitmap.createScaledBitmap(getBitmapFromVectorDrawable(applicationContext,
            R.drawable.ic_marker_aiport), 80, 80, false)

        val mapFragment = supportFragmentManager.findFragmentById(R.id.map) as SupportMapFragment?
        mapFragment!!.getMapAsync(this)

        if (intent.hasExtra("flightId")) {
            var flightid = intent.getStringExtra("flightId")
            isHistory = intent.getBooleanExtra("isHistory", false)
            onMoveCamera = intent.getBooleanExtra("onMoveCamera", false)

            if (!flightid.isNullOrEmpty()) {
                getTrack(flightid)
            }
        }

        runnable = object : Runnable {
            override fun run() {
                if (selectedFlight != null) {
                    if (countDownTime == 0) {
                        getTrack(selectedFlight!!.id)
                    } else {
                        if (!isHistory) {
                            nextMarker = callNextPosition(selectedFlight?.track!!.last(), true, nextMarker)
                        }
                        caculatorNextPos()
                    }
                }
                countDownTime--
                handler.postDelayed(this, 500)
            }
        }
        handler.postDelayed(runnable, 1000)

        runnableAuto = object : Runnable {
            override fun run() {
                caculatorNextPos()
                handlerAuto.postDelayed(this, 400)
            }
        }

        handlerAuto.postDelayed(runnableAuto, 1000)


        getToolbar().title = "Theo dõi chuyến bay"
        getToolbar().setNavigationIcon(R.drawable.ic_action_back_home)
        supportActionBar!!.setDisplayShowHomeEnabled(true)


        getViewBindding().searchButton.setOnClickListener {
            val flin = Intent(this, FlightHistoryActivityV2::class.java)
            startActivity(flin)
        }
        initAnalytics()
    }

    fun initMapTypeClick(googleMap: GoogleMap) {
        getViewBindding().mapType.btnNormal.setOnClickListener {
            googleMap.mapType = GoogleMap.MAP_TYPE_NORMAL
            flightDetailBinding?.root?.visibility = View.GONE
        }
        getViewBindding().mapType.btnStatelLte.setOnClickListener {
            googleMap.mapType = GoogleMap.MAP_TYPE_HYBRID
            flightDetailBinding?.root?.visibility = View.GONE
        }
        getViewBindding().mapType.btnTerrain.setOnClickListener {
            googleMap.mapType = GoogleMap.MAP_TYPE_TERRAIN
            flightDetailBinding?.root?.visibility = View.GONE
        }
    }

    override fun onResume() {
        super.onResume()
        if (intent.hasExtra("flightId")) {
            val flightId = intent.getStringExtra("flightId")
            isHistory = intent.getBooleanExtra("isHistory", false)
            onMoveCamera = intent.getBooleanExtra("onMoveCamera", false)

            if (flightId != null) getTrack(flightId)
        }

        handler.postDelayed(runnable, 100)
        handlerAuto.postDelayed(runnableAuto, 200)

    }

    private fun enableMyLocation() {
        if (!::googleMapInit.isInitialized) return
        if (ContextCompat.checkSelfPermission(this,
                Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
            googleMapInit.isMyLocationEnabled = true
            SmartLocation.with(this).location().oneFix().start(OnLocationUpdatedListener() {
                val latlng = LatLng(it.latitude, it.longitude)
                googleMapInit.animateCamera(CameraUpdateFactory.newLatLng(latlng))
                googleMapInit.animateCamera(CameraUpdateFactory.zoomTo(7.2f), 2000, null)
            })
        } else { // Permission to access the location is missing. Show rationale and request permission
            requestPermission(this, LOCATION_PERMISSION_REQUEST_CODE, Manifest.permission.ACCESS_FINE_LOCATION, false)
        }
    }


    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode != LOCATION_PERMISSION_REQUEST_CODE) {
            return
        }
        if (isPermissionGranted(permissions,
                grantResults,
                Manifest.permission.ACCESS_FINE_LOCATION)) { // Enable the my location layer if the permission has been granted.
            enableMyLocation()
        } else { // Permission was denied. Display an error message
            // Display the missing permission error dialog when the fragments resume.
            permissionDenied = true
        }
    }

//    override fun onNewIntent(intent: Intent?) {
//        super.onNewIntent(intent)
//        setIntent(intent)
//    }

    override fun onMapReady(googleMap: GoogleMap) {
        try {
            googleMap.uiSettings.isRotateGesturesEnabled = false
            googleMap.uiSettings.isMyLocationButtonEnabled = true


            try {
                val success = googleMap.setMapStyle(MapStyleOptions.loadRawResourceStyle(this, R.raw.style_json))
                if (!success) {

                }
            } catch (e: Resources.NotFoundException) {
                AppConfigs.logException(e)
            }

            googleMapInit = googleMap
            val hn = LatLng(11.651784, 107.288378)

            googleMap.moveCamera(CameraUpdateFactory.newLatLng(hn))
            googleMap.moveCamera(CameraUpdateFactory.zoomTo(7.2f))
            googleMap.uiSettings.isMyLocationButtonEnabled = true

            googleMap.setOnCameraIdleListener {
                if (!isHistory) {

                    getLiveMap(googleMap.projection.visibleRegion.latLngBounds, googleMap.cameraPosition.zoom)
                    filterAircraftByZoom(googleMap.cameraPosition.zoom)
                }
            }

            googleMap.setOnMarkerClickListener { marker ->
                if (marker.tag != null && marker.tag is FPosition) {
                    marker.isVisible = false
                    val curentPos = (marker.tag as FPosition)
                    nextMarker = callNextPosition(curentPos, false, nextMarker)
                    selectedFlight = null
                    flightDetailBinding?.root?.visibility = View.GONE

                    loadMarkerIcon(nextMarker!!, curentPos, "red")
                    clearMapItem("removeMarker", curentPos.key)

                    if (!onGetTrack) getTrack(curentPos.key)


                }
                true
            }
            initMapTypeClick(googleMap)
            enableMyLocation()
        } catch (e: Exception) {
            AppConfigs.logException(e)
        }

    }

    fun callNextPosition(curentPos: FPosition, onDrawPoly: Boolean, marker: Marker?): Marker? {
        try {
            if (curentPos.speed <= 10) return marker
            val r = 6371 * 1000.toDouble() // Earth Radius in m
            var now = Calendar.getInstance().time
            var timeDiff = (now.time - curentPos.timestamp!!.time) / 500
            val distance = (curentPos.speed * 1000 / 7200) * timeDiff

            var lat2 = Math.asin(Math.sin(Math.toRadians(curentPos.latitude)) * Math.cos(distance / r) + Math.cos(Math.toRadians(
                curentPos.latitude)) * Math.sin(distance / r) * Math.cos(Math.toRadians(curentPos.heading + 0.0)))
            var lon2 = (Math.toRadians(curentPos.longitude) + Math.atan2(Math.sin(Math.toRadians(curentPos.heading + 0.0)) * Math.sin(
                distance / r) * Math.cos(Math.toRadians(curentPos.latitude)),
                Math.cos(distance / r) - Math.sin(Math.toRadians(curentPos.latitude)) * Math.sin(lat2)))
            lat2 = Math.toDegrees(lat2)
            lon2 = Math.toDegrees(lon2)

            var nextPos = LatLng(lat2, lon2)

            //            curentPos.latitude = lat2
            //            curentPos.longitude = lon2

            if (marker == null) {
                var mk = googleMapInit.addMarker(MarkerOptions().position(nextPos)
                    .icon(BitmapDescriptorFactory.fromBitmap(curentPos.getAircraftBitmap(true))).flat(false)
                    .rotation(curentPos.heading + 0f).anchor(0.5f, 0.5f))

                return mk
            } else {
                marker.position = nextPos
                marker.rotation = curentPos.heading + 0f
            }

            if (onDrawPoly) {
                if (nextPolyline != null) {
                    nextPolyline!!.remove()
                    nextPolylineReturn!!.remove()
                }


                nextPolyline = googleMapInit.addPolyline(PolylineOptions().clickable(false).width(6f)
                    .add(curentPos.getLatLng(), nextPos).color(FlightMapUtils.getAltitudeColor(curentPos.altitude)))
                val pattern: List<PatternItem> = arrayListOf(Dash(10f), Gap(20f))

                nextPolylineReturn = googleMapInit.addPolyline(PolylineOptions().clickable(false).pattern(pattern)
                    .width(5f).add(nextPos,
                        LatLng(selectedFlight?.airport?.destination?.latitude!!.toDouble(),
                            selectedFlight?.airport?.destination?.longitude!!.toDouble())).color(Color.GRAY))
            }

            return marker

        } catch (e: Exception) {
            e.printStackTrace()
            AppConfigs.logException(e)
            return marker
        }
    }

    fun updateUi(flightHistory: FlightHistoryItem?) {
        try {
            val latLngBoundsBuilder = LatLngBounds.Builder()

            clearMapItem("airportList", "")
            try {
                val fromAirport = googleMapInit.addMarker(MarkerOptions().icon(BitmapDescriptorFactory.fromBitmap(
                    airport)).position(LatLng(flightHistory?.airport?.origin!!.latitude!!.toDouble(),
                    flightHistory.airport.origin.longitude!!.toDouble())).title(flightHistory.airport.origin.city))
                fromAirport!!.tag = "airport"

                val toAirPort = googleMapInit.addMarker(MarkerOptions().icon(BitmapDescriptorFactory.fromBitmap(airport))
                    .position(LatLng(flightHistory.airport.destination.latitude!!.toDouble(),
                        flightHistory.airport.destination.longitude!!.toDouble()))
                    .title(flightHistory.airport.destination.city))
                toAirPort!!.tag = "airport"

                airportList.add(fromAirport)
                airportList.add(toAirPort)
                latLngBoundsBuilder.include(fromAirport.position)
                latLngBoundsBuilder.include(toAirPort.position)

            } catch (e: java.lang.Exception) {

            }

            if (!flightHistory?.track?.isEmpty()!!) {
                var i = 0
                for (i in 0 until flightHistory.track.size - 1) {

                    var line = PolylineOptions().clickable(true)
                        .add(flightHistory.track[i].getLatLng(), flightHistory.track[i + 1].getLatLng())
                    line.color(FlightMapUtils.getAltitudeColor(flightHistory.track[i].altitude))
                    polylineList.add(googleMapInit.addPolyline(line))

                }
                clearMapItem("removeMarker", flightHistory.id)

                if (!isHistory) {
                    nextMarker = callNextPosition(flightHistory.track.last(), true, nextMarker)
                }

                if (onMoveCamera) { //googleMapInit.animateCamera(CameraUpdateFactory.newLatLngZoom(flightHistory.track.last().getLatLng(), 9.5f), 1200, null)
                    val bounds: LatLngBounds = latLngBoundsBuilder.build()
                    googleMapInit.animateCamera(CameraUpdateFactory.newLatLngBounds(bounds, 100), 2000, null)

                    onMoveCamera = false
                }
            }

            filterAircraftByZoom(googleMapInit.cameraPosition.zoom)

            flightDetailBinding = Widget.showFlightHistoryShort(this,
                getViewBindding().tripContainer,
                flightHistory,
                flightDetailBinding)
            flightDetailBinding?.root?.visibility = View.VISIBLE
            firebaseAnalytics.logEvent("flight_history_flight_track_view", null)


        } catch (e: Exception) {
            e.printStackTrace()
            AppConfigs.logException(e)
        }
    }

    fun getTrack(flightId: String) {
        try {
            if (!onGetTrack && flightId.isNotEmpty()) {
                getViewBindding().progressLoader.visibility = View.VISIBLE
                onGetTrack = true
                val param = JSONObject()
                param.put("flightId", flightId)
                if (isHistory) {
                    param.put("isHistory", true)
                }
                clearMapItem("polyline", "")
                SSLSendRequest(this).GET(false, "Flight/Track", param, object : SSLSendRequest.CallBackInterface {
                    override fun onSuccess(response: JSONObject, cached: Boolean) {

                        if (!response.isNull("data")) {
                            val historyItem = object : TypeToken<FlightHistoryItem>() {}.type
                            selectedFlight = AppController.instance.gSon.fromJson<FlightHistoryItem>(response.getJSONObject(
                                "data").toString(), historyItem)
                            countDownTime = 60

                            updateUi(selectedFlight)
                            onGetTrack = false
                            getViewBindding().progressLoader.visibility = View.GONE
                        } else {
                            onGetTrack = false
                            getViewBindding().progressLoader.visibility = View.GONE
                        }
                    }

                    override fun onFail(er: VolleyError) {
                        onGetTrack = false
                        getViewBindding().progressLoader.visibility = View.GONE
                    }
                })
            }
        } catch (e: Exception) {
            onGetTrack = false
            getViewBindding().progressLoader.visibility = View.GONE
            AppConfigs.Log("Flight Track Error", e.printStackTrace().toString())
            AppConfigs.logException(e)
        }

    }

    fun clearMapItem(type: String, flightId: String) {
        if (type == "removeMarker") {
            markerList.onEach { marker ->
                if (marker.isVisible) {
                    marker.isVisible = !(marker.tag is FPosition && (marker.tag as FPosition).key == flightId)
                }

            }
        }
        if (type == "liveData") {
            var tmp: ArrayList<Marker> = ArrayList()
            markerList.onEach { marker ->
                if (marker.tag is FPosition) {
                    marker.remove()
                } else {
                    tmp.add(marker)
                }
            }
            markerList = tmp

        }
        if (type == "airportList") {

            airportList.onEach { marker ->

                marker.remove()
            }
            airportList.clear()

        }
        if (type == "polyline") {
            polylineList.onEach { line ->
                line.remove()
            }
            polylineList.clear()
            nextPolyline?.remove()
            nextPolylineReturn?.remove()

        }

    }

    private fun getLiveMap(currentCameraBounds: LatLngBounds, zoomLevel: Float) {
        if (!onLiveMapLoading) {
            onLiveMapLoading = true
            getViewBindding().progressLoader.visibility = View.VISIBLE
            try {
                val param = JSONObject()
                param.put("bounds",
                    "" + currentCameraBounds.northeast.latitude + "," + currentCameraBounds.southwest.latitude + "," + currentCameraBounds.southwest.longitude + "," + currentCameraBounds.northeast.longitude)
                param.put("zoom", zoomLevel)
                SSLSendRequest(this).GET(false, "Flight/LiveMap", param, object : SSLSendRequest.CallBackInterface {
                    override fun onSuccess(response: JSONObject, cached: Boolean) {
                        if (!response.isNull("data")) {
                            val liveMapDataType = object : TypeToken<LiveMapData>() {}.type
                            var liveMapData = AppController.instance.gSon.fromJson<LiveMapData>(response.getJSONObject("data")
                                .toString(), liveMapDataType)
                            if (liveMapData.flights != null && liveMapData.flights.size > 0) {
                                for (airPlanePosition in liveMapData.flights) {
                                    if (selectedFlight == null || airPlanePosition.key != selectedFlight!!.id) {

                                        airPlanePosition.timestamp = Calendar.getInstance().time
                                        if (!isExistMarker(airPlanePosition)) {
                                            var marker = googleMapInit.addMarker(MarkerOptions().position(
                                                airPlanePosition.getLatLng()).flat(false)
                                                .icon(BitmapDescriptorFactory.fromBitmap(airPlanePosition.getAircraftBitmap(
                                                    false))).rotation(airPlanePosition.heading + 0f).anchor(0.5f, 0.5f))
                                            marker!!.tag = airPlanePosition
                                            loadMarkerIcon(marker, airPlanePosition, "yellow")
                                            markerList.add(marker)

                                        } else {
                                            updateMarkerItem(airPlanePosition)
                                        }
                                        getViewBindding().progressLoader.visibility = View.GONE

                                    }

                                }
                                filterAircraftByZoom(zoomLevel)
                            }

                        }
                        onLiveMapLoading = false
                    }

                    override fun onFail(er: VolleyError) {
                        onLiveMapLoading = false
                    }
                })

            } catch (e: Exception) {
                AppConfigs.Log("Flight LiveMap Error", e.printStackTrace().toString())
                AppConfigs.logException(e)
                onLiveMapLoading = false
            }
        }
    }

    override fun onBackPressed() {
        super.onBackPressed()
        overridePendingTransition(R.anim.left_to_right, R.anim.right_to_left)
        handler.removeCallbacks(runnable)
        finish()
    }

    private fun initAnalytics() {

        val params = Bundle()
        firebaseAnalytics.logEvent("flight_history_map_view", params)

    }

    private fun filterAircraftByZoom(zoomLevel: Float) {
        markerList.onEach { marker ->
            marker.isVisible = false
            if (marker.tag is FPosition) {
                val alt = (marker.tag as FPosition).altitude
                val speed = (marker.tag as FPosition).speed
                var isShow = true

                if (zoomLevel < 4 && alt < 12000) {
                    isShow = false
                }
                if (zoomLevel < 4.5 && alt < 11200) {
                    isShow = false
                }
                if (zoomLevel < 5 && alt < 11000) {
                    isShow = false
                }
                if (zoomLevel < 5.5 && alt < 8000) {
                    isShow = false
                }
                if (zoomLevel < 6 && alt < 6000) {
                    isShow = false
                }
                if (zoomLevel < 6.5 && alt < 4500) {
                    isShow = false
                }
                if (zoomLevel < 7 && alt < 4000) {
                    isShow = false
                }
                if (zoomLevel < 7.5 && alt < 500) {
                    isShow = false
                }
                if (zoomLevel < 12 && speed < 11) {
                    isShow = false
                }

                if (selectedFlight != null && (marker.tag as FPosition).key == selectedFlight!!.id) {
                    isShow = false
                }

                marker.isVisible = isShow

            }

        }
    }

    private fun caculatorNextPos() {

        if (googleMapInit.cameraPosition.zoom > 6.3) {
            markerList.forEachIndexed { index, mk ->
                if ((mk.tag as FPosition).key != curentPosition.key && mk.isVisible) {
                    var marker = callNextPosition(mk.tag as FPosition, false, mk)
                    if (marker != null) markerList[index] = marker
                }
            }
        }
    }

    private fun updateMarkerItem(airPlanePosition: FPosition): Boolean {

        markerList.onEach { mk ->

            if ((mk.tag as FPosition).key == airPlanePosition.key && (mk.tag as FPosition).hash != airPlanePosition.hash) {

                mk.tag = airPlanePosition
                mk.position = airPlanePosition.getLatLng()
                mk.rotation = airPlanePosition.heading + 0f
                return true
            }
        }

        return false
    }


    private fun isExistMarker(position: FPosition): Boolean {
        markerList.onEach { mk ->
            if ((mk.tag as FPosition).key == position.key) {
                return true
            }
        }
        return false
    }

    private fun loadMarkerIcon(marker: Marker, airPlanePosition: FPosition, color: String) {

        try {

            Glide.with(applicationContext).asBitmap()
                .load("https://ssl.12bay.vn/images/aircraft/" + color + "/" + airPlanePosition.aircraftImage + ".png")
                .into(object : CustomTarget<Bitmap>() {
                    override fun onResourceReady(resource: Bitmap,
                        transition: com.bumptech.glide.request.transition.Transition<in Bitmap>?) {
                        marker.setIcon(BitmapDescriptorFactory.fromBitmap(Bitmap.createScaledBitmap(resource,
                            resource.width,
                            resource.height,
                            false)))
                    }

                    override fun onLoadCleared(placeholder: Drawable?) {
                        TODO("Not yet implemented")
                    }

                })

        } catch (ex: Exception) {
            marker.setIcon(BitmapDescriptorFactory.fromBitmap(airPlanePosition.getAircraftBitmap(false)))
        }

    }

    private fun initAnalytics(fnumber: String) {

        val params = Bundle()
        params.putString(FirebaseAnalytics.Param.FLIGHT_NUMBER, fnumber)
        firebaseAnalytics.logEvent("flight_history_view", params)
        firebaseAnalytics.setCurrentScreen(this, "flight_history_list", null)
    }

    override fun onStop() {
        super.onStop()
        handler.removeCallbacks(runnable)
        handlerAuto.removeCallbacks(runnableAuto)

    }
}