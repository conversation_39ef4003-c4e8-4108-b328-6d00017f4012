package com.hqt.view.ui.booking

import android.app.Activity
import android.app.ProgressDialog
import android.content.BroadcastReceiver
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.core.view.isVisible
import androidx.lifecycle.ViewModelProvider
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.android.volley.VolleyError
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.snackbar.Snackbar
import com.google.firebase.analytics.FirebaseAnalytics
import com.hqt.data.model.AddOnType
import com.hqt.data.model.Booking
import com.hqt.data.model.BookingV2
import com.hqt.data.model.Passenger
import com.hqt.data.model.PassengerType
import com.hqt.data.model.PaxInfoList
import com.hqt.datvemaybay.Common
import com.hqt.datvemaybay.PnrActivity
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ActivityBookingLayoutBinding
import com.hqt.util.AppConfigs
import com.hqt.util.AppController
import com.hqt.util.Helper
import com.hqt.util.SSLSendRequest
import com.hqt.util.SSLSendRequest.CallBackInterface
import com.hqt.util.SharedPrefs
import com.hqt.util.ViewUtil
import com.hqt.util.Widget
import com.hqt.util.Widget.retrievePassenger
import com.hqt.view.ui.BaseActivityKt
import com.hqt.view.ui.reward.ui.activity.RewardActivity
import com.hqt.view.ui.addon.SelectAddOnActivity
import com.hqt.view.ui.flightSearch.model.FareData
import com.hqt.view.ui.seatmap.SelectSeatActivity
import com.hqt.viewmodel.BookingViewModel
import org.json.JSONException
import org.json.JSONObject
import q.rorbin.badgeview.QBadgeView

class BookingActivity : BaseActivityKt<ActivityBookingLayoutBinding>() {

    override val layoutId: Int = R.layout.activity_booking_layout
    lateinit var baseBooking: Booking
    lateinit var booking: BookingV2
    lateinit var viewModel: BookingViewModel
    var sheetDialog: BottomSheetDialog? = null
    var sheetViewLayout: View? = null
    val REQUEST_CODE_ADDON_SELECT = 4

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        getToolbar().title = "Điền thông tin"
        getToolbar().setNavigationIcon(R.drawable.ic_action_back_home)
        supportActionBar!!.setDisplayShowHomeEnabled(true)


        if (intent.hasExtra("interBooking")) {
            booking = intent.getSerializableExtra("bookingDetail") as BookingV2
            initAnalytics(booking, FirebaseAnalytics.Event.BEGIN_CHECKOUT)
            baseBooking = booking
            baseBooking.type = Booking.BookingType.INTER

            AppConfigs.Log("baseBooking.type", baseBooking.type.toString())

        } else if (intent.hasExtra("BookingTour")) {
            booking = intent.getSerializableExtra("bookingDetail") as BookingV2
            initAnalytics(booking, FirebaseAnalytics.Event.BEGIN_CHECKOUT)
            baseBooking = booking
            baseBooking.type = Booking.BookingType.FLIGHT


        } else {
            booking = intent.getSerializableExtra("bookingDetail") as BookingV2
            initAnalytics(booking, FirebaseAnalytics.Event.BEGIN_CHECKOUT)
            baseBooking = booking
            baseBooking.type = Booking.BookingType.FLIGHT
        }

        Common.MAX_DEPATURE_DATE = baseBooking.departure_date
        if (baseBooking.is_round_trip) {
            Common.MAX_DEPATURE_DATE = baseBooking.return_date
        }

        viewModel = ViewModelProvider.AndroidViewModelFactory(application)
            .create(BookingViewModel::class.java)

        viewModel.mBooking.value = (baseBooking)

        viewModel.isUserSigned.value = (isUserSigned)
        viewModel.getBooking().observe(this) { bk ->
            baseBooking = bk
            if (bk.pax_info.adult.size > 0 && getViewBindding().content.txtContactName.text.isEmpty()) {
                getViewBindding().content.txtContactName.setText(bk.pax_info.adult.get(0).fullName)
            }
        }

        getViewBindding().viewModel = viewModel
        getViewBindding().lifecycleOwner = this

        showBookingTripDetail(baseBooking)

        if (baseBooking.type == Booking.BookingType.FLIGHT) {
            getBagFeeApi(booking, false)
        }

        initDefaultInfo()
        setClickHandlers() //START SHOW SHOWCASE
        setUpFlight()

    }

    private fun initDefaultInfo() {
        try {
            if (isUserSigned && AppController.instance.user != null) {
                booking.contact_name = AppController.instance.user?.userName!!
                booking.contact_email = AppController.instance.user?.userEmail!!
                booking.contact_phone = AppController.instance.user?.phoneNumber!!
            }
        } catch (e: Exception) {

        }

        val settings = getSharedPreferences("12BAY-APP-CONFIG", 0)
        if (!isUserSigned) {
            getViewBindding().content.txtContactPhone.setText(
                settings.getString("phone", "").toString()
                    .replace("+84", "0")
            )
            getViewBindding().content.txtContactName.setText(
                settings.getString("name", "").toString()
            )
            val Umail = settings.getString("Uemail", "").toString()
            getViewBindding().content.txtContactEmail.setText(
                settings.getString("email", Umail).toString()
            )
        } else {
            refreshLayout()
        }
    }

    private fun setClickHandlers() {

        getViewBindding().content.paxInPut.setOnClickListener { //re calculator fee
            viewModel.mBooking.value = baseBooking
        }
        getViewBindding().content.btnGetVoucher.setOnClickListener {

            if (baseBooking.voucher.isNullOrEmpty()) {
                showSelectVoucherDialog()

            } else {
                useVoucher()
            }
        }
        getViewBindding().content.btnBookVe.setOnClickListener {
            AppConfigs.Log("baseBooking.type", baseBooking.type.toString())
            if (!baseBooking.pax_info.isValidated()) {

                ViewUtil.makeMeShake(getViewBindding().content.paxInPut.getChildAt(0), 30, 5)

                Toast.makeText(
                    applicationContext,
                    "Vui lòng nhập đầy đủ thông tin hành khách",
                    Toast.LENGTH_SHORT
                )
                    .show()
                getViewBindding().content.scrollView.smoothScrollTo(
                    0,
                    (getViewBindding().content.horizalScroll.bottom) - 200
                )
            } else if (isValidatedContact()) {

                showConfirmDialog()

            }
        }
        getViewBindding().content.loginLayout.setOnClickListener {
            signIn()
        }
        getViewBindding().content.showSeatSelect.setOnClickListener {
            val seatMap = Intent(applicationContext, SelectSeatActivity::class.java)
            seatMap.putExtra("bookingDetail", booking)
            startActivityForResult(seatMap, REQUEST_CODE_ADDON_SELECT)
        }
        getViewBindding().content.showAddOnSelect?.setOnClickListener {
            val seatMap = Intent(applicationContext, SelectAddOnActivity::class.java)
            seatMap.putExtra("bookingDetail", booking)
            startActivityForResult(seatMap, REQUEST_CODE_ADDON_SELECT)
        }
    }

    private fun isValidatedContact(): Boolean {
        AppConfigs.Log("baseBooking", baseBooking.contact_name)
        var contactName = Common.unAccent(getViewBindding().content.txtContactName.text.toString())

        getViewBindding().content.txtContactName.setText(contactName)

        if (getViewBindding().content.txtContactName.text.isEmpty()) {
            Toast.makeText(applicationContext, "Vui lòng điền tên liên hệ", Toast.LENGTH_SHORT)
                .show()
            getViewBindding().content.txtContactName.requestFocus()
            return false
        } else if (Helper.validatorInput(
                contactName.toString(),
                Helper.ValidatorType.FULLNAME.text
            ) != null
        ) {
            Toast.makeText(
                applicationContext,
                "Vui lòng nhập đúng định dạng tên liên hệ",
                Toast.LENGTH_SHORT
            ).show()
            getViewBindding().content.txtContactName.requestFocus()
            return false
        } else if (getViewBindding().content.txtContactPhone.text.isEmpty()) {
            Toast.makeText(
                applicationContext,
                "Vui lòng điền số điện thoại liên hệ",
                Toast.LENGTH_SHORT
            ).show()
            getViewBindding().content.txtContactPhone.requestFocus()
            return false
        } else if (!Common.isEmailValid(
                getViewBindding().content.txtContactEmail.getText().toString()
            )
        ) {
            AlertDialog.Builder(this).setIcon(R.drawable.ic_bell_alert).setTitle("Chú ý")
                .setMessage(Common.convertHTML("Vui lòng nhập đúng email hoặc bỏ trống nếu không có"))
                .setPositiveButton("Nhập Email") { dialogInterface, i -> getViewBindding().content.txtContactEmail.requestFocus() }
                .setNegativeButton("Bỏ qua") { dialogInterface, i ->
                    getViewBindding().content.txtContactEmail.setText("<EMAIL>")
                    showConfirmDialog()

                }.show()
            return false
        }
        return true
    }

    private fun sendBooking() {
        mergeBooking()

        initAnalytics(baseBooking, FirebaseAnalytics.Event.PURCHASE)

        val dialog = ProgressDialog(this)
        dialog.setMessage("Đang thực hiện đặt chỗ ...\nVui lòng đợi giây lát!")
        dialog.isIndeterminate = false
        dialog.max = 100
        dialog.setCanceledOnTouchOutside(false)
        dialog.setCancelable(false)
        dialog.setProgressStyle(ProgressDialog.STYLE_SPINNER)
        dialog.show()

        var postParam = JSONObject()
        try {
            val contactInfo = JSONObject()
            contactInfo.put("fullname", baseBooking.contact_name)
            contactInfo.put("phone", baseBooking.contact_phone)
            contactInfo.put("email", baseBooking.contact_email)


            postParam =
                JSONObject(AppController.instance.gSon.toJson(booking, BookingV2::class.java))
            postParam.put("contact", contactInfo)
            postParam.put("gcm", Common.FCM_TOKEN)

            AppConfigs.Log("booking", AppController.instance.gSon.toJson(postParam).toString())
        } catch (e: JSONException) {
            e.printStackTrace()
        }

        SSLSendRequest(this).POST(
            false,
            "AirLines/Bookings",
            postParam,
            object : CallBackInterface {
                override fun onSuccess(response: JSONObject, cached: Boolean) {
                    try {
                        if (response.has("data")) {
                            val jsonBookingTrain = response.getJSONObject("data")
                            val bookingReturn = AppController.instance.gSon.fromJson(
                                jsonBookingTrain.toString(),
                                BookingV2::class.java
                            )

                            if (bookingReturn.id.isEmpty()) {
                                Toast.makeText(
                                    applicationContext,
                                    "Thật xin lỗi :( \nCó lỗi xảy ra khi đặt chỗ \n Vui lòng thử lại hoặc gọi " + AppConfigs.getInstance().config.getString(
                                        "hotline"
                                    ) + " để được hỗ trợ!",
                                    Toast.LENGTH_SHORT
                                ).show()
                            } else {

                                val `in` = Intent(applicationContext, PnrActivity::class.java)
                                `in`.putExtra("email", bookingReturn.contact_email)
                                `in`.putExtra("bookingId", bookingReturn.id)
                                `in`.putExtra("showLoading", true)

                                startActivity(`in`)

                                val intent = Intent("bookingInsert")
                                LocalBroadcastManager.getInstance(applicationContext)
                                    .sendBroadcast(intent)
                                finish()
                            }

                        }
                        dialog.dismiss()

                    } catch (e: JSONException) {
                        dialog.dismiss()
                        Toast.makeText(
                            applicationContext,
                            "Thật xin lỗi :( \nCó lỗi xảy ra khi đặt chỗ \n Vui lòng thử lại hoặc gọi " + AppConfigs.getInstance().config.getString(
                                "hotline"
                            ) + " để được hỗ trợ!",
                            Toast.LENGTH_SHORT
                        ).show()
                        AppConfigs.logException(e, postParam.toString())
                    }
                }

                override fun onFail(e: VolleyError) {
                    dialog.dismiss()
                    Toast.makeText(
                        applicationContext,
                        "Thật xin lỗi :( \nCó lỗi xảy ra khi đặt chỗ \n Vui lòng thử lại hoặc gọi " + AppConfigs.getInstance().config.getString(
                            "hotline"
                        ) + " để được hỗ trợ!",
                        Toast.LENGTH_SHORT
                    ).show()
                    AppConfigs.logException(e, postParam.toString())
                }
            })
    }

    private fun useVoucher() {
        val dialog = ProgressDialog(this)
        dialog.setMessage("Đang tải dữ liệu ...")
        dialog.isIndeterminate = false
        dialog.max = 100
        dialog.setCanceledOnTouchOutside(true)
        dialog.setCancelable(true)
        dialog.setProgressStyle(ProgressDialog.STYLE_SPINNER)
        dialog.show()

        val postParam = JSONObject()
        postParam.put("voucher", baseBooking.voucher)
        postParam.put("booking", baseBooking.getBaseBookingSortInfo())
        postParam.put(
            "checksum",
            Common.getMd5Hash(baseBooking.voucher + baseBooking.getBaseBookingSortInfo())
        )

        SSLSendRequest(this).POST(
            false,
            "AirLines/Voucher/Use",
            postParam,
            object : CallBackInterface {
                override fun onSuccess(response: JSONObject, cached: Boolean) {
                    try {
                        if (!response.isNull("data")) {
                            val data = response.getJSONObject("data")
                            val rtCode = data.getString("voucher")
                            val rtDiscount = data.getInt("discount")
                            val rtresult = data.getString("text")

                            if (rtDiscount > 0) {
                                baseBooking.discount = rtDiscount
                                baseBooking.voucher = rtCode
                                getViewBindding().content.btnBookVe.requestFocus()
                                viewModel.mBooking.value = baseBooking
                            } else {
                                getViewBindding().content.txtVoucherCode.setText("")
                            }
                            Toast.makeText(this@BookingActivity, rtresult, Toast.LENGTH_SHORT)
                                .show()

                        } else {
                            Common.showAlertDialog(
                                this@BookingActivity,
                                "Thật tiếc",
                                "Mã giảm giá không đúng!",
                                false,
                                false
                            )
                        }
                        if (dialog.isShowing) dialog.dismiss()
                    } catch (e: JSONException) {
                        AppConfigs.logException(e)
                        e.printStackTrace()
                    }
                }

                override fun onFail(e: VolleyError) {
                    if (dialog.isShowing) dialog.dismiss()
                    Common.showAlertDialog(
                        this@BookingActivity,
                        "Thông báo !",
                        "Không tìm thấy voucher \nVui lòng liên lạc chúng tôi để được hỗ trợ",
                        false,
                        false
                    )
                }
            })
    }

    private fun showPaxInput(isAllowEdit: Boolean, paxUpdate: Boolean) {

        getViewBindding().content.paxInPut.removeAllViews()
        for (i in 1..baseBooking.adult) {
            var pax = Passenger()
            if (paxUpdate) pax = baseBooking.pax_info.adult[i - 1]

            pax.startInput(PassengerType.ADULT, i)
            Widget.showPaxList(
                false,
                pax,
                baseBooking.is_round_trip,
                this,
                getViewBindding().content.paxInPut,
                isAllowEdit
            )
            if (!paxUpdate) baseBooking.pax_info.addPassenger(pax)

        }
        for (i in 1..baseBooking.child) {
            var pax = Passenger()
            if (paxUpdate) pax = baseBooking.pax_info.child[i - 1]
            pax.startInput(PassengerType.CHILD, i)
            Widget.showPaxList(
                false,
                pax,
                baseBooking.is_round_trip,
                this,
                getViewBindding().content.paxInPut,
                isAllowEdit
            )
            if (!paxUpdate) baseBooking.pax_info.addPassenger(pax)
        }
        for (i in 1..baseBooking.student) {
            val pax = Passenger()
            pax.startInput(PassengerType.STUDENT, i)
            Widget.showPaxList(
                false,
                pax,
                baseBooking.is_round_trip,
                this,
                getViewBindding().content.paxInPut,
                isAllowEdit
            )
            if (!paxUpdate) baseBooking.pax_info.addPassenger(pax)
        }
        for (i in 1..baseBooking.infant) {
            var pax = Passenger()
            if (paxUpdate) pax = baseBooking.pax_info.infant[i - 1]

            pax.startInput(PassengerType.INFANT, i)
            Widget.showPaxList(
                false,
                pax,
                baseBooking.is_round_trip,
                this,
                getViewBindding().content.paxInPut,
                isAllowEdit
            )

            if (!paxUpdate) baseBooking.pax_info.addPassenger(pax)
        }
        viewModel.mBooking.value = baseBooking
    }

    private fun initAnalytics(booking: Booking, event: String) {
        try {

            val params = Bundle()
            params.putString(FirebaseAnalytics.Param.ORIGIN, booking.origin_code)
            params.putString(FirebaseAnalytics.Param.DESTINATION, booking.destination_code)
            params.putString(FirebaseAnalytics.Param.START_DATE, booking.departure_date)
            params.putString(
                FirebaseAnalytics.Param.END_DATE,
                booking.return_date
            ) //  params.putString(FirebaseAnalytics.Param.FLIGHT_NUMBER, booking.departure_f?.flightNumber)
            //            params.putString(FirebaseAnalytics.Param.ITEM_NAME,
            //                booking.departure_f?.originCode + booking.departure_f?.destinationCode + booking.departure_f?.flightNumber)
            params.putString(
                FirebaseAnalytics.Param.NUMBER_OF_PASSENGERS,
                (booking.adult + booking.child + booking.infant).toString() + ""
            )
            firebaseAnalytics.logEvent(event, params)

            firebaseAnalytics.setCurrentScreen(this, "booking_input", null)

        } catch (e: Exception) {

        }
    }

    override fun onResume() {
        super.onResume()

        if (isUserSigned && getViewBindding().content.loginLayout.isVisible) {
            refreshLayout()
        }
    }

    fun mergeBooking() {
        if (baseBooking.type == Booking.BookingType.INTER || baseBooking.type == Booking.BookingType.FLIGHT) {

            booking.voucher = baseBooking.voucher
            booking.discount = baseBooking.discount
            booking.pax_info = baseBooking.pax_info
            booking.bag_fee = booking.getTotalBagFee()

        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean { // Inflate the menu; this adds items to the action bar if it is present.
        menuInflater.inflate(R.menu.main_reward, menu)
        try {
            Handler(Looper.getMainLooper()).postDelayed({
                val rewardButton = findViewById(R.id.action_reward) as View
                QBadgeView(applicationContext).setGravityOffset(0f, 0f, true)
                    .bindTarget(rewardButton).setBadgeText("!")
                    .setOnDragStateChangedListener { dragState, badge, targetView ->
                    }
            }, 1000)
        } catch (e: java.lang.Exception) {
        }
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean { // Take appropriate action for each action item click
        when (item.itemId) {
            android.R.id.home -> return true
            R.id.action_reward -> {
                val `in` = Intent(this, RewardActivity::class.java)
                getResult.launch(`in`)
                return true
            }

            else -> {
            }
        }
        return false
    }

    private val getResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
            if (it.resultCode == Activity.RESULT_OK) {
                if (it.data?.hasExtra("voucherCode") == true) {
                    getViewBindding().content.txtVoucherCode.setText(it.data?.getStringExtra("voucherCode"))
                    getViewBindding().content.btnGetVoucher.performClick()
                }
                val value = it.data?.getStringExtra("input")
            }
        }

    fun getBagFeeApi(booking: BookingV2?, isReturnTrip: Boolean): Boolean {
        try {
            if (booking == null) return false
            val params = JSONObject()
            try {
                params.put(
                    "fareBasis",
                    if (booking.departure_f == null) "" else booking.departure_f!!.fareBasis
                )
                params.put(
                    "stops",
                    if (booking.departure_f == null) "" else booking.departure_f!!.stops
                )
                params.put(
                    "flightKey",
                    if (booking.departure_f == null) "" else booking.departure_f!!.flightKey
                )
            } catch (e: JSONException) {
                AppConfigs.logException(e)
            }
            var request =
                booking.departure_f!!.provider + "/" + booking.origin_code + "/" + booking.destination_code
            if (isReturnTrip) {
                request =
                    booking.return_f!!.provider + "/" + booking.destination_code + "/" + booking.origin_code
                try {
                    params.put(
                        "fareBasis",
                        if (booking.return_f != null) booking.return_f!!.fareBasis else ""
                    )
                    params.put(
                        "stops",
                        if (booking.return_f != null) booking.return_f!!.stops else ""
                    )
                    params.put(
                        "flightKey",
                        if (booking.return_f != null) booking.return_f!!.flightKey else ""
                    )
                } catch (e: JSONException) {
                }
            }
            SSLSendRequest(this).GET(
                true,
                "AirLines/BagFee/$request",
                params,
                object : CallBackInterface {
                    override fun onSuccess(response: JSONObject, cached: Boolean) {
                        try {
                            val listBags = response.getJSONArray("data")
                            if (isReturnTrip) {
                                Common.BAGGAGE_RETURN = listBags
                            } else {
                                Common.BAGGAGE_FEE = listBags
                            }

                            if (booking.is_round_trip && !isReturnTrip) {
                                getBagFeeApi(booking, true)
                            } else {

                                showPaxInput(true, false)
                            }
                        } catch (e: JSONException) {
                            AppConfigs.logException(e)
                        }
                    }

                    override fun onFail(error: VolleyError) {
                        error.printStackTrace()
                        AppConfigs.logException(error)

                    }
                })
        } catch (e: Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }
        return true
    }

    private fun showBookingTripDetail(bookingBase: Booking) {
        getViewBindding().swipeRefreshLayout.isEnabled = false
        getViewBindding().shimmerViewContainer.visibility = View.GONE

        getViewBindding().content.tripContainer.removeAllViews()
        if (bookingBase.type == Booking.BookingType.FLIGHT) {
            val viewDeparture = ViewUtil.bindFlightDataToView(
                booking.departure_f,
                getViewBindding().content.tripContainer,
                this,
                false,
                booking.adult,
                booking.child,
                booking.infant
            )

            viewDeparture.setOnClickListener {
                getViewBindding().content.horizalScroll.fullScroll(View.FOCUS_LEFT)
            }
            if (booking.is_round_trip) {
                getViewBindding().content.tripContainerRt.removeAllViews()
                val viewReturn = ViewUtil.bindFlightDataToView(
                    booking.return_f,
                    getViewBindding().content.tripContainerRt,
                    this,
                    true,
                    booking.adult,
                    booking.child,
                    booking.infant
                )
                viewReturn.setOnClickListener {
                    getViewBindding().content.horizalScroll.fullScroll(View.FOCUS_RIGHT)
                }
            }
        }
        if (bookingBase.type == Booking.BookingType.INTER) {
            showPaxInput(true, false)
            val viewDeparture = Widget.createViewFlightInterInfo(
                this,
                booking.fareData!!.getDepartureFlight()!!,
                getViewBindding().content.tripContainer
            )

            viewDeparture?.setOnClickListener {
                getViewBindding().content.horizalScroll.fullScroll(View.FOCUS_LEFT)
            }
            viewDeparture?.findViewById<TextView>(R.id.viewFlightDetail)?.setOnClickListener {
                showBottomSheetFlightInfo(booking.fareData, false)
            }
            if (bookingBase.is_round_trip && booking.fareData!!.getReturnFlight() != null) {
                getViewBindding().content.tripContainerRt.removeAllViews()
                val viewReturn = Widget.createViewFlightInterInfo(
                    this,
                    booking.fareData!!.getReturnFlight()!!,
                    getViewBindding().content.tripContainerRt
                )

                viewReturn?.setOnClickListener {
                    getViewBindding().content.horizalScroll.fullScroll(View.FOCUS_RIGHT)


                }
                viewReturn?.findViewById<TextView>(R.id.viewFlightDetail)?.setOnClickListener {
                    showBottomSheetFlightInfo(booking.fareData, true)
                }
            }

        }
    }

    override fun refreshLayout() {
        try {
            if (isUserSigned && AppController.instance.user != null) {
                retrievePassenger(this, firebaseUser?.uid)
                getViewBindding().content.loginLayout.visibility = (View.GONE)
                getViewBindding().content.layoutPointReward.visibility = (View.VISIBLE)
                getViewBindding().content.txtContactName.setText(
                    Common.unAccent(AppController.instance.user?.userName)
                        .toUpperCase()
                )

                val phone =
                    if (firebaseUser?.phoneNumber != null && !firebaseUser?.getPhoneNumber().equals(
                            "",
                            ignoreCase = true
                        )
                    ) AppController.instance.user?.phoneNumber else SharedPrefs.getInstance()
                        .get("phone", String::class.java).toString()

                getViewBindding().content.txtContactPhone.setText(phone?.replace("+84", "0"))
                getViewBindding().content.txtContactEmail.setText(AppController.instance.user?.userEmail)
            } else {
                getViewBindding().content.loginLayout.visibility = View.VISIBLE
                getViewBindding().content.layoutPointReward.visibility = (View.GONE)
            }
        } catch (e: java.lang.Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }
    }

    fun showConfirmDialog() {
        val alertDialog = AlertDialog.Builder(this).create()
        alertDialog.setTitle("Chú ý")
        alertDialog.setIcon(R.drawable.ic_bell_alert)
        if (baseBooking.type != Booking.BookingType.INTER) {
            if (booking.departure_f?.stops!! > 0) {
                alertDialog.setMessage(Common.convertHTML("Chuyến bay của bạn chọn là chuyến bay <b>NỐI CHUYẾN</b> (có " + booking.departure_f?.stops + " điểm dừng)<br>Thời gian bay là <b>" + booking.departure_f?.duration + "</b><br>Bạn có chắc chắn muốn đặt vé ?"))
            } else if (booking.is_round_trip) {
                if (booking.return_f?.stops!! > 0) {
                    alertDialog.setMessage(Common.convertHTML("Chuyến bay lượt về của bạn là chuyến bay <b>NỐI CHUYẾN</b> (có " + booking.departure_f?.stops + " điểm dừng)<br>Thời gian bay là <b>" + booking.return_f?.duration + "</b><br>Bạn có chắc chắn muốn đặt vé ?"))
                } else {
                    alertDialog.setMessage(Common.convertHTML(Common.AppPopup))
                }
            } else {
                alertDialog.setMessage(Common.convertHTML(Common.AppPopup))
            }
        } else {
            alertDialog.setMessage(Common.convertHTML("Quý khách cần chủ động kiểm tra thông tin <b>VISA, điều kiện nhập cảnh </b>của mình trước khi đặt vé."))
        }
        alertDialog.setButton(
            DialogInterface.BUTTON_NEGATIVE,
            "Đặt vé"
        ) { dialog, which -> sendBooking() }
        alertDialog.setButton(DialogInterface.BUTTON_POSITIVE, "Kiểm tra lại") { dialog, which -> }
        alertDialog.show()
    }

    fun showSelectVoucherDialog() {
        val alertDialog = AlertDialog.Builder(this).create()
        alertDialog.setTitle("Chú ý")
        alertDialog.setIcon(R.drawable.ic_bell_alert)

        alertDialog.setMessage(Common.convertHTML("Vui lòng nhập mã <b>Giảm Giá </b>. Nếu chưa có nhấn vào lấy mã để nhận mã giảm giá!"))

        alertDialog.setButton(DialogInterface.BUTTON_NEGATIVE, "Lấy mã") { dialog, which ->
            val `in` = Intent(this, RewardActivity::class.java)
            getResult.launch(`in`)
        }
        alertDialog.setButton(
            DialogInterface.BUTTON_POSITIVE,
            "Nhập lại"
        ) { dialog, which -> getViewBindding().content.txtVoucherCode.requestFocus() }
        alertDialog.show()
    }

    override fun onStart() {
        super.onStart()
        LocalBroadcastManager.getInstance(this)
            .registerReceiver(mMessageReceiver, IntentFilter("bookingupdate"))
    }

    private val mMessageReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val message = intent.getStringExtra("message")
            val snackbar = Snackbar.make(getViewBindding().coordinatorLayout, message!!, 60000)
                .setAction("XEM CHI TIẾT") {
                    val `in` = Intent(applicationContext, PnrActivity::class.java)
                    startActivity(`in`)
                    finish()
                }
            snackbar.show()
        }
    }

    override fun onSystemSettingChange(type: AppConfigs.SystemSettingType) {

        if (type == AppConfigs.SystemSettingType.INTERNET) {
            if (this::viewModel.isInitialized) {
                viewModel.updateInternetStatus(isInternetConnected)
            }
        }

        if (type == AppConfigs.SystemSettingType.USER) {
            if (this::viewModel.isInitialized) {
                viewModel.updateUserStatus(isUserSigned)
                getViewBindding().viewModel = viewModel
                if (isUserSigned && AppController.instance.user != null) {
                    getViewBindding().content.txtContactName.setText(AppController.instance.user?.userName)
                    getViewBindding().content.txtContactEmail.setText(AppController.instance.user?.userEmail)
                    getViewBindding().content.txtContactPhone.setText(AppController.instance.user?.phoneNumber)
                }
            }
        }
    }

    private fun setUpFlight() {
        sheetDialog = BottomSheetDialog(this, R.style.SheetDialogTransparent)
        sheetViewLayout = layoutInflater.inflate(R.layout.select_flight_inter_layout, null)
        sheetDialog!!.setContentView(sheetViewLayout!!)
    }

    private fun showBottomSheetFlightInfo(fare: FareData?, isReturnTrip: Boolean) {
        try {

            Widget.createFareDetailInfo(
                this,
                fare!!,
                sheetViewLayout!!.findViewById(R.id.flightInfo)
            )
            sheetDialog!!.behavior.state = BottomSheetBehavior.STATE_EXPANDED
            sheetDialog!!.behavior.isHideable = true
            sheetDialog!!.behavior.isDraggable = true
            sheetDialog!!.show()

            sheetViewLayout!!.findViewById<TextView>(R.id.txtGrandTotalPrice).text =
                Common.dinhDangTien(fare.totalPrice!!)

            if (isReturnTrip) {
                sheetViewLayout!!.findViewById<View>(R.id.departure_container).visibility =
                    View.GONE
            } else {
                sheetViewLayout!!.findViewById<View>(R.id.return_container).visibility = View.GONE
            }
            sheetViewLayout?.findViewById<View>(R.id.quickViewLayout)?.visibility = View.GONE
            sheetViewLayout?.findViewById<View>(R.id.close_view)?.visibility = View.VISIBLE

            sheetViewLayout?.findViewById<Button>(R.id.btnBackFlight)?.setOnClickListener {
                if (sheetDialog != null && sheetDialog!!.isShowing) sheetDialog?.hide()
            }


        } catch (e: java.lang.Exception) {
            e.printStackTrace()
            if (sheetDialog!!.isShowing) sheetDialog!!.dismiss()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {

        if (resultCode == RESULT_OK && requestCode == REQUEST_CODE_ADDON_SELECT) {
            if (data != null && data.hasExtra("bookingDetail")) {
                val pax_info = data.getSerializableExtra("bookingDetail") as PaxInfoList
                addAddOnInfo(pax_info)
                showPaxInput(true, true)
            }
        }
        super.onActivityResult(requestCode, resultCode, data)
    }

    private fun addAddOnInfo(pax_info: PaxInfoList) {
        try {
            var haveAddOnMeal = false
            var haveAddOnSeat = false
            var rSeat = 0
            var sAddOn = 0
            var sSeat = 0
            var rAddOn = 0


            for (i in 0 until pax_info.adult.size) {
                baseBooking.pax_info.adult[i].addOn = pax_info.adult[i].addOn
                baseBooking.pax_info.adult[i].addOnReturn = pax_info.adult[i].addOnReturn

                sAddOn += baseBooking.pax_info.adult[i].addOn.filter { it.type == AddOnType.MEAL }.size + baseBooking.pax_info.adult[i].addOnReturn.filter { it.type == AddOnType.MEAL }.size
                if ((sAddOn) > 0) {
                    haveAddOnMeal = true
                }
                rSeat += baseBooking.pax_info.adult[i].addOn.filter { it.type == AddOnType.SEAT }.size + baseBooking.pax_info.adult[i].addOnReturn.filter { it.type == AddOnType.SEAT }.size
                if ((rSeat) > 0) {
                    haveAddOnSeat = true
                }
            }
            for (i in 0 until pax_info.child.size) {
                baseBooking.pax_info.child[i].addOn = pax_info.child[i].addOn
                baseBooking.pax_info.child[i].addOnReturn = pax_info.child[i].addOnReturn

                rAddOn += baseBooking.pax_info.child[i].addOn.filter { it.type == AddOnType.MEAL }.size + baseBooking.pax_info.child[i].addOnReturn.filter { it.type == AddOnType.MEAL }.size
                if ((rAddOn) > 0) {
                    haveAddOnMeal = true
                }

                rSeat += baseBooking.pax_info.child[i].addOn.filter { it.type == AddOnType.SEAT }.size + baseBooking.pax_info.child[i].addOnReturn.filter { it.type == AddOnType.SEAT }.size
                if ((rSeat) > 0) {
                    haveAddOnSeat = true
                }
            }

            if (haveAddOnSeat) {
                val totalS = sSeat + rSeat
                getViewBindding().content.txtSeatSelect.text = "Đã chọn " + (totalS) + " ghế"
                getViewBindding().content.txtSeatSelectDetail.text =
                    resources.getString(R.string.txt_seat_select_detail_done)
            } else {
                getViewBindding().content.txtSeatSelect.text =
                    resources.getString(R.string.txt_seat_select)
                getViewBindding().content.txtSeatSelectDetail.text =
                    resources.getString(R.string.txt_seat_select_detail)
            }
            if (haveAddOnMeal) {
                val totalA = sAddOn + rAddOn
                getViewBindding().content.txtAddonSelect.text = "Đã chọn " + totalA + " món"
                getViewBindding().content.txtAddonSelectDetail.text =
                    resources.getString(R.string.txt_seat_select_detail_done)
            } else {
                getViewBindding().content.txtAddonSelect.text =
                    resources.getString(R.string.txt_addon_select)
                getViewBindding().content.txtAddonSelectDetail.text =
                    resources.getString(R.string.txt_addon_select_done)
            }
        } catch (e: Exception) {
            AppConfigs.logException(e)
            e.printStackTrace()
        }

    }

}
