package com.hqt.view.ui.payment.ui.main

import android.app.Application
import androidx.annotation.NonNull
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.hqt.data.model.BookingTrain
import com.hqt.data.model.OrderInfo
import com.hqt.viewmodel.BaseViewModel

class MainViewModel(@NonNull application: Application?) : BaseViewModel(application!!) {
    // TODO: Implement the ViewModel
    var orderInfo = MutableLiveData<OrderInfo?>()
    val dpPhoneNumber = ObservableField("")
    val avatarUrl = ObservableField("")
}