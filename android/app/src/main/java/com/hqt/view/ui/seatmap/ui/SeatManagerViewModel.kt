package com.hqt.view.ui.seatmap.ui

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.hqt.data.model.AddOnInfo
import com.hqt.data.model.AddOnType
import com.hqt.data.model.PaxInfoList
import com.hqt.util.AppConfigs
import com.hqt.util.Log
import com.hqt.view.ui.booking.data.model.BookingV3
import com.hqt.view.ui.booking.data.model.PaxInfoListV2
import com.hqt.view.ui.meal.data.api.MealApiHelper

import com.hqt.view.ui.seatmap.ui.view.SeatViewV2
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject


@HiltViewModel
class SeatManagerViewModel @Inject constructor(
    private val seatApiHelper: MealApiHelper
) : ViewModel() {



    var seatViewListReturn: ArrayList<SeatViewV2> = ArrayList()
    var seatViewList: ArrayList<SeatViewV2> = ArrayList()
    var totalPax = 1

    var paxSeatString = ""
    var paxSeatReturnString = ""

    var booking: BookingV3 = BookingV3()
    var doneClick  = MutableLiveData(false)





    private fun addSeatToPax(paxInfo: PaxInfoListV2, seatList: ArrayList<SeatViewV2>, isReturn: Boolean): PaxInfoListV2 {

        var pi = 0
        var i = 0
        paxInfo.adult.forEach { passenger ->

            if (pi == i && i < seatList.size) {
                val currentSeat = seatList[i]
                val seatInfo = AddOnInfo()
                seatInfo.price = currentSeat.seatOption?.seatCharges ?: 0
                seatInfo.text = currentSeat.seatOption?.rowIdentifier + currentSeat.seatOption?.seatIdentifier
                seatInfo.value = currentSeat.seatOption?.selectionKey ?: ""

                val addOnList: ArrayList<AddOnInfo> = ArrayList()
                addOnList.add(seatInfo)
                passenger.updateAddOn(AddOnType.SEAT, addOnList, isReturn)


                i++
            } else if (pi >= seatList.size) {
                passenger.updateAddOn(AddOnType.SEAT, ArrayList(), isReturn)
            }
            pi++
        }
        paxInfo.child.forEach { passenger ->

            if (pi == i && i < seatList.size) {
                val currentSeat = seatList[i]
                val seatInfo = AddOnInfo()
                seatInfo.price = currentSeat.seatOption?.seatCharges ?: 0
                seatInfo.text = currentSeat.seatOption?.rowIdentifier + currentSeat.seatOption?.seatIdentifier
                seatInfo.value = currentSeat.seatOption?.selectionKey ?: ""

                val addOnList: ArrayList<AddOnInfo> = ArrayList()
                addOnList.add(seatInfo)
                passenger.updateAddOn(AddOnType.SEAT, addOnList, isReturn)


                i++
            } else if (pi >= seatList.size) {
                passenger.updateAddOn(AddOnType.SEAT, ArrayList(), isReturn)
            }
            pi++
        }
        return paxInfo

    }

    fun updateSeatSelected(list: ArrayList<SeatViewV2>, isReturn: Boolean) {
        try {
            if (isReturn) {
                seatViewListReturn = list
                booking.pax_info = addSeatToPax(booking.pax_info, seatViewListReturn, true)
            } else {
                seatViewList = list
                booking.pax_info = addSeatToPax(booking.pax_info, seatViewList, false)

            }
        } catch (e: Exception) {
            e.printStackTrace()
            AppConfigs.logException(e)
        }
    }


    fun getPaxSeatStr(isReturn: Boolean): String {

//        AppConfigs.Log("getPaxSeatStr", paxSeatString)

        Log.d("getPaxSeatStr", paxSeatString + paxSeatReturnString)
        if (isReturn)
            return paxSeatReturnString
        return paxSeatString

    }




}