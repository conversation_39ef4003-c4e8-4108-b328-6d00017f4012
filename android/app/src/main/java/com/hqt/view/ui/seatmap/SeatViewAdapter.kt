//package com.hqt.view.ui.seatmap
//
//import android.content.Context
//import android.view.View
//import android.view.ViewGroup
//import androidx.recyclerview.widget.RecyclerView
//
//
//class SeatViewAdapter(var mContext: Context, internal var contents: List<SeatView>) :
//    RecyclerView.Adapter<SeatViewAdapter.ViewHolder>() {
//    class ViewHolder(var context: Context, var seatView: SeatView, itemView: View) : RecyclerView.ViewHolder(itemView) {
//
//    }
//
//    override fun getItemCount(): Int {
//        return contents.size
//    }
//
//    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
//
//        val seatView = SeatView(parent.context, it, seatRowIndex.toString(), percent, isReturnTrip)
//
//        return ViewHolder(mContext, seatView)
//    }
//
//
//    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
//
//        holder.seatView = contents[position]
//    }
//
//    override fun getItemId(position: Int): Long {
//        return position.toLong()
//    }
//
//    override fun getItemViewType(position: Int): Int {
//        return position
//    }
//
//
//}