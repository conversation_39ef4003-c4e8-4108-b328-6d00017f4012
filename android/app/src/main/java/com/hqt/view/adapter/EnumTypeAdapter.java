package com.hqt.view.adapter;

import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;
import com.hqt.data.model.PassengerTitle;
import com.hqt.data.model.PassengerType;

import org.json.JSONObject;

import java.lang.reflect.Type;

public class EnumTypeAdapter implements JsonDeserializer<Enum>, JsonSerializer<Enum> {

    public static final EnumTypeAdapter sInstance = new EnumTypeAdapter();

    @Override
    public Enum deserialize(JsonElement json, Type type, JsonDeserializationContext context)
            throws JsonParseException {
        try {
            if (type instanceof Class && ((Class<?>) type).isEnum()) {
                if (type == PassengerType.class) {
                    JSONObject item = new JSONObject(json.toString());
                    return PassengerType.valueOf(PassengerType.class, item.getString("value").toUpperCase());
                }
                if (type == PassengerTitle.class) {
                    JSONObject item = new JSONObject(json.toString());
                    return PassengerType.valueOf(PassengerTitle.class, item.getString("value").toUpperCase());
                }
                return Enum.valueOf((Class<Enum>) type, json.getAsString().toUpperCase());
            }

            return null;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public JsonElement serialize(Enum src, Type typeOfSrc, JsonSerializationContext context) {
        if (src == null) {
            return null;
        }
        JsonObject jsonObject = new JsonObject();
        if (typeOfSrc instanceof Class && ((Class<?>) typeOfSrc).isEnum()) {
            if (typeOfSrc == PassengerType.class) {
                jsonObject.addProperty("value", src.toString());
                jsonObject.addProperty("text", ((PassengerType) src).getText());

            } else if (typeOfSrc == PassengerTitle.class) {
                jsonObject.addProperty("value", src.toString());
                jsonObject.addProperty("text", ((PassengerTitle) src).getText());

            } else {
                return context.serialize(src.toString());
            }
        }
        return jsonObject;
    }
}