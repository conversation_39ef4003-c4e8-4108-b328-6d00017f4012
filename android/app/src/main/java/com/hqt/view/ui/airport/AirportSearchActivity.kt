package com.hqt.view.ui.airport

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.android.volley.VolleyError
import com.hqt.view.ui.search.data.model.AirportGroup
import com.hqt.datvemaybay.R
import com.hqt.datvemaybay.databinding.ActivityAirportSearchBinding
import com.hqt.util.AppConfigs
import com.hqt.util.AppController
import com.hqt.util.SSLSendRequest
import com.hqt.util.SSLSendRequest.CallBackInterface
import com.hqt.util.base.BaseActivity
import com.hqt.view.ui.search.ui.adapter.airport.AirportViewAdapter
import dagger.hilt.android.AndroidEntryPoint
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import org.json.JSONException
import org.json.JSONObject


@AndroidEntryPoint
class AirportSearchActivity : BaseActivity<ActivityAirportSearchBinding>() {


    private var disposable: Disposable? = null
    private lateinit var mAdapter: AirportViewAdapter
    private var airportList: ArrayList<AirportGroup> = ArrayList()
    override fun getLayoutRes(): Int {
        return R.layout.activity_airport_search
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        getToolbar()?.title = "Tìm sân bay"
        getToolbar()?.setNavigationIcon(R.drawable.ic_action_back_home)


        mAdapter = AirportViewAdapter(airportList)
        binding.myRecyclerView.adapter = mAdapter
        val layoutManager: RecyclerView.LayoutManager = LinearLayoutManager(this)
        binding.myRecyclerView.layoutManager = layoutManager
        binding.myRecyclerView.setHasFixedSize(true)

        binding.editSearch.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                val query = binding.editSearch.text.toString()
                if (query.length >= 2) {
                    getSearch(query)
                }
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        })

        getSearch("")

        //binding.durationView.drawDuration(arrayListOf(100, 50, 50, 10))
        getAirportInfo("")

    }




    private fun getSearch(query: String) {
        disposable =
            AppController.instance.getService().getAirport(query).subscribeOn(Schedulers.io())
                .doOnSubscribe {
                    binding.myRecyclerView.visibility = View.GONE
                    binding.shimmerViewContainer.visibility = View.VISIBLE
                    binding.shimmerViewContainer.startShimmer()
                }.observeOn(AndroidSchedulers.mainThread()).subscribe({ response ->
                    AppController.instance

                    AppConfigs.Log("getAirport", response.data!!.size.toString())

                    airportList.clear()
                    airportList.addAll(response.data)
                    mAdapter.notifyDataSetChanged()

                    binding.shimmerViewContainer.stopShimmer()
                    binding.shimmerViewContainer.visibility = View.GONE
                    binding.myRecyclerView.visibility = View.VISIBLE

                }, { throwable ->
                    throwable.printStackTrace()
                })
    }

    fun getAirportInfo(dep: String?) {
        try {


            SSLSendRequest(this).GET(
                true,
                "AirLines/AirPort",
                null,
                object : CallBackInterface {
                    override fun onSuccess(response: JSONObject, cached: Boolean) {
                        try {
                            val settings = getSharedPreferences("12BAY-APP-CONFIG", 0)
                            val editor = settings.edit()
                            editor.putString("AIRPORTLIST", response.toString())
                            editor.apply()
                        } catch (e: JSONException) {
                            AppConfigs.logException(e)
                        }
                    }

                    override fun onFail(error: VolleyError) {
                        error.printStackTrace()
                        AppConfigs.logException(error)

                    }
                })


            // Adding request to request queue
        } catch (e: Exception) {
            AppConfigs.logException(e)
        }
    }


}