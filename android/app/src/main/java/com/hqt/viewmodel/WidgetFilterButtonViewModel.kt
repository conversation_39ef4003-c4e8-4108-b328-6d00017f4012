package com.hqt.viewmodel

import android.app.Application
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.view.View
import android.view.ViewGroup
import android.view.ViewParent
import android.widget.TextView
import androidx.annotation.NonNull
import androidx.lifecycle.MutableLiveData
import com.mikepenz.iconics.typeface.library.fontawesome.FontAwesome
import com.mikepenz.iconics.IconicsDrawable
import com.mikepenz.iconics.utils.colorInt
import com.mikepenz.iconics.utils.colorString
import com.mikepenz.iconics.utils.paddingDp
import com.mikepenz.iconics.utils.sizeDp
import java.lang.Exception


class WidgetFilterButtonViewModel(@NonNull application: Application) : BaseViewModel(application) {
    var app = application
    var onSort = MutableLiveData<Boolean>()
    var sortKey: String = ""
    var btnOne: SortButton? = null
    var btnTwo: SortButton? = null
    var btnThree: SortButton? = null
    var buttonClick: SortButton? = null
    var textDefaultColor = MutableLiveData<Int>()
    private fun setDefaultColor(parentView: ViewParent) {
        try {
            if (parentView is ViewGroup) {
                for (i in 0 until parentView.childCount) {
                    var v = parentView.getChildAt(i)
                    val btn = (v as TextView)
                    btn.setTextColor(Color.parseColor("#808080"))
                    var dfIcon = IconicsDrawable(app, FontAwesome.Icon.faw_grip_vertical).apply {
                        colorString = "#808080"
                        sizeDp = (16)
                        paddingDp = (2)
                    }
                    btn.setCompoundDrawablesWithIntrinsicBounds(null, null, dfIcon, null)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }

    fun onSortClick(view: View, button: SortButton) {
        setDefaultColor(view.parent)
        val btn = (view as TextView)
        buttonClick = button
        textDefaultColor.postValue(Color.parseColor("#808080"))

        if (!button.onAlt) {
            button.onAlt = true
            if (button.btnAltIcon != null) btn.setCompoundDrawablesWithIntrinsicBounds(null, null, button.btnAltIcon, null)
            btn.text = button.btnAltText
            sortKey = button.sortTypeAlt
            onSort.postValue(true)
            btn.setTextColor(Color.parseColor("#0093ce"))

        } else {
            onSort.postValue(false)
            button.onAlt = false
            btn.text = button.btnText
            sortKey = button.sortType
            btn.setTextColor(Color.parseColor("#0093ce"))
            if (button.btnIcon != null) btn.setCompoundDrawablesWithIntrinsicBounds(null, null, button.btnIcon, null)
        }
    }

    fun initOne() {
        onSort.value = false
        btnOne = SortButton("departureTime", "departureTimeDesc", "Khởi hành", "Khởi hành", IconicsDrawable(app, FontAwesome.Icon.faw_sort_amount_up).apply {
            colorString = "#0093ce"
            sizeDp = 16
            paddingDp = 2
        },

            IconicsDrawable(app, FontAwesome.Icon.faw_sort_amount_down).apply {
                colorString = "#0093ce"
                sizeDp = (16)
                paddingDp = (2)
            })

        btnTwo = SortButton("duration", "durationDesc", "Thời gian đi", "Thời gian đi", IconicsDrawable(app, FontAwesome.Icon.faw_sort_amount_up).apply {
            colorString = "#0093ce"
            sizeDp = (16)
            paddingDp = (2)
        }, IconicsDrawable(app, FontAwesome.Icon.faw_sort_amount_down).apply {
            colorString = "#0093ce"
            sizeDp = (16)
            paddingDp = (2)
        })

        btnThree = SortButton("availableTrain", "allTrain", "Tất cả", "Còn chỗ", IconicsDrawable(app, FontAwesome.Icon.faw_list).apply {
            colorString = "#0093ce"
            sizeDp = (16)
            paddingDp = (2)
        }, IconicsDrawable(app, FontAwesome.Icon.faw_filter).apply {
            colorString = "#0093ce"
            sizeDp = (16)
            paddingDp = (2)
        })

        buttonClick = btnOne
        textDefaultColor.value = Color.parseColor("#808080")
    }

    fun initTow() {
        onSort.value = false
        btnOne = SortButton("seatClass", "seatClassDesc", "Loại chỗ", "Loại chỗ", IconicsDrawable(app, FontAwesome.Icon.faw_sort_amount_up).apply {
            colorString = "#0093ce"
            sizeDp = (16)
            paddingDp = (2)
        }, IconicsDrawable(app, FontAwesome.Icon.faw_sort_amount_down).apply {
            colorString = "#0093ce"
            sizeDp = (16)
            paddingDp = (2)
        })
        btnTwo = SortButton("fare", "fareDesc", "Giá vé", "Giá vé", IconicsDrawable(app, FontAwesome.Icon.faw_sort_amount_up).apply {
            colorString = "#0093ce"
            sizeDp = (16)
            paddingDp = (2)
        }, IconicsDrawable(app, FontAwesome.Icon.faw_sort_amount_down).apply {
            colorString = "#0093ce"
            sizeDp = (16)
            paddingDp = (2)
        })
        btnThree = SortButton("availableTrain", "allTrain", "Tất cả", "Còn chỗ", IconicsDrawable(app, FontAwesome.Icon.faw_list).apply {
            colorString = "#0093ce"
            sizeDp = (16)
            paddingDp = (2)
        }, IconicsDrawable(app, FontAwesome.Icon.faw_filter).apply {
            colorString = "#0093ce"
            sizeDp = (16)
            paddingDp = (2)
        })
        buttonClick = btnOne
        textDefaultColor.value = Color.parseColor("#808080")
    }


}

class SortButton(_sortType: String, _sortTypeAlt: String, _btnText: String, _btnAltText: String, _btnIcon: IconicsDrawable?, _btnAltIcon: IconicsDrawable?) {
    var btnText: String = _btnText
    var btnAltText: String = _btnAltText
    var sortType: String = _sortType
    var sortTypeAlt: String = _sortTypeAlt
    var onAlt: Boolean = false
    var btnIcon: Drawable? = _btnIcon
    var btnAltIcon: Drawable? = _btnAltIcon
}