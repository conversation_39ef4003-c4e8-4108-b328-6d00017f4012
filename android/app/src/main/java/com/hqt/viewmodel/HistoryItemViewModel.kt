package com.hqt.viewmodel

import android.content.Context
import android.graphics.drawable.Drawable
import androidx.core.content.ContextCompat
import com.google.gson.reflect.TypeToken
import com.hqt.data.model.*
import com.hqt.datvemaybay.R
import com.hqt.util.AppConfigs
import com.hqt.util.AppController
import java.util.*

class HistoryItemViewModel(var mContext: Context, var booking: BookingJson) {
    var type: Booking.BookingType = booking.type
    var isFlightDetail: Boolean = true
    var flight: BookingV2 = BookingV2()
    var train: BookingTrain = BookingTrain()
    var bus: BookingBus = BookingBus()
    fun getListPaxTitle(): String {
        return when (type) {
            Booking.BookingType.FLIGHT -> ("" + booking.adult + " " + mContext.getString(R.string.fa_male) + " " + booking.child + " " + mContext.getString(
                R.string.fa_child) + " " + booking.infant + " " + mContext.getString(R.string.fa_female))
            Booking.BookingType.INTER -> ("" + booking.adult + " " + mContext.getString(R.string.fa_male) + " " + booking.child + " " + mContext.getString(
                R.string.fa_child) + " " + booking.infant + " " + mContext.getString(R.string.fa_female))
            Booking.BookingType.TRAIN -> ("" + booking.adult + " " + mContext.getString(R.string.fa_male) + " " + booking.child + " " + mContext.getString(
                R.string.fa_child) + " " + booking.student + " " + mContext.getString(R.string.fa_user_graduate) + " " + booking.older + " " + mContext.getString(
                R.string.fa_blind))
            else -> ("" + booking.adult + " " + mContext.getString(R.string.fa_male))
        }

    }

    fun getAirLogo(provider: String): Drawable? {
        if (provider.isNotEmpty() && booking.type == Booking.BookingType.FLIGHT) {
            try {
                ContextCompat.getDrawable(mContext,
                    mContext.resources.getIdentifier("logo_" + provider.toLowerCase(Locale.ROOT),
                        "drawable",
                        mContext.packageName))
            } catch (e: Exception) {

                AppConfigs.Log("provider", provider)
                e.printStackTrace()
                ContextCompat.getDrawable(mContext, R.drawable.ic_logo_no_free)
            }
        }
        if (booking.type == Booking.BookingType.TRAIN) {
            return ContextCompat.getDrawable(mContext,
                mContext.resources.getIdentifier("logo_vr", "drawable", mContext.packageName))

        }
        return ContextCompat.getDrawable(mContext, R.drawable.transparent)
    }

    fun getReturnLogo(): Drawable? {
        if (booking.is_round_trip && booking.type == Booking.BookingType.FLIGHT) {
            if (isFlightDetail && flight.departure_f!!.provider != flight.return_f!!.provider) {
                return this.getAirLogo(flight.return_f!!.provider ?: "")
            }
        }
        return ContextCompat.getDrawable(mContext, R.drawable.transparent)
    }

    fun getGrandTotal(): Int {
        if (booking.type == Booking.BookingType.FLIGHT || booking.type == Booking.BookingType.INTER) {
            return flight.total + flight.bag_fee - flight.discount + flight.addon_fee
        } else if (booking.type == Booking.BookingType.TRAIN) {
            return train.getGrandTotal() - train.discount
        } else {
            return bus.getGrandTotal() - bus.discount
        }
    }

    fun getAirportLogo(): String {
        return AppConfigs.getInstance().config.getString("root_api") + "/api/v1/AirLines/Image/" + booking.destination_code + "?size=s"
    }

    init {
        if (booking.type == Booking.BookingType.TRAIN) {
            isFlightDetail = false
            train = AppController.instance.gSon.fromJson<BookingTrain>(AppController.instance.gSon.toJson(booking),
                BookingTrain::class.java)
        } else if (booking.type == Booking.BookingType.BUS) {
            isFlightDetail = false
            bus = AppController.instance.gSon.fromJson<BookingBus>(AppController.instance.gSon.toJson(booking),
                BookingBus::class.java)
        } else {
            flight = AppController.instance.gSon.fromJson<BookingV2>(AppController.instance.gSon.toJson(booking),
                BookingV2::class.java)
        }
    }

}