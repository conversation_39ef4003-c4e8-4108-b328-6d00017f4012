package com.hqt.viewmodel

import android.app.Application
import android.graphics.Color
import androidx.annotation.NonNull
import androidx.lifecycle.MutableLiveData
import com.hqt.view.ui.flighthistory.data.model.FlightHistory
import kotlin.collections.ArrayList

class FlightHistoryViewModel(@NonNull application: Application?) : BaseViewModel(application!!) {

    var flightHistory = MutableLiveData<FlightHistory>()
    fun getDays(): ArrayList<String> {
        var days: ArrayList<String> = ArrayList()

        days.add("14 ngày")
        days.add("30 ngày")
        days.add("60 ngày")
        days.add("90 ngày")
        return days
    }

    fun getSelectedDay(): String {
        return "14 ngày"
    }

    
    fun getScoreColor(score: Float): Int {

        if (score > 0.8) {
            return Color.parseColor("#4CAF50")
        } else if (score > 0.7) {
            return Color.parseColor("#FB953B")
        } else if (score > 0.5) {
            return Color.parseColor("#d62d20")
        } else {
            return Color.parseColor("#03a1e4")
        }

    }

    init {
        flightHistory.postValue(FlightHistory())
    }

}