package com.hqt.viewmodel

import android.app.Application
import android.graphics.Color
import androidx.annotation.NonNull
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.hqt.datvemaybay.Common
import com.hqt.data.model.BookingTrain
import com.hqt.util.AppConfigs

open class TrainBookingContentViewModel(@NonNull application: Application) :
    BaseViewModel(application!!) {
    var booking = MutableLiveData<BookingTrain>()
    var isLoading = MutableLiveData<Boolean>()

    fun isHaveVoucher(): Boolean {
        return booking.value!!.discount > 0
    }

    fun discountText(): String {
        return if (booking.value!!.discount > 0)
            Common.dinhDangTien(booking.value!!.discount)
        else {
            ""
        }
    }

    fun isCreatedBooking(): Boolean {
        if (booking.value!!.status.isEmpty() || booking.value!!.status == "created") {
            return true
        }
        return false
    }

    fun getInputPaxTitle(): String {
        if (isShowBookingView()) {
            return "Thông tin hành khách"
        }
        return "Nhập thông tin hành khách"
    }

    fun getInputContactTitle(): String {
        if (isShowBookingView()) {
            return "Thông tin đơn hàng"
        }
        return "Nhập thông tin liên hệ"
    }

    fun isShowBookingView(): Boolean {
        if ((booking.value!!.status.isNotEmpty() || booking.value!!.id.isNotEmpty())) {
            return true
        }
        return false
    }

    fun isShowPayment(): Boolean {
        if (booking.value!!.status.isEmpty()) {
            return false
        }
        if (booking.value!!.status.contentEquals("done")) {
            return false
        }
        return true
    }

    fun getTimeLimitText(): String {
        if (booking.value!!.payment.status) {
            var timeLimit = Common.getDateTimeFromFormat(booking.value!!.expired_date)
            return Common.dateToString(timeLimit, "HH:mm dd/MM/yyyy")
        }
        return "Hết hạn thanh toán"

    }

    fun formatTotal(value: Int): String {
        return Common.dinhDangTien(value)
    }

    fun getBookButtonText(): String {
        if (booking.value!!.payment.status) {
            return "THANH TOÁN NGAY"
        }
        if (booking.value!!.status.isNotEmpty()) {
            return "LIÊN LẠC HỖ TRỢ"
        }

        return "Đặt vé"
    }

    fun getStatusBackgoundColor(): Int {
        if (booking.value!!.status == "created") {
            return Color.parseColor("#FB953B")
        } else if (booking.value!!.status == "fail" || booking.value!!.status == "expired") {
            return Color.parseColor("#B1B1B1")
        } else if (booking.value!!.status == "done") {
            return Color.parseColor("#4CAF50")
        } else if (booking.value!!.status == "waiting_payment") {
            return Color.parseColor("#ff0088cd")
        }
        return Color.parseColor("#FB953B")
    }

    fun isShowLogin(): Boolean {
        if (isUserSigned.value!!) {
            return false
        }
        return true
    }

    fun onChange() {
        booking.postValue(booking.value)// force postValue to notify Observers
        // can also use user.postValue()
    }

    fun getBooking(): LiveData<BookingTrain> {
        return booking
    }

    init {
        booking.value = BookingTrain()
    }
}