package com.hqt.viewmodel

import android.app.Application
import android.graphics.Color
import androidx.annotation.NonNull
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.hqt.data.model.BookingBus
import com.hqt.datvemaybay.Common
import com.hqt.data.model.BookingTrain
import com.hqt.data.model.User
import com.hqt.util.AppConfigs
import com.hqt.util.AppController

class BookingBusViewModel(@NonNull application: Application?) : BaseViewModel(application!!) {
    var mBooking = MutableLiveData<BookingBus>()
    var isLoading = MutableLiveData<Boolean>()

    fun isHaveVoucher(): Boolean {
        return mBooking.value!!.discount > 0
    }

    fun discountText(): String {
        return if (mBooking.value!!.discount > 0) Common.dinhDangTien(mBooking.value!!.discount)
        else {
            ""
        }
    }

    fun isCreatedBooking(): Boolean {
        if (mBooking.value!!.status.isEmpty() || mBooking.value!!.status == "created") {
            return true
        }
        return false
    }

    fun getInputPaxTitle(): String {
        if (isShowBookingView()) {
            return "Thông tin hành khách"
        }
        return "Nhập thông tin hành khách"
    }

    fun getInputContactTitle(): String {
        if (isShowBookingView()) {
            return "Thông tin đơn hàng"
        }
        return "Nhập thông tin liên hệ"
    }

    fun isShowBookingView(): Boolean {
        if ((mBooking.value!!.status.isNotEmpty() || mBooking.value!!.id.isNotEmpty())) {
            return true
        }
        return false
    }

    fun isShowPayment(): Boolean {
        if (mBooking.value!!.status.isEmpty()) {
            return false
        }
        if (mBooking.value!!.status.contentEquals("done")) {
            return false
        }
        return true
    }

    fun getTimeLimitText(): String {
        if (mBooking.value!!.payment.status) {
            var timeLimit = Common.getDateTimeFromFormat(mBooking.value!!.expired_date)
            return Common.dateToString(timeLimit, "HH:mm dd/MM/yyyy")
        }
        return "Hết hạn thanh toán"

    }

    fun formatTotal(value: Int): String {
        return Common.dinhDangTien(value)
    }

    fun getBookButtonText(): String {
        if (mBooking.value!!.payment.status) {
            return "THANH TOÁN NGAY"
        }
        if (mBooking.value!!.status.isNotEmpty()) {
            return "LIÊN LẠC HỖ TRỢ"
        }

        return "Đặt vé"
    }

    fun getStatusBackgoundColor(): Int {
        if (mBooking.value!!.status == "created") {
            return Color.parseColor("#FB953B")
        } else if (mBooking.value!!.status == "fail" || mBooking.value!!.status == "expired") {
            return Color.parseColor("#B1B1B1")
        } else if (mBooking.value!!.status == "done") {
            return Color.parseColor("#4CAF50")
        } else if (mBooking.value!!.status == "waiting_payment") {
            return Color.parseColor("#FB953B")
        } else if (mBooking.value!!.status == "payment_success") {
            return Color.parseColor("#FB953B")
        }
        return Color.parseColor("#FB953B")
    }

    fun isShowLogin(): Boolean {
        if (isUserSigned.value!!) {
            return false
        }
        return true
    }

    fun getUser(): User? {
        if (isUserSigned.value!! && AppController.instance.user != null) {
            return AppController.instance.user
        }
        return null

    }

    fun onChange() {
        mBooking.postValue(mBooking.value) // force postValue to notify Observers
        // can also use user.postValue()
    }

    fun getBooking(): LiveData<BookingBus> {
        return mBooking
    }

    init {
        mBooking.value = BookingBus()
    }
}