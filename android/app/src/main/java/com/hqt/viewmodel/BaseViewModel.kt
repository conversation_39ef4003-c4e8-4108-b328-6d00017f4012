package com.hqt.viewmodel

import android.app.Application
import androidx.annotation.NonNull
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData

open class BaseViewModel(application: Application) : AndroidViewModel(application) {
    val isUserSigned = MutableLiveData<Boolean>()
    val isInternetConneted = MutableLiveData<Boolean>()

    fun updateUserStatus(isLogin: <PERSON>olean) {
        isUserSigned.value = isLogin
    }

    fun updateInternetStatus(isConnected: Boolean) {
        isInternetConneted.value = isConnected
    }

    init {
        isUserSigned.value = false
        isInternetConneted.value = true
    }
}