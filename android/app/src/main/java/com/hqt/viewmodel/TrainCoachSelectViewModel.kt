package com.hqt.viewmodel

import android.app.Application
import android.graphics.Color
import androidx.annotation.NonNull
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.hqt.datvemaybay.Common
import com.hqt.data.model.BookingTrain
import com.hqt.data.model.TrainCoach

class TrainCoachSelectViewModel(@NonNull application: Application?) : BaseViewModel(application!!) {
    var onSort = MutableLiveData<Boolean>()
    var mCoach = MutableLiveData<TrainCoach>()

    fun getCoachTitle(): String {
        return mCoach.value?.getCoachOrder() + mCoach.value?.detail
    }

    fun onChange() {
        mCoach.postValue(mCoach.value) // force postValue to notify Observers
        // can also use user.postValue()
    }

    init {
        mCoach.value = TrainCoach()
    }
}