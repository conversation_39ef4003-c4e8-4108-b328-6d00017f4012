package com.hqt.viewmodel

import android.content.Context
import android.view.View
import androidx.databinding.BaseObservable
import com.hqt.data.model.Passenger
import com.hqt.data.model.PassengerTitle
import com.hqt.data.model.PassengerType
import com.hqt.datvemaybay.R
import com.hqt.util.AppConfigs
import com.hqt.util.Helper
import com.tiper.MaterialSpinner
import org.json.JSONArray
import java.util.EnumSet

class TypePaxViewModel(var pax: Passenger, var isTrain: Boolean) : BaseObservable() {
    fun getPaxTypeList(): ArrayList<String> {

        val types: ArrayList<String> = ArrayList()
        EnumSet.allOf(PassengerType::class.java).forEach { type -> types.add(type.text) }
        return types
    }

    fun getIsTrainBooking(): Boolean {
        return isTrain
    }

    fun getIsShowInput(validator: Helper.ValidatorType): Boolean {

        when (validator) {
            Helper.ValidatorType.FIRSTNAME -> return true
            Helper.ValidatorType.LASTNAME -> return true
            Helper.ValidatorType.FULLNAME -> return true
            Helper.ValidatorType.BIRTDATE -> return true
            Helper.ValidatorType.IDNUMBER -> return true
            Helper.ValidatorType.EMAIL -> return false
            Helper.ValidatorType.PHONE -> return false
            else -> false
        }

        return Helper.isInputRequired(pax, validator, isTrain)
    }

    fun getCountryList() {


    }

    fun readJsonFromRaw(context: Context): List<String> {
        val inputStream = context.resources.openRawResource(R.raw.country)
        val jsonText = inputStream.bufferedReader().use { it.readText() }

        // Chuyển đổi JSON thành danh sách chuỗi
        val jsonArray = JSONArray(jsonText)

        return List(jsonArray.length()) { jsonArray.getJSONObject(it).getString("name") }
    }

    fun getPaxGenderList(): ArrayList<String> {
        var start = 0
        var end = 1
        if (pax.type == PassengerType.ADULT || pax.type == PassengerType.OLDER) {
            start = 0
            end = 1
        }
        if (pax.type == PassengerType.CHILD || pax.type == PassengerType.INFANT || pax.type == PassengerType.STUDENT) {
            AppConfigs.Log("PassengerType", pax.type.toString())
            start = 2
            end = 3
        }
        var i = 0

        val types: ArrayList<String> = ArrayList()
        EnumSet.allOf(PassengerTitle::class.java).forEach { type ->
            if ((i in start..end)) {
                types.add(type.text)
            }
            i++
        }
        return types
    }

    fun getSelectedPaxGender(): String {
        EnumSet.allOf(PassengerTitle::class.java).forEach { type ->
            if (type == pax.title) {
                return type.text
            }
        }
        return ""
    }


    fun getTitleByText(text: String): PassengerTitle {

        EnumSet.allOf(PassengerTitle::class.java).forEach { type ->
            if (type.text == text) {
                return type
            }
        }
        return PassengerTitle.MR
    }

    fun onSelectTitleItem(parent: MaterialSpinner, view: View?, pos: Int, id: Long) {
        pax.title = getTitleByText(parent.adapter!!.getItem(pos).toString())
    }


}