package com.hqt.viewmodel

import android.app.Application
import android.graphics.Color
import android.view.View
import android.widget.Toast
import androidx.annotation.NonNull
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.hqt.datvemaybay.Common
import com.hqt.data.model.BookingTrain
import com.hqt.data.model.FlightWatches
import com.hqt.util.AppConfigs
import com.hqt.util.DateOnly

class FlightWatchesViewModel(@NonNull application: Application?) : BaseViewModel(application!!) {
    var flightWatches = MutableLiveData<FlightWatches>()
    var onLoading = MutableLiveData<Boolean>()
    fun getDateString(date: DateOnly): String {
        return try {
            if (date.value == null) {
                ""
            } else (Common.getDayOfWeek(date.value, true) + ", " + Common.dateToString(date.value, "dd/MM/yy"))
        } catch (e: Exception) {
            e.printStackTrace()
            ""
        }
    }

    fun getMaxPriceText(): String {
        return if (flightWatches.value?.max_price == 0) {
            ""
        } else {
            "Ngân sách: " + Common.dinhDangTien(flightWatches.value!!.max_price)
        }
    }

    fun onHelpClick(view: View) {
        Toast.makeText(view.context, "Giá vé có thể thay đổi.\nNhấn Đặt Vé Ngay để cập nhật giá mới nhất", Toast.LENGTH_LONG).show()
    }

    init {
        flightWatches.value = FlightWatches()
        onLoading.value = true
    }

}