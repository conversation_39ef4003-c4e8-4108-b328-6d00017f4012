package com.hqt.viewmodel

import android.app.Application
import androidx.annotation.NonNull
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.hqt.data.model.BookingTrain


class CountNumberViewModel(@NonNull application: Application?) : AndroidViewModel(application!!) {
    val scoreTeamA = MutableLiveData<Int>()
    val scoreTeamB = MutableLiveData<Int>()

    var booking = MutableLiveData<BookingTrain>()

    fun increaseScroeTeamA(score: Int) {
        scoreTeamA.value = scoreTeamA.value!! + score
    }

    fun increaseScroeTeamB(score: Int) {
        scoreTeamB.value = scoreTeamB.value!! + score
    }

    fun onChange() {
        booking.value = booking.value // force postValue to notify Observers
        // can also use user.postValue()
    }

    fun getBooking(): LiveData<BookingTrain> {
        return booking
    }

    init {
        scoreTeamA.value = 0
        scoreTeamB.value = 0
    }
}