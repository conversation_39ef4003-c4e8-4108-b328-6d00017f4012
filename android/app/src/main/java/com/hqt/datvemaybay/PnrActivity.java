package com.hqt.datvemaybay;

import static com.hqt.view.ui.flighthistory.other.PermissionUtils.createNotificationChannel;
import static com.hqt.view.ui.flighthistory.other.PermissionUtils.requestPermission;

import android.Manifest;
import android.app.Activity;
import android.app.ProgressDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.net.Uri;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Handler;
import android.text.Html;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.HorizontalScrollView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.AppCompatButton;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.core.content.ContextCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.android.volley.VolleyError;
import com.facebook.shimmer.ShimmerFrameLayout;
import com.github.clans.fab.FloatingActionButton;
import com.github.clans.fab.FloatingActionMenu;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.firebase.analytics.FirebaseAnalytics;
import com.hqt.data.model.Booking;
import com.hqt.data.model.BookingBus;
import com.hqt.data.model.BookingTrain;
import com.hqt.data.model.BookingV2;
import com.hqt.util.AppConfigs;
import com.hqt.util.AppController;
import com.hqt.util.SSLSendRequest;
import com.hqt.util.ViewUtil;
import com.hqt.util.Widget;
import com.hqt.view.ui.BaseActivity;
import com.hqt.view.ui.HomeActivity;
import com.hqt.view.ui.bus.BusBookingViewActivity;
import com.hqt.view.ui.flightSearch.model.FareData;
import com.hqt.view.ui.payment.NewPaymentActivity;
import com.hqt.view.ui.search.ui.activity.SearchActivityV2;
import com.hqt.view.ui.train.TrainBookingViewActivity;

import org.json.JSONObject;

import java.util.Calendar;
import java.util.Date;


public class PnrActivity extends BaseActivity {
    private AlertDialog myDialog;
    private String pnr, pnrReturn, curentBookingId = "", paxNameList = "", contactName, contactPhone, contactEmail;
    TextView txtDisCount, from, to, txtGiaTongCong, txtBookingId, txtPnr, txtPnrReturn, txtPnrOneName, txtStatus, txtTimeLimit, txtHeaderStatus;
    LinearLayout pnrReturnLayout, view, timeLimitLayout, bottomSheet;
    AppCompatButton btnBookVe, btnPaynow;
    Boolean motChieu = true;
    LinearLayout paxInPut;
    ProgressDialog dialog;
    String noiDungSms;
    Boolean error = true, pause = false, fromCache = false, onRefresh = false, firstShowHelp = true;
    FloatingActionMenu fabMenu;
    FloatingActionButton fabSend, fabCall, fabBook, fabPay;
    String bId = "0";
    JSONObject PaxList = null;
    SharedPreferences settings;
    String PaymentStatus = "";
    Date TimeLimit;
    String TimeLimitText = null;
    String token = null;
    SwipeRefreshLayout swipeRefreshLayout;
    ShimmerFrameLayout mShimmerViewContainer;
    private FirebaseAnalytics mFirebaseAnalytics;
    CoordinatorLayout coordinatorLayout;
    LinearLayout layoutPointReward;
    BookingV2 bookingDetail;
    CountDownTimer Timer = null;
    Boolean onyInfoView = false;
    Context mContext;
    BottomSheetDialog dialogLoading = null;

    BottomSheetDialog sheetDialog = null;
    View sheetViewLayout = null;

    @Override
    protected int getLayoutId() {
        return R.layout.pnr_layout;
    }

    public void onCreate(Bundle savedInstanceState) {

        super.onCreate(savedInstanceState);
        coordinatorLayout = findViewById(R.id.coordinatorLayout);

        mFirebaseAnalytics = FirebaseAnalytics.getInstance(this);

        getToolbar().setTitle("Thông tin đặt chỗ");
        getToolbar().setNavigationIcon(R.drawable.ic_action_back_home);
        mContext = this;

        //STATUS BAR COLOR
        swipeRefreshLayout = findViewById(R.id.swipe_refresh_layout);
        swipeRefreshLayout.setColorSchemeResources(R.color.green, R.color.red, R.color.google_yellow);
        swipeRefreshLayout.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                if (!onRefresh) {
                    onRefresh = true;
                    new Handler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            getDBookingDetail(false);

                        }
                    }, 500);
                }
            }
        });

        if (getIntent().getExtras() != (null)) {
            curentBookingId = getIntent().getExtras().getString("bookingId");
            contactEmail = getIntent().getExtras().getString("email");
            token = getIntent().getStringExtra("token");
            onyInfoView = getIntent().getBooleanExtra("onyInfoView", false);

            if (getIntent().getBooleanExtra("showLoading", false)) {
                dialogLoading = Widget.showLoading(this);
            }

        } else {
            curentBookingId = Common.BOOKING_ID;
            contactEmail = Common.UEmail;
        }


        mShimmerViewContainer = findViewById(R.id.shimmer_view_container);
        txtHeaderStatus = findViewById(R.id.headStatus);
        txtBookingId = findViewById(R.id.txtBookingId);
        txtGiaTongCong = findViewById(R.id.txtGiaTongCong);
        txtDisCount = findViewById(R.id.txtdiscount);
        txtPnr = findViewById(R.id.pnr);
        txtPnrReturn = findViewById(R.id.pnrReturn);
        txtPnrOneName = findViewById(R.id.txtPnr);
        layoutPointReward = findViewById(R.id.layoutPointReward);
        btnBookVe = findViewById(R.id.btnBookVe);
        btnPaynow = findViewById(R.id.btnPaynow);
        bottomSheet = findViewById(R.id.bottom_sheet);

        pnrReturnLayout = findViewById(R.id.pnrReturnLayout);
        view = findViewById(R.id.view);
        paxInPut = findViewById(R.id.paxInPut);
        txtStatus = findViewById(R.id.txtStatus);
        txtTimeLimit = findViewById(R.id.txtTimeLimit);

        settings = getSharedPreferences("12BAY-APP-CONFIG", 0);
        txtBookingId.setText("#" + Common.BOOKING_ID);

        txtBookingId.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent("bookingInsert");
                LocalBroadcastManager.getInstance(getApplicationContext()).sendBroadcast(intent);
            }
        });

        btnBookVe.setOnClickListener(new OnClickListener() {

                                         @Override
                                         public void onClick(View v) {

                                             // TODO Auto-generated method stub
                                             if (bookingDetail != null) {

                                                 AlertDialog.Builder builder = new AlertDialog.Builder(PnrActivity.this);
                                                 LayoutInflater inflater = LayoutInflater.from(getApplicationContext());
                                                 View alertView = inflater.inflate(R.layout.pnr_tip_layout, null);

                                                 ((TextView) alertView.findViewById(R.id.name)).setText(Common.convertHTML(" Chào <b>" + bookingDetail.getContact_name() + "</b> \uD83C\uDF1F"));
                                                 ((TextView) alertView.findViewById(R.id.bold)).setText(Common.convertHTML(" Vui lòng nhấn <b>GỌI ĐIỆN THOẠI</b> hoặc <b>GỬI SMS</b> để được chúng tôi hỗ trợ nhanh nhất"));
                                                 ((TextView) alertView.findViewById(R.id.status)).setText(Common.convertHTML(" Trạng thái đặt chỗ của bạn là: <b>" + txtStatus.getText() + "</b>"));

                                                 if (bookingDetail.getStatus().equals("waiting_payment")) {
                                                     ((TextView) alertView.findViewById(R.id.payment)).setText(Html.fromHtml(" Chuyến bay của bạn đã được giữ chỗ thành công \uD83D\uDC4D."));
                                                     ((TextView) alertView.findViewById(R.id.payment_line2)).setText(Html.fromHtml(" Bạn hãy thực hiện <b> THANH TOÁN </b> trước <b>" + TimeLimitText + "</b> để hoàn thành đặt chỗ."));
                                                     (alertView.findViewById(R.id.bold)).setVisibility(View.GONE);

                                                     ((TextView) alertView.findViewById(R.id.payment)).setVisibility(View.VISIBLE);
                                                     ((TextView) alertView.findViewById(R.id.payment_line2)).setVisibility(View.VISIBLE);

                                                 } else if (bookingDetail.getStatus().equals("created")) {

                                                     ((TextView) alertView.findViewById(R.id.payment)).setText(Html.fromHtml(" 12bay đang tiến hành xác nhận đặt chỗ với hãng hàng không \uD83D\uDC4D."));
                                                     ((TextView) alertView.findViewById(R.id.payment_line2)).setText(Html.fromHtml(" Quá trình này có thế mất 1-5 phút. Bạn vui lòng đợi hoặc liên hệ để được hỗ trợ nhanh nhất !"));

                                                     ((TextView) alertView.findViewById(R.id.payment)).setVisibility(View.VISIBLE);
                                                     ((TextView) alertView.findViewById(R.id.payment_line2)).setVisibility(View.VISIBLE);

                                                     (alertView.findViewById(R.id.bold)).setVisibility(View.GONE);
                                                     ((TextView) alertView.findViewById(R.id.status)).setVisibility(View.GONE);

                                                 } else {
                                                     (alertView.findViewById(R.id.payment)).setVisibility(View.GONE);
                                                 }

                                                 builder.setView(alertView);
                                                 builder.setPositiveButton("Gửi SMS", new DialogInterface.OnClickListener() {
                                                     public void onClick(DialogInterface dialog, int id) {
                                                         Common.sendSms(getApplicationContext(), Common.unAccent(bookingDetail.getSmsMessage()), AppConfigs.getInstance().getConfig().getString("sms"));
                                                     }
                                                 }).setNegativeButton("Gọi Điện", new DialogInterface.OnClickListener() {
                                                     public void onClick(DialogInterface dialog, int id) {

                                                         dialog.cancel();
                                                         String p = "";
                                                         Intent i = new Intent(Intent.ACTION_DIAL);

                                                         p = "tel:" + AppConfigs.getInstance().getConfig().getString("hotline");

                                                         i.setData(Uri.parse(p));
                                                         startActivity(i);
                                                     }
                                                 }).setNeutralButton("\uD83C\uDF1FNhắn Zalo", new DialogInterface.OnClickListener() {
                                                     public void onClick(DialogInterface dialog, int id) {

                                                         Uri uri = Uri.parse("https://zalo.me/4377741633721725644");
                                                         Intent intent = new Intent(Intent.ACTION_VIEW, uri);
                                                         intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                                                         startActivity(intent);

                                                     }
                                                 });

                                                 if (bookingDetail.getStatus().equals("waiting_payment")) {
                                                     builder.setNeutralButton("\uD83C\uDF1FTHANH TOÁN", new DialogInterface.OnClickListener() {
                                                         public void onClick(DialogInterface dialog, int id) {
                                                             btnPaynow.performClick();
                                                         }
                                                     });
                                                 }

                                                 builder.setCancelable(true);
                                                 myDialog = builder.create();


                                                 try {
                                                     if (!PnrActivity.this.isFinishing() && !myDialog.isShowing() && dialogLoading == null) { //&& dialogLoading != null && !dialogLoading.isShowing()

                                                         myDialog.show();
                                                     }
                                                 } catch (Exception e) {
                                                     e.printStackTrace();
                                                     AppConfigs.logException(e);
                                                 }

                                             }
                                         }

                                     }

        );

        btnPaynow.setOnClickListener(new OnClickListener() {

                                         @Override
                                         public void onClick(View v) {
                                             // TODO Auto-generated method stub

                                             if (isInternetConnected()) {
                                                 try {
                                                     Intent i;
//TODO
                                                     if (AppConfigs.getInstance().getConfig().getBoolean("is_on_payoo")) {
                                                         i = new Intent(getApplicationContext(), NewPaymentActivity.class);

                                                     } else {
                                                         i = new Intent(Intent.ACTION_VIEW);
                                                     }

                                                     i.setData(Uri.parse(bookingDetail.getPayment().getUrl()));
                                                     i.putExtra("token", bookingDetail.getToken());
                                                     i.putExtra("paymentUrl", bookingDetail.getPayment().getUrl());
                                                     i.putExtra("orderId", bookingDetail.getId());
                                                     startActivity(i);

                                                 } catch (Exception e) {
                                                     AppConfigs.logException(e);
                                                     e.printStackTrace();
                                                 }

                                             } else {
                                                 //Nếu không có kết nối với intenet thì
                                                 Common.showAlertDialog(PnrActivity.this, "Không thể kết nối Internet",
                                                         "Xin vui lòng kết nối Wifi hoặc 3g để tiếp tục", false, true);
                                             }
                                         }

                                     }

        );
        AppCompatButton btnBookVeBottom = bottomSheet.findViewById(R.id.btnBookVe);
        AppCompatButton btnPaynowBottom = bottomSheet.findViewById(R.id.btnPaynow);

        btnBookVeBottom.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                btnBookVe.performClick();
            }
        });
        btnPaynowBottom.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                btnPaynow.performClick();
            }
        });
        layoutPointReward.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                if (!isUserSigned()) {
                    signIn();
                }

            }
        });

        findViewById(R.id.btnCall).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                btnBookVe.performClick();
            }
        });
        findViewById(R.id.btnRebook).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
//                Intent in = new Intent(getApplicationContext(), SearchActivity.class);
                Intent in = new Intent(getApplicationContext(), SearchActivityV2.class);
                in.putExtra("originCode", bookingDetail.getOrigin_code());
                in.putExtra("destinationCode", bookingDetail.getDestination_code());
                in.putExtra("departureDate", bookingDetail.getDeparture_date());
                if (bookingDetail.is_round_trip())
                    in.putExtra("returnDate", bookingDetail.getReturn_date());
                in.putExtra("adultCount", bookingDetail.getAdult());
                in.putExtra("childCount", bookingDetail.getChild());
                in.putExtra("infantCount", bookingDetail.getInfant());

                startActivity(in);

            }
        });

        Intent in = getIntent();
        fromCache = in.getBooleanExtra("fromCache", false);

        if (isInternetConnected()) {
            // getDBookingDetail();
            //onresume
        } else {

            if (fromCache) {
                ProcessBooking(new JSONObject(), false);
            } else {
                Common.showAlertDialog(PnrActivity.this, "Không thể kết nối Internet",
                        "Xin vui lòng kết nối Wifi hoặc 3g để tiếp tục", false, true);
            }
        }

        sheetDialog = new BottomSheetDialog(this, R.style.SheetDialogTransparent);
        sheetViewLayout = getLayoutInflater().inflate(R.layout.select_flight_inter_layout, null);
        sheetDialog.setContentView(sheetViewLayout);


        createNotificationChannel(this);
        requestPermission(this, 9999, Manifest.permission.POST_NOTIFICATIONS, false);
    }

    private void showBottomSheetFlightInfo(FareData fare, Boolean isReturnTrip) {
        try {

            Widget.createFareDetailInfo(this, fare, sheetViewLayout.findViewById(R.id.flightInfo));
            sheetDialog.getBehavior().setState(BottomSheetBehavior.STATE_EXPANDED);
            sheetDialog.getBehavior().setDraggable(false);
            sheetDialog.getBehavior().setHideable(true);
            sheetDialog.show();


            ((View) sheetViewLayout.findViewById(R.id.quickViewLayout)).setVisibility(View.GONE);
            ((View) sheetViewLayout.findViewById(R.id.close_view)).setVisibility(View.VISIBLE);

            ((Button) sheetViewLayout.findViewById(R.id.btnBackFlight)).setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (sheetDialog != null && sheetDialog.isShowing()) sheetDialog.hide();
                }
            });


        } catch (Exception e) {
            e.printStackTrace();
            if (sheetDialog.isShowing()) sheetDialog.dismiss();
        }
    }

    public void getDBookingDetail(Boolean nextPayment) {

        JSONObject postParam = new JSONObject();
        try {
            postParam.put("email", contactEmail);
            postParam.put("id", curentBookingId);
            postParam.put("token", token);
        } catch (Exception e) {
            AppConfigs.logException(e);
            e.printStackTrace();
        }

        (new SSLSendRequest(this)).POST(true, "AirLines/Bookings/Detail", postParam, new SSLSendRequest.CallBackInterface() {
            @Override
            public void onSuccess(JSONObject response, boolean cached) {
                try {
                    if (!response.isNull("data")) {
                        JSONObject data = response.getJSONObject("data");

                        ProcessBooking(data, nextPayment);
                        onRefresh = false;
                        swipeRefreshLayout.setRefreshing(false);

                    } else {
                        error = true;
                        showAlertDialog(PnrActivity.this, "Thông báo !", "Không tìm thấy đơn hàng \nVui lòng liên lạc chúng tôi để được hỗ trợ", false);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    AppConfigs.logException(e);
                }
            }

            @Override
            public void onFail(VolleyError e) {
                swipeRefreshLayout.setRefreshing(false);
                if (dialog != null && dialog.isShowing()) dialog.dismiss();
                AppConfigs.Log("TG", "Error: " + e.getMessage());
                error = true;
                showAlertDialog(PnrActivity.this, "Thông báo !", "Không tìm thấy đơn hàng \nVui lòng liên lạc chúng tôi để được hỗ trợ", false);
            }
        });

    }

    private void ProcessBooking(JSONObject data, Boolean nextPayment) {

        try {
            if (fromCache) {
                bookingDetail = Common.curentBooking;
            } else {
                bookingDetail = AppController.getInstance().getGSon().fromJson(data.toString(), BookingV2.class);
                if (bookingDetail.getType() == Booking.BookingType.TRAIN) {
                    BookingTrain bookingReturn = new BookingTrain();
                    bookingReturn.setToken(bookingDetail.getToken());
                    bookingReturn.setContact_email(bookingDetail.getContact_email());
                    bookingReturn.setId(bookingDetail.getId());
                    Intent inn = new Intent(this, TrainBookingViewActivity.class);
                    inn.putExtra("BookingInfo", bookingReturn);
                    startActivity(inn);
                    finish();
                }
                if (bookingDetail.getType() == Booking.BookingType.BUS) {
                    BookingBus bookingReturn = new BookingBus();
                    bookingReturn.setToken(bookingDetail.getToken());
                    bookingReturn.setContact_email(bookingDetail.getContact_email());
                    bookingReturn.setId(bookingDetail.getId());
                    Intent inn = new Intent(this, BusBookingViewActivity.class);
                    inn.putExtra("BookingInfo", bookingReturn);
                    startActivity(inn);
                    finish();
                }
                Common.curentBooking = bookingDetail;
            }
            findViewById(R.id.statusPenddingLayout).setVisibility(View.GONE);
            getToolbar().setSubtitle("Mã đơn hàng #" + bookingDetail.getId());

            ((TextView) findViewById(R.id.txtName)).setText(bookingDetail.getContact_name());
            ((TextView) findViewById(R.id.txtEmail)).setText(bookingDetail.getContact_email());
            ((TextView) findViewById(R.id.txtPhone)).setText(bookingDetail.getContact_phone());

            TimeLimit = Common.getDateTimeFromFormat(bookingDetail.getExpired_date());
            if (bookingDetail.getStatus().equals("waiting_payment") && TimeLimit != (null) && TimeLimit.after(Calendar.getInstance().getTime())) {

                if (nextPayment) btnPaynow.performClick();

                txtTimeLimit.setText(Common.dateToString(TimeLimit, "HH:mm dd/MM/yyyy"));
                TimeLimitText = Common.dateToString(TimeLimit, "HH:mm dd/MM");
                //SHOW Count down
                findViewById(R.id.headStatusLayout).setVisibility(View.VISIBLE);
                final long endDate = TimeLimit.getTime();
                final long startTime = System.currentTimeMillis();
                final long millisUntilFinished = endDate - startTime;
                if (Timer == (null)) {
                    Timer = new CountDownTimer(endDate, 1000) {
                        public void onTick(long millisUntilFinished) {
                            txtHeaderStatus.setText(Common.convertHTML("Đợi thanh toán " + Common.getDateTextCountDown(millisUntilFinished, startTime) + " ⏱️"));
                            txtHeaderStatus.setBackgroundColor(Color.parseColor("#ff0088cd"));
                        }

                        public void onFinish() {
                            txtHeaderStatus.setText("Hết hạn thanh toán ⏱️");
                            txtHeaderStatus.setBackgroundColor(Color.parseColor("#B1B1B1"));
                        }
                    };
                    Timer.start();
                } else {
                    Timer.start();
                }

            } else {
                if (Timer != (null)) Timer.cancel();
            }
            txtStatus.setVisibility(View.VISIBLE);
            if (bookingDetail.getPayment().getStatus()) {

                findViewById(R.id.statusPenddingLayout).setVisibility(View.GONE);
                btnPaynow.setVisibility(View.VISIBLE);
                btnBookVe.setVisibility(View.GONE);
                (bottomSheet.findViewById(R.id.btnPaynow)).setVisibility(View.VISIBLE);
                (bottomSheet.findViewById(R.id.btnBookVe)).setVisibility(View.GONE);

            } else {
                findViewById(R.id.timeLimit).setVisibility(View.GONE);
                btnPaynow.setVisibility(View.GONE);
                btnBookVe.setVisibility(View.VISIBLE);
                (bottomSheet.findViewById(R.id.btnPaynow)).setVisibility(View.GONE);
                (bottomSheet.findViewById(R.id.btnBookVe)).setVisibility(View.VISIBLE);

            }

            if (dialogLoading != null && !bookingDetail.getStatus().equals("created") && dialogLoading.isShowing()) {
                dialogLoading.dismiss();
            }

            if (bookingDetail.getStatus().equals("done")) {
                layoutPointReward.setVisibility(View.VISIBLE);
                txtHeaderStatus.setText(bookingDetail.getStatus_text());
                txtHeaderStatus.setBackground(ContextCompat.getDrawable(this, R.color.stt_green));

            } else if (bookingDetail.getStatus().equals("payment_success")) {
                txtHeaderStatus.setBackground(ContextCompat.getDrawable(this, R.color.stt_yellow));
                txtHeaderStatus.setText("Đang xác nhận xuất vé ⌛");
                (findViewById(R.id.addonlayout)).setVisibility(View.GONE);
                (findViewById(R.id.payment_success_view)).setVisibility(View.VISIBLE);

                LinearLayout widget_chat = findViewById(R.id.widget_chat);
                Widget.chatInfo(getApplicationContext(), widget_chat);


            } else if (bookingDetail.getStatus().equals("created")) {

                layoutPointReward.setVisibility(View.VISIBLE);
                txtHeaderStatus.setText("Đang xác nhận chỗ với hãng hàng không ⌛");
                txtHeaderStatus.setBackground(ContextCompat.getDrawable(this, R.color.stt_yellow));
                findViewById(R.id.statusPenddingLayout).setVisibility(View.VISIBLE);
                txtStatus.setVisibility(View.GONE);

            } else if (bookingDetail.getStatus().equals("fail")) {
                txtHeaderStatus.setText(Html.fromHtml("Đặt chỗ không thành công ⏳"));
                txtHeaderStatus.setBackgroundColor(Color.parseColor("#B1B1B1"));
                layoutPointReward.setVisibility(View.GONE);
            } else if (bookingDetail.getStatus().equals("expired")) {
                layoutPointReward.setVisibility(View.GONE);
                txtHeaderStatus.setText(Html.fromHtml("Hết hạn"));
                txtHeaderStatus.setBackgroundColor(Color.parseColor("#B1B1B1"));
            }

            Calendar depDate = Common.stringToDate(bookingDetail.getDeparture_date(), "yyyy-MM-dd");
            if (!depDate.getTime().before(new Date())) {
                if (bookingDetail.getStatus().equals("expired") || bookingDetail.getStatus().equals("fail")) {
                    findViewById(R.id.rebookLayout).setVisibility(View.VISIBLE);
                    btnBookVe.setVisibility(View.GONE);
                }
            }
            LinearLayout container = findViewById(R.id.container);
            container.removeAllViews();


            if (bookingDetail.getType() == Booking.BookingType.INTER) {

                View viewDeparture = Widget.createViewFlightInterInfo(this,
                        bookingDetail.getFareData().getDepartureFlight(),
                        container);

                viewDeparture.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        ((HorizontalScrollView) findViewById(R.id.horizalScroll)).fullScroll(View.FOCUS_LEFT);
                    }
                });

                ((TextView) viewDeparture.findViewById(R.id.viewFlightDetail)).setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        showBottomSheetFlightInfo(bookingDetail.getFareData(), false);
                    }
                });


                if (bookingDetail.is_round_trip()) {
                    LinearLayout containerRt = findViewById(R.id.containerRt);
                    containerRt.removeAllViews();
                    View viewReturn = Widget.createViewFlightInterInfo(this,
                            bookingDetail.getFareData().getReturnFlight(),
                            containerRt);

                    viewReturn.setOnClickListener(new OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            ((HorizontalScrollView) findViewById(R.id.horizalScroll)).fullScroll(View.FOCUS_RIGHT);
                        }
                    });
                    ((TextView) viewReturn.findViewById(R.id.viewFlightDetail)).setOnClickListener(new OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            showBottomSheetFlightInfo(bookingDetail.getFareData(), false);
                        }
                    });

                }

            } else {

                View view = ViewUtil.bindFlightDataToView(bookingDetail.getDeparture_f(), container, this, false, bookingDetail.getAdult(), bookingDetail.getChild(), bookingDetail.getInfant());
                view.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        ((HorizontalScrollView) findViewById(R.id.horizalScroll)).fullScroll(View.FOCUS_LEFT);
                    }
                });

                if (bookingDetail.is_round_trip()) {
                    LinearLayout containerRt = findViewById(R.id.containerRt);
                    containerRt.removeAllViews();
                    View viewRt = ViewUtil.bindFlightDataToView(bookingDetail.getReturn_f(), containerRt, this, true, bookingDetail.getAdult(), bookingDetail.getChild(), bookingDetail.getInfant());
                    containerRt.setOnClickListener(new OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            ((HorizontalScrollView) findViewById(R.id.horizalScroll)).fullScroll(View.FOCUS_RIGHT);
                        }
                    });
                }
            }

            LinearLayout paxList = findViewById(R.id.paxInPut);
            paxList.removeAllViews();
            paxList.addView(ViewUtil.getPaxView(bookingDetail.getPax_info(), this));


            txtGiaTongCong.setText(Common.dinhDangTien(bookingDetail.getTotal() + bookingDetail.getAddon_fee() - bookingDetail.getDiscount() + bookingDetail.getBag_fee()));
            txtPnr.setText(bookingDetail.getPnr());
            txtBookingId.setText("#" + bookingDetail.getId());
            txtStatus.setText(bookingDetail.getStatus_text());

            if (bookingDetail.is_round_trip()) {
                txtPnrReturn.setText(bookingDetail.getPnr_return());
                if (bookingDetail.getDeparture_f().getProvider().equals(bookingDetail.getReturn_f().getProvider())) {
                    txtPnrOneName.setText("Mã đặt chỗ:");
                } else {
                    txtPnrOneName.setText("Mã đặt chỗ lượt đi:");
                    pnrReturnLayout.setVisibility(View.VISIBLE);
                }

            }

            if (bookingDetail.getDeparture_f().getRewardPoint() != (null)) {
                int pointReward = (bookingDetail.getAdult() + bookingDetail.getChild()) * (bookingDetail.getDeparture_f().getRewardPoint() + (bookingDetail.is_round_trip() ? bookingDetail.getReturn_f().getRewardPoint() : 0));
                ((TextView) findViewById(R.id.txtPoint)).setText("Nhận " + pointReward + " điểm ");
            }

            if (bookingDetail.getStatus().equals("done")) {
                mFirebaseAnalytics.setUserProperty("user_has_purchase", "true");

                bottomSheet.setVisibility(View.GONE);
                try {
                    Bundle params = new Bundle();
                    params.putString(FirebaseAnalytics.Param.CURRENCY, "VND");
                    params.putString(FirebaseAnalytics.Param.ORIGIN, (bookingDetail.getOrigin_code()));
                    params.putString(FirebaseAnalytics.Param.DESTINATION, (bookingDetail.getDestination_code()));
                    params.putInt(FirebaseAnalytics.Param.NUMBER_OF_PASSENGERS, bookingDetail.getAdult() + bookingDetail.getChild() + bookingDetail.getInfant());
                    params.putInt(FirebaseAnalytics.Param.PRICE, (bookingDetail.getTotal() + bookingDetail.getDiscount() + bookingDetail.getDiscount()));
                    params.putInt(FirebaseAnalytics.Param.VALUE, (bookingDetail.getTotal() + bookingDetail.getDiscount() + bookingDetail.getDiscount()));
                    params.putString(FirebaseAnalytics.Param.TRANSACTION_ID, bookingDetail.getDeparture_f().getUuid());
                    params.putString(FirebaseAnalytics.Param.START_DATE, bookingDetail.getDeparture_date());
                    if (bookingDetail.is_round_trip())
                        params.putString(FirebaseAnalytics.Param.END_DATE, bookingDetail.getReturn_date());
                    params.putString(FirebaseAnalytics.Param.FLIGHT_NUMBER, bookingDetail.getDeparture_f().getFlightNumber());
                    params.putString(FirebaseAnalytics.Param.TRAVEL_CLASS, bookingDetail.getDeparture_f().getFareBasis());
                    getFirebaseAnalytics().logEvent(FirebaseAnalytics.Event.PURCHASE, params);
                } catch (Exception e) {
                    AppConfigs.logException(e);
                }


                (findViewById(R.id.addonlayout)).setVisibility(View.VISIBLE);
                btnBookVe.setVisibility(View.GONE);
                com.mikepenz.iconics.view.IconicsButton btnCheckin = findViewById(R.id.btnCheckin);
                com.mikepenz.iconics.view.IconicsButton btnCheckve = findViewById(R.id.btnCheckve);
                com.mikepenz.iconics.view.IconicsButton btnPrintTicket = findViewById(R.id.btnPrintTicket);

                AppCompatButton btnCheckinBottom = bottomSheet.findViewById(R.id.btnCheckin);
                AppCompatButton btnCheckveBottom = bottomSheet.findViewById(R.id.btnCheckve);

                (bottomSheet.findViewById(R.id.addonlayout)).setVisibility(View.VISIBLE);
                (bottomSheet.findViewById(R.id.btnPaynow)).setVisibility(View.GONE);

                btnCheckinBottom.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        btnCheckin.performClick();
                    }
                });
                btnCheckveBottom.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        btnCheckve.performClick();
                    }
                });

                btnPrintTicket.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View view) {

                        Widget.showTicket(mContext, bookingDetail.getToken(), bookingDetail.is_round_trip() && Common.stringToDate(bookingDetail.getDeparture_date(), "yyyy-MM-dd").before(Calendar.getInstance()));

                    }
                });

                btnCheckin.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        Intent in = new Intent(getApplicationContext(), Checkin.class);
                        in.putExtra("pnr", bookingDetail.getPnr());
                        startActivity(in);

                        Bundle params = new Bundle();
                        params.putString("booking_view_screen", "check_in");
                        getFirebaseAnalytics().logEvent("click", params);
                    }
                });
                btnCheckve.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View view) {

                        Intent checkve = new Intent(getApplicationContext(), CheckVe.class);
                        checkve.putExtra("pnr", bookingDetail.getPnr());
                        startActivity(checkve);

                        Bundle params = new Bundle();
                        params.putString("booking_view_screen", "check_pnr");
                        getFirebaseAnalytics().logEvent("click", params);
                    }
                });
            }

            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    mShimmerViewContainer.stopShimmer();
                    mShimmerViewContainer.setVisibility(View.GONE);
                    swipeRefreshLayout.setVisibility(View.VISIBLE);
                    findViewById(R.id.bottom_sheet).setVisibility(View.VISIBLE);
                    view.setVisibility(View.VISIBLE);
                }
            }, 1000);

            if ((bookingDetail.getStatus().equals("waiting_payment") || bookingDetail.getStatus().equals("created")) && !onRefresh && firstShowHelp) {
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (!onyInfoView) {
                            btnBookVe.performClick();
                            firstShowHelp = false;
                        }

                    }
                }, 3000);
            }

            if (onyInfoView) {
                bottomSheet.setVisibility(View.GONE);
                bottomSheet.findViewById(R.id.btnPaynow).setVisibility(View.GONE);
                btnBookVe.setVisibility(View.GONE);
                btnPaynow.setVisibility(View.GONE);
            }

        } catch (Exception e) {
            AppConfigs.logException(e);
            e.printStackTrace();
        }
    }

    public void showAlertDialog(final Context context, String title, String message, Boolean status) {
        AlertDialog alertDialog = new AlertDialog.Builder(context).create();

        alertDialog.setTitle(title);
        alertDialog.setIcon(R.drawable.ic_bell_alert);
        alertDialog.setMessage(message);
        alertDialog.setButton(DialogInterface.BUTTON_POSITIVE, "OK", new DialogInterface.OnClickListener() {

            public void onClick(DialogInterface dialog, int which) {

                if (isTaskRoot()) {
                    Intent intent = new Intent(context, HomeActivity.class);
                    intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
                    context.getApplicationContext().startActivity(intent);
                } else {
                    onBackPressed();
                }


            }
        });
        alertDialog.setButton(DialogInterface.BUTTON_NEGATIVE, "GỌI PHÒNG VÉ", new DialogInterface.OnClickListener() {
            public void onClick(DialogInterface dialog, int which) {

                String p = "";
                Intent i = new Intent(Intent.ACTION_DIAL);
                p = "tel:" + AppConfigs.getInstance().getConfig().getString("hotline");

                i.setData(Uri.parse(p));
                i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                context.getApplicationContext().startActivity(i);
            }
        });

        if (!PnrActivity.this.isFinishing() && alertDialog != null && !alertDialog.isShowing())
            alertDialog.show();
    }

    //GET SETTING
    @Override
    public void onBackPressed() {
        if (this.isTaskRoot()) {
            Intent in = new Intent(this, HomeActivity.class);
            startActivity(in);
            finish();
        } else {
            super.onBackPressed();
        }

    }

    @Override
    public void onResume() {
        // neu booking hien tai so vs booking moi nhan khac nhau thi reload và không phải pasue
        if (isInternetConnected()) {

            if (!PnrActivity.this.isFinishing() && (dialog != (null))) dialog.show();

            getDBookingDetail(false);
        } else {
            Common.showAlertDialog(PnrActivity.this, "Không thể kết nối Internet",
                    "Xin vui lòng kết nối Wifi hoặc 3g để tiếp tục", false, true);
        }


        super.onResume();
    }

    @Override
    public void onPause() {
        pause = true;
        super.onPause();
    }

    public static void setWindowFlag(Activity activity, final int bits, boolean on) {
        Window win = activity.getWindow();
        WindowManager.LayoutParams winParams = win.getAttributes();
        if (on) {
            winParams.flags |= bits;
        } else {
            winParams.flags &= ~bits;
        }
        win.setAttributes(winParams);
    }

    @Override
    public void setmMessageReceiverAction() {
        Toast.makeText(this, "Trạng thái đơn hàng đã được cập nhật", Toast.LENGTH_SHORT).show();
        try {
            if (dialogLoading != null && dialogLoading.isShowing()) dialogLoading.dismiss();
        } catch (Exception e) {

        }

        getDBookingDetail(true);


    }


}