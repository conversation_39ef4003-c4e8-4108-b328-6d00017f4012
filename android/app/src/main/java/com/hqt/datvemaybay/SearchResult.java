package com.hqt.datvemaybay;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.Typeface;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentStatePagerAdapter;

import com.android.volley.VolleyError;
import com.facebook.shimmer.ShimmerFrameLayout;
import com.github.clans.fab.FloatingActionButton;
import com.github.clans.fab.FloatingActionMenu;
import com.github.florent37.materialviewpager.MaterialViewPager;
import com.github.florent37.materialviewpager.header.HeaderDesign;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.firebase.analytics.FirebaseAnalytics;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.hqt.data.model.BookingV2;
import com.hqt.data.model.Flight;
import com.hqt.util.AppConfigs;
import com.hqt.util.Log;
import com.hqt.util.SSLSendRequest;
import com.hqt.util.Widget;
import com.hqt.view.ui.BaseActivity;
import com.hqt.view.ui.FlightListFragment;
import com.hqt.view.ui.booking.BookingActivity;
import com.hqt.view.ui.flightwaches.NewFlightWatchesActivity;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class SearchResult extends BaseActivity {

    int _xDelta;
    int _yDelta;

    private MaterialViewPager mViewPager;
    private String originCode, destinationCode, departureTime, returnTime;
    int adultCount = 1, childCount = 0, infantCount = 0;
    private Button BtnBookVe;
    Boolean isRoundTrip = false;
    int isLoadding = 0;

    Boolean flightSelected = false;
    FloatingActionMenu fabMenu;
    FloatingActionButton fabSortPrice, fabPriceType, fabSortRefesh, fabSortDate;
    FlightListFragment frag1, frag2;
    String sortBy = "price";

    boolean isHaveResult = false;
    private Toolbar toolbar;

    private List<Flight> departureFlightList = new ArrayList<Flight>();
    private List<Flight> returnFlightList = new ArrayList<Flight>();

    int totalViewPaper = 1;
    ProgressBar progressBar;
    ShimmerFrameLayout mShimmerViewContainer;
    Typeface fontAwesome;
    CoordinatorLayout coordinatorLayout;
    LinearLayout emptyState;
    BookingV2 bookingDetail = new BookingV2();
    BottomSheetDialog sheetDialog;
    BottomSheetDialog sheetFlightWatchDialog;
    View sheetViewLayout;

    @Override
    protected int getLayoutId() {
        return R.layout.search_result_layout;
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        coordinatorLayout = findViewById(R.id.coordinatorLayout);

        LinearLayout llBottomSheet = findViewById(R.id.select_flight_sheet);
        sheetDialog = new BottomSheetDialog(this, R.style.SheetDialogTransparent);
        sheetFlightWatchDialog = new BottomSheetDialog(this, R.style.SheetDialogTransparent);
        sheetViewLayout = getLayoutInflater().inflate(R.layout.select_flight_layout, null);
        sheetDialog.setContentView(sheetViewLayout);

        sheetFlightWatchDialog.setContentView(getLayoutInflater().inflate(R.layout.select_flight_layout, null));

        mShimmerViewContainer = findViewById(R.id.shimmer_view_container);

        fontAwesome = Typeface.createFromAsset(getAssets(), "fonts/fontawesome-webfont.ttf");

        emptyState = initEmptyState("Ohh noo! Hết chỗ mất rồi bạn ơi ! <br>Bạn có thể dùng tính năng <b>Săn Vé </b> để thông báo ngay có vé lại", R.drawable.no_flight, -1, new EmptyStateCallBackInterface() {
            @Override
            public void negativeButton(Button button) {
                button.setText("Tìm ngày khác");
                button.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        finish();
                    }
                });
            }

            @Override
            public void positiveButton(Button button) {
                button.setText("Săn vé cho hành trình này");
                button.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        Intent in = new Intent(getApplicationContext(), NewFlightWatchesActivity.class);
                        in.putExtra("origin", originCode);
                        in.putExtra("destination", destinationCode);
                        in.putExtra("depatureDate", bookingDetail.getDeparture_date());
                        startActivity(in);
                        finish();
                    }
                });
            }
        });


        BtnBookVe = sheetViewLayout.findViewById(R.id.btnBookVe);
        sheetViewLayout.findViewById(R.id.btnBack).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (sheetDialog.isShowing()) sheetDialog.dismiss();
            }
        });
        progressBar = findViewById(R.id.progressBar);

//        setupToolbar();

        //HAND INTENT
        Intent in = getIntent();
        originCode = in.getStringExtra("originCode");
        destinationCode = in.getStringExtra("destinationCode");
        departureTime = in.getStringExtra("departureTime");
        returnTime = in.getStringExtra("returnTime");
        adultCount = in.getIntExtra("adult", 1);
        childCount = in.getIntExtra("child", 0);
        infantCount = in.getIntExtra("infant", 0);
        isRoundTrip = in.getBooleanExtra("isRoundTrip", false);

        bookingDetail.setOrigin_code(originCode);
        bookingDetail.setDestination_code(destinationCode);
        bookingDetail.setDeparture_date(departureTime);
        bookingDetail.setReturn_date(returnTime);
        bookingDetail.setAdult(adultCount);
        bookingDetail.setChild(childCount);
        bookingDetail.setInfant(infantCount);
        bookingDetail.set_round_trip(isRoundTrip);


        getApi();

        if (isRoundTrip) totalViewPaper = 2;

        mViewPager.getViewPager().setAdapter(new FragmentStatePagerAdapter(getSupportFragmentManager()) {

            @Override
            public Fragment getItem(int position) {
                if (position % totalViewPaper == 0) {
                    return FlightListFragment.newInstance();
                }
                return FlightListFragment.newInstance();
            }

            @Override
            public int getCount() {
                return totalViewPaper;
            }

            @Override
            public CharSequence getPageTitle(int position) {
                switch (position % totalViewPaper) {
                    case 0:
                        return "Lượt đi";
                    case 1:
                        return "Lượt về";
                }
                return "";
            }
        });

        mViewPager.setMaterialViewPagerListener(new MaterialViewPager.Listener() {
                                                    @Override
                                                    public HeaderDesign getHeaderDesign(int page) {
                                                        switch (page) {
                                                            case 0: {
                                                                toolbar.setSubtitle(departureTime);

                                                                View view = toolbar.getChildAt(3);
                                                                if (view instanceof TextView) {
                                                                    TextView textView = (TextView) view;
                                                                    textView.setTypeface(fontAwesome);
                                                                }

                                                                getSupportActionBar().setTitle(Common.getAirPortName(originCode, true) + " → " + Common.getAirPortName(destinationCode, true));
                                                                toolbar.setSubtitle(departureTime + "  " + adultCount + getString(R.string.fa_male) + " " + childCount + getString(R.string.fa_child) + " " + infantCount + getString(R.string.fa_female));
                                                                return HeaderDesign.fromColorResAndUrl(
                                                                        R.color.primary, AppConfigs.getInstance().getConfig().getString("root_api") + "/api/v1/AirLines/Image/" + destinationCode);
                                                            }
                                                            case 1: {
                                                                toolbar.setSubtitle(returnTime);
                                                                toolbar.setSubtitle(returnTime + "  " + adultCount + getString(R.string.fa_male) + " " + childCount + getString(R.string.fa_child) + " " + infantCount + getString(R.string.fa_female));
                                                                getSupportActionBar().setTitle(Common.getAirPortName(destinationCode, true) + " → " + Common.getAirPortName(originCode, true));

                                                                return HeaderDesign.fromColorResAndUrl(
                                                                        R.color.google_blue, AppConfigs.getInstance().getConfig().getString("root_api") + "/api/v1/AirLines/Image/" + destinationCode);
                                                            }
                                                        }
                                                        return null;
                                                    }
                                                }

        );

        mViewPager.getViewPager().setOffscreenPageLimit(mViewPager.getViewPager().getAdapter().getCount());
        mViewPager.getPagerTitleStrip().setViewPager(mViewPager.getViewPager());

        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                getSupportActionBar().setTitle(Common.getAirPortName(originCode, true) + " - " + Common.getAirPortName(destinationCode, true));

            }
        }, 50);

        BtnBookVe.setOnClickListener(new View.OnClickListener() {
                                         @Override
                                         public void onClick(View v) {
                                             // TODO Auto-generated method stub

                                             if (isInternetConnected()) {

                                                 if (!isRoundTrip && (bookingDetail.getDeparture_f() != (null))) {

                                                     Intent in = new Intent(getApplicationContext(), BookingActivity.class);
                                                     in.putExtra("bookingDetail", bookingDetail);
                                                     startActivity(in);
                                                     overridePendingTransition(R.anim.enter, R.anim.exit);
                                                     getFirebaseAnalytics().setUserProperty("favorite_route", bookingDetail.getOrigin_code() + bookingDetail.getDestination_code());
                                                     if (sheetDialog.isShowing()) sheetDialog.dismiss();
                                                 } else {

                                                     if (bookingDetail.getDeparture_f() == (null)) {
                                                         Toast.makeText(SearchResult.this, "Vui lòng chọn chuyến bay chiều đi !", Toast.LENGTH_SHORT).show();
                                                         mViewPager.getViewPager().setCurrentItem(0);
                                                         if (sheetDialog.isShowing()) sheetDialog.dismiss();

                                                     } else if (bookingDetail.getReturn_f() == (null)) {
                                                         Toast.makeText(SearchResult.this, "Vui lòng chọn chuyến bay chiều về !", Toast.LENGTH_SHORT).show();
                                                         mViewPager.getViewPager().setCurrentItem(1);
                                                         if (sheetDialog.isShowing()) sheetDialog.dismiss();
                                                     } else {
                                                         Intent in = new Intent(getApplicationContext(), BookingActivity.class);
                                                         in.putExtra("bookingDetail", bookingDetail);
                                                         startActivity(in);
                                                         overridePendingTransition(R.anim.enter, R.anim.exit);
                                                         getFirebaseAnalytics().setUserProperty("favorite_route", bookingDetail.getOrigin_code() + bookingDetail.getDestination_code());
                                                     }


                                                 }

                                             } else {
                                                 //Nếu không có kết nối với intenet thì
                                                 Common.showAlertDialog(SearchResult.this, "Không thể kết nối Internet",
                                                         "Xin vui lòng kết nối Wifi hoặc 3g để tiếp tục", false, true);
                                             }

                                         }
                                     }

        );

        fabMenu = findViewById(R.id.fb_menu);
        fabSortPrice = findViewById(R.id.fab_sortPrice);
        fabSortDate = findViewById(R.id.fab_sortTime);
        fabSortRefesh = findViewById(R.id.fab_sortRefesh);
        fabPriceType = findViewById(R.id.fab_price);


        findViewById(R.id.fab_alert_price).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showBottomSheetPriceAlert();
            }
        });

        if (Common.SHOWFULLPRICE) fabPriceType.setLabelText("Hiển thị giá NET");
        else fabPriceType.setLabelText("Hiển thị giá thuế phí");

        fabMenu.setIconAnimated(false);
        fabMenu.hideMenuButton(false);
        if (sortBy.equals("price")) {
            fabSortPrice.setEnabled(false);
        }

        fabSortPrice.setOnClickListener(new View.OnClickListener() {
                                            public void onClick(View v) {
                                                //TODO something when floating action menu first item clicked
                                                showFlightToView("price");
                                                sortBy = "price";
                                                fabSortPrice.setEnabled(false);
                                                fabSortDate.setEnabled(true);
                                                fabMenu.close(true);
                                            }
                                        }

        );
        fabSortDate.setOnClickListener(new View.OnClickListener() {
                                           public void onClick(View v) {
                                               //TODO something when floating action menu second item clicked
                                               showFlightToView("date");
                                               sortBy = "date";
                                               fabSortPrice.setEnabled(true);
                                               fabSortDate.setEnabled(false);
                                               fabMenu.close(true);
                                           }
                                       }

        );
        fabSortRefesh.setOnClickListener(new View.OnClickListener() {
                                             public void onClick(View v) {
                                                 //TODO something when floating action menu second item clicked
                                                 departureFlightList.clear();
                                                 returnFlightList.clear();
                                                 isLoadding = 0;
                                                 mShimmerViewContainer.startShimmer();
                                                 mShimmerViewContainer.setVisibility(View.VISIBLE);

                                                 showFlightToView("price");
                                                 getApi();
                                                 fabMenu.close(true);
                                             }
                                         }

        );
        fabPriceType.setOnClickListener(new View.OnClickListener() {
                                            public void onClick(View v) {
                                                //TODO something when floating action menu second item clicked
                                                Common.SHOWFULLPRICE = !Common.SHOWFULLPRICE;
                                                if (Common.SHOWFULLPRICE) fabPriceType.setLabelText("Hiển thị giá NET");
                                                else fabPriceType.setLabelText("Hiển thị giá thuế phí");

                                                showFlightToView(sortBy);
                                                fabMenu.close(true);
                                            }
                                        }

        );
    }

    @Override
    public void setupToolbar() {
        mViewPager = findViewById(R.id.materialViewPager);

        toolbar = mViewPager.getToolbar();
        if (toolbar != (null)) {

            setSupportActionBar(toolbar);
            toolbar.inflateMenu(R.menu.main);
            Objects.requireNonNull(getSupportActionBar()).setDisplayShowHomeEnabled(true);
            toolbar.setNavigationIcon(R.drawable.ic_action_back_home);
            toolbar.setTitleTextColor(Color.WHITE);
            toolbar.setSubtitleTextColor(Color.WHITE);
            toolbar.setNavigationOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    onBackPressed();
                }
            });

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {

                getWindow().setFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS,
                        WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
                getWindow().setStatusBarColor(Color.TRANSPARENT);

            }
        }
    }

    public void setSelectedFlight(Flight flight) {

        Bundle params = new Bundle();
        params.putString(FirebaseAnalytics.Param.ORIGIN, Common.getAirPortCode(originCode));
        params.putString(FirebaseAnalytics.Param.DESTINATION, Common.getAirPortCode(destinationCode));
        params.putString(FirebaseAnalytics.Param.START_DATE, departureTime);
        params.putString(FirebaseAnalytics.Param.END_DATE, returnTime);
        params.putString(FirebaseAnalytics.Param.FLIGHT_NUMBER, flight.getFlightNumber());
        params.putString(FirebaseAnalytics.Param.ITEM_NAME, originCode + destinationCode + flight.getFlightNumber());
        params.putString(FirebaseAnalytics.Param.TRAVEL_CLASS, flight.getFareBasis());
        params.putString(FirebaseAnalytics.Param.NUMBER_OF_PASSENGERS, (adultCount + childCount + infantCount) + "");
        getFirebaseAnalytics().logEvent(FirebaseAnalytics.Event.VIEW_ITEM, params);

        flightSelected = true;
        int totalPrices = 0;
        int pointReward = 0;
        ((TextView) sheetViewLayout.findViewById(R.id.txtTotalPax)).setText(adultCount + childCount + infantCount + " người");
        if (flight.getOriginCode().equals(originCode)) {
            bookingDetail.setDeparture_f(flight);
        } else {
            bookingDetail.setReturn_f(flight);
        }
        if (bookingDetail.getDeparture_f() != null) {
            totalPrices = bookingDetail.getDeparture_f().getAdult() * adultCount + bookingDetail.getDeparture_f().getChild() * childCount + bookingDetail.getDeparture_f().getInfant() * infantCount;
            ((TextView) sheetViewLayout.findViewById(R.id.quickViewDepTitle)).setText((Common.getAirPortName(bookingDetail.getDeparture_f().getOriginCode(), true) + " ⇾ " + Common.getAirPortName(bookingDetail.getDeparture_f().getDestinationCode(), true) + " " + bookingDetail.getDeparture_f().getDepartureDateTime().substring(0, 11)));
            ((TextView) sheetViewLayout.findViewById(R.id.quickViewDepPrice)).setText(Common.dinhDangTien(totalPrices));
            pointReward = bookingDetail.getDeparture_f().getRewardPoint();
        }
        if (bookingDetail.getReturn_f() != null) {
            int returnTotal = bookingDetail.getReturn_f().getAdult() * adultCount + bookingDetail.getReturn_f().getChild() * childCount + bookingDetail.getReturn_f().getInfant() * infantCount;
            totalPrices += returnTotal;
            sheetViewLayout.findViewById(R.id.quickViewRetLayout).setVisibility(View.VISIBLE);
            ((TextView) sheetViewLayout.findViewById(R.id.quickViewRetTitle)).setText((Common.getAirPortName(bookingDetail.getReturn_f().getOriginCode(), true) + " ⇾ " + Common.getAirPortName(bookingDetail.getReturn_f().getDestinationCode(), true) + " " + bookingDetail.getReturn_f().getDepartureDateTime().substring(0, 11)));
            ((TextView) sheetViewLayout.findViewById(R.id.quickViewRetPrice)).setText(Common.dinhDangTien(returnTotal));
            pointReward = pointReward + bookingDetail.getReturn_f().getRewardPoint();

        }
        ((TextView) sheetViewLayout.findViewById(R.id.txtGrandTotalPrice)).setText(Common.dinhDangTien(totalPrices));
        if (pointReward > 0) {
            sheetViewLayout.findViewById(R.id.layoutPointReward).setVisibility(View.VISIBLE);
            ((TextView) sheetViewLayout.findViewById(R.id.txtPoint)).setText("Nhận " + pointReward + " điểm ");
        } else {
            sheetViewLayout.findViewById(R.id.layoutPointReward).setVisibility(View.GONE);
        }

        bookingDetail.setTotal(totalPrices);


    }

    public void getApi() {
        isLoadding = 0;
        isHaveResult = false;

        if (isInternetConnected()) {
            emptyState.setVisibility(View.GONE);
            departureFlightList.clear();
            returnFlightList.clear();

            String[] airList = AppConfigs.getInstance().getConfig().getString("search_airlines").split("-");
            for (String air : airList) {
                getFlightTask(air);
            }
            Log.d("airList", airList);



            Bundle params = new Bundle();
            params.putString(FirebaseAnalytics.Param.ORIGIN, Common.getAirPortCode(originCode));
            params.putString(FirebaseAnalytics.Param.DESTINATION, Common.getAirPortCode(destinationCode));
            params.putString(FirebaseAnalytics.Param.START_DATE, departureTime);
            params.putString(FirebaseAnalytics.Param.END_DATE, returnTime);
            params.putString(FirebaseAnalytics.Param.NUMBER_OF_PASSENGERS, (adultCount + childCount + infantCount) + "");
            getFirebaseAnalytics().logEvent(FirebaseAnalytics.Event.VIEW_SEARCH_RESULTS, params);


        } else {
            Common.showAlertDialog(SearchResult.this, "Không thể kết nối Internet",
                    "Xin vui lòng kết nối Wifi hoặc 3g để tiếp tục", false, true);
        }
    }

    public void getFlightTask(String airline) {
        JSONObject postParam = new JSONObject();
        try {
            postParam.put("departureDate", departureTime);
            postParam.put("returnDate", returnTime);
            postParam.put("adultCount", adultCount + "");
            postParam.put("childCount", childCount + "");
            postParam.put("infantCount", infantCount + "");
            postParam.put("isRoundTrip", isRoundTrip ? "1" : "0");

        } catch (JSONException e) {
            e.printStackTrace();
            AppConfigs.logException(e);
        }

        (new SSLSendRequest(this)).POST(false, airline + "/iSearch/" + originCode + "/" + destinationCode, postParam, new SSLSendRequest.CallBackInterface() {
            @Override
            public void onSuccess(JSONObject response, boolean cached) {
                AppConfigs.Log("response", response.toString());
                isLoadding++;
                if (!response.isNull("data")) {
                    ProcessFlightSearchData(response);
                }
            }

            @Override
            public void onFail(VolleyError er) {
                isLoadding++;
                if (isLoadding >= 4) {
                    progressBar.setVisibility(View.GONE);
                    if (!isHaveResult) {
                        emptyState.setVisibility(View.VISIBLE);
                        mShimmerViewContainer.stopShimmer();
                        mShimmerViewContainer.setVisibility(View.GONE);

                    }
                }
            }
        });

    }

    private void ProcessFlightSearchData(JSONObject json) {
        try {
            if (json.has("data")) {
                // Getting Array of Contacts
                Type listType = new TypeToken<ArrayList<Flight>>() {
                }.getType();

                Gson gsons = new Gson();
                List<Flight> departureData = gsons.fromJson(json.getJSONObject("data").getJSONArray("departure").toString(), listType);
                List<Flight> returnData = gsons.fromJson(json.getJSONObject("data").getJSONArray("return").toString(), listType);

                departureFlightList.addAll(departureData);
                returnFlightList.addAll(returnData);

                if ((departureFlightList.size() + returnFlightList.size()) > 0) {
                    isHaveResult = true;
                }
                if (isLoadding < 3 && isHaveResult) {
                    progressBar.setVisibility(View.VISIBLE);
                }
                if (isLoadding >= 4) {
                    progressBar.setVisibility(View.GONE);
                    if (isHaveResult)
                        Toast.makeText(getApplication(), "Bấm chọn 1 vé bất kì để xem chi tiết giá vé!", Toast.LENGTH_SHORT).show();
                    else if (!isHaveResult) {
                        fabMenu.setVisibility(View.GONE);
                        emptyState.setVisibility(View.VISIBLE);
                        mShimmerViewContainer.stopShimmer();
                        mShimmerViewContainer.setVisibility(View.GONE);
                    }
                }
                showFlightToView(sortBy);

            } else {
                // error = true;
            }

        } catch (Exception e) {
            AppConfigs.Log("DATA", json.toString());
            AppConfigs.logException(e);
            isHaveResult = false;
            e.printStackTrace();
            Toast.makeText(this, ":( Không tìm thấy dữ liệu, Thử lại một lần nữa bạn nhé !", Toast.LENGTH_SHORT).show();
            onBackPressed();
        }
    }

    public void clearSelect() {
        frag1.clearSelect();
        if (isRoundTrip) {
            frag2.clearSelect();
        }
    }

    public void showFlightToView(String sortBy) {
        try {
            fabMenu.showMenuButton(true);
            frag1 = (FlightListFragment) mViewPager.getViewPager().getAdapter().instantiateItem(mViewPager, 0);
            frag1.addFlight(departureFlightList, sortBy);
            if (isRoundTrip) {
                frag2 = (FlightListFragment) mViewPager.getViewPager().getAdapter().instantiateItem(mViewPager, 1);
                frag2.addFlight(returnFlightList, sortBy);
            }

            if (mShimmerViewContainer.isShimmerStarted() && isHaveResult) {
                mShimmerViewContainer.setVisibility(View.GONE);
                mShimmerViewContainer.stopShimmer();
            }

        } catch (Exception e) {
            e.printStackTrace();
            AppConfigs.logException(e);
            Toast.makeText(this, ":( Không tìm thấy dữ liệu, Thử lại một lần nữa bạn nhé !", Toast.LENGTH_SHORT).show();
            onBackPressed();
        }
    }


    @Override
    public void onBackPressed() {
        try {

        } catch (Exception e) {
            AppConfigs.logException(e);
        }


        super.onBackPressed();
        overridePendingTransition(R.anim.left_to_right, R.anim.right_to_left);
    }

    public void clickNext() {
        BtnBookVe.performClick();
    }

    public void showAlertDialog(Context context, String title, String message, Boolean status) {
        try {
            androidx.appcompat.app.AlertDialog alertDialog = new androidx.appcompat.app.AlertDialog.Builder(context).create();
            alertDialog.setTitle(title);
            alertDialog.setMessage(message);
            alertDialog.setOnCancelListener(new DialogInterface.OnCancelListener() {
                @Override
                public void onCancel(DialogInterface dialogInterface) {
                    finish();
                }
            });
            alertDialog.setIcon(R.drawable.ic_bell_alert);
            alertDialog.setButton(DialogInterface.BUTTON_POSITIVE, "ĐẶT LẠI", new DialogInterface.OnClickListener() {
                public void onClick(DialogInterface dialog, int which) {
                    finish();
                }
            });
            alertDialog.setButton(DialogInterface.BUTTON_NEGATIVE, "GỌI PHÒNG VÉ", new DialogInterface.OnClickListener() {
                public void onClick(DialogInterface dialog, int which) {

                    finish();
                    Intent i = new Intent(Intent.ACTION_DIAL);
                    String p = "tel:" + AppConfigs.getInstance().getConfig().getString("hotline");
                    i.setData(Uri.parse(p));
                    startActivity(i);
                }
            });

            if (!SearchResult.this.isFinishing()) {
                alertDialog.show();

            }
        } catch (Exception e) {
            AppConfigs.logException(e);
        }
    }

    public static void hideKeyboard(AppCompatActivity act) {
        if (act != null && act.getCurrentFocus() != null) {
            InputMethodManager inputMethodManager = (InputMethodManager) act.getSystemService(AppCompatActivity.INPUT_METHOD_SERVICE);
            inputMethodManager.hideSoftInputFromWindow(act.getCurrentFocus().getWindowToken(), 0);
        }
    }

    public void showBottomSheetFlightInfo(Flight flight) {
        try {
            sheetViewLayout.findViewById(R.id.next_view).setVisibility(View.VISIBLE);
            Widget.createFlightInfo(this, flight, sheetViewLayout.findViewById(R.id.flightInfo));
            sheetDialog.getBehavior().setState(BottomSheetBehavior.STATE_EXPANDED);
            sheetDialog.show();
        } catch (Exception e) {
            e.printStackTrace();
            if (sheetDialog.isShowing()) sheetDialog.dismiss();
        }
    }

    public void showBottomSheetPriceAlert() {
        try {

            sheetFlightWatchDialog.findViewById(R.id.next_view).setVisibility(View.GONE);
//            Widget.createPriceAlertInfo(this, bookingDetail, sheetFlightWatchDialog.findViewById(R.id.flightInfo));
            sheetFlightWatchDialog.getBehavior().setState(BottomSheetBehavior.STATE_EXPANDED);
            sheetFlightWatchDialog.show();

        } catch (Exception e) {
            e.printStackTrace();
            if (sheetFlightWatchDialog.isShowing()) sheetFlightWatchDialog.dismiss();
        }
    }


}