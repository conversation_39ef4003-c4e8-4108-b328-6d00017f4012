package com.hqt.datvemaybay;

import android.app.Activity;
import android.app.Dialog;
import android.app.ProgressDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.AppCompatSpinner;

import android.view.LayoutInflater;
import android.view.View;
import android.webkit.JavascriptInterface;
import android.widget.ArrayAdapter;
import android.widget.Toast;

import com.hqt.view.ui.bus.BusBookingViewActivity;

/**
 * Created by TN on 21/10/2015.
 */
public class WebAppInterface {
    Context mContext;
    private AlertDialog myDialog;
    ProgressDialog dialog;

    /**
     * Instantiate the interface and set the context
     */
    public WebAppInterface(Context c) {
        mContext = c;
    }

    /**
     * Show a toast from the web page
     */

    @SuppressWarnings("unused")
    @JavascriptInterface
    public void openBusPayment(String token) {
        Intent in = new Intent(mContext, BusBookingViewActivity.class);
        in.putExtra("BookingToken", token);
        
        mContext.startActivity(in);
        ((Activity) mContext).finish();
    }

    /**
     * Show a toast from the web page
     */

    @SuppressWarnings("unused")
    @JavascriptInterface
    public void showToast(String toast) {
        Toast.makeText(mContext, toast, Toast.LENGTH_SHORT).show();
    }

    @SuppressWarnings("unused")
    @JavascriptInterface
    public void showLoading() {
        try {
            ProgressDialog dialog = new ProgressDialog(mContext);
            dialog.setMessage("Đang tải dữ liệu ...");
            dialog.setIndeterminate(false);
            dialog.setMax(100);
            dialog.setCanceledOnTouchOutside(true);
            dialog.setCancelable(true);
            dialog.setProgressStyle(ProgressDialog.STYLE_SPINNER);
            dialog.show();
        } catch (Exception e) {

        }
    }

    @SuppressWarnings("unused")
    @JavascriptInterface
    public void monthView(String link) {

        Intent in = new Intent(mContext, MonthWebViewActivity.class);
        in.putExtra("URL", link);
        mContext.startActivity(in);
        ((Activity) mContext).overridePendingTransition(R.anim.enter, R.anim.exit);
    }

    @SuppressWarnings("unused")
    @JavascriptInterface
    public void showPax(final String y, final String m, final String d, final String from, final String to) {

        ArrayAdapter<CharSequence> listSoLuongHanhKhachAdapter = ArrayAdapter
                .createFromResource(mContext, R.array.soHanhKhachNguoiLon,
                        R.layout.spinner_layout);
        listSoLuongHanhKhachAdapter
                .setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);


        ArrayAdapter<CharSequence> listSoLuongHanhKhachTreemAdapter = ArrayAdapter
                .createFromResource(mContext, R.array.soHanhKhachTreEm,
                        R.layout.spinner_layout);
        listSoLuongHanhKhachTreemAdapter
                .setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);

        final AlertDialog.Builder builder = new AlertDialog.Builder(mContext);
        LayoutInflater inflater = (LayoutInflater) mContext
                .getSystemService(mContext.LAYOUT_INFLATER_SERVICE);
        View alertView = inflater.inflate(R.layout.pax_number_layout, null);
        final AppCompatSpinner adult = (AppCompatSpinner) (alertView.findViewById(R.id.nguoiLon));
        final AppCompatSpinner child = (AppCompatSpinner) (alertView.findViewById(R.id.treEm));
        final AppCompatSpinner infant = (AppCompatSpinner) (alertView.findViewById(R.id.emBe));
        adult.setAdapter(listSoLuongHanhKhachAdapter);
        child.setAdapter(listSoLuongHanhKhachTreemAdapter);
        infant.setAdapter(listSoLuongHanhKhachTreemAdapter);


        builder.setView(alertView);

        builder.setTitle("Chọn hành khách");
        builder.setIcon(R.drawable.ic_passenger);
        builder.setPositiveButton("Đặt vé", new DialogInterface.OnClickListener() {
            public void onClick(DialogInterface dialog, int id) {
                // if this button is clicked, close
                // current activity
                Intent book = new Intent(mContext, SearchResult.class);
                book.putExtra("act", "Domestic");
                book.putExtra("departureTime", y + "-" + m + "-" + d);
                book.putExtra("originCode", from);
                book.putExtra("destinationCode", to);
                book.putExtra("returnTime", "");
                book.putExtra("adult", Integer.valueOf(adult.getSelectedItem().toString()));
                book.putExtra("child", Integer.valueOf(child.getSelectedItem().toString()));
                book.putExtra("infant", Integer.valueOf(infant.getSelectedItem().toString()));
                book.putExtra("isRoundTrip", false);

                mContext.startActivity(book);
                ((Activity) mContext).overridePendingTransition(R.anim.enter, R.anim.exit);


            }
        })
                .setNegativeButton("Làm lại", new DialogInterface.OnClickListener() {
                    public void onClick(DialogInterface dialog, int id) {


                    }
                });

        builder.setCancelable(true);
        myDialog = builder.create();
        if (!myDialog.isShowing()) myDialog.show();

    }

    @SuppressWarnings("unused")
    @JavascriptInterface
    public void finishLoading(Dialog dialog) {
        if (dialog.isShowing()) dialog.dismiss();
    }
}