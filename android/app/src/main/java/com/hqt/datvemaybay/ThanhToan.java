package com.hqt.datvemaybay;

import android.os.Bundle;
import android.webkit.WebView;
import com.hqt.view.ui.BaseActivity;

public class ThanhToan extends BaseActivity {

    private WebView webView;
    private int mProgressStatus = 0;

    @Override
    public int getLayoutId() {
        return R.layout.thanh_toan;
    }

    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        getToolbar().setTitle("Thanh toán");
        getToolbar().setNavigationIcon(R.drawable.ic_action_back_home);
        Common.BankTextCopy(this);

    }

    @Override
    public void onBackPressed() {
        finish();
    }

}