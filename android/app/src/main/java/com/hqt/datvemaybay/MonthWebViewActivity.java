package com.hqt.datvemaybay;


import android.annotation.SuppressLint;

import androidx.appcompat.app.AlertDialog;

import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;

import android.net.Uri;
import android.os.Build;
import android.os.Bundle;

import androidx.appcompat.widget.AppCompatSpinner;

import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.WindowManager;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ArrayAdapter;

import android.widget.ProgressBar;
import android.widget.Toast;

import com.airbnb.lottie.LottieAnimationView;

import com.hqt.util.AppConfigs;
import com.hqt.view.ui.BaseActivity;
import com.hqt.view.ui.LollipopFixedWebView;

import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class MonthWebViewActivity extends BaseActivity {

    private LollipopFixedWebView webView;
    private AlertDialog myDialog;
    private int mProgressStatus = 0;
    ProgressBar progressBar;
    int count = 5;
    String webUrl = "";
    boolean loadErr = false;
    String url, destinationCode, originCode, day, month, year;

    @Override
    protected int getLayoutId() {
        return R.layout.web_view;
    }

    @SuppressLint("SetJavaScriptEnabled")
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        getToolbar().setTitle("12bay.vn");
        getToolbar().setNavigationIcon(R.drawable.ic_action_back_home);
        getToolbar().setSubtitle("Chi tiết giá trong tháng");

        getToolbar().setNavigationOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                onBackPressed();
            }
        });

        progressBar = findViewById(R.id.progressBar);


        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            getWindow().addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            getWindow().setStatusBarColor(getResources().getColor(R.color.primary_dark));
        }

        Intent in = getIntent();
        webUrl = in.getStringExtra("URL");

        webView = findViewById(R.id.webView1);
        getWebviewVersionInfo();
        WebSettings webSettings = webView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setAllowFileAccess(false);
        webView.addJavascriptInterface(new WebAppInterface(this), "Android");
        webView.addJavascriptInterface(new WebAppInterface(this), "AndroidWeekView");
        webView.setWebViewClient(new WebViewClient() {
            private int webViewPreviousState;
            private final int PAGE_STARTED = 0x1;

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                view.loadUrl(url);
                return true;
            }

            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
                webViewPreviousState = PAGE_STARTED;
                progressBar.setVisibility(View.VISIBLE);
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                try {
                    if (webViewPreviousState == PAGE_STARTED) {
                        progressBar.setVisibility(View.GONE);

                    }
                } catch (Exception e) {

                }
            }

            @Override
            public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                // Do something
                loadErr = true;
                LottieAnimationView animationView = findViewById(R.id.animation_view);
                animationView.setAnimation(R.raw.antenna);
                animationView.loop(true);
                animationView.playAnimation();

                getToolbar().setTitle("12bay.vn");
                view.setVisibility(View.GONE);
                progressBar.setVisibility(View.GONE);

                Toast.makeText(getApplicationContext(), "Vui lòng kiểm tra lại kết nối", Toast.LENGTH_SHORT).show();
            }

        });

        if (isInternetConnected()) {
            if (Common.isOldChrome(webView)) {
                webView.loadUrl(Common.addUrlMobileParams(Common.getV2Link(webUrl)));
            } else {
                webView.loadUrl(Common.addUrlMobileParams(webUrl));
            }

            AppConfigs.Log("web x urlload", webUrl);
        } else {
            Common.showAlertDialog(MonthWebViewActivity.this, "Không thể kết nối Internet",
                    "Xin vui lòng kết nối Wifi hoặc 3g để tiếp tục", false, true);
        }

    }

    public void showPaxInput() {
        ArrayAdapter<CharSequence> listSoLuongHanhKhachAdapter = ArrayAdapter
                .createFromResource(MonthWebViewActivity.this, R.array.soHanhKhachNguoiLon,
                        R.layout.spinner_layout);
        listSoLuongHanhKhachAdapter
                .setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);

        ArrayAdapter<CharSequence> listSoLuongHanhKhachTreemAdapter = ArrayAdapter
                .createFromResource(MonthWebViewActivity.this, R.array.soHanhKhachTreEm,
                        R.layout.spinner_layout);
        listSoLuongHanhKhachTreemAdapter
                .setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);

        final AlertDialog.Builder builder = new AlertDialog.Builder(this);
        LayoutInflater inflater = (LayoutInflater) getBaseContext()
                .getSystemService(LAYOUT_INFLATER_SERVICE);
        View alertView = inflater.inflate(R.layout.pax_number_layout, null);
        final AppCompatSpinner adult = (alertView.findViewById(R.id.nguoiLon));
        final AppCompatSpinner child = (alertView.findViewById(R.id.treEm));
        final AppCompatSpinner infant = (alertView.findViewById(R.id.emBe));
        adult.setAdapter(listSoLuongHanhKhachAdapter);
        child.setAdapter(listSoLuongHanhKhachTreemAdapter);
        infant.setAdapter(listSoLuongHanhKhachTreemAdapter);
        builder.setView(alertView);
        builder.setTitle("Chọn hành khách");
        builder.setIcon(R.drawable.ic_passenger);
        builder.setPositiveButton("Đặt vé", new DialogInterface.OnClickListener() {
            public void onClick(DialogInterface dialog, int id) {
                // if this button is clicked, close
                // current activity
                Intent book = new Intent(getApplicationContext(), SearchResult.class);
                book.putExtra("departureTime", year + "-" + month + "-" + day);
                book.putExtra("originCode", originCode);
                book.putExtra("destinationCode", destinationCode);
                book.putExtra("returnTime", "");
                book.putExtra("adult", Integer.valueOf(adult.getSelectedItem().toString()));
                book.putExtra("child", Integer.valueOf(child.getSelectedItem().toString()));
                book.putExtra("infant", Integer.valueOf(infant.getSelectedItem().toString()));
                book.putExtra("isRoundTrip", false);
                startActivity(book);
                overridePendingTransition(R.anim.enter, R.anim.exit);

            }
        })
                .setNegativeButton("Làm lại", new DialogInterface.OnClickListener() {
                    public void onClick(DialogInterface dialog, int id) {

                    }
                });

        builder.setCancelable(true);
        myDialog = builder.create();
        if (!myDialog.isShowing()) myDialog.show();
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        overridePendingTransition(R.anim.left_to_right, R.anim.right_to_left);

    }

    public void getWebviewVersionInfo() {
        try {
            // Overridden UA string
            String alreadySetUA = webView.getSettings().getUserAgentString();
            webView.getSettings().setUserAgentString(null);
            String webViewVersion = webView.getSettings().getUserAgentString();

            Pattern pattern = Pattern.compile("Chrome/(\\d+)\\.");
            Matcher matcher = pattern.matcher(webViewVersion);


            if (matcher.find()) {
                int chromeVersion = Integer.valueOf(Integer.valueOf(matcher.group(1)));

                Bundle bun = new Bundle();
                bun.putString("user_agent", webViewVersion);
                bun.putInt("version", chromeVersion);
                bun.putInt("app_version", BuildConfig.VERSION_CODE);
                bun.putString("app_version", BuildConfig.VERSION_NAME);

                getFirebaseAnalytics().logEvent("WEBVIEW_BROWSER", bun);

                if (chromeVersion < 54) {

                    AlertDialog alertDialog = new AlertDialog.Builder(this).create();

                    alertDialog.setTitle("Thông báo");
                    alertDialog.setIcon(R.drawable.ic_bell_alert);
                    alertDialog.setMessage("Phiên bản phần mềm đã cũ\nVui lòng cập nhật để tiếp tục :)");
                    alertDialog.setButton(DialogInterface.BUTTON_POSITIVE, "OK", new DialogInterface.OnClickListener() {

                        public void onClick(DialogInterface dialog, int which) {

                            final String appPackageName = "com.google.android.webview";
                            try {
                                startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse("market://details?id=" + appPackageName)));
                            } catch (android.content.ActivityNotFoundException anfe) {
                                startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse("https://play.google.com/store/apps/details?id=" + appPackageName)));
                            }
                        }
                    });
                    alertDialog.setButton(DialogInterface.BUTTON_NEUTRAL, "Trở về", new DialogInterface.OnClickListener() {
                        public void onClick(DialogInterface dialog, int which) {

                        }
                    });
                    alertDialog.setCancelable(true);
                    alertDialog.show();

                }
            }

            webView.getSettings().setUserAgentString(alreadySetUA);

        } catch (Exception e) {
            AppConfigs.logException(e);
        }
    }


}