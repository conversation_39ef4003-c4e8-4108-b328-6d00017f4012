package com.hqt.datvemaybay;


import android.app.Activity;
import android.content.ActivityNotFoundException;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.ShortcutInfo;
import android.content.pm.ShortcutManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.Icon;
import android.net.Uri;
import android.os.Build;
import android.provider.Telephony;
import android.text.Html;
import android.text.InputFilter;
import android.text.InputType;
import android.text.Spanned;
import android.text.style.RelativeSizeSpan;
import android.view.View;
import android.webkit.WebView;
import android.widget.AbsListView;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.content.res.AppCompatResources;
import androidx.core.graphics.drawable.DrawableCompat;

import com.android.volley.Request;
import com.android.volley.Response;
import com.android.volley.VolleyError;
import com.android.volley.toolbox.JsonObjectRequest;
import com.bayvn.charts.BarData;
import com.google.firebase.iid.FirebaseInstanceId;
import com.hqt.data.model.BookingBus;
import com.hqt.data.model.BookingTrain;
import com.hqt.data.model.BookingV2;
import com.hqt.data.model.FareTrend;
import com.hqt.util.AppConfigs;
import com.hqt.util.AppController;
import com.hqt.util.amlich.LunarCalendarUtil;
import com.hqt.util.amlich.YMD;
import com.hqt.view.ui.HomeActivity;
import com.hqt.view.ui.reward.ui.activity.RewardActivity;
import com.hqt.view.ui.bus.BusBookingViewActivity;
import com.hqt.view.ui.bus.BusSearchActivity;
import com.hqt.view.ui.flighthistory.ui.activity.FlightHistoryActivityV2;
import com.hqt.view.ui.flightwaches.FlightWachesViewActivity;
import com.hqt.view.ui.priceboard.MonthViewActivity;
import com.hqt.view.ui.search.ui.activity.SearchActivityV2;
import com.hqt.view.ui.tour.TourDetailActivity;
import com.hqt.view.ui.tour.TourListActivity;
import com.hqt.view.ui.train.TrainBookingViewActivity;
import com.hqt.view.ui.train.TrainSearchActivity;
import com.mikepenz.iconics.Iconics;

import org.json.JSONArray;
import org.json.JSONObject;
import org.threeten.bp.LocalDate;

import java.io.InputStream;
import java.math.BigInteger;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.text.Normalizer;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.FormatterClosedException;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.inject.Singleton;

@Singleton
public final class Common {

    static final String VOUCHER_LINK = "";
    public static boolean isAppRunning = true;
    public final static int RC_SIGN_IN_UI = 123;
    public static boolean onCheckin = false;
    public static String FCM_TOKEN = "";
    public static String DEVICE_ID = "";
    public static String TRACKING_CODE = "";
    public static Boolean SHOWFULLPRICE = false;
    public static String Email = "";
    public static String CURRENT_NOTIFICATION = "";
    public static String AppPopup = "Thông tin hành khách (<b>Giới tính</b> và <b>họ tên</b>) bạn nhập đã chính xác!\nBạn có chắc chắn muốn đặt vé?";
    public static String Notification = "";
    public static String UDisplayName = "";
    public static String UEmail = "";
    public static String Uid = "";
    public static String UPhone = "";
    public static int appVer = 9999;
    public static String UPhoto = "";
    static boolean isLoaddingSetting = false;
    static String BOOKING_ID = "";
    static String IP = "";
    static boolean isCheckVe = true;
    static boolean isCheap = true;
    static String KEY = "";
    public static String ID_DEVICE = "";
    static boolean isNewInfo = false;
    static JSONObject PAYMENT = null;
    static JSONObject AIRPORTLIST = null;
    static JSONObject BOOKING_DETAIL = null;
    static List<String> LISTPAX = null;
    static String PhoneNumber = "";
    public static String WEBLINK = "https://12bay.vn/san-ve-re/";
    static String VOUCHER_HTML = "0";
    static int VOUCHER_ON = 0;
    static String DeviceName = android.os.Build.MANUFACTURER + "|" + android.os.Build.MODEL;
    static List<String> Bag_Price = new ArrayList<String>();
    static List<String> Bag_Price_return = new ArrayList<String>();
    public static JSONArray BAGGAGE_FEE = new JSONArray();
    public static JSONArray BAGGAGE_RETURN = new JSONArray();

    public static boolean isNewData = false;

    public static BookingV2 curentBooking = null;
    public static BookingV2 bookingDetail = null;
    public static String MAX_DEPATURE_DATE = "";
    SimpleDateFormat colFormat = new SimpleDateFormat("ddMMyyyy", Locale.US);

    public static String getAirPortCode(String name) {
        try {
            String newCode = name.substring(name.length() - 3, name.length());
            return newCode;
        } catch (Exception e) {
            return "";
        }
    }

    public static String dinhDangTien(int gia) {
        DecimalFormat df = new DecimalFormat();
        DecimalFormatSymbols symbols = new DecimalFormatSymbols();
        symbols.setDecimalSeparator(',');
        symbols.setGroupingSeparator('.');
        df.setDecimalFormatSymbols(symbols);

        return df.format((gia)) + "đ";
    }

    public static String splitTotalPrice(String total, Boolean isGetPrefix) {

        if (total.length() > 4) {
            if (isGetPrefix) {
                return total.substring(0, total.length() - 4);
            } else {
                return total.substring(total.length() - 4);
            }

        } else {
            return total;
        }

    }

    public static String rutGonTien(int gia) {
        gia = gia / 1000;

        DecimalFormat df = new DecimalFormat();
        DecimalFormatSymbols symbols = new DecimalFormatSymbols();
        symbols.setDecimalSeparator(',');
        symbols.setGroupingSeparator('.');
        df.setDecimalFormatSymbols(symbols);
        String rs = df.format(gia);

        return rs + "K";
    }

    public static int daysBetween(Date d1, Date d2) {
        return (int) ((d2.getTime() - d1.getTime()) / (1000 * 60 * 60 * 24));
    }

    public static String getDuration(Date date1, Date date2) {
        try {
            long diffInMillisec = date1.getTime() - date2.getTime();
            long diffInHours = TimeUnit.MILLISECONDS.toHours(diffInMillisec);
            long diffInMin = TimeUnit.MILLISECONDS.toMinutes(diffInMillisec - diffInHours * 60 * 60 * 1000);

            return diffInHours + "h" + diffInMin + "'";
        } catch (Exception e) {
            AppConfigs.logException(e);
            return "";
        }
    }

    public static String getDurationBefore(Date date2) {
        try {
            Date date1 = new Date();
            long diffInMillisec = date1.getTime() - date2.getTime();

            long diffInDays = TimeUnit.MILLISECONDS.toDays(diffInMillisec);
            long diffInHours = TimeUnit.MILLISECONDS.toHours(diffInMillisec - diffInDays * 24);
            long diffInMin = TimeUnit.MILLISECONDS.toMinutes(diffInHours - diffInHours * 60 * 60 * 1000);
            String dateText = "";
            if (diffInDays > 0) {
                return dateText = diffInDays + " ngày";
            } else if (diffInHours > 0) {
                return dateText = diffInHours + " giờ";
            } else {
                return dateText = diffInMin + " phút";
            }


        } catch (Exception e) {
            AppConfigs.logException(e);
            return "";
        }
    }

    public static long hoursBetween(Calendar startDate, Calendar endDate) {

        long end = endDate.getTimeInMillis();
        long start = startDate.getTimeInMillis();

        return TimeUnit.MILLISECONDS.toHours(Math.abs(end - start));
    }

    public static void insertDay(String day, ArrayList<String> mLabel, ArrayList<String> mDayLabel) {

        if (mLabel.indexOf(day) < 0) {
            mLabel.add(day);
        }
    }

    public static String rutGonTen(String name) {
        try {
            String newCode = name.substring(0, name.indexOf(" - "));
            return newCode;
        } catch (Exception e) {
            return name;
        }

    }

    public static void BankTextCopy(final Context mContext) {
        try {

            LinearLayout bank_vcb = ((Activity) mContext).findViewById(R.id.bank_vcb);
            LinearLayout bank_acb = ((Activity) mContext).findViewById(R.id.bank_acb);
            LinearLayout bank_vietin = ((Activity) mContext).findViewById(R.id.bank_vietin);
            LinearLayout bank_agri = ((Activity) mContext).findViewById(R.id.bank_agri);
            LinearLayout bank_donga = ((Activity) mContext).findViewById(R.id.bank_donga);
            LinearLayout bank_bidv = ((Activity) mContext).findViewById(R.id.bank_bidv);
            LinearLayout bank_tech = ((Activity) mContext).findViewById(R.id.bank_tech);

            final ClipboardManager clipboard = (ClipboardManager) mContext.getSystemService(Context.CLIPBOARD_SERVICE);

            bank_vcb.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    String bank_info = "VIETCOMBANK,\n" +
                            "NGUYỄN VĂN TRÌNH,\n" +
                            "Số TK: *************\n" +
                            "Chi nhánh Q7 HCM";
                    ClipData clip = ClipData.newPlainText("text", bank_info);
                    clipboard.setPrimaryClip(clip);
                    Toast toast = Toast.makeText(mContext, "Đã sao chép VCB", Toast.LENGTH_SHORT);
                    toast.show();
                }
            });
            bank_acb.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    String bank_info = "Ngân Hàng ACB\n" +
                            "NGUYỄN VĂN TRÌNH,\n" +
                            "Số TK :*********";
                    ClipData clip = ClipData.newPlainText("text", bank_info);
                    clipboard.setPrimaryClip(clip);
                    Toast toast = Toast.makeText(mContext, "Đã sao chép ACB", Toast.LENGTH_SHORT);
                    toast.show();

                }
            });
            bank_vietin.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    String bank_info = "VietinBank,\n" +
                            "CTY TNHH MTV TM NGUYỄN DƯƠNG\n" +
                            "Số TK: ************,\n" +
                            "Chi Nhánh 4, HCM";
                    ClipData clip = ClipData.newPlainText("text", bank_info);
                    clipboard.setPrimaryClip(clip);
                    Toast toast = Toast.makeText(mContext, "Đã sao chép VietinBank", Toast.LENGTH_SHORT);
                    toast.show();
                }
            });
            bank_agri.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    String bank_info = "AgriBank,\n" +
                            "DƯƠNG THỊ ĐÀO\n" +
                            "SoTK: *************\n" +
                            "Chi nhánh Đông Sái Gòn";
                    ClipData clip = ClipData.newPlainText("text", bank_info);
                    clipboard.setPrimaryClip(clip);
                    Toast toast = Toast.makeText(mContext, "Đã sao chép", Toast.LENGTH_SHORT);
                    toast.show();

                }
            });

            bank_donga.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    String bank_info = "ĐôngÁ Bank,\n" +
                            "NGUYỄN VĂN TRÌNH\n" +
                            "SoTK: **********\n" +
                            "Chi nhánh HCM";
                    ClipData clip = ClipData.newPlainText("text", bank_info);
                    clipboard.setPrimaryClip(clip);
                    Toast toast = Toast.makeText(mContext, "Đã sao chép Đông Á", Toast.LENGTH_SHORT);
                    toast.show();
                }
            });
            bank_bidv.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    String bank_info = "BIDV,\n" +
                            "DƯƠNG THỊ ĐÀO\n" +
                            "Số TK: **************,\n" +
                            "Chi nhánh Thị Nghè, HCM";
                    ClipData clip = ClipData.newPlainText("text", bank_info);
                    clipboard.setPrimaryClip(clip);
                    Toast toast = Toast.makeText(mContext, "Đã sao chép BIDV", Toast.LENGTH_SHORT);
                    toast.show();
                }
            });
            bank_tech.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    String bank_info = "Techcombank,\n" +
                            "NGUYỄN VĂN TRÌNH\n" +
                            "SoTK: **************\n" +
                            "Chi nhánh Tô Ký, Q12, HCM";
                    ClipData clip = ClipData.newPlainText("text", bank_info);
                    clipboard.setPrimaryClip(clip);
                    Toast toast = Toast.makeText(mContext, "Đã sao chép Techcombank", Toast.LENGTH_SHORT);
                    toast.show();
                }
            });
        } catch (Exception e) {
            AppConfigs.logException(e);
        }

    }

    public static ArrayList<String> getMlabel(ArrayList<Calendar> mDays) {
        try {
            String label = "";
            ArrayList<String> mLabel = new ArrayList<String>();
            SimpleDateFormat colFormat = new SimpleDateFormat("dd/MM");
            for (Calendar d : mDays) {
                label = colFormat.format(d.getTime());
                mLabel.add(label);
            }

            return mLabel;

        } catch (Exception e) {

            e.printStackTrace();
            AppConfigs.logException(e);
            return null;

        }

    }

    public static String getLunarDate(Date date) {
        if (date == null) return "";
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        float tz = TimeUnit.HOURS.convert(cal.getTimeZone().getRawOffset(), TimeUnit.MILLISECONDS);
        YMD lunarDate = LunarCalendarUtil.convertSolar2Lunar(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH) + 1, cal.get(Calendar.DAY_OF_MONTH), tz);

        return "(" + lunarDate.day + "/" + lunarDate.month + " âm lịch)";
    }

    public static String getLunarFromDate(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        float tz = TimeUnit.HOURS.convert(cal.getTimeZone().getRawOffset(), TimeUnit.MILLISECONDS);
        YMD lunarDate = LunarCalendarUtil.convertSolar2Lunar(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH) + 1, cal.get(Calendar.DAY_OF_MONTH), tz);

        return lunarDate.day + "/" + lunarDate.month;
    }

    public static String getDayOfWeek(String date) {

        String weekDay = "";
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy", Locale.US);
        Date c = new Date();
        try {
            c = dateFormat.parse(date);
        } catch (ParseException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(c);

        int dayOfWeek = cal.get(Calendar.DAY_OF_WEEK);
        if (Calendar.MONDAY == dayOfWeek) {
            weekDay = "Thứ hai";
        } else if (Calendar.TUESDAY == dayOfWeek) {
            weekDay = "Thứ ba";
        } else if (Calendar.WEDNESDAY == dayOfWeek) {
            weekDay = "Thứ tư";
        } else if (Calendar.THURSDAY == dayOfWeek) {
            weekDay = "Thứ năm";
        } else if (Calendar.FRIDAY == dayOfWeek) {
            weekDay = "Thứ sáu";
        } else if (Calendar.SATURDAY == dayOfWeek) {
            weekDay = "Thứ bảy";
        } else if (Calendar.SUNDAY == dayOfWeek) {
            weekDay = "Chủ nhật";
        }

        return weekDay + ", " + date.substring(0, 5);
    }

    public static void CopyClipboard(Context mContext, String text) {
        ClipboardManager clipboard = (ClipboardManager) mContext.getSystemService(Context.CLIPBOARD_SERVICE);
        ClipData clip = ClipData.newPlainText("text", text);
        clipboard.setPrimaryClip(clip);
        Toast toast = Toast.makeText(mContext, "Đã sao chép " + text, Toast.LENGTH_SHORT);
        toast.show();

    }

    public static String getDateOfWeek(DayOfWeek dayOfWeek) {
        String weekDay = "";

        if (DayOfWeek.MONDAY == dayOfWeek) {
            weekDay = "T1x";
        } else if (DayOfWeek.TUESDAY == dayOfWeek) {
            weekDay = "T3";
        } else if (DayOfWeek.WEDNESDAY == dayOfWeek) {
            weekDay = "T4";
        } else if (DayOfWeek.THURSDAY == dayOfWeek) {
            weekDay = "T5";
        } else if (DayOfWeek.FRIDAY == dayOfWeek) {
            weekDay = "T6";
        } else if (DayOfWeek.SATURDAY == dayOfWeek) {
            weekDay = "T7";
        } else if (DayOfWeek.SUNDAY == dayOfWeek) {
            weekDay = "CVN";
        }
        return weekDay;
    }

    public static Calendar convertToDate(LocalDate date) {
        Calendar d = Calendar.getInstance();
        d.set(date.getYear(), date.getMonthValue() - 1, date.getDayOfMonth());
        return d;
    }

    public static String getDayOfWeek(Date date, Boolean sort) {
        try {
            if (date == null) {
                return "";
            }
            String weekDay = "";

            Calendar cal = Calendar.getInstance();
            cal.setTime(date);

            int dayOfWeek = cal.get(Calendar.DAY_OF_WEEK);
            if (Calendar.MONDAY == dayOfWeek) {
                weekDay = "T2";
            } else if (Calendar.TUESDAY == dayOfWeek) {
                weekDay = "T3";
            } else if (Calendar.WEDNESDAY == dayOfWeek) {
                weekDay = "T4";
            } else if (Calendar.THURSDAY == dayOfWeek) {
                weekDay = "T5";
            } else if (Calendar.FRIDAY == dayOfWeek) {
                weekDay = "T6";
            } else if (Calendar.SATURDAY == dayOfWeek) {
                weekDay = "T7";
            } else if (Calendar.SUNDAY == dayOfWeek) {
                weekDay = "CN";
            }

            return weekDay;
        } catch (Exception e) {
            AppConfigs.logException(e);
            e.printStackTrace();
            return "";
        }
    }

    public static Integer parserInt(String txt) {
        try {
            String value = txt;
            value = value.replaceAll("[^0-9]", "");
            return Integer.parseInt(value);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String getDayOfWeek(Date date) {
        if (date == null) {
            return "";
        }
        String weekDay = "";

        Calendar cal = Calendar.getInstance();
        cal.setTime(date);

        int dayOfWeek = cal.get(Calendar.DAY_OF_WEEK);
        if (Calendar.MONDAY == dayOfWeek) {
            weekDay = "Thứ hai";
        } else if (Calendar.TUESDAY == dayOfWeek) {
            weekDay = "Thứ ba";
        } else if (Calendar.WEDNESDAY == dayOfWeek) {
            weekDay = "Thứ tư";
        } else if (Calendar.THURSDAY == dayOfWeek) {
            weekDay = "Thứ năm";
        } else if (Calendar.FRIDAY == dayOfWeek) {
            weekDay = "Thứ sáu";
        } else if (Calendar.SATURDAY == dayOfWeek) {
            weekDay = "Thứ bảy";
        } else if (Calendar.SUNDAY == dayOfWeek) {
            weekDay = "Chủ nhật";
        }

        return weekDay;
    }


    public static boolean isEmailValid(String email) {
        boolean isValid = false;

        String expression = "^[\\w\\.-]+@([\\w\\-]+\\.)+[A-Z]{2,4}$";
        CharSequence inputStr = email;

        Pattern pattern = Pattern.compile(expression, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(inputStr);
        if (matcher.matches()) {
            isValid = true;
        }
        return isValid;
    }

    public static String unAccent(String s) {
        //không dấu
        if (s != (null)) {
            String temp = Normalizer.normalize(s, Normalizer.Form.NFD);
            Pattern pattern = Pattern.compile("\\p{InCombiningDiacriticalMarks}+");
            return pattern.matcher(temp).replaceAll("").replaceAll("Đ", "D").replaceAll("đ", "d");
        } else {
            return "";
        }

    }

    public static ArrayList<String> getLabelForAddValue(ArrayList<Calendar> mDays) {
        try {
            Collections.sort(mDays, new Comparator<Calendar>() {
                public int compare(Calendar s1, Calendar s2) {
                    return s1.compareTo(s2);
                }

                ;

            });

            String label = "";
            ArrayList<String> mLabel = new ArrayList<String>();
            SimpleDateFormat colFormat = new SimpleDateFormat("yyyyMMdd", Locale.US);
            for (Calendar d : mDays) {
                label = colFormat.format(d.getTime());
                mLabel.add(label);
            }

            return mLabel;

        } catch (Exception e) {

            return null;
        }

    }

    public static String getATA(String name) {

        String[] b = name.split("\\s+");
        name = b[b.length - 1].replace(" ", "");

        return name;

    }

    public static String getKeyHash() {
        Date date = new Date();
        String dateString = dateToString(date, "yyyy_MM_dd");
        return getMd5Hash("12bay.vn" + dateString + Common.IP);
    }

    public static String getMd5Hash(String input) {
        try {

            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] messageDigest = md.digest(input.getBytes());
            BigInteger number = new BigInteger(1, messageDigest);
            String md5 = number.toString(16);

            while (md5.length() < 32)
                md5 = "0" + md5;

            return md5;
        } catch (NoSuchAlgorithmException e) {

            return null;
        }
    }

    public static Calendar getDateFromString(String d) {
        if (d == null) return null;
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
        Date c = new Date();
        try {
            c = dateFormat.parse(d);
        } catch (ParseException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(c);
        return calendar;
    }


    public static String dateToString(Date d, String format) {
        try {
            if (d == null) {
                return "";
            }
            String now = "";

            if (format == (null) || format.length() < 3) return "";

            if (format.startsWith("DOWF")) {
                now = getDayOfWeek(d);
                format = format.replace("DOWF", "");
            }
            if (format.startsWith("DOW")) {
                now = getDayOfWeek(d, true);
                format = format.replace("DOW", "");
            }


            try {
                SimpleDateFormat dateFormat = new SimpleDateFormat(format, Locale.US);
                if (d == (null)) return "";
                now = now + dateFormat.format(d.getTime());
            } catch (FormatterClosedException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
                return "";
            }
            return now;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    public static Calendar stringToDate(String d, String format) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(format, Locale.US);
        if (d.equals("")) return null;
        Date c = new Date();
        try {
            c = dateFormat.parse(d);
        } catch (ParseException e) {
            // TODO Auto-generated catch block
            return Calendar.getInstance();
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(c);
        return calendar;

    }

    public static Date getDateTimeFromFormat(String d, String format) {
        if (d == null) {
            return null;
        }
        if (d != (null) && d.length() < 2) {
            return null;
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat(format, Locale.US);
        Date c = new Date();
        try {
            c = dateFormat.parse(d);
        } catch (ParseException e) {
            // TODO Auto-generated catch block
            return null;
        }
        return c;
    }

    public static Date getDateTimeFromFormat(String d) {
        if (d == null) {
            return null;
        }
        if (d != (null) && d.length() < 2) {
            return null;
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("HH:mm dd/MM/yyyy", Locale.US);
        Date c = new Date();
        try {
            c = dateFormat.parse(d);
        } catch (ParseException e) {
            // TODO Auto-generated catch block
            try {

                SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                c = dateFormat2.parse(d);
            } catch (ParseException eb) {
                eb.printStackTrace();

            }
        }
        return c;
    }

    private String getDurationx(Date d1, Date d2) {
        Duration diff = Duration.between(d1.toInstant(), d2.toInstant());


        long days = diff.toDays();
        diff = diff.minusDays(days);
        long hours = diff.toHours();
        diff = diff.minusHours(hours);
        long minutes = diff.toMinutes();
        diff = diff.minusMinutes(minutes);
        long seconds = diff.toMillis();

        StringBuilder formattedDiff = new StringBuilder();
        if (days != 0) {
            if (days == 1) {
                formattedDiff.append(days + " Day ");

            } else {
                formattedDiff.append(days + " Days ");
            }
        }
        if (hours != 0) {
            if (hours == 1) {
                formattedDiff.append(hours + " hour ");
            } else {
                formattedDiff.append(hours + " hours ");
            }
        }
        if (minutes != 0) {
            if (minutes == 1) {
                formattedDiff.append(minutes + " minute ");
            } else {
                formattedDiff.append(minutes + " minutes ");
            }
        }
        if (seconds != 0) {
            if (seconds == 1) {
                formattedDiff.append(seconds + " second ");
            } else {
                formattedDiff.append(seconds + " seconds ");
            }
        }


        return formattedDiff.toString();
    }

    public static int getInfantFee(String from, String to) {

        from = getATA(from);
        to = getATA(to);

        String changDai = "SGNHANSGN,SGNVIISGN,SGNTHDSGN,SGNHPHSGN,SGNVDHSGN,BMVVIIBMV,BMVHPHBMV,HANDLIHAN,BMVTHDBMV,HANCXRHAN,HANPQCHAN,VIICXRVII,HANTBBHAN";
        String changNgan = "SGNDADSGN,SGNUIHSGN,SGNBMVSGN,SGNPQCSGN,SGNHUISGN,SGNTBBSGN,SGNCXRSGN,HANDADHAN,SGNVCLSGN,SGNPXUSGN,SGNDLISGN,HUIDLIHUI";
        String changQuocTe = "SGNBKKSGN,HANBKKHAN,HANHKGHAN";
        String changQuocTe2 = "SGNSINSGN,DADKIXDAD,HANKIXHAN,SGNNRTSGN,SGNPHHSGN";

        if (changDai.contains(from + to)) {
            return 150000;
        } else if (changNgan.contains(from + to)) {
            return 110000;
        } else if (changQuocTe.contains(from + to)) {
            return 300000;
        } else if (changQuocTe2.contains(from + to)) {
            return 700000;
        } else return 0;


    }

    public static int getInfantFeeVJ(String from, String to) {

        from = getATA(from);
        to = getATA(to);

        String changQuocTe = "SGNBKKSGN,HANBKKHAN,HANHKGHAN,SGNICNSGN,SGNTPESGN,HANTPEHAN,SGNKHHSGN,SGNRGNSGN,CXRPVGCXR";
        String changQuocTe2 = "SGNSINSGN,DADKIXDAD,HANKIXHAN";

        if (changQuocTe.contains(from + to)) {
            return 200000;
        } else if (changQuocTe2.contains(from + to)) {
            return 200000;
        } else return 110000;

    }

    public static ArrayList<String> getDayLabel(ArrayList<Calendar> mDays) {
        try {
            ArrayList<String> mLabel = new ArrayList<String>();

            String weekDay = "";
            for (Calendar d : mDays) {

                int dayOfWeek = d.get(Calendar.DAY_OF_WEEK);
                if (Calendar.MONDAY == dayOfWeek) {
                    weekDay = "T2";
                } else if (Calendar.TUESDAY == dayOfWeek) {
                    weekDay = "T3";
                } else if (Calendar.WEDNESDAY == dayOfWeek) {
                    weekDay = "T4";
                } else if (Calendar.THURSDAY == dayOfWeek) {
                    weekDay = "T5";
                } else if (Calendar.FRIDAY == dayOfWeek) {
                    weekDay = "T6";
                } else if (Calendar.SATURDAY == dayOfWeek) {
                    weekDay = "T7";
                } else if (Calendar.SUNDAY == dayOfWeek) {
                    weekDay = "CN";
                }

                mLabel.add(weekDay);
            }
            return mLabel;

        } catch (Exception e) {

            return null;

        }

    }

    public static int getDayId(ArrayList<Calendar> mDays, Calendar day) {
        try {
//			Collections.sort(mDays, new Comparator<Calendar>() {
//				public int compare(Calendar s1, Calendar s2) {
//					return s1.compareTo(s2);
//				}
//				;
//			});

            int id = 0;
            for (int i = 0; i < mDays.size(); i++) {

                if (mDays.get(i).get(Calendar.DAY_OF_YEAR) == day.get(Calendar.DAY_OF_YEAR)) {

                    id = i;

                }

            }
            return id;

        } catch (Exception e) {

            return 2;

        }

    }

    public static String getFullDayLabel(Calendar mDay) {
        try {


            String weekDay = "";
            SimpleDateFormat colFormat = new SimpleDateFormat("dd-MM-yyyy", Locale.US);

            int dayOfWeek = mDay.get(Calendar.DAY_OF_WEEK);
            if (Calendar.MONDAY == dayOfWeek) {
                weekDay = "Thứ 2";
            } else if (Calendar.TUESDAY == dayOfWeek) {
                weekDay = "Thứ 3";
            } else if (Calendar.WEDNESDAY == dayOfWeek) {
                weekDay = "Thứ 4";
            } else if (Calendar.THURSDAY == dayOfWeek) {
                weekDay = "Thứ 5";
            } else if (Calendar.FRIDAY == dayOfWeek) {
                weekDay = "Thứ 6";
            } else if (Calendar.SATURDAY == dayOfWeek) {
                weekDay = "Thứ 7";
            } else if (Calendar.SUNDAY == dayOfWeek) {
                weekDay = "Chủ Nhật";
            }


            return weekDay + ", " + colFormat.format(mDay.getTime());

        } catch (Exception e) {

            return null;

        }

    }

    public static boolean isNameFormat(String name) {
        name = name.trim();
        Pattern p = Pattern.compile("^[a-zA-Z]+((\\s)?((\\'|\\-|\\.)?([a-zA-Z])+))*$");
        Matcher m = p.matcher(name);
        boolean b = m.matches();

        boolean c = false;
        if (name.contains(" ")) c = true;
        return (b && c);
    }

    public static String getAirPortName(String code, boolean isShort) {

        try {
            if (code == (null)) {
                return "";
            }
            String newCode = code.length() > 2 ? code.substring(code.length() - 3, code.length()) : code;

            String name = "";
            if (AIRPORTLIST != null && !AIRPORTLIST.isNull("data")) {
                JSONArray listAirports = AIRPORTLIST.getJSONArray("data");
                for (int i = 0; i < listAirports.length(); i++) {
                    JSONObject airport = listAirports.getJSONObject(i);
                    if (airport.getString("code").equals(code)) {
                        if (isShort)
                            return airport.getString("short_name");
                        return airport.getString("city");
                    }
                    ;
                }
            }
            return code.contains("-") ? code.substring(0, code.length() - 5) : code;

        } catch (Exception e) {
            AppConfigs.logException(e);
            return code;
        }
    }

    public static void showAlertDialog(Context context, String title, String message, Boolean finishOnOk, Boolean openWifi) {
        try {
            AlertDialog alertDialog = new AlertDialog.Builder(context).create();
            alertDialog.setTitle(title);
            alertDialog.setMessage(message);
            alertDialog.setIcon(R.drawable.ic_bell_alert);
            if (finishOnOk) {
                alertDialog.setCanceledOnTouchOutside(false);
                alertDialog.setCancelable(false);
            }
            alertDialog.setButton(DialogInterface.BUTTON_POSITIVE, "OK", new DialogInterface.OnClickListener() {

                public void onClick(DialogInterface dialog, int which) {
                    if (finishOnOk) {
//                        android.os.Process.killProcess(android.os.Process.myPid());
//                        System.exit(1);
                        ((Activity) context).finish();
                    } else {

                        try {
                            if (openWifi) {
                                Intent intent = new Intent(Intent.ACTION_MAIN);
                                intent.setClassName("com.android.settings", "com.android.settings.wifi.WifiSettings");
                                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                                context.getApplicationContext().startActivity(intent);
                            }

                        } catch (Exception e) {
                            AppConfigs.Log("Err", e.toString());

                        }
                    }
                }
            });
            alertDialog.setButton(DialogInterface.BUTTON_NEGATIVE, "GỌI PHÒNG VÉ", new DialogInterface.OnClickListener() {
                public void onClick(DialogInterface dialog, int which) {
                    try {
                        String p = "";
                        Intent i = new Intent(Intent.ACTION_DIAL);
                        p = "tel:" + AppConfigs.getInstance().getConfig().getString("hotline");

                        i.setData(Uri.parse(p));
                        i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        context.getApplicationContext().startActivity(i);
                    } catch (Exception e) {
                        AppConfigs.Log("Err", e.toString());

                    }
                }
            });

            alertDialog.show();
        } catch (Exception e) {
            e.printStackTrace();
            AppConfigs.logException(e);
        }
    }


    public static void commonInit(Context mContext) {
        try {

            SharedPreferences settings = mContext.getSharedPreferences("12BAY-APP-CONFIG", 0);
            UPhone = (settings.getString("Uphone", ""));
            UDisplayName = (settings.getString("UdisplayName", "12bay.vn"));
            Email = (settings.getString("email", ""));
            UEmail = (settings.getString("Uemail", ""));
            DEVICE_ID = FirebaseInstanceId.getInstance().getId();


            Uid = (settings.getString("Uid", FirebaseInstanceId.getInstance().getId()));
            UPhoto = (settings.getString("Uphoto", ""));
            FCM_TOKEN = (settings.getString("FCM-TOKEN", ""));

            AIRPORTLIST = new JSONObject(settings.getString("AIRPORTLIST", "{}"));

            ServerUtilities.postAppSetting(mContext, "INITSETING");

            if (AIRPORTLIST.isNull("data")) {
                getListAirport(mContext);
            }
        } catch (Exception e) {
            AppConfigs.logException(e);
            e.printStackTrace();
        }

    }

    public static void commonSave(Context mContext, String displayName, String email, String uid, String photo, String phone) {
        try {
            isNewInfo = true;
            SharedPreferences settings = mContext.getSharedPreferences("12BAY-APP-CONFIG", 0);
            SharedPreferences.Editor editor = settings.edit();
            if (photo != (null) && !photo.equals("")) {
                editor.putString("Uphoto", photo);
                UPhoto = photo;

            }
            if (displayName != (null) && !displayName.equals("")) {
                editor.putString("UdisplayName", displayName);
                UDisplayName = displayName;

            }
            if (email != null && !email.equals("")) {
                editor.putString("Uemail", email);
                UEmail = email;

            }
            if (uid != (null) && !uid.equals("")) {
                editor.putString("Uid", uid);
                Uid = uid;

            } else {
                Uid = FirebaseInstanceId.getInstance().getId();
                editor.putString("Uid", Uid);
            }
            if (phone != (null) && !phone.equals("")) {
                editor.putString("Uphone", phone);
                UPhone = phone;
            }
            editor.apply();
        } catch (Exception e) {
            AppConfigs.logException(e);
        }
    }

    public static boolean sendSms(Context context, String text, String numbersStr) {

        Uri uri = Uri.parse("sms:" + numbersStr);
        Intent intent = new Intent();
        intent.setData(uri);
        intent.putExtra(Intent.EXTRA_TEXT, text);
        intent.putExtra("sms_body", text);
        intent.putExtra("address", numbersStr);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            intent.setAction(Intent.ACTION_SENDTO);
            String defaultSmsPackageName = Telephony.Sms.getDefaultSmsPackage(context);
            if (defaultSmsPackageName != null) {
                intent.setPackage(defaultSmsPackageName);
            }
        } else {
            intent.setAction(Intent.ACTION_VIEW);
            intent.setType("vnd.android-dir/mms-sms");
        }

        try {
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_MULTIPLE_TASK);
            context.startActivity(intent);
        } catch (ActivityNotFoundException e) {
            e.printStackTrace();
            return false;
        }

        return true;
    }

    public static void getListAirport(Context mcontext) {
        String tag_json_obj = "json_get_airport";
        String url = AppConfigs.getInstance().getConfig().getString("root_api") + "/api/v1/AirLines/AirPort/";

        JsonObjectRequest jsonObjReq = new JsonObjectRequest(Request.Method.GET,
                url, null,
                new Response.Listener<JSONObject>() {

                    @Override
                    public void onResponse(JSONObject response) {

                        if (!response.isNull("data")) {
                            SharedPreferences settings = mcontext.getSharedPreferences("12BAY-APP-CONFIG", 0);
                            SharedPreferences.Editor editor = settings.edit();
                            editor.putString("AIRPORTLIST", response.toString());
                            editor.apply();
                            AIRPORTLIST = response;
                        }
                    }
                }, new Response.ErrorListener() {

            @Override
            public void onErrorResponse(VolleyError error) {

            }
        });
        AppController.getInstance().addToRequestQueue(jsonObjReq, tag_json_obj);
    }

    public static Intent ConvertLinkAction(Context mContext, Uri url) {

        Intent in = new Intent(mContext, HomeActivity.class);

        if (url != null) {
            AppConfigs.Log("action_link", url.toString());
            try {
                String link = url.toString().replace("www.", "");

                link = link.replace("http://r.12bay.vn", "https://12bay.vn/r");

                if (link.contains("12bay.vn/san-ve-re") || link.contains("12bay.vn/redirect/san-ve-re")) {
                    if (link.contains("/20")) {

                        String tmp = link.substring(link.indexOf("san-ve-re") + 9).replace("/", "");

                        in = new Intent(mContext, MonthViewActivity.class);
                        in.putExtra("origin", tmp.substring(0, 3));
                        in.putExtra("destination", tmp.substring(3, 6));
                        in.putExtra("monthName", tmp.substring(6, 12));

                        return in;


                    } else {

                        in = new Intent(mContext, HomeActivity.class);
                        String routeString = link.substring(link.indexOf("san-ve-re") + 9).replace("/", "");
                        if (routeString.length() >= 6) {
                            in.putExtra("origin", routeString.substring(0, 3));
                            in.putExtra("destination", routeString.substring(3, 6));
                        }

                        in.putExtra("PRICEBOARD", true);
                        if (in.hasExtra("PRICEBOARD")) {
                            AppConfigs.Log("PRICEBOARD", link);
                        }
                        return in;
                    }
                }
                if (link.contains("OpenXBrowser")) {
                    in = new Intent(Intent.ACTION_VIEW, Uri.parse(link.replace("OpenXBrowser", "")));
                    return in;
                }
                if (link.startsWith("https://blog.12bay.vn") || link.startsWith("http://r.12bay.vn") || link.startsWith("https://12bay.vn/ho-tro")) {
                    in = new Intent(mContext, WebViewActivity.class);
                    in.putExtra("promoUrl", link);

                    return in;
                }

                if (link.startsWith("https://12bay.vn/diem-thuong")) {
                    if ((mContext.getClass() == HomeActivity.class)) {
                        ((HomeActivity) mContext).clickNavigation(4);
                    }
                    return null;
                }
                if (link.startsWith("https://12bay.vn/voucher")) {

                    String token = link.replace("https://12bay.vn/voucher/", "");
                    in = new Intent(mContext, RewardActivity.class);
                    in.putExtra("action", "viewVoucher");
                    in.putExtra("token", token);
                    return in;
                }

                if (link.startsWith("https://12bay.vn/promotion")) {

                    String token = link.replace("https://12bay.vn/promotion/", "");
                    in = new Intent(mContext, RewardActivity.class);
                    in.putExtra("action", "viewPromotion");
                    in.putExtra("token", token);
                    return in;
                }

                if (link.startsWith("https://12bay.vn/r/") || link.startsWith("https://12bay.vn/thanh-toan") || link.startsWith("https://12bay.vn/xu-ly-don-hang") || link.startsWith("https://12bay.vn/don-hang")) {

                    String token = link.replace("https://12bay.vn/r/", "").replace("https://12bay.vn/thanh-toan/", "").replace("https://12bay.vn/xu-ly-don-hang/", "").replace("https://12bay.vn/don-hang/", "");
                    if (token.length() >= 32) {
                        token = token.substring(0, 32);
                    }

                    AppConfigs.Log("token", token);

                    if (link.toLowerCase().contains("?train")) {
                        BookingTrain booking = new BookingTrain();
                        booking.setToken(token);
                        in = new Intent(mContext, TrainBookingViewActivity.class);
                        in.putExtra("BookingInfo", booking);

                    } else if (link.toLowerCase().contains("?bus")) {
                        BookingBus booking = new BookingBus();
                        booking.setToken(token);
                        in = new Intent(mContext, BusBookingViewActivity.class);
                        in.putExtra("BookingInfo", booking);
                    } else {

                        in = new Intent(mContext, PnrActivity.class);
                        in.putExtra("token", token);
                        return in;
                    }
                }
                if (link.startsWith("https://12bay.vn/theo-doi-gia")) {
                    String flightWatchId = link.replace("https://12bay.vn/theo-doi-gia/", "");
                    in = new Intent(mContext, FlightWachesViewActivity.class);
                    in.putExtra("flightWatchId", Integer.parseInt(flightWatchId));
                    in.putExtra("fromNoti", true);
                    return in;
                }
                if (link.startsWith("https://12bay.vn/ve-tau")) {
                    in = new Intent(mContext, TrainSearchActivity.class);
                    return in;
                }
                if (link.startsWith("https://12bay.vn/ve-xe-khach")) {
                    in = new Intent(mContext, BusSearchActivity.class);
                    return in;
                }

                if (link.startsWith("https://12bay.vn/tour/cat")) {
                    in = new Intent(mContext, TourListActivity.class);
                    in.putExtra("link", link);
                    return in;
                }
                if (link.startsWith("https://12bay.vn/tour/detail")) {
                    in = new Intent(mContext, TourDetailActivity.class);
                    in.putExtra("link", link);
                    return in;
                }

                if (link.startsWith("https://12bay.vn/lich-bay")) {
                    in = new Intent(mContext, FlightHistoryActivityV2.class);
                    in.putExtra("link", link);
                    return in;
                }

                if (link.startsWith("https://12bay.vn/chuyen-bay")) {

                    String adult = (url.getQueryParameter("adult") == (null) ? "1" : url.getQueryParameter("adult"));
                    String child = (url.getQueryParameter("child") == (null) ? "0" : url.getQueryParameter("child"));
                    String infant = (url.getQueryParameter("infant") == (null) ? "0" : url.getQueryParameter("infant"));
                    String returnTime = url.getQueryParameter("returnDate") == (null) ? "" : url.getQueryParameter("returnDate");

                    String from = "";
                    String to = "";
                    String departureTime = "";

                    String[] urlPart = link.split("/");
                    if (urlPart.length >= 6) {
                        from = urlPart[4];
                        to = urlPart[5];
                    }
                    if (urlPart.length >= 7) {
                        departureTime = urlPart[6];
                    }
                    if (urlPart.length >= 10) {
                        adult = urlPart[7];
                        child = urlPart[8];
                        infant = urlPart[9];
                    }
                    if (urlPart.length >= 11) {
                        returnTime = urlPart[10];
                    }

                    boolean is_round_trip = returnTime.equals("") ? false : true;
                    if (departureTime != "") {
                        in = new Intent(mContext, SearchResult.class);
                        in.putExtra("page", 0);
                        in.putExtra("originCode", from.toUpperCase());
                        in.putExtra("destinationCode", to.toUpperCase());
                        in.putExtra("departureTime", Common.dateToString(Common.stringToDate(departureTime, "yyyyMMdd").getTime(), "yyyy-MM-dd"));
                        in.putExtra("returnTime", is_round_trip ? Common.dateToString(Common.stringToDate(returnTime, "yyyyMMdd").getTime(), "yyyy-MM-dd") : "");
                        in.putExtra("adult", adult);
                        in.putExtra("child", child);
                        in.putExtra("infant", infant);
                        in.putExtra("isRoundTrip", is_round_trip);
                    } else if (from != "" && to != "") {

                        in = new Intent(mContext, SearchActivityV2.class);
                        in.putExtra("originCode", from.toUpperCase());
                        in.putExtra("destinationCode", to.toUpperCase());
                    }


                }
            } catch (Exception e) {
                AppConfigs.logException(e);
                e.printStackTrace();
            }
        }
        return in;
    }

    public static void showPaxNew(LinearLayout paxInPut, Context mContext) {

        //NEW ITEM PAX INPUT
        LinearLayout item;
        TextView spinItem;
        TextView paxNameView, txtPaxBag;
        paxInPut.removeAllViews();
        try {
            JSONObject pax_list = BOOKING_DETAIL.getJSONObject("pax_info");
            AppConfigs.Log("pax_list", pax_list.toString());
            String paxNameList = "";
            //ALDUT PAX INPUT
            for (int i = 0; i < pax_list.getJSONArray("adult").length(); i++) {
                //item
                boolean isBag = false;
                JSONObject pax = pax_list.getJSONArray("adult").getJSONObject(i);
                AppConfigs.Log("pax", pax.toString());
                String paxName = pax.getString("fullname");
                if (!paxName.equals("")) {

                    item = new LinearLayout(mContext);
                    item.setId(i);
                    item.setOrientation(LinearLayout.HORIZONTAL);
                    item.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT,
                            AbsListView.LayoutParams.WRAP_CONTENT));
                    item.setPadding(25, 10, 10, 10);
                    //spinner
                    spinItem = new TextView(mContext);
                    spinItem.setLayoutParams(new LinearLayout.LayoutParams(190, Spinner.LayoutParams.WRAP_CONTENT));
                    spinItem.setPadding(0, 0, 40, 0);

                    //BAG
                    txtPaxBag = new TextView(mContext);
                    txtPaxBag.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.FILL_PARENT,
                            LinearLayout.LayoutParams.WRAP_CONTENT));
                    txtPaxBag.setPadding(25, 5, 10, 5);
                    String pag_text = "";
                    String pag_textSMS = "";
                    JSONObject pag = pax.getJSONObject("baggage");
                    isBag = false;
                    if (!pag.getString("text").equals("Không thêm") && !pag.getString("text").equals("Kí gửi 0Kg")) {
                        pag_text += "{faw_suitcase} Hành lý lượt đi: " + pag.getString("value") + "kg ";
                        pag_textSMS += "D" + pag.getInt("value");
                        isBag = true;

                    }
                    JSONObject pagR = pax.getJSONObject("returnBaggage");

                    if (!pagR.getString("text").equals("Không thêm") && !pagR.getString("text").equals("Kí gửi 0Kg")) {
                        pag_text += " - Lượt về: " + pagR.getString("value") + "kg";
                        pag_textSMS += "V" + pag.getInt("value");
                        isBag = true;

                    }

                    txtPaxBag.setText(pag_text);
                    new Iconics.Builder().style(new RelativeSizeSpan(0.5f)).on(txtPaxBag).build();

                    String title = (pax.getJSONObject("title").isNull("text") ? "" : pax.getJSONObject("title").getString("text"));
                    spinItem.setText(title);

                    item.addView(spinItem);
                    //EDIT TEXT
                    paxNameView = new TextView(mContext);
                    paxNameView.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.FILL_PARENT,
                            LinearLayout.LayoutParams.WRAP_CONTENT));
                    paxNameView.setBackgroundResource(R.drawable.edit_text);
                    paxNameView.setTextColor(Color.BLACK);
                    paxNameView.setPadding(20, 10, 10, 10);
                    paxNameView.setInputType(InputType.TYPE_TEXT_FLAG_CAP_CHARACTERS);
                    paxNameView.setText(paxName);
                    item.addView(paxNameView);

                    paxNameList += "(" + title + ")" + paxName + "|" + pag_textSMS;
                    //Add to layout
                    paxInPut.addView(item);
                    if (isBag) paxInPut.addView(txtPaxBag);
                }

            }
            for (int i = 0; i < pax_list.getJSONArray("child").length(); i++) {
                //item
                boolean isBag = false;
                JSONObject pax = pax_list.getJSONArray("child").getJSONObject(i);
                String paxName = pax.getString("fullname");
                if (!paxName.equals("")) {

                    item = new LinearLayout(mContext);
                    item.setId(i);
                    item.setOrientation(LinearLayout.HORIZONTAL);
                    item.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT,
                            AbsListView.LayoutParams.WRAP_CONTENT));
                    item.setPadding(25, 10, 10, 10);
                    //spinner
                    spinItem = new TextView(mContext);
                    spinItem.setLayoutParams(new LinearLayout.LayoutParams(190, Spinner.LayoutParams.WRAP_CONTENT));
                    //BAG
                    txtPaxBag = new TextView(mContext);
                    txtPaxBag.setPadding(25, 5, 10, 5);
                    txtPaxBag.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.FILL_PARENT,
                            LinearLayout.LayoutParams.WRAP_CONTENT));
                    spinItem.setPadding(0, 0, 40, 0);

                    String pag_text = "";
                    String pag_textSMS = "";
                    JSONObject pag = pax.getJSONObject("baggage");
                    isBag = false;
                    if (!pag.getString("text").equals("Không thêm") && !pag.getString("text").equals("Kí gửi 0Kg")) {
                        pag_text += "{faw_suitcase} Hành lý lượt đi: " + pag.getString("value") + "kg ";
                        pag_textSMS += "D" + pag.getInt("value");
                        isBag = true;

                    }

                    JSONObject pagR = pax.getJSONObject("returnBaggage");

                    if (!pagR.getString("text").equals("Không thêm") && !pagR.getString("text").equals("Kí gửi 0Kg")) {
                        pag_text += " - Lượt về: " + pagR.getString("value") + "kg";
                        pag_textSMS += "V" + pag.getInt("value");
                        isBag = true;

                    }

                    txtPaxBag.setText(pag_text);
                    new Iconics.Builder().style(new RelativeSizeSpan(0.5f)).on(txtPaxBag).build();

                    String title = (pax.getJSONObject("title").isNull("text") ? "" : pax.getJSONObject("title").getString("text"));
                    spinItem.setText(title);

                    item.addView(spinItem);
                    //EDIT TEXT
                    paxNameView = new TextView(mContext);
                    paxNameView.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.FILL_PARENT,
                            LinearLayout.LayoutParams.WRAP_CONTENT));
                    paxNameView.setBackgroundResource(R.drawable.edit_text);
                    paxNameView.setTextColor(Color.BLACK);
                    paxNameView.setInputType(InputType.TYPE_TEXT_FLAG_CAP_CHARACTERS);
                    paxNameView.setText(paxName);
                    paxNameView.setPadding(20, 10, 10, 10);
                    item.addView(paxNameView);
                    paxNameList += "(" + title + ")" + paxName + "|" + pag_textSMS;
                    //Add to layout
                    paxInPut.addView(item);
                    if (isBag) paxInPut.addView(txtPaxBag);
                }

            }
            for (int i = 0; i < pax_list.getJSONArray("infant").length(); i++) {
                //item
                boolean isBag = false;
                JSONObject pax = pax_list.getJSONArray("infant").getJSONObject(i);
                String paxName = pax.getString("fullname");
                if (!paxName.equals("")) {

                    item = new LinearLayout(mContext);
                    item.setId(i);
                    item.setOrientation(LinearLayout.HORIZONTAL);
                    item.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT,
                            AbsListView.LayoutParams.WRAP_CONTENT));
                    item.setPadding(25, 10, 10, 10);
                    //spinner
                    spinItem = new TextView(mContext);
                    spinItem.setLayoutParams(new LinearLayout.LayoutParams(190, Spinner.LayoutParams.WRAP_CONTENT));
                    spinItem.setPadding(0, 0, 40, 0);
                    //BAG
                    txtPaxBag = new TextView(mContext);
                    txtPaxBag.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.FILL_PARENT,
                            LinearLayout.LayoutParams.WRAP_CONTENT));
                    txtPaxBag.setPadding(25, 5, 10, 5);
                    isBag = true;

                    txtPaxBag.setText("{faw_birthday_cake} Ngày sinh: " + pax.getString("birthday"));
                    new Iconics.Builder().style(new RelativeSizeSpan(0.5f)).on(txtPaxBag).build();

                    String title = pax.getJSONObject("title").getString("text");
                    spinItem.setText(title);

                    item.addView(spinItem);
                    //EDIT TEXT
                    paxNameView = new TextView(mContext);
                    paxNameView.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.FILL_PARENT,
                            LinearLayout.LayoutParams.WRAP_CONTENT));
                    paxNameView.setBackgroundResource(R.drawable.edit_text);
                    paxNameView.setTextColor(Color.BLACK);
                    paxNameView.setInputType(InputType.TYPE_TEXT_FLAG_CAP_CHARACTERS);
                    paxNameView.setText(paxName);
                    paxNameView.setPadding(20, 10, 10, 10);
                    item.addView(paxNameView);

                    paxNameList += "(" + title + ")" + paxName + "|" + pax.getString("birthday");
                    //Add to layout
                    paxInPut.addView(item);
                    if (isBag) paxInPut.addView(txtPaxBag);
                }

            }
        } catch (Exception e) {
            AppConfigs.logException(e);
            e.printStackTrace();
        }
    }

    public static String getDateTextCountDown(long millisUntilFinished, long startTime) {
        Long serverUptimeSeconds = (millisUntilFinished - startTime) / 1000;
        Long hoursLeft = ((serverUptimeSeconds % 86400) / 3600);
        Long minutesLeft = ((serverUptimeSeconds % 86400) % 3600) / 60;
        Long secondsLeft = ((serverUptimeSeconds % 86400) % 3600) % 60;
        String hoursLeftText = ((hoursLeft < 10 && hoursLeft >= 0) ? ("0" + hoursLeft) : hoursLeft + "");
        String minutesLeftText = ((minutesLeft < 10 && minutesLeft >= 0) ? ("0" + minutesLeft) : minutesLeft + "");
        String secondsLeftText = ((secondsLeft < 10 && secondsLeft >= 0) ? ("0" + secondsLeft) : secondsLeft + "");
        String textStatus = hoursLeftText + ":" + minutesLeftText + ":" + secondsLeftText;
        return textStatus;
    }


    public static InputFilter editTextUpCaseFilter() {
        return new InputFilter() {
            @Override
            public CharSequence filter(CharSequence cs, int start,
                                       int end, Spanned spanned, int dStart, int dEnd) {
                // TODO Auto-generated method stub
                if (cs.equals("")) {
                    return cs;
                }
                //unAccent(cs.toString()).toUpperCase();
                AppConfigs.Log("xxxxx", cs.toString());

                if (cs.toString().matches("[a-z]+")) {
                    return cs.toString().toUpperCase();
                }
                if (cs.toString().matches("[A-Z ]+")) {
                    return cs;
                }
                return "";
            }
        };
    }

    public static int createID() {
        Date now;
        now = new Date();
        int id = Integer.parseInt(new SimpleDateFormat("ddHHmmss", Locale.US).format(now));
        return id;
    }

    public static String addUrlMobileParams(String url) {
        String link = "";
        if (url.contains("?")) {
            link = (url + "&app=true&device=phone&utm_source=MOBILE&utm_medium=ANDROID&source=ANDROID&ver=" + BuildConfig.VERSION_CODE);
        } else {
            link = (url + "?app=true&device=phone&utm_source=MOBILE&source=ANDROID&utm_medium=ANDROID&ver=" + BuildConfig.VERSION_CODE);
        }

        return link;

    }

    public static Bitmap getBitmapFromVectorDrawable(Context context, int drawableId) {
        Drawable drawable = AppCompatResources.getDrawable(context, drawableId);
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
            drawable = (DrawableCompat.wrap(drawable)).mutate();
        }

        Bitmap bitmap = Bitmap.createBitmap(drawable.getIntrinsicWidth(),
                drawable.getIntrinsicHeight(), Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        drawable.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
        drawable.draw(canvas);

        return bitmap;

    }

    public static void createShortcut(@NonNull Activity activity) {

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) { // code for adding shortcut on pre oreo device

            ShortcutManager shortcutManager = activity.getSystemService(ShortcutManager.class);
            assert shortcutManager != null;
            List<ShortcutInfo> shortCutList = new ArrayList<>();

            Intent shortcutIntent = new Intent(activity, HomeActivity.class);
            shortcutIntent.putExtra("PRICEBOARD", true);
            shortcutIntent.setAction(Intent.ACTION_ALL_APPS);
            shortcutIntent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
            ShortcutInfo pinShortcutInfo = new ShortcutInfo.Builder(activity, "browser-shortcut-1")
                    .setIntent(shortcutIntent)
                    .setIcon(Icon.createWithResource(activity, R.drawable.ic_flight_watched_btn))
                    .setShortLabel("Săn vé rẻ")
                    .build();
            shortCutList.add(pinShortcutInfo);

            Intent shortcutTrain = new Intent(activity, TrainSearchActivity.class);
            shortcutTrain.setAction(Intent.ACTION_ALL_APPS);
            shortcutTrain.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
            ShortcutInfo pinShortcutInfosTrain = new ShortcutInfo.Builder(activity, "browser-shortcus-3")
                    .setIntent(shortcutTrain)
                    .setIcon(Icon.createWithResource(activity, R.drawable.ic_transportation))
                    .setShortLabel("Đặt vé tàu")
                    .build();

            shortCutList.add(pinShortcutInfosTrain);

            Intent shortcutBus = new Intent(activity, BusSearchActivity.class);
            shortcutBus.setAction(Intent.ACTION_ALL_APPS);
            shortcutBus.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
            ShortcutInfo pinShortcutInfosBus = new ShortcutInfo.Builder(activity, "browser-shortcus-4")
                    .setIntent(shortcutBus)
                    .setIcon(Icon.createWithResource(activity, R.drawable.ic_bus_line))
                    .setShortLabel("Đặt vé xe khách")
                    .build();

            shortCutList.add(pinShortcutInfosBus);

            Intent shortcutFlight = new Intent(activity, SearchActivityV2.class);
            shortcutFlight.setAction(Intent.ACTION_ALL_APPS);
            shortcutFlight.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
            ShortcutInfo pinShortcutFlight = new ShortcutInfo.Builder(activity, "browser-shortcus-2")
                    .setIntent(shortcutFlight)
                    .setIcon(Icon.createWithResource(activity, R.drawable.icon_flight_flat))
                    .setShortLabel("Đặt vé máy bay")
                    .build();

            shortCutList.add(pinShortcutFlight);


            shortcutManager.setDynamicShortcuts(shortCutList);

        }
    }

    public static String join(String separator, String[] input) {

        if (input == null || input.length <= 0) return "";

        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < input.length; i++) {

            sb.append(input[i]);

            // if not the last item
            if (i != input.length - 1) {
                sb.append(separator);
            }

        }

        return sb.toString();

    }

    public static long stringToLong(String in) {
        try {
            in = in.replace("₫", "").replaceFirst("[^0-9]", "");
            return Long.valueOf(in);
        } catch (Exception e) {
            e.printStackTrace();
            return -1;
        }

    }

    public static boolean isOldChrome(WebView mWebview) {
        Pattern mPattern = Pattern.compile("Chrome/([0-9]+)");
        Matcher matcher = mPattern.matcher(mWebview.getSettings().getUserAgentString());
        if (matcher.find()) {
            if (Integer.valueOf(matcher.group().replace("Chrome/", "")) <= 54) {
                return true;
            }
        }
        return false;
    }

    public static String getV2Link(String link) {
        link.replace("https://12bay.vn", "https://v2.12bay.vn").replace("https://pay.12bay.vn", "https://v2.12bay.vn");
        return link;
    }

    public static Spanned convertHTML(String text) {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
            return Html.fromHtml(text, Html.FROM_HTML_MODE_LEGACY);
        } else {
            return Html.fromHtml(text);
        }
    }

    public static ArrayList<BarData> convertTrendListToBardata(List<FareTrend> fareTrends, Boolean baseFare) {
        ArrayList<BarData> dataList = new ArrayList<BarData>();
        try {
            for (FareTrend fare : fareTrends) {
                int price = fare.getMin_adult_price();
                if (baseFare) price = fare.getMin_price();

                BarData bar = new BarData(Common.dateToString(fare.getDate().getValue(), "dd/MM"), price + 0f, Common.dinhDangTien(price));
                dataList.add(bar);
            }

            return dataList;
        } catch (Exception e) {
            return dataList;
        }

    }

    public static Bitmap getBitmapfromUrl(String imageUrl) {
        try {
            if (imageUrl == null) {
                return null;
            } else if (imageUrl.length() < 4) {
                return null;
            }

            URL url = new URL(imageUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setDoInput(true);
            connection.connect();
            InputStream input = connection.getInputStream();
            Bitmap bitmap = BitmapFactory.decodeStream(input);
            return bitmap;

        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            return null;

        }
    }

    public static Date addDay(Date date, int i) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DAY_OF_YEAR, i);
        return cal.getTime();
    }

    //   public static byte[] ivbyte = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
    //private static final String TRANSFORMATION = "AES/CBC/PKCS5Padding";
//    public static String CryptoAESencrypt(String input, String key) {
//        try {
//            byte[] bytes = input.getBytes();
//            Cipher ciper = Cipher.getInstance(TRANSFORMATION);
//
//            IvParameterSpec iv = new IvParameterSpec(ivbyte, 0, ciper.getBlockSize());
//            SecretKey secret = new SecretKeySpec(key.getBytes(), "AES");
//
//            ciper.init(Cipher.ENCRYPT_MODE, secret, iv);
//            byte[] result = ciper.doFinal(bytes);
//            return Base64.encodeToString(result, Base64.NO_WRAP);
//        } catch (Exception e) {
//            System.out.println(e);
//        }
//        return null;
//    }

    public static String removeWordPhoneFormat(String phone) {
        phone = phone.replace("+84", "0");
        return phone;
    }

    public static String addWorldPhoneFormat(String phone) {
        if (phone == null) {
            return "";
        }
        if (phone.startsWith("+")) {
            return phone;
        }

        if (phone.startsWith("0")) {
            phone = "84" + phone.substring(1);
        }


        return "+" + phone;
    }
}
