package com.hqt.datvemaybay;


import android.annotation.SuppressLint;
import android.app.ProgressDialog;
import android.content.SharedPreferences;
import android.graphics.Bitmap;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;

import androidx.appcompat.widget.Toolbar;

import android.view.View;
import android.view.WindowManager;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ProgressBar;
import android.widget.Toast;


import com.github.clans.fab.FloatingActionButton;
import com.github.clans.fab.FloatingActionMenu;
import com.hqt.util.AppConfigs;
import com.hqt.view.ui.BaseActivity;


@SuppressLint("SetJavaScriptEnabled")
public class PaymentActivity extends BaseActivity {
    AsyncTask<Void, Void, Void> mRegisterTask;
    private WebView webView;
    ProgressDialog dialog;
    Boolean isConnectionExist = false, error = true;
    CheckInternet check;
    int appVer = 9999;
    boolean loadErr = false;
    String toListCode = "";
    FloatingActionMenu fabMenu;
    FloatingActionButton fabFillter, fabAll;
    final int REQUEST_CODE_FILLTER = 1;
    Boolean isShowTip = false;
    Toolbar toolbar;
    ProgressBar progressBar;
    String promoLink;
    String orderId = "#";
    SharedPreferences settings;

    @Override
    public int getLayoutId() {
        return R.layout.web_view;
    }

    @SuppressLint("AddJavascriptInterface")
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);


        promoLink = getIntent().getExtras().getString("paymentUrl", null);
        orderId = getIntent().getExtras().getString("orderId", null);
        toolbar = getToolbar();
        
        toolbar.setSubtitle("Đơn hàng: #" + orderId);
        toolbar.setTitle("Thanh toán");

        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayShowHomeEnabled(true);
        toolbar.setNavigationIcon(R.drawable.ic_action_back_home);
        progressBar = findViewById(R.id.progressBar);

        check = new CheckInternet(getApplicationContext());
        isConnectionExist = check.checkMobileInternetConn();

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            getWindow().setFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS,
                    WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        }

        webView = findViewById(R.id.webView1);
        WebSettings webSettings = webView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setAllowFileAccess(false);
        webView.addJavascriptInterface(new WebAppInterface(this), "Android");

        webView.setWebViewClient(new WebViewClient() {
            private int webViewPreviousState;
            private final int PAGE_STARTED = 0x1;

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                view.loadUrl(url);
                return true;
            }

            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
                webViewPreviousState = PAGE_STARTED;

            }

            @Override
            public void onPageFinished(WebView view, String url) {

                progressBar.setVisibility(View.GONE);
                if (webViewPreviousState == PAGE_STARTED) {
                    try {
                        if (dialog != null && dialog.isShowing()) dialog.dismiss();
                    } catch (Exception e) {
                        AppConfigs.logException(e);
                    }
                }

            }

            @Override
            public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                // Do something

                loadErr = true;
                Toast.makeText(getApplicationContext(), "Vui lòng kiểm tra lại kết nối", Toast.LENGTH_SHORT).show();
            }

        });

        if (isConnectionExist) {
            webView.loadUrl(promoLink);
        } else {
            Common.showAlertDialog(PaymentActivity.this, ":( Không có Internet",
                    "Xin vui lòng kiểm tra lại Wifi/3G để tiếp tục", false, true);
        }

    }


    @Override
    public void onBackPressed() {
        if (webView.canGoBack() && !loadErr) {
            webView.goBack();
        } else {
            finish();
        }
    }


    /**
     * Receiving push messages
     */


    @Override
    protected void onDestroy() {
        super.onDestroy();
    }


}