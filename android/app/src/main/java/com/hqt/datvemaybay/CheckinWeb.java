package com.hqt.datvemaybay;


import android.app.ProgressDialog;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;

import androidx.appcompat.widget.Toolbar;

import android.view.View;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceResponse;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.Toast;

import com.github.clans.fab.FloatingActionButton;
import com.github.clans.fab.FloatingActionMenu;
import com.hqt.util.AppConfigs;
import com.hqt.view.ui.BaseActivity;
import com.hqt.view.ui.HomeActivity;

import java.net.URL;

import im.delight.android.webview.AdvancedWebView;


public class CheckinWeb extends BaseActivity {

    AdvancedWebView webView;
    ProgressDialog dialog;
    ProgressBar processbar;
    LinearLayout emptyState;
    boolean isCheckin = false;
    Toolbar toolbar;
    private int mProgressStatus = 0;
    int count = 5;
    boolean loadErr = false;
    FloatingActionMenu fabMenu;
    FloatingActionButton fabSend, fabCall, fabBook, fabPay;
    Intent in;
    String urlCheckin;
    static String car = "";

    @Override
    protected int getLayoutId() {
        return R.layout.web_blog_view;
    }

    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        getToolbar().setTitle("Checkin Online");
        getToolbar().setSubtitle("Làm thủ tục trực tuyến");

        setSupportActionBar(getToolbar());
        getSupportActionBar().setDisplayShowHomeEnabled(true);
        getToolbar().setNavigationIcon(R.drawable.ic_action_back_home);
        getToolbar().setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                onBackPressed();
            }
        });

        emptyState = initEmptyState("Không thể kết nối internet:( Vui lòng kiểm tra lại kết nối ", (R.drawable.no_flight), R.raw.antenna, new EmptyStateCallBackInterface() {
            @Override
            public void negativeButton(Button button) {
                button.setText("Trang chủ");
                button.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        finish();
                    }
                });
            }

            @Override
            public void positiveButton(Button button) {
                button.setText("Kiểm tra kết nối");
                button.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        try {
                            Intent intent = new Intent(Intent.ACTION_MAIN);
                            intent.setClassName("com.android.settings", "com.android.settings.wifi.WifiSettings");
                            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                            startActivity(intent);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });

            }
        });

        processbar = findViewById(R.id.progressBar);
        processbar.setProgress(0);
        processbar.setVisibility(View.VISIBLE);

        in = getIntent();
        String car = in.getStringExtra("car");
        urlCheckin = in.getStringExtra("urlCheckin");


        webView = findViewById(R.id.webView1);
        webView.setInitialScale(1);
        webView.getSettings().setLoadWithOverviewMode(true);
        webView.getSettings().setUseWideViewPort(true);
        webView.getSettings().setAllowFileAccess(false);
        webView.setMinimumWidth(100);
        webView.setScrollBarStyle(WebView.SCROLLBARS_OUTSIDE_OVERLAY);
        webView.setScrollbarFadingEnabled(false);
        webView.requestFocus(View.FOCUS_DOWN);
        webView.setWebChromeClient(new WebChromeClient() {
            public void onProgressChanged(WebView view, int progress) {

                processbar.setVisibility(View.VISIBLE);
                processbar.setProgress(progress);
                if (progress == 100) {
                    processbar.setVisibility(View.GONE);
                }
            }

            @Override
            public void onReceivedTitle(WebView view, String title) {
            }

        });
//        webView.setWebViewClient(new WebViewClient() {
//            @Override
//            public boolean shouldOverrideUrlLoading(WebView view, String url) {
//                view.loadUrl(url);
//                return false;
//            }
//
//            @Override
//            public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
//                // Do something
//                view.setVisibility(View.GONE);
//                processbar.setVisibility(View.GONE);
//                emptyState.setVisibility(View.VISIBLE);
//                Toast.makeText(getApplicationContext(), "Vui lòng kiểm tra lại kết nối", Toast.LENGTH_SHORT).show();
//                loadErr = true;
//            }
//
//
//            @Override
//            public void onPageFinished(WebView view, String url) {
//                AppConfigs.Log("url", url);
//                if (url.contains("step6_congrat")) {
//                    Toast.makeText(getApplicationContext(), "Checkin Online thành công. Chụp màn hình thẻ lên máy bay để lưu lại !", Toast.LENGTH_LONG).show();
//                }
//            }
//        });


        webView.loadUrl(Common.addUrlMobileParams(urlCheckin));
        floatButton();
    }

    public void floatButton() {

        fabMenu = findViewById(R.id.fb_menu);
        fabBook = findViewById(R.id.fab_search);
        fabCall = findViewById(R.id.fab_call);
        fabSend = findViewById(R.id.fab_sendSMS);
        fabPay = findViewById(R.id.fab_pay);
        fabMenu.setVisibility(View.GONE);

        fabBook.setOnClickListener(new View.OnClickListener() {
            public void onClick(View v) {
                //TODO something when floating action menu first item clicked
                Intent intent = new Intent(getApplicationContext(), HomeActivity.class);
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                startActivity(intent);

            }
        });
        fabSend.setOnClickListener(new View.OnClickListener() {
            public void onClick(View v) {
                //TODO something when floating action menu second item clicked
                Intent in = new Intent(getApplicationContext(), WebViewActivity.class);
                startActivity(in);
            }
        });
        fabCall.setOnClickListener(new View.OnClickListener() {
            public void onClick(View v) {
                //TODO something when floating action menu second item clicked
                String p = "";
                Intent i = new Intent(Intent.ACTION_DIAL);
                p = "tel:" + AppConfigs.getInstance().getConfig().getString("hotline");

                i.setData(Uri.parse(p));
                startActivity(i);

            }
        });
        fabPay.setOnClickListener(new View.OnClickListener() {
            public void onClick(View v) {
                //TODO something when floating action menu second item clicked

                Intent in = new Intent(getApplicationContext(), ThanhToan.class);
                startActivity(in);
            }
        });


    }

    @Override
    public void onBackPressed() {
        if (webView.getUrl().contains("#checkin")) {
            finish();
        }
        if (webView.getUrl().contains("step6_congrat")) {
            finish();
        }
        if (webView.getUrl().contains("/checkin/confirmation")) {
            finish();
        }
        if (isCheckin) {
            finish();
        }
        if (webView.canGoBack() && !loadErr) {
            webView.goBack();
        } else {
            finish();
        }
    }


}