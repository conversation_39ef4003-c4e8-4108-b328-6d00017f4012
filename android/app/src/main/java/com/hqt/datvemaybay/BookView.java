package com.hqt.datvemaybay;

import static android.view.View.generateViewId;
import static com.hqt.datvemaybay.Common.FCM_TOKEN;
import static com.hqt.datvemaybay.Common.RC_SIGN_IN_UI;
import static com.hqt.datvemaybay.Common.isNameFormat;
import static com.hqt.datvemaybay.Common.isNewData;
import static com.hqt.datvemaybay.Common.showAlertDialog;
import static com.hqt.datvemaybay.Common.unAccent;

import android.animation.LayoutTransition;
import android.app.ProgressDialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.Html;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.WindowManager;
import android.view.inputmethod.EditorInfo;
import android.widget.AbsListView;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.AutoCompleteTextView;
import android.widget.Button;
import android.widget.EditText;
import android.widget.HorizontalScrollView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.ScrollView;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.AppCompatButton;
import androidx.cardview.widget.CardView;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.android.volley.VolleyError;
import com.firebase.ui.auth.IdpResponse;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.android.material.snackbar.Snackbar;
import com.google.firebase.analytics.FirebaseAnalytics;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseUser;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.google.gson.Gson;
import com.hqt.data.model.BookingV2;
import com.hqt.data.model.Passenger;
import com.hqt.data.model.PassengerTitle;
import com.hqt.data.model.PassengerType;
import com.hqt.data.model.PaxInfoList;
import com.hqt.util.AppConfigs;
import com.hqt.util.SSLSendRequest;
import com.hqt.util.SharedPrefs;
import com.hqt.util.ViewUtil;
import com.hqt.util.Widget;
import com.hqt.util.amlich.AmLich;
import com.hqt.view.adapter.PassengerAdapter;
import com.hqt.view.ui.BaseActivity;
import com.hqt.view.ui.reward.ui.activity.RewardActivity;
import com.hqt.view.ui.seatmap.SelectSeatActivity;
import com.mikepenz.iconics.Iconics;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import q.rorbin.badgeview.Badge;
import q.rorbin.badgeview.QBadgeView;
import uk.co.deanwild.materialshowcaseview.MaterialShowcaseSequence;
import uk.co.deanwild.materialshowcaseview.MaterialShowcaseView;
import uk.co.deanwild.materialshowcaseview.ShowcaseConfig;

public class BookView extends BaseActivity {
    private AlertDialog myDialog;
    private int tongGiaTien = 0, discount = 0, retCode, giaGiaVe = 0;
    int tongCongVe = 0;
    int tongCong = 0;
    int hanhLy = 0;
    int dateSlectId = 0;
    boolean showCase = true;
    List<LinearLayout> allItem = new ArrayList<LinearLayout>();
    TextView txtVoucherCode, txtDisCount, from, to, txtGiaTongCong, txtGiaVe;
    EditText txtPhone, txtName, txtEmail;
    AppCompatButton btnBookVe, btnGetvoucher;
    Boolean inputError = false;
    LinearLayout paxInPut, bagSelectcontainer, bagSelectcontainerRt;
    HorizontalScrollView horizalScroll;
    ProgressDialog dialog;
    Boolean haveVoucher = false, error = true;
    String noiDungSms, VOUCHERCODE, code, result, email;
    ScrollView scrollView1;
    SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy", Locale.US);
    Boolean promo = false;
    final int REQUEST_CODE_GETDATE = 3;
    final int REQUEST_CODE_GETVOUCHER = 3;
    final int INPUT_TYPE = 99;
    ProgressBar load_bag;
    CoordinatorLayout coordinatorLayout;
    BookingV2 bookingDetail = new BookingV2();
    JSONArray bagFeeDeparture = new JSONArray();
    JSONArray bagFeeReturn = new JSONArray();
    BottomSheetDialog dialogPromotionView;
    CardView cardViewLoginLayout;
    LinearLayout layoutPointReward;
    MaterialShowcaseSequence sequence;

    PaxInfoList paxInfoList = new PaxInfoList();

    @Override
    protected int getLayoutId() {
        return R.layout.activity_book;
    }

    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        LocalBroadcastManager.getInstance(this).registerReceiver(mMessageReceiver, new IntentFilter("bookingupdate"));

        Intent in = getIntent();
        bookingDetail = (BookingV2) in.getSerializableExtra("bookingDetail");
        if (bookingDetail == null) {
            finish();
        }
        coordinatorLayout = findViewById(R.id.coordinatorLayout);

        ShowcaseConfig config = new ShowcaseConfig();
        config.setDelay(500);

        sequence = new MaterialShowcaseSequence(this, "SHOWCASE_BOOKVIEW"); //
        sequence.setConfig(config);
        sequence.setOnItemShownListener(new MaterialShowcaseSequence.OnSequenceItemShownListener() {
            @Override
            public void onShow(MaterialShowcaseView itemView, int position) {
                if (showCase) {
                    Toast.makeText(getApplicationContext(), "Nhấn OK Đã hiểu để tiếp tục", Toast.LENGTH_SHORT).show();
                    showCase = false;
                }
            }
        });

        dialogPromotionView = new BottomSheetDialog(this);
        dialogPromotionView.setContentView(getLayoutInflater().inflate(R.layout.select_bag_layout, null));

        Button btnSelectBag = dialogPromotionView.findViewById(R.id.btnSelectBag);
        btnSelectBag.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                if (dialogPromotionView.isShowing()) {
                    dialogPromotionView.dismiss();
                }
            }
        });

        getToolbar().setTitle("Điền thông tin");
        getToolbar().setNavigationIcon(R.drawable.ic_action_back_home);

        load_bag = findViewById(R.id.load_bag);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            getWindow().addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            getWindow().setStatusBarColor(getResources().getColor(R.color.primary_dark));
        }


        txtName = findViewById(R.id.txtName);
        //  txtName.setFilters(new InputFilter[]{Common.editTextUpCaseFilter()});

        txtPhone = findViewById(R.id.txtPhone);
        txtEmail = findViewById(R.id.txtEmail);
        // ho quoc thinh ic_empty_ticket
        txtGiaVe = findViewById(R.id.txtGiaVe);
        txtGiaTongCong = findViewById(R.id.txtGiaTongCong);
        txtVoucherCode = findViewById(R.id.txtVoucherCode);
        txtDisCount = findViewById(R.id.txtdiscount);
        btnBookVe = findViewById(R.id.btnBookVe);
        btnGetvoucher = findViewById(R.id.btnGetVoucher);

        layoutPointReward = findViewById(R.id.layoutPointReward);
        cardViewLoginLayout = findViewById(R.id.loginLayout);
        cardViewLoginLayout.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                signIn();
            }
        });


        if (isUserSigned()) {
            cardViewLoginLayout.setVisibility(View.GONE);
            layoutPointReward.setVisibility(View.VISIBLE);
        } else {
            cardViewLoginLayout.setVisibility(View.VISIBLE);
            layoutPointReward.setVisibility(View.GONE);
        }

        layoutPointReward.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                if (!isUserSigned()) {
                    signIn();
                }

            }
        });
        findViewById(R.id.show_seat_select).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {

                Intent seatMap = new Intent(getApplicationContext(), SelectSeatActivity.class);
                seatMap.putExtra("flightKey", "HANSGN28102022|VJ157|W1_ECO");
                startActivity(seatMap);
            }
        });


        SharedPreferences settings = getSharedPreferences("12BAY-APP-CONFIG", 0);
        if (!isUserSigned()) {
            txtPhone.setText(settings.getString("phone", "").toString().replace("+84", "0"));
            txtName.setText(settings.getString("name", "").toString());
            String Umail = (settings.getString("Uemail", "").toString());
            txtEmail.setText(settings.getString("email", Umail).toString());
        } else {
            refreshLayout();
        }

        if (Common.FCM_TOKEN.equals("")) {
            Common.FCM_TOKEN = settings.getString("FCM-TOKEN", Common.FCM_TOKEN).toString();
        }


        genFlightInfo();
        Common.Bag_Price.clear();
        Common.Bag_Price_return.clear();
        Common.Bag_Price.add("Không thêm");
        Common.Bag_Price_return.add("Không thêm");

        getBagFeeApi(bookingDetail, false);

        giaGiaVe = (bookingDetail == null) ? 0 : bookingDetail.getTotal();

        txtGiaVe.setText(Common.dinhDangTien(giaGiaVe));
        txtGiaTongCong.setText(Common.dinhDangTien(giaGiaVe - discount + hanhLy));
        int pointReward = 0;
        try {
            pointReward = (bookingDetail.getAdult() + bookingDetail.getChild()) * (bookingDetail.getDeparture_f().getRewardPoint() + (bookingDetail.is_round_trip() ? bookingDetail.getReturn_f().getRewardPoint() : 0));
        } catch (Exception e) {

        }

        ((TextView) findViewById(R.id.txtPoint)).setText("Nhận " + pointReward + " điểm ");
        try {
            Bundle params = new Bundle();
            params.putString(FirebaseAnalytics.Param.ORIGIN, (bookingDetail.getOrigin_code()));
            params.putString(FirebaseAnalytics.Param.DESTINATION, (bookingDetail.getDestination_code()));
            params.putInt(FirebaseAnalytics.Param.NUMBER_OF_PASSENGERS, bookingDetail.getAdult() + bookingDetail.getChild() + bookingDetail.getInfant());
            params.putInt(FirebaseAnalytics.Param.PRICE, (giaGiaVe - discount + hanhLy));
            params.putInt(FirebaseAnalytics.Param.VALUE, (giaGiaVe - discount + hanhLy));
            params.putString(FirebaseAnalytics.Param.CURRENCY, "VND");
            params.putString(FirebaseAnalytics.Param.TRANSACTION_ID, bookingDetail.getDeparture_f().getUuid());
            params.putString(FirebaseAnalytics.Param.START_DATE, bookingDetail.getDeparture_date());
            if (bookingDetail.is_round_trip())
                params.putString(FirebaseAnalytics.Param.END_DATE, bookingDetail.getReturn_date());
            params.putString(FirebaseAnalytics.Param.FLIGHT_NUMBER, bookingDetail.getDeparture_f().getFlightNumber());
            params.putString(FirebaseAnalytics.Param.TRAVEL_CLASS, bookingDetail.getDeparture_f().getFareBasis());
            getFirebaseAnalytics().logEvent(FirebaseAnalytics.Event.BEGIN_CHECKOUT, params);

        } catch (Exception e) {
            AppConfigs.logException(e);
        }


        btnBookVe.setOnClickListener(new OnClickListener() {

                                         @Override
                                         public void onClick(View v) {
                                             // TODO Auto-generated method stub

                                             //AppConfigs.Log("paxInfoList", AppController.getInstance().gSon.toJson(paxInfoList).toString());
                                             if (isInternetConnected()) {
                                                 getPaxStringV4(paxInPut);

                                                 if (inputError) {

                                                 } else if (txtName.getText().toString().equals("")) {
                                                     Toast.makeText(getApplicationContext(), "Vui lòng điền tên liên lạc", Toast.LENGTH_SHORT).show();
                                                     txtName.requestFocus();
                                                 } else if (txtPhone.getText().toString().equals("")) {
                                                     Toast.makeText(getApplicationContext(), "Vui lòng điền số điện thoại liên lạc", Toast.LENGTH_SHORT).show();
                                                     txtPhone.requestFocus();
                                                 } else if (txtEmail.getText().toString().equals("")) {

                                                     new AlertDialog.Builder(BookView.this)
                                                             .setIcon(R.drawable.ic_bell_alert)
                                                             .setTitle("Chú ý")
                                                             .setMessage(Html.fromHtml("Vui lòng nhập đúng email hoặc bỏ trống nếu không có"))
                                                             .setPositiveButton("Nhập Email", new DialogInterface.OnClickListener() {
                                                                 @Override
                                                                 public void onClick(DialogInterface dialogInterface, int i) {
                                                                     txtEmail.requestFocus();
                                                                 }
                                                             })
                                                             .setNegativeButton("Bỏ qua", new DialogInterface.OnClickListener() {
                                                                 @Override
                                                                 public void onClick(DialogInterface dialogInterface, int i) {
                                                                     txtEmail.setText("<EMAIL>");
                                                                     btnBookVe.performClick();
                                                                 }
                                                             }).show();

                                                 } else if (!Common.isEmailValid(txtEmail.getText().toString())) {
                                                     Toast.makeText(getApplicationContext(), "Vui lòng nhập đúng định dạng email", Toast.LENGTH_SHORT).show();
                                                     txtEmail.requestFocus();

                                                 } else {


                                                     SharedPreferences settings = getSharedPreferences("12BAY-APP-CONFIG", 0);
                                                     SharedPreferences.Editor editor = settings.edit();
                                                     editor.putString("name", Common.unAccent(txtName.getText().toString()));
                                                     editor.putString("phone", Common.unAccent(txtPhone.getText().toString()));
                                                     editor.putString("email", email = txtEmail.getText().toString());
                                                     editor.apply();

                                                     showConfirmDialog();

                                                 }
                                             } else {
                                                 //Nếu không có kết nối với intenet thì
                                                 showAlertDialog(BookView.this, "Không thể kết nối Internet",
                                                         "Xin vui lòng kết nối Wifi hoặc 3g để tiếp tục", false, true);
                                             }
                                         }

                                     }

        );

        btnGetvoucher.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                if (txtVoucherCode.getText().toString().equals("")) {
                    Toast.makeText(getApplicationContext(), "Vui lòng nhập mã giảm giá", Toast.LENGTH_SHORT).show();
                    txtVoucherCode.requestFocus();
                } else {
                    VOUCHERCODE = txtVoucherCode.getText().toString();
                    //new GetVoucherTask().execute();
                    getVoucher();

                }
            }
        });
        //START SHOW SHOWCASE
        try {
            if (isUserSigned()) {
                Widget.retrievePassenger(this, getFirebaseUser().getUid());
            }
        } catch (Exception e) {
            AppConfigs.logException(e);
            e.printStackTrace();
        }
    }

    @Override
    public void refreshLayout() {
        try {
            if (isUserSigned()) {
                Widget.retrievePassenger(this, getFirebaseUser().getUid());

                cardViewLoginLayout.setVisibility(View.GONE);
                layoutPointReward.setVisibility(View.VISIBLE);
                txtName.setText(Common.unAccent(getFirebaseUser().getDisplayName()).toUpperCase());
                String phone = (getFirebaseUser().getPhoneNumber() != null && !getFirebaseUser().getPhoneNumber().equalsIgnoreCase("")) ? getFirebaseUser().getPhoneNumber() : SharedPrefs.getInstance().get("phone", String.class).toString();
                txtPhone.setText(phone.replace("+84", "0"));
                txtEmail.setText(getFirebaseUser().getEmail());

            } else {
                cardViewLoginLayout.setVisibility(View.VISIBLE);
                layoutPointReward.setVisibility(View.GONE);
            }
        } catch (Exception e) {
            AppConfigs.logException(e);
            e.printStackTrace();
        }
    }

    public void genPaxInputx() {
        load_bag.setVisibility(View.GONE);
        paxInPut = findViewById(R.id.paxInPut);
        for (int i = 0; i < bookingDetail.getAdult(); i++) {

            Passenger pax = new Passenger();
            pax.startInput(PassengerType.ADULT, i);
            Widget.showPaxList(
                    false,
                    pax,
                    false,
                    this,
                    paxInPut,
                    true
            );
            paxInfoList.addPassenger(pax);
        }
        for (int i = 0; i < bookingDetail.getChild(); i++) {

            Passenger pax = new Passenger();
            pax.startInput(PassengerType.CHILD, i);
            Widget.showPaxList(
                    false,
                    pax,
                    false,
                    this,
                    paxInPut,
                    true
            );
            paxInfoList.addPassenger(pax);
        }
        for (int i = 0; i < bookingDetail.getInfant(); i++) {

            Passenger pax = new Passenger();
            pax.startInput(PassengerType.INFANT, i);
            Widget.showPaxList(
                    false,
                    pax,
                    false,
                    this,
                    paxInPut,
                    true
            );
            paxInfoList.addPassenger(pax);
        }

    }

    public void genPaxInput() {
        load_bag.setVisibility(View.GONE);
        paxInPut = findViewById(R.id.paxInPut);
        //NEW ITEM PAX INPUT
        LinearLayout item, item2;
        Spinner spinItem;
        AutoCompleteTextView prevEditItem = null;
        AutoCompleteTextView editItem;
        TextView dateItem;
        int paxIndex = -1;
        int paxCount = bookingDetail.getAdult() + bookingDetail.getChild() + bookingDetail.getInfant();
        //ALDUT PAX INPUT
        for (int i = 0; i < bookingDetail.getAdult(); i++) {
            paxIndex++;
            //item hanh ly
            //item
            item = new LinearLayout(this);
            item.setId(i);
            item.setTag("PAXNAME");
            item.setOrientation(LinearLayout.HORIZONTAL);
            item.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT,
                    AbsListView.LayoutParams.WRAP_CONTENT));
            //spinner
            spinItem = new Spinner(this);
            spinItem.setTag("SPINNAME");
            spinItem.setLayoutParams(new Spinner.LayoutParams(Spinner.LayoutParams.WRAP_CONTENT, Spinner.LayoutParams.WRAP_CONTENT));
            ArrayAdapter<CharSequence> adapter = ArrayAdapter.createFromResource(this,
                    R.array.spinerPaxType, android.R.layout.simple_spinner_item);
            adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
            spinItem.setBackground(getResources().getDrawable(R.drawable.spinner_bg));
            spinItem.setPadding(0, 0, 50, 0);
            spinItem.setAdapter(adapter);


            item.addView(spinItem);
            //EDIT TEXT
            editItem = new AutoCompleteTextView(this);
            editItem.setId(generateViewId());
            editItem.setImeOptions(EditorInfo.IME_ACTION_NEXT);


            editItem.setTag("EDITNAME");
            editItem.setLayoutParams(new LinearLayout.LayoutParams(0,
                    LinearLayout.LayoutParams.WRAP_CONTENT, 4f));
            editItem.setBackgroundResource(R.drawable.edit_text);
            editItem.setPadding(20, 10, 0, 10);
//
            editItem.setTextColor(Color.BLACK);
            editItem.setHint("Điền họ và tên");

            editItem.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View view) {
                    //bottomSheetBehavior.setState(BottomSheetBehavior.STATE_HIDDEN);
                }
            });
            editItem.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                @Override
                public void onFocusChange(View view, boolean b) {
                    //bottomSheetBehavior.setState(BottomSheetBehavior.STATE_HIDDEN);
                }
            });
//            editItem.setInputType(InputType.TYPE_TEXT_FLAG_CAP_CHARACTERS);

            if (prevEditItem != null) {
                prevEditItem.setNextFocusDownId(editItem.getId());
            }
            prevEditItem = editItem;

            item.addView(editItem);

            //Add to layout
            paxInPut.addView(item);
            allItem.add(item);

            //ADD HANH LY
            TextView bagViewText = genBagView(spinItem, editItem, i);

            if (i == 0) {
                sequence.addSequenceItem(spinItem, "Hướng dẫn cơ bản",
                        "Nhấn chọn giới tính của hành khách", "OK Đã hiểu");
                sequence.addSequenceItem(editItem,
                        "Nhập tên hành khách", "OK Đã hiểu");
                sequence.addSequenceItem(bagViewText,
                        "Nhấn chọn hành lý ký gửi nếu có", "OK Đã hiểu!");
            }
            addAutoComplete(editItem, spinItem, PassengerType.ADULT);


        }
        //CHILD PAX INPUT
        for (int i = 0; i < (bookingDetail.getChild()); i++) {
            //item
            paxIndex++;
            item = new LinearLayout(this);
            item.setId(i);

            item.setTag("PAXNAME");
            item.setOrientation(LinearLayout.HORIZONTAL);
            item.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT,
                    AbsListView.LayoutParams.WRAP_CONTENT));
            //spinner
            spinItem = new Spinner(this);
            spinItem.setTag("SPINNAME");
            spinItem.setLayoutParams(new Spinner.LayoutParams(Spinner.LayoutParams.WRAP_CONTENT, Spinner.LayoutParams.WRAP_CONTENT));
            ArrayAdapter<CharSequence> adapter = ArrayAdapter.createFromResource(this,
                    R.array.spinerChildPaxType, android.R.layout.simple_spinner_item);
            adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
            spinItem.setAdapter(adapter);
            spinItem.setBackground(getResources().getDrawable(R.drawable.spinner_bg));
            spinItem.setPadding(0, 0, 50, 0);
            item.addView(spinItem);
            //EDIT TEXT
            editItem = new AutoCompleteTextView(this);
            editItem.setId(generateViewId());
            editItem.setImeOptions(EditorInfo.IME_ACTION_NEXT);
            editItem.setTag("EDITNAME");
            editItem.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.FILL_PARENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT));
            editItem.setBackgroundResource(R.drawable.edit_text);
            editItem.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View view) {
                    //bottomSheetBehavior.setState(BottomSheetBehavior.STATE_HIDDEN);
                }
            });

            editItem.setHint("Họ tên ");
            // editItem.setInputType(InputType.TYPE_TEXT_FLAG_CAP_CHARACTERS);
            editItem.setPadding(20, 10, 10, 10);
            editItem.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View view) {
                    //bottomSheetBehavior.setState(BottomSheetBehavior.STATE_HIDDEN);
                }
            });
            editItem.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                @Override
                public void onFocusChange(View view, boolean b) {
                    //bottomSheetBehavior.setState(BottomSheetBehavior.STATE_HIDDEN);
                }
            });
            if (prevEditItem != null) {
                prevEditItem.setNextFocusDownId(editItem.getId());
            }
            prevEditItem = editItem;
            item.addView(editItem);
            //Add to layout
            paxInPut.addView(item);
            allItem.add(item);
            //ADD HANH LY
            genBagView(spinItem, editItem, i);
            addAutoComplete(editItem, spinItem, PassengerType.CHILD);

        }
        //inpput Listent
        //INFANT PAX INPUT
        for (int i = 0; i < (bookingDetail.getInfant()); i++) {
            //item
            paxIndex++;
            dateSlectId = i;
            item = new LinearLayout(this);
            item.setId(generateViewId());
            item.setTag("INFANTPAXNAME");
            item.setOrientation(LinearLayout.HORIZONTAL);
            item.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT,
                    AbsListView.LayoutParams.WRAP_CONTENT));
            //spinner
            spinItem = new Spinner(this);
            spinItem.setTag("SPINNAME");
            spinItem.setLayoutParams(new Spinner.LayoutParams(Spinner.LayoutParams.WRAP_CONTENT, Spinner.LayoutParams.WRAP_CONTENT));
            ArrayAdapter<CharSequence> adapter = ArrayAdapter.createFromResource(this,
                    R.array.spinerInpantPaxType, android.R.layout.simple_spinner_item);
            adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
            spinItem.setBackground(getResources().getDrawable(R.drawable.spinner_bg));
            spinItem.setPadding(0, 0, 50, 0);
            spinItem.setAdapter(adapter);
            item.addView(spinItem);
            //EDIT TEXT
            item2 = new LinearLayout(this);
            item2.setTag("INFANTCOL2");
            item2.setOrientation(LinearLayout.VERTICAL);
            item2.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT,
                    AbsListView.LayoutParams.WRAP_CONTENT));

            editItem = new AutoCompleteTextView(this);
            editItem.setId(generateViewId());
            editItem.setImeOptions(EditorInfo.IME_ACTION_NEXT);
            editItem.setTag("EDITNAME");
            editItem.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.FILL_PARENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT));
            editItem.setBackgroundResource(R.drawable.edit_text);
            editItem.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View view) {
                    //bottomSheetBehavior.setState(BottomSheetBehavior.STATE_HIDDEN);
                }
            });
            editItem.setHint("Họ tên ");
            //   editItem.setInputType(InputType.TYPE_TEXT_FLAG_CAP_CHARACTERS);
            editItem.setPadding(20, 10, 10, 10);
            editItem.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View view) {
                    //bottomSheetBehavior.setState(BottomSheetBehavior.STATE_HIDDEN);
                }
            });
            editItem.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                @Override
                public void onFocusChange(View view, boolean b) {
                    //bottomSheetBehavior.setState(BottomSheetBehavior.STATE_HIDDEN);
                }
            });
            if (prevEditItem != (null)) {
                prevEditItem.setNextFocusDownId(editItem.getId());
            }
            prevEditItem = editItem;

            item2.addView(editItem);

            dateItem = new TextView(this);
            dateItem.setTag("INFANTDATE");
            dateItem.setId(i);
            LinearLayout.LayoutParams pram = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.FILL_PARENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT);
            pram.setMargins(0, 2, 0, 2);
            dateItem.setLayoutParams(pram);
            dateItem.setBackgroundResource(R.drawable.edit_text);
            dateItem.setHint("Ngày sinh");
            editItem.setAllCaps(true);
            dateItem.setTextColor(Color.BLACK);
            dateItem.setPadding(10, 10, 10, 10);
            item2.addView(dateItem);
            item.addView(item2);
            dateItem.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View view) {
                    Calendar now = Calendar.getInstance();
                    Calendar minDate = Calendar.getInstance();
                    if (bookingDetail.is_round_trip()) {
                        minDate.setTime(bookingDetail.getReturn_f().getDepartureDate());
                    } else {
                        minDate.setTime(bookingDetail.getDeparture_f().getDepartureDate());
                    }

                    minDate.add(Calendar.YEAR, -2);
                    String curentBirthDay = ((TextView) view).getText().toString();
                    Intent ii = new Intent(getApplicationContext(), AmLich.class);
                    ii.putExtra("depDate", curentBirthDay.equals("") ? Common.dateToString(now.getTime(), "yyyy-MM-dd") : curentBirthDay);
                    ii.putExtra("minDate", Common.dateToString(minDate.getTime(), "yyyy-MM-dd"));
                    ii.putExtra("act", dateFormat.format(now.getTime()));
                    ii.putExtra("id", view.getId());
                    startActivityForResult(ii, REQUEST_CODE_GETDATE);
                }
            });
            if (prevEditItem != (null)) {
                prevEditItem.setNextFocusDownId(editItem.getId());
            }
            prevEditItem = editItem;
            //Add to layout

            paxInPut.addView(item);
            allItem.add(item);
            addAutoComplete(editItem, spinItem, PassengerType.INFANT);

        }
        if (AppConfigs.getInstance().getConfig().getBoolean("show_tip")) {
            sequence.start();
        }
    }

    public TextView genBagView(Spinner title, EditText txtPaxNameInput, int i) {
        final LinearLayout itemBag, itemBagVe, itemTitle, item;
        final TextView textTitle;
        TextView textBag;
        final Spinner spinerBag = new Spinner(this);
        ;
        final Spinner spinerBagVe = new Spinner(this);
        ;

        item = new LinearLayout(this);
        item.setTag("BAGINPUT");
        item.setOrientation(LinearLayout.VERTICAL);
        item.setVisibility(View.VISIBLE);

        item.setLayoutParams(new LinearLayout.LayoutParams(0, 0));
        item.setLayoutTransition(new LayoutTransition());

        itemTitle = new LinearLayout(this);
        itemTitle.setTag("TITLE");
        itemTitle.setOrientation(LinearLayout.HORIZONTAL);
        itemTitle.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT,
                AbsListView.LayoutParams.WRAP_CONTENT));
        textTitle = new TextView(this);
        textTitle.setPadding(25, 15, 10, 15);
        textTitle.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT));

        textTitle.setText("{faw_suitcase} Mua thêm hành lý (tùy chọn)");
        new Iconics.Builder().on(textTitle).build();
        textTitle.setTextColor(Color.parseColor("#000000"));


        itemTitle.addView(textTitle);

        itemBag = new LinearLayout(this);
        itemBag.setId(i + 100);
        itemBag.setTag("BAGDI");
        itemBag.setOrientation(LinearLayout.HORIZONTAL);
        itemBag.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT,
                AbsListView.LayoutParams.WRAP_CONTENT));
        textBag = new TextView(this);
        textBag.setPadding(25, 5, 10, 5);
        textBag.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT));
        if (bookingDetail.is_round_trip()) {
            textBag.setText("- Hành lý lượt đi:");
        } else {
            textBag.setText("- Hành lý:");
        }
        textBag.setTextColor(Color.BLACK);
        itemBag.addView(textBag);


        spinerBag.setLayoutParams(new Spinner.LayoutParams(Spinner.LayoutParams.FILL_PARENT, Spinner.LayoutParams.WRAP_CONTENT));
        CharSequence[] charSequenceItems = Common.Bag_Price.toArray(new CharSequence[Common.Bag_Price.size()]);

        ArrayAdapter<CharSequence> adapterB = new ArrayAdapter(this,
                android.R.layout.simple_spinner_item, charSequenceItems);
        adapterB.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinerBag.setAdapter(adapterB);
        spinerBag.setTag("SPINBAGDI");
        spinerBag.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            int price = 0;

            @Override
            public void onItemSelected(AdapterView<?> adapterView, View view, int i, long l) {

                int priceSelect = getBagValue(adapterView.getItemAtPosition(i).toString(), true);
                addBagFee(priceSelect - price);
                price = priceSelect;

            }

            @Override
            public void onNothingSelected(AdapterView<?> adapterView) {

            }
        });
        itemBag.addView(spinerBag);


        //Add to layout
        item.addView(itemBag);
        paxInPut.addView(itemTitle);
        allItem.add(itemBag);

        //
        if (bookingDetail.is_round_trip()) {
            itemBagVe = new LinearLayout(this);
            itemBagVe.setId(i + 200);
            itemBagVe.setTag("SPINBAGVE");
            itemBagVe.setOrientation(LinearLayout.HORIZONTAL);
            itemBagVe.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT,
                    AbsListView.LayoutParams.WRAP_CONTENT));
            textBag = new TextView(this);
            textBag.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT));
            textBag.setText("- Hành lý lượt về:");
            textBag.setPadding(25, 5, 10, 5);
            textBag.setTextColor(Color.BLACK);
            itemBagVe.addView(textBag);

            spinerBagVe.setTag("SPINBAGVE");
            CharSequence[] charSequenceItemsVe = Common.Bag_Price_return.toArray(new CharSequence[Common.Bag_Price_return.size()]);

            ArrayAdapter<CharSequence> adapterBV = new ArrayAdapter(this,
                    android.R.layout.simple_spinner_item, charSequenceItemsVe);

            spinerBagVe.setLayoutParams(new Spinner.LayoutParams(Spinner.LayoutParams.FILL_PARENT, Spinner.LayoutParams.WRAP_CONTENT));
            adapterBV.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);

            //new la vn thi free 20 ki
            spinerBagVe.setAdapter(adapterBV);
            spinerBagVe.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                int price = 0;

                @Override
                public void onItemSelected(AdapterView<?> adapterView, View view, int i, long l) {

                    //Toast.makeText(getApplicationContext(), adapterView.getItemAtPosition(i).toString(), Toast.LENGTH_SHORT).show();
                    int priceSelect = getBagValue(adapterView.getItemAtPosition(i).toString(), true);

                    addBagFee(priceSelect - price);  //trừ giá của hành lý chọn trước
                    price = priceSelect;

                }

                @Override
                public void onNothingSelected(AdapterView<?> adapterView) {

                }
            });


            itemBagVe.addView(spinerBagVe);
            item.addView(itemBagVe);
            allItem.add(itemBagVe);


        }

        itemTitle.setOnClickListener(new OnClickListener() {
            boolean show = true;

            @Override
            public void onClick(View view) {
                txtPaxNameInput.clearFocus();
                hideKeyBoard(txtPaxNameInput);
                new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        String paxName = title.getSelectedItem() + " " + txtPaxNameInput.getText().toString();
                        addBagSelectSheet(paxName, textTitle, spinerBag, spinerBagVe);

                    }
                }, 100);


            }
        });
        paxInPut.addView(item);
        return textTitle;

    }

    public void addBagFee(int fee) {
        hanhLy = hanhLy + fee;
        LinearLayout bagLayout = findViewById(R.id.bagLayout);
        if (hanhLy > 0) {

            bagLayout.setVisibility(View.VISIBLE);
            TextView txtBagFee = findViewById(R.id.txtbagFee);
            txtBagFee.setText(Common.dinhDangTien(hanhLy));
            txtGiaTongCong.setText(Common.dinhDangTien(giaGiaVe - discount + hanhLy));

        } else {
            txtGiaTongCong.setText(Common.dinhDangTien(giaGiaVe - discount + hanhLy));
            bagLayout.setVisibility(View.GONE);
        }

    }

    public int getBagValue(String bagString, boolean showPrice) {
        int value = 0;
        int p = 0;
        bagString = bagString.replace(",", "");

        Pattern regex = Pattern.compile("\\d+");
        Matcher m = regex.matcher(bagString);
        while (m.find()) {
            try {
                if (Integer.valueOf(m.group()) < 100 && Integer.valueOf(m.group()) > 0) {
                    value = Integer.valueOf(m.group());
                }
                if (Integer.valueOf(m.group()) > 100) {
                    p = Integer.valueOf(m.group());
                }
            } catch (IllegalArgumentException e) {
                AppConfigs.logException(e);
            }
        }

        return showPrice ? p : value;

    }

    public void genFlightInfo() {
        try {
            LinearLayout paxInPut;
            //SET CHIEU DI INFO
            Gson gson = new Gson();

            if (bookingDetail == null || bookingDetail.getDeparture_f() == null) {
                Toast.makeText(this, "Có lỗi xẩy ra. Vui lòng thực hiện lại", Toast.LENGTH_SHORT).show();
                finish();
                FirebaseCrashlytics.getInstance().log("bookingDetail.getDeparture_f() == null");
            }

            LinearLayout container = findViewById(R.id.container);
            container.removeAllViews();
            View view = ViewUtil.bindFlightDataToView(bookingDetail.getDeparture_f(), container, this, false, bookingDetail.getAdult(), bookingDetail.getChild(), bookingDetail.getInfant());
            view.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View view) {
                    ((HorizontalScrollView) findViewById(R.id.horizalScroll)).fullScroll(View.FOCUS_LEFT);
                }
            });
            if (bookingDetail.is_round_trip()) {
                if (bookingDetail.getReturn_f() == null) {
                    Toast.makeText(this, "Có lỗi xẩy ra. Vui lòng thực hiện lại", Toast.LENGTH_SHORT).show();
                    finish();
                    FirebaseCrashlytics.getInstance().log("bookingDetail.getReturn_f() == null");
                }

                LinearLayout containerRt = findViewById(R.id.containerRt);
                containerRt.removeAllViews();

                View viewRt = ViewUtil.bindFlightDataToView(bookingDetail.getReturn_f(), containerRt, this, true, bookingDetail.getAdult(), bookingDetail.getChild(), bookingDetail.getInfant());
                containerRt.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        ((HorizontalScrollView) findViewById(R.id.horizalScroll)).fullScroll(View.FOCUS_RIGHT);
                    }
                });
            }
            if (bookingDetail.getDeparture_f().getPromo() || (bookingDetail.is_round_trip() && bookingDetail.getReturn_f().getPromo())) {
                new AlertDialog.Builder(this)
                        .setIcon(R.drawable.ic_bell_alert)
                        .setTitle("Chú ý")
                        .setMessage(Html.fromHtml("Đây là hạng vé <b>Khuyến Mãi</b><br>" +
                                "Quý khách vui lòng <b>chuyển khoản</b> hoặc <b>thanh toán online</b> ngay sau khi xác nhận đơn hàng thành công!"))
                        .setPositiveButton("Đồng ý", null)
                        .show();
            }


            Date now = new Date();
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.DATE, 1);
            now = cal.getTime();

            if (bookingDetail.getDeparture_f().getDepartureDate().before(now)) {
                new AlertDialog.Builder(this)
                        .setIcon(R.drawable.ic_bell_alert)
                        .setTitle("Chú ý")
                        .setMessage(Html.fromHtml("Đây là chuyến bay <b>Cận Ngày</b>(Trong vòng 24h) không giữ chỗ được!<br>Phải thực hiện<b> Thanh Toán ngay</b> để xuất vé!<br>" +
                                "Quý khách vui lòng <b>chuyển khoản</b> hoặc <b>thanh toán online</b> ngay sau khi xác nhận đơn hàng thành công!"))
                        .setPositiveButton("Đồng ý", null)
                        .show();
            }


        } catch (Exception e) {
            AppConfigs.logException(e);
            Toast.makeText(this, "Có lỗi xẩy ra. Vui lòng thực hiện lại", Toast.LENGTH_SHORT).show();
            finish();
        }
    }

    public JSONObject getPaxStringV4(LinearLayout paxInput) {

        JSONObject paxLists = new JSONObject();
        JSONObject paxtitle = new JSONObject();
        JSONObject paxbaggage = new JSONObject();
        JSONObject paxreturnBaggage = new JSONObject();
        String paxfullName = "";
        String paxType = "adult";
        JSONObject pax = new JSONObject();
        JSONArray paxadults = new JSONArray();
        JSONArray paxchilds = new JSONArray();
        JSONArray paxinfants = new JSONArray();


        String paxString = "";
        String paxTemp = "";
        if (Common.LISTPAX != (null)) Common.LISTPAX.clear();
        else Common.LISTPAX = new ArrayList<String>();
        inputError = false;
        String paxSub = "";
        int paxCount = paxInput != null ? paxInput.getChildCount() : 0;
        for (int i = 0; i < paxCount; i++) {
            View item = paxInput.getChildAt(i);
            try {
                //PAX ITEM
                if (item.getTag().equals("PAXNAME")) {
                    View pSub = ((LinearLayout) item).getChildAt(0);//spinner sub
                    paxSub = ((Spinner) pSub).getSelectedItem().toString();

                    if (paxSub.equals("Ông")) {

                        paxtitle.put("text", "Ông");
                        paxtitle.put("value", "Mr");
                        paxType = "adult";
                    } else if (paxSub.equals("Bà")) {

                        paxtitle.put("text", "Bà");
                        paxtitle.put("value", "Mrs");
                        paxType = "adult";
                    } else if (paxSub.equals("Trẻ em Trai")) {

                        paxtitle.put("text", "Bé Trai");
                        paxtitle.put("value", "Mstr");
                        paxType = "child";
                    } else if (paxSub.equals("Trẻ em Gái")) {

                        paxtitle.put("text", "Bé Gái");
                        paxtitle.put("value", "Miss");
                        paxType = "child";
                    } else if (paxSub.equals("Em bé Trai")) {

                        paxtitle.put("text", "Bé Trai");
                        paxtitle.put("value", "Mstr");
                        paxType = "infant";
                    } else if (paxSub.equals("Em bé Gái")) {

                        paxtitle.put("text", "Bé Gái");
                        paxtitle.put("value", "Miss");
                        paxType = "infant";
                    }

                    View pName = ((LinearLayout) item).getChildAt(1);//EDIT TEXT
                    paxfullName = paxString = ((EditText) pName).getText().toString().toUpperCase();

                    if ((((EditText) pName).getText().toString().trim().equalsIgnoreCase(""))) {
                        Toast.makeText(this, "Vui lòng nhập tên hành khách", Toast.LENGTH_SHORT).show();
                        (pName).requestFocus();
                        inputError = true;
                        break;
                    }

                    String paxName = ((EditText) pName).getText().toString();
                    ((EditText) pName).setText(unAccent(paxName).toUpperCase());

                    if (!isNameFormat((((EditText) pName).getText().toString()))) {
                        Toast.makeText(this, "Vui lòng nhập tiếng việt không dấu đúng định dạng (TRAN NGUYEN A)  ", Toast.LENGTH_SHORT).show();
                        (pName).requestFocus();
                        inputError = true;
                        break;
                    }
                    paxTemp += paxString;


                } else if (item.getTag().equals("BAGINPUT")) {
                    String bagDi, bagVe = "";
                    View bagDiView = ((LinearLayout) item).getChildAt(0);
                    View spinBagDi = ((LinearLayout) bagDiView).getChildAt(1);
                    String pagDiString = ((Spinner) spinBagDi).getSelectedItem().toString();

                    paxbaggage.put("value", getBagValue(pagDiString, false));
                    paxbaggage.put("price", getBagValue(pagDiString, true));
                    paxbaggage.put("text", pagDiString);

                    if (bookingDetail.is_round_trip()) {
                        View bagVeView = ((LinearLayout) item).getChildAt(1);
                        View spinBagVe = ((LinearLayout) bagVeView).getChildAt(1);
                        String pagVeString = ((Spinner) spinBagVe).getSelectedItem().toString();

                        paxreturnBaggage.put("value", getBagValue(pagVeString, false));
                        paxreturnBaggage.put("price", getBagValue(pagVeString, true));
                        paxreturnBaggage.put("text", pagVeString);
                    } else {
                        paxreturnBaggage.put("value", 0);
                        paxreturnBaggage.put("price", 0);
                        paxreturnBaggage.put("text", "Không thêm");
                    }

                    pax.put("fullname", Common.unAccent(paxfullName));
                    pax.put("baggage", paxbaggage);
                    pax.put("returnBaggage", paxreturnBaggage);
                    pax.put("title", paxtitle);

                    if (paxType.equals("adult")) {
                        paxadults.put(pax);
                    } else if (paxType.equals("child")) {
                        paxchilds.put(pax);
                    }
                    paxreturnBaggage = new JSONObject();
                    paxbaggage = new JSONObject();
                    paxtitle = new JSONObject();
                    paxfullName = "";
                    pax = new JSONObject();

                } else if (item.getTag().equals("INFANTPAXNAME")) {
                    View pSub = ((LinearLayout) item).getChildAt(0);
                    paxSub = ((Spinner) pSub).getSelectedItem().toString();

                    if (paxSub.equals("Em bé Trai")) {

                        paxtitle.put("text", "Bé Trai");
                        paxtitle.put("value", "Mstr");
                        paxType = "infant";
                    } else if (paxSub.equals("Em bé Gái")) {

                        paxtitle.put("text", "Bé Gái");
                        paxtitle.put("value", "Miss");
                        paxType = "infant";
                    }
                    View nameDate = ((LinearLayout) item).getChildAt(1);
                    View infantName = ((LinearLayout) nameDate).getChildAt(0);
                    View infantDate = ((LinearLayout) nameDate).getChildAt(1);

                    paxfullName = paxString = ((EditText) infantName).getText().toString().toUpperCase();
                    ;
                    String paxStringDate = ((TextView) infantDate).getText().toString();

                    pax.put("fullname", Common.unAccent(paxfullName));
                    pax.put("title", paxtitle);
                    pax.put("birthday", paxStringDate);

                    if (paxType.equals("infant")) {
                        paxinfants.put(pax);
                    }
                    paxfullName = "";
                    paxStringDate = "";
                    pax = new JSONObject();

                    if ((((EditText) infantName).getText().toString().trim().equalsIgnoreCase(""))) {
                        Toast.makeText(this, "Vui lòng nhập tên hành khách", Toast.LENGTH_SHORT).show();
                        (infantName).requestFocus();
                        inputError = true;
                        break;
                    }

                    if (!isNameFormat((((EditText) infantName).getText().toString()))) {
                        Toast.makeText(this, "Vui lòng nhập tiếng việt không dấu đúng định dạng (TRAN NGUYEN A)  ", Toast.LENGTH_SHORT).show();
                        (infantName).requestFocus();
                        inputError = true;
                        break;
                    }

                    if ((((TextView) infantDate).getText().toString().trim().equalsIgnoreCase(""))) {
                        Toast.makeText(this, "Vui lòng nhập ngày sinh của em bé", Toast.LENGTH_SHORT).show();
                        inputError = true;
                        break;
                    }
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        try {
            paxLists.put("adult", paxadults);
            paxLists.put("child", paxchilds);
            paxLists.put("infant", paxinfants);

        } catch (JSONException e) {
            e.printStackTrace();
        }
        return paxLists;
    }

    public void setString(LinearLayout paxInput, int id, String text) {

        View item = paxInput.getChildAt(bookingDetail.getAdult() * 3 + bookingDetail.getChild() * 3 + id);
        if (item instanceof LinearLayout) {
            for (int j = 0; j < ((LinearLayout) item).getChildCount(); j++) {
                View pax = ((LinearLayout) item).getChildAt(j);

                if (pax instanceof LinearLayout) {

                    View tv = ((LinearLayout) pax).getChildAt(1);

                    if (tv instanceof TextView) {
                        ((TextView) tv).setText(text);
                    }


                }
            }

        }

    }

    public void postBook() {

        final ProgressDialog dialog;
        dialog = new ProgressDialog(BookView.this);
        dialog.setMessage("Đang thực hiện đặt chỗ ...\nVui lòng đợi giây lát!");
        dialog.setIndeterminate(false);
        dialog.setMax(100);
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(false);
        dialog.setProgressStyle(ProgressDialog.STYLE_SPINNER);
        dialog.show();

        Gson gson = new Gson();
        JSONObject postParam = new JSONObject();
        try {
            JSONObject contactInfo = new JSONObject();
            contactInfo.put("fullname", txtName.getText().toString());
            contactInfo.put("phone", txtPhone.getText().toString());
            contactInfo.put("email", txtEmail.getText().toString());

            postParam.put("is_round_trip", bookingDetail.is_round_trip());
            postParam.put("origin_code", bookingDetail.getOrigin_code());
            postParam.put("destination_code", bookingDetail.getDestination_code());
            postParam.put("departure_date", bookingDetail.getDeparture_date());
            postParam.put("return_date", bookingDetail.is_round_trip() ? bookingDetail.getReturn_date() : null);
            postParam.put("adult", bookingDetail.getAdult());
            postParam.put("child", bookingDetail.getChild());
            postParam.put("infant", bookingDetail.getInfant());
            postParam.put("departure_f", new JSONObject(gson.toJson(bookingDetail.getDeparture_f())));
            postParam.put("return_f", bookingDetail.is_round_trip() ? new JSONObject(gson.toJson(bookingDetail.getReturn_f())) : null);
            postParam.put("pax_info", getPaxStringV4(paxInPut));
            postParam.put("total", (bookingDetail.getTotal() - discount + hanhLy));
            postParam.put("contact", contactInfo);
            postParam.put("payment", "");
            postParam.put("gcm", FCM_TOKEN);
            postParam.put("bag_fee", hanhLy);
            postParam.put("discount", discount);
            postParam.put("voucher", txtVoucherCode.getText());
            postParam.put("description", "");

        } catch (JSONException e) {
            e.printStackTrace();
        }

        (new SSLSendRequest(this)).POST(false, "AirLines/Booking", postParam, new SSLSendRequest.CallBackInterface() {
            @Override
            public void onSuccess(JSONObject response, boolean cached) {
                try {
                    if (response.has("data")) {

                        JSONObject json = response.getJSONObject("data");
                        Common.BOOKING_ID = json.getString("id");
                        Common.BOOKING_DETAIL = json;
                        Common.Email = json.getString("contact_email");
                        isNewData = true;
                    } else {
                        Common.BOOKING_ID = null;
                    }
                    dialog.dismiss();
                    if (Common.BOOKING_ID == null) {
                        Toast.makeText(getApplicationContext(), "Thật xin lỗi :( \nCó lỗi xảy ra khi kết nối máy chủ đặt chỗ \n Vui lòng thử lại hoặc gọi " + AppConfigs.getInstance().getConfig().getString("hotline") + " để được hỗ trợ!", Toast.LENGTH_SHORT).show();
                    } else {
                        Intent in = new Intent(getApplicationContext(), PnrActivity.class);
                        in.putExtra("email", Common.Email);
                        in.putExtra("bookingId", Common.BOOKING_ID);
                        in.putExtra("showLoading", true);

                        startActivity(in);

                        Intent intent = new Intent("bookingInsert");
                        LocalBroadcastManager.getInstance(getApplicationContext()).sendBroadcast(intent);

                        finish();
                    }

                } catch (JSONException e) {
                    dialog.dismiss();
                    Toast.makeText(getApplicationContext(), "Thật xin lỗi :( \nCó lỗi xảy ra khi kết nối máy chủ đặt chỗ \n Vui lòng thử lại hoặc gọi " + AppConfigs.getInstance().getConfig().getString("hotline") + " để được hỗ trợ!", Toast.LENGTH_SHORT).show();
                    AppConfigs.logException(e);
                    e.printStackTrace();
                }
            }

            @Override
            public void onFail(VolleyError e) {
                dialog.dismiss();
                Toast.makeText(getApplicationContext(), "Thật xin lỗi :( \nCó lỗi xảy ra khi kết nối máy chủ đặt chỗ \n Vui lòng thử lại hoặc gọi " + AppConfigs.getInstance().getConfig().getString("hotline") + " để được hỗ trợ!", Toast.LENGTH_SHORT).show();
                AppConfigs.logException(e);
                e.printStackTrace();

            }
        });
    }

    public void getVoucher() {

        dialog = new ProgressDialog(BookView.this);
        dialog.setMessage("Đang tải dữ liệu ...");
        dialog.setIndeterminate(false);
        dialog.setMax(100);
        dialog.setCanceledOnTouchOutside(true);
        dialog.setCancelable(true);
        dialog.setProgressStyle(ProgressDialog.STYLE_SPINNER);
        dialog.show();
        Map<String, String> postParam = new HashMap<String, String>();

        postParam.put("voucher", VOUCHERCODE);
        postParam.put("device_id", Common.ID_DEVICE);

        (new SSLSendRequest(this)).POST(false, "AirLines/Voucher/Use", new JSONObject(postParam), new SSLSendRequest.CallBackInterface() {
            @Override
            public void onSuccess(JSONObject response, boolean cached) {
                try {
                    if (!response.isNull("data")) {
                        JSONObject data = response.getJSONObject("data");
                        ProcessVoucher(data);
                    } else {
                        error = true;
                        showAlertDialog(BookView.this, "Thật tiếc", "Không tìm thấy thông tin voucher!", false, false);
                    }
                    if (dialog.isShowing()) dialog.dismiss();
                } catch (JSONException e) {
                    AppConfigs.logException(e);
                    e.printStackTrace();
                }
            }

            @Override
            public void onFail(VolleyError e) {
                if (dialog.isShowing()) dialog.dismiss();
                AppConfigs.Log("TG", "Error: " + e.getMessage());
                error = true;
                showAlertDialog(BookView.this, "Thông báo !", "Không tìm thấy voucher \nVui lòng liên lạc chúng tôi để được hỗ trợ", false, true);
            }
        });
    }

    private void ProcessVoucher(JSONObject data) {
        try {

            String rtcode = data.getString("voucher");
            int rtdiscount = data.getInt("discount");
            String rtresult = data.getString("text");

            code = rtcode;
            discount = rtdiscount;
            result = rtresult;

            if (rtdiscount > 0) {
                (findViewById(R.id.voucherLayout)).setVisibility(View.VISIBLE);
                Toast.makeText(getApplicationContext(), rtresult, Toast.LENGTH_SHORT).show();
                txtVoucherCode.setText(code);
                txtDisCount.setText("-" + Common.dinhDangTien(discount));
                txtGiaTongCong.setText(Common.dinhDangTien(giaGiaVe - discount + hanhLy));
                haveVoucher = true;
            } else {
                Toast.makeText(getApplicationContext(), result, Toast.LENGTH_SHORT).show();
            }

        } catch (JSONException e) {
            e.printStackTrace();
        }


    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        overridePendingTransition(R.anim.left_to_right, R.anim.right_to_left);

    }


    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK && requestCode == REQUEST_CODE_GETDATE) {
            if (data.hasExtra("date")) {

                Date inDate = Common.getDateFromString(data.getExtras().getString("date")).getTime();
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.US);

                setString(paxInPut, data.getExtras().getInt("id"), dateFormat.format(inDate));

            }
        }
        if (resultCode == RESULT_OK && requestCode == REQUEST_CODE_GETVOUCHER) {
            if (data.hasExtra("voucherCode")) {

                txtVoucherCode.setText(data.getStringExtra("voucherCode"));
                btnGetvoucher.performClick();

            }
        }
        if (requestCode == RC_SIGN_IN_UI) {
            IdpResponse response = IdpResponse.fromResultIntent(data);
            if (resultCode == RESULT_OK) {
                FirebaseUser user = FirebaseAuth.getInstance().getCurrentUser();
                Toast.makeText(getApplicationContext(), "Chào  " + user.getDisplayName(), Toast.LENGTH_SHORT).show();
                refreshLayout();

            } else {
                AppConfigs.Log("Login : ", " Fail");
            }
        }
    }

    public void showConfirmDialog() {

        AlertDialog alertDialog = new AlertDialog.Builder(BookView.this).create();
        alertDialog.setTitle("Chú ý");
        alertDialog.setIcon(R.drawable.ic_bell_alert);

        if (bookingDetail.getDeparture_f().getStops() > 0) {
            alertDialog.setMessage(Html.fromHtml("Chuyến bay của bạn chọn là chuyến bay <b>NỐI CHUYẾN</b> (có 1 điểm dừng)<br>Thời gian bay là <b>" + bookingDetail.getDeparture_f().getDuration() + "</b><br>Bạn có chắc chắn muốn đặt vé ?"));
        } else if (bookingDetail.is_round_trip()) {
            if (bookingDetail.getReturn_f().getStops() > 0) {
                alertDialog.setMessage(Html.fromHtml("Chuyến bay lượt về của bạn là chuyến bay <b>NỐI CHUYẾN</b> (có 1 điểm dừng)<br>Thời gian bay là <b>" + bookingDetail.getReturn_f().getDuration() + "</b><br>Bạn có chắc chắn muốn đặt vé ?"));
            } else {
                alertDialog.setMessage(Html.fromHtml(Common.AppPopup));
            }
        } else {
            alertDialog.setMessage(Html.fromHtml(Common.AppPopup));
        }
        alertDialog.setButton(DialogInterface.BUTTON_NEGATIVE, "Đặt vé", new DialogInterface.OnClickListener() {

            public void onClick(DialogInterface dialog, int which) {
                postBook();

            }
        });
        alertDialog.setButton(DialogInterface.BUTTON_POSITIVE, "Kiểm tra lại", new DialogInterface.OnClickListener() {
            public void onClick(DialogInterface dialog, int which) {

            }
        });

        alertDialog.show();

    }

    public void addBagSelectSheet(String paxName, TextView textTitle, Spinner bagSpinner, Spinner bagSpinnerVe) {

        bagSelectcontainer = dialogPromotionView.findViewById(R.id.bagSelectContainer);
        bagSelectcontainer.removeAllViews();
        String bagDepInKg = "";
        String bagRetInKg = "";

        ImageView depLogo = dialogPromotionView.findViewById(R.id.depLogo);
        ((TextView) dialogPromotionView.findViewById(R.id.depPaxName)).setText(paxName);
        depLogo.setImageResource(getResources().getIdentifier(bookingDetail.getDeparture_f().getLogo().toString(), "drawable", getPackageName()));
        ((TextView) dialogPromotionView.findViewById(R.id.depRoute)).setText((Common.getAirPortName(bookingDetail.getOrigin_code(), true) + " \uD83D\uDEEB " + Common.getAirPortName(bookingDetail.getDestination_code(), true)));
        TextView depBag = dialogPromotionView.findViewById(R.id.depBag);
        TextView retBag = dialogPromotionView.findViewById(R.id.retBag);

        List<View> listBagView = new ArrayList<>();

        try {
            for (int i = 0; i < bagFeeDeparture.length(); i++) {
                View bag = ViewUtil.genBagView(i, bagFeeDeparture.getJSONObject(i), getApplicationContext());

                if (bagSpinner.getSelectedItemPosition() == i) {
                    bag.findViewById(R.id.bagViewBg).setSelected(true);
                    ((TextView) bag.findViewById(R.id.txtBagValue)).setTextColor(getResources().getColor(R.color.primary_dark));
                    ((TextView) bag.findViewById(R.id.txtBagPrice)).setTextColor(getResources().getColor(R.color.primary_dark));
                } else {
                    bag.findViewById(R.id.bagViewBg).setSelected(false);
                    ((TextView) bag.findViewById(R.id.txtBagValue)).setTextColor(getResources().getColor(R.color.textDark));
                    ((TextView) bag.findViewById(R.id.txtBagPrice)).setTextColor(getResources().getColor(R.color.textDark));
                }

                bag.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        String pos = ((TextView) view.findViewById(R.id.pos)).getText().toString();
                        bagSpinner.setSelection((Integer.valueOf(pos)));
                        TextView txtBagDepKg = view.findViewById(R.id.txtBagValue);
                        TextView txtBagDepText = view.findViewById(R.id.txtBagPrice);

                        depBag.setText(txtBagDepKg.getText().toString());
                        String bagTitle = "";

                        if (depBag.getText().toString().length() > 3) {
                            bagTitle = bagTitle + " Lượt đi " + depBag.getText().toString();
                        }
                        if (bookingDetail.is_round_trip() && retBag.getText().toString().length() > 3) {
                            bagTitle = bagTitle + " Lượt về " + retBag.getText().toString();
                        }

                        textTitle.setText(("{faw_suitcase} Hành lý" + bagTitle));
                        new Iconics.Builder().on(textTitle).build();
                        clearSelectBagView(listBagView);

                        (view.findViewById(R.id.bagViewBg)).setSelected(true);
                        txtBagDepKg.setTextColor(getResources().getColor(R.color.primary_dark));
                        txtBagDepText.setTextColor(getResources().getColor(R.color.primary_dark));
                    }
                });
                listBagView.add(bag);
                bagSelectcontainer.addView(bag);
            }

            if (bookingDetail.is_round_trip()) {
                bagSelectcontainerRt = dialogPromotionView.findViewById(R.id.bagSelectContainerRt);
                bagSelectcontainerRt.removeAllViews();
                ImageView retLogo = dialogPromotionView.findViewById(R.id.retLogo);
                retLogo.setImageResource(getResources().getIdentifier(bookingDetail.getReturn_f().getLogo().toString(), "drawable", getPackageName()));
                ((TextView) dialogPromotionView.findViewById(R.id.retRoute)).setText((Common.getAirPortName(bookingDetail.getDestination_code(), true) + " \uD83D\uDEEB " + Common.getAirPortName(bookingDetail.getOrigin_code(), true)));

                List<View> listBagViewRt = new ArrayList<>();

                for (int i = 0; i < bagFeeReturn.length(); i++) {
                    View bag = ViewUtil.genBagView(i, bagFeeReturn.getJSONObject(i), getApplicationContext());
                    bag.findViewById(R.id.bagViewBg).setSelected(bagSpinnerVe.getSelectedItemPosition() == i);
                    bag.setOnClickListener(new OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            String pos = ((TextView) view.findViewById(R.id.pos)).getText().toString();
                            bagSpinnerVe.setSelection((Integer.valueOf(pos)));
                            TextView txtBagRtKg = view.findViewById(R.id.txtBagValue);
                            TextView txtBagRtText = view.findViewById(R.id.txtBagPrice);

                            retBag.setText(txtBagRtKg.getText().toString());
                            String bagTitle = "";

                            if (depBag.getText().toString().length() > 3) {
                                bagTitle = bagTitle + " Lượt đi " + depBag.getText().toString();
                            }
                            if (bookingDetail.is_round_trip() && retBag.getText().toString().length() > 3) {
                                bagTitle = bagTitle + " Lượt về " + retBag.getText().toString();
                            }
                            textTitle.setText(("{faw_suitcase} Hành lý" + bagTitle));

                            new Iconics.Builder().on(textTitle).build();
                            clearSelectBagView(listBagViewRt);
                            (view.findViewById(R.id.bagViewBg)).setSelected(true);
                            txtBagRtKg.setTextColor(getResources().getColor(R.color.primary_dark));
                            txtBagRtText.setTextColor(getResources().getColor(R.color.primary_dark));
                        }
                    });
                    listBagViewRt.add(bag);
                    bagSelectcontainerRt.addView(bag);
                }
                dialogPromotionView.findViewById(R.id.selectBagReturnLayout).setVisibility(View.VISIBLE);
            } else {
                dialogPromotionView.findViewById(R.id.selectBagReturnLayout).setVisibility(View.GONE);
            }


            dialogPromotionView.show();
        } catch (JSONException e) {
            e.printStackTrace();
            AppConfigs.logException(e);
        }


    }

    public void clearSelectBagView(List<View> listBagView) {
        for (View bagView : listBagView) {
            ((TextView) bagView.findViewById(R.id.txtBagPrice)).setTextColor(getResources().getColor(R.color.textDark));
            ((TextView) bagView.findViewById(R.id.txtBagValue)).setTextColor(getResources().getColor(R.color.textDark));

            bagView.findViewById(R.id.bagViewBg).setSelected(false);
        }
    }

    public boolean getBagFeeApi(BookingV2 booking, boolean isReturnTrip) {

        try {
            if (booking == null) return false;

            JSONObject params = new JSONObject();
            try {
                params.put("fareBasis", booking.getDeparture_f() == null ? "" : booking.getDeparture_f().getFareBasis());
                params.put("stops", booking.getDeparture_f() == null ? "" : booking.getDeparture_f().getStops());
            } catch (JSONException e) {
                e.printStackTrace();
                AppConfigs.logException(e);
            }


            String request = booking.getDeparture_f().getProvider() + "/" + booking.getOrigin_code() + "/" + booking.getDestination_code();

            if (isReturnTrip) {
                request = booking.getReturn_f().getProvider() + "/" + booking.getDestination_code() + "/" + booking.getOrigin_code();
                try {
                    params.put("fareBasis", booking.getReturn_f() != (null) ? booking.getReturn_f().getFareBasis() : "");
                    params.put("stops", booking.getReturn_f() != (null) ? booking.getReturn_f().getStops() : "");
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }

            (new SSLSendRequest(this)).GET(true, "AirLines/BagFee/" + request, params, new SSLSendRequest.CallBackInterface() {
                @Override
                public void onSuccess(JSONObject response, boolean cached) {
                    try {
                        JSONArray listBags = response.getJSONArray("data");

                        if (isReturnTrip) {
                            bagFeeReturn = listBags;
                        } else {
                            bagFeeDeparture = listBags;
                        }

                        if (listBags.length() > 0) {
                            for (int i = 0; i < listBags.length(); i++) {
                                JSONObject price = listBags.getJSONObject(i);
                                if (price.getInt("price") > 0) {
                                    if (isReturnTrip) {
                                        Common.Bag_Price_return.add(price.getString("text"));
                                    } else {
                                        Common.Bag_Price.add(price.getString("text"));
                                    }
                                }
                            }
                        }

                        if (booking.is_round_trip() && !isReturnTrip) {
                            getBagFeeApi(booking, true);
                        } else {
                            genPaxInput();
                        }

                    } catch (JSONException e) {
                        AppConfigs.logException(e);
                        e.printStackTrace();
                        genPaxInput();
                    }
                }

                @Override
                public void onFail(VolleyError error) {
                    error.printStackTrace();
                    AppConfigs.logException(error);
                    genPaxInput();
                }
            });
        } catch (Exception e) {
            AppConfigs.logException(e);
            e.printStackTrace();
        }
        return true;
    }

    @Override
    public void onStart() {
        super.onStart();
        LocalBroadcastManager.getInstance(this).registerReceiver(mMessageReceiver, new IntentFilter("bookingupdate"));

    }

    @Override
    public void onStop() {
        super.onStop();
        LocalBroadcastManager.getInstance(this).unregisterReceiver(mMessageReceiver);

    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        // Inflate the menu; this adds items to the action bar if it is present.
        getMenuInflater().inflate(R.menu.main_reward, menu);
        try {
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    View rewardButton = (View) findViewById(R.id.action_reward);

                    new QBadgeView(getApplicationContext())
                            .setGravityOffset(0, 0, true)
                            .bindTarget(rewardButton)
                            .setBadgeText("!")
                            .setOnDragStateChangedListener(new Badge.OnDragStateChangedListener() {
                                @Override
                                public void onDragStateChanged(int dragState, Badge badge, View targetView) {
                                    if (Badge.OnDragStateChangedListener.STATE_SUCCEED == dragState) {

                                    }

                                }
                            });
                }
            }, 1000);
        } catch (Exception e) {

        }

        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        // Take appropriate action for each action item click

        int itemId = item.getItemId();
        if (itemId == android.R.id.home) {
            return true;
        } else if (itemId == R.id.action_reward) {
            Intent in = new Intent(this, RewardActivity.class);
            startActivityForResult(in, REQUEST_CODE_GETVOUCHER);
            return true;
        }
        return false;
    }

    private BroadcastReceiver mMessageReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {

            String message = intent.getStringExtra("message");
            Snackbar snackbar = Snackbar
                    .make(coordinatorLayout, message, 60000)
                    .setAction("XEM CHI TIẾT", new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            Intent in = new Intent(getApplicationContext(), PnrActivity.class);
                            startActivity(in);
                            finish();
                        }
                    });
            snackbar.show();
        }
    };

    private void addAutoComplete(AutoCompleteTextView autoCompleteTextView, Spinner
            paxTitle, PassengerType paxType) {

        PassengerAdapter adapter = new PassengerAdapter(this, R.layout.pax_input_item_list, Widget.getPassengerList(), true);
        autoCompleteTextView.setAdapter(adapter);
        autoCompleteTextView.setThreshold(1);
        autoCompleteTextView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> adapterView, View view, int pos, long id) {

                Passenger selectedPerson = (Passenger) adapterView.getItemAtPosition(pos);
                if (selectedPerson.getTitle() == PassengerTitle.MR || selectedPerson.getTitle() == PassengerTitle.MSTR)
                    paxTitle.setSelection(0);
                else
                    paxTitle.setSelection(1);
            }
        });

    }

}