package com.hqt.datvemaybay;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import com.android.volley.Request;
import com.android.volley.Response;
import com.android.volley.VolleyError;
import com.android.volley.toolbox.JsonObjectRequest;

import com.facebook.shimmer.ShimmerFrameLayout;
import com.hqt.data.model.BusStation;
import com.hqt.util.AppConfigs;
import com.hqt.util.AppController;
import com.hqt.util.SSLSendRequest;
import com.hqt.util.SharedPrefs;
import com.hqt.view.adapter.AirPortAdapter;
import com.hqt.view.adapter.BusStationAdapter;
import com.hqt.view.adapter.TrainStationAdapter;
import com.hqt.data.model.Airport;
import com.hqt.data.model.TrainStation;
import com.hqt.view.ui.BaseActivity;
import com.mikepenz.iconics.context.IconicsContextWrapper;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

public class AirportSearch extends BaseActivity {
    // Declare Variables
    AirPortAdapter adapter;
    TrainStationAdapter adapterTr;
    BusStationAdapter adapterBus;
    EditText editsearch;
    RecyclerView recyclerView;
    LinearLayoutManager layoutManager;
    List<Airport> arraylist = new ArrayList<>();
    List<TrainStation> arraylistTr = new ArrayList<>();
    List<BusStation> arraylistBus = new ArrayList<>();
    ArrayList<Airport> arraylistAirport = new ArrayList<>();
    ArrayList<TrainStation> arraylistTrainStation = new ArrayList<>();
    ArrayList<BusStation> arraylistBusStation = new ArrayList<>();
    ShimmerFrameLayout mShimmerViewContainer;
    Button btnChoice;
    Boolean onChoiseAll = false;


    @Override
    protected int getLayoutId() {
        return R.layout.list_airport;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        getToolbar().setTitle("Chọn sân bay");
        getToolbar().setNavigationIcon(R.drawable.ic_action_back_home);
        recyclerView = findViewById(R.id.my_recycler_view);
        layoutManager = new LinearLayoutManager(this);
        recyclerView.setLayoutManager(layoutManager);
        editsearch = findViewById(R.id.search);
        mShimmerViewContainer = findViewById(R.id.shimmer_view_container);
        btnChoice = findViewById(R.id.btn_choice);

        boolean isSearchTrain = false;
        boolean isSearchBus = false;
        Intent in = getIntent();
        if (in.hasExtra("isSearchTrain")) {
            editsearch.setHint("Tìm thành phố hoặc ga");
            adapterTr = new TrainStationAdapter(this, arraylistTr);
            recyclerView.setAdapter(adapterTr);
            isSearchTrain = true;
        } else if (in.hasExtra("isSearchBus")) {
            editsearch.setHint("Tìm thành phố hoặc nơi đi");
            adapterBus = new BusStationAdapter(this, arraylistBus);
            recyclerView.setAdapter(adapterBus);
            isSearchBus = true;

        } else {
            int searchType = 0;
            if (in.hasExtra("PRICEBOARD_ORIGIN")) {
                onChoiseAll = true;
                searchType = 1;
                btnChoice.setVisibility(View.VISIBLE);
                btnChoice.setText("Chọn tất cả");
            } else if (in.hasExtra("PRICEBOARD_DESTINATION")) {
                searchType = 2;
                btnChoice.setVisibility(View.VISIBLE);
                btnChoice.setText("Chọn");

            } else if (in.hasExtra("PRICEBOARDMONTH_ORIGIN")) {
                onChoiseAll = false;
                searchType = 0;
                btnChoice.setVisibility(View.VISIBLE);
                btnChoice.setText("Chọn");
            }
            adapter = new AirPortAdapter(this, arraylist, searchType);
            recyclerView.setAdapter(adapter);

        }

        if (in.hasExtra("dep")) {
            String dep = in.getStringExtra("dep");

            if (isSearchTrain) {
                getToolbar().setTitle("Chọn ga đến");
                getTrainStation(dep);
            } else if (isSearchBus) {
                getToolbar().setTitle("Chọn nơi đến");
                getBusStation();
            } else {
                getToolbar().setTitle("Chọn sân bay đến");
                getAirportInfo(dep);
            }


        } else {
            if (isSearchTrain) {
                getToolbar().setTitle("Chọn ga đi");
                getTrainStation("");
            } else if (isSearchBus) {
                getToolbar().setTitle("Chọn nơi đi");
                getBusStation();
            } else {
                getToolbar().setTitle("Chọn sân bay đi");
                getAirportInfo("");
            }

        }
        btnChoice.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent in = new Intent();
                if (onChoiseAll) {
                    SharedPrefs.getInstance().delete("PRICEBOARD_ORIGIN");
                    SharedPrefs.getInstance().delete("PRICEBOARD_DESTINATION");
                    in.putExtra("SELECTALL", true);
                    setResult(RESULT_OK, in);
                } else {
                    if (adapter.getSelectItem().size() > 0) {
                        SharedPrefs.getInstance().put("PRICEBOARD_DESTINATION", adapter.getSelectItem());
                    }
                    setResult(RESULT_OK, in);
                }
                finish();
            }
        });
    }

    public void getTrainStation(String dep) {
        try {

            (new SSLSendRequest(this)).GET(true, "Trains/GetStation", new JSONObject(), new SSLSendRequest.CallBackInterface() {
                @Override
                public void onSuccess(JSONObject response, boolean cached) {
                    try {

                        JSONArray listTrainStations = response.getJSONArray("data");
                        if (listTrainStations.length() > 0) {
                            for (int i = 0; i < listTrainStations.length(); i++) {
                                JSONObject airport = listTrainStations.getJSONObject(i);
                                TrainStation wp = new TrainStation(airport.getInt("id"), airport.getString("name"), airport.getString("code"), airport.getString("searchText"));
                                arraylistTr.add(wp);
                            }

                            arraylistTrainStation.addAll(arraylistTr);
                            adapterTr.notifyDataSetChanged();

                            editsearch.clearFocus();
                            editsearch.addTextChangedListener(new TextWatcher() {
                                @Override
                                public void afterTextChanged(Editable arg0) {
                                    // TODO Auto-generated method stub
                                    String text = editsearch.getText().toString().toLowerCase(Locale.getDefault());
                                    String charText = text.toLowerCase();
                                    arraylistTr.clear();
                                    if (charText.length() == 0) {
                                        arraylistTr.addAll(arraylistTrainStation);
                                    } else {
                                        for (TrainStation wp : arraylistTrainStation) {
                                            if (wp.getSearchText().toLowerCase().contains(charText)) {
                                                arraylistTr.add(wp);
                                            }
                                        }
                                    }
                                    adapterTr.notifyDataSetChanged();
                                }

                                @Override
                                public void beforeTextChanged(CharSequence arg0, int arg1,
                                                              int arg2, int arg3) {
                                }

                                @Override
                                public void onTextChanged(CharSequence arg0, int arg1, int arg2,
                                                          int arg3) {
                                }
                            });
                        }
                        mShimmerViewContainer.stopShimmer();
                        mShimmerViewContainer.setVisibility(View.GONE);
                    } catch (JSONException e) {
                        //get defuult
                        //getListAirPortDefault();
                    }
                }

                @Override
                public void onFail(VolleyError error) {

                }
            });
            // Adding request to request queue
        } catch (Exception e) {
            AppConfigs.logException(e);
        }
    }

    public void getBusStation() {
        try {
            mShimmerViewContainer.startShimmer();
            mShimmerViewContainer.setVisibility(View.VISIBLE);

            (new SSLSendRequest(this)).GET(true, "Bus/Area", new JSONObject(), new SSLSendRequest.CallBackInterface() {
                @Override
                public void onSuccess(JSONObject response, boolean cached) {
                    try {

                        JSONArray listTrainStations = response.getJSONArray("data");
                        if (listTrainStations.length() > 0) {
                            for (int i = 0; i < listTrainStations.length(); i++) {
                                JSONObject airport = listTrainStations.getJSONObject(i);
                                BusStation wp = new BusStation(airport.getInt("id"), airport.getString("name"), airport.getString("base"), airport.getString("searchText"));
                                arraylistBus.add(wp);
                            }

                            arraylistBusStation.addAll(arraylistBus);
                            adapterBus.notifyDataSetChanged();

                            editsearch.clearFocus();
                            editsearch.addTextChangedListener(new TextWatcher() {
                                @Override
                                public void afterTextChanged(Editable arg0) {
                                    // TODO Auto-generated method stub
                                    String text = editsearch.getText().toString().toLowerCase(Locale.getDefault());
                                    String charText = text.toLowerCase();
                                    arraylistBus.clear();
                                    if (charText.length() == 0) {
                                        arraylistBus.addAll(arraylistBusStation);
                                    } else {
                                        for (BusStation wp : arraylistBusStation) {
                                            if (wp.getSearchText().toLowerCase().contains(charText.toLowerCase())) {
                                                arraylistBus.add(wp);
                                            }
                                        }
                                    }
                                    adapterBus.notifyDataSetChanged();
                                }

                                @Override
                                public void beforeTextChanged(CharSequence arg0, int arg1,
                                                              int arg2, int arg3) {
                                }

                                @Override
                                public void onTextChanged(CharSequence arg0, int arg1, int arg2,
                                                          int arg3) {
                                }
                            });
                        }
                        mShimmerViewContainer.stopShimmer();
                        mShimmerViewContainer.setVisibility(View.GONE);
                    } catch (JSONException e) {

                    }
                }

                @Override
                public void onFail(VolleyError error) {

                }
            });
            // Adding request to request queue
        } catch (Exception e) {
            AppConfigs.logException(e);
        }
    }

    public void getAirportInfo(String dep) {
        try {

            String tag_json_obj = "json_get_airport";
            String url = AppConfigs.getInstance().getConfig().getString("root_api") + "/api/v1/AirLines/AirPort/" + dep;
            final Context mcontext = this;
            JsonObjectRequest jsonObjReq = new JsonObjectRequest(Request.Method.GET,
                    url, null,
                    new Response.Listener<JSONObject>() {

                        @Override
                        public void onResponse(JSONObject response) {
                            try {
                                if (dep == null || dep.equals("")) {
                                    SharedPreferences settings = getSharedPreferences("12BAY-APP-CONFIG", 0);
                                    SharedPreferences.Editor editor = settings.edit();
                                    editor.putString("AIRPORTLIST", response.toString());
                                    editor.apply();
                                }

                                JSONArray listAirports = response.getJSONArray("data");
                                if (listAirports.length() > 0) {
                                    for (int i = 0; i < listAirports.length(); i++) {
                                        JSONObject airport = listAirports.getJSONObject(i);
                                        Airport wp = new Airport(airport.getString("name"), Common.unAccent(airport.getString("name")), airport.getString("city"), Common.unAccent(airport.getString("city")), airport.getString("code"), airport.getString("thumb"));
                                        arraylist.add(wp);
                                    }
                                    arraylistAirport.addAll(arraylist);
                                    adapter.notifyDataSetChanged();
                                    editsearch = findViewById(R.id.search);
                                    editsearch.clearFocus();
                                    editsearch.addTextChangedListener(new TextWatcher() {
                                        @Override
                                        public void afterTextChanged(Editable arg0) {
                                            // TODO Auto-generated method stub
                                            String text = editsearch.getText().toString().toLowerCase(Locale.getDefault());


                                            String charText = text.toLowerCase();
                                            arraylist.clear();
                                            if (charText.length() == 0) {
                                                arraylist.addAll(arraylistAirport);
                                            } else {
                                                for (Airport wp : arraylistAirport) {
                                                    if (wp.getName().toLowerCase().contains(charText)) {
                                                        arraylist.add(wp);
                                                    } else if (wp.getNameEn().toLowerCase().contains(charText)) {
                                                        arraylist.add(wp);
                                                    } else if (wp.getCity().toLowerCase().contains(charText)) {
                                                        arraylist.add(wp);
                                                    } else if (wp.getCityEn().toLowerCase().contains(charText)) {
                                                        arraylist.add(wp);
                                                    } else {
                                                        if (wp.getCode().toLowerCase().contains(charText)) {
                                                            arraylist.add(wp);
                                                        }
                                                    }
                                                }
                                            }
                                            adapter.notifyDataSetChanged();
                                        }

                                        @Override
                                        public void beforeTextChanged(CharSequence arg0, int arg1,
                                                                      int arg2, int arg3) {
                                        }

                                        @Override
                                        public void onTextChanged(CharSequence arg0, int arg1, int arg2,
                                                                  int arg3) {
                                        }
                                    });
                                }
                            } catch (JSONException e) {
                                //get defuult
                                getListAirPortDefault();
                            }
                            mShimmerViewContainer.stopShimmer();
                            mShimmerViewContainer.setVisibility(View.GONE);
                        }
                    }, new Response.ErrorListener() {

                @Override
                public void onErrorResponse(VolleyError error) {

                    getListAirPortDefault();
                }
            });
            AppController.getInstance().addToRequestQueue(jsonObjReq, tag_json_obj);

            // Adding request to request queue
        } catch (Exception e) {
            AppConfigs.logException(e);

        }


    }

    public void getListAirPortDefault() {
        Airport wp = new Airport("Tân Sơn Nhất", "Tan Son Nhat", "TP Hồ Chí Minh", "TP Ho Chi Minh", "SGN", "");
        arraylist.add(wp);
        wp = new Airport("Đà nẵng", "Da Nang", "Đà nẵng", "Da Nang", "DAD", "");
        arraylist.add(wp);
        wp = new Airport("Nội Bài", "Noi Bai", "Hà Nội", "Ha Noi", "HAN", "");
        arraylist.add(wp);
        wp = new Airport("Nha Trang", "Nha Trang", "Nha Trang", "Nha Trang", "CXR", "");
        arraylist.add(wp);
        wp = new Airport("TP Vinh", "TP Vinh", "TP Vinh", "TP Vinh", "VII", "");
        arraylist.add(wp);
        wp = new Airport("Cát Bi", "Cat Bi", "Hải Phòng", "Hai Phong", "HPH", "");
        arraylist.add(wp);
        wp = new Airport("Phú Quốc", "Phu Quoc", "Phú Quốc", "Phu Quoc", "PQC", "");
        arraylist.add(wp);
        wp = new Airport("Đồng Hới", "Dong Hoi", "Đồng Hới", "Quảng Bình", "VDH", "");
        arraylist.add(wp);
        wp = new Airport("Liên Khương", "Lien Khuong", "Đà Lạt", "da Lat", "DLI", "");
        arraylist.add(wp);
        wp = new Airport("Côn Đảo", "Con Dao", "Côn Đảo", "Con Dao", "VCS", "");
        arraylist.add(wp);
        wp = new Airport("Chu Lai", "Chu Lai", "Chu Lai", "Chu Lai", "VCL", "");
        arraylist.add(wp);
        wp = new Airport("Pleiku", "Pleiku", "Pleiku", "Pleiku", "PXU", "");
        arraylist.add(wp);
        wp = new Airport("Cần Thơ", "Can Tho", "Cần Thơ", "Can Tho", "VCA", "");
        arraylist.add(wp);
        wp = new Airport("Cà Mau", "Ca Mau", "Cà Mau", "Ca Mau", "CAH", "");
        arraylist.add(wp);
        wp = new Airport("Phú Bài", "Phu Bai", "Huế", "Hue", "HUI", "");
        arraylist.add(wp);
        wp = new Airport("Phù Cát", "Phu Cat", "Quy Nhơn", "Quy Nhon", "UIH", "");
        arraylist.add(wp);
        wp = new Airport("Rạch Giá", "Rach Gia", "Rạch Giá", "Rach Gia", "VKG", "");
        arraylist.add(wp);
        wp = new Airport("Thanh Hóa", "Thanh Hoa", "Thanh Hóa", "Thanh Hoa", "THD", "");
        arraylist.add(wp);
        wp = new Airport("Tuy Hòa", "Tuy Hoa", "Tuy Hòa", "Tuy Hoa", "TBB", "");
        arraylist.add(wp);
        wp = new Airport("Buôn Ma Thuột", "Buon Ma Thuot", "Buôn Ma Thuột", "Buon Ma Thuot", "BMV", "");
        arraylist.add(wp);
        wp = new Airport("Changi", "Changi", "Singapore", "Singapore", "SIN", "");
        arraylist.add(wp);
        wp = new Airport("Jakarta", "Jakarta", "Jakarta (Indonesia)", "Jakarta", "JKT", "");
        arraylist.add(wp);
        wp = new Airport("Bangkok", "Bangkok", "Bangkok (Thailand)", "Bangkok", "BKK", "");
        arraylist.add(wp);
        wp = new Airport("Kuala Lumpur", "Kuala Lumpur", "Kuala Lumpur (Malaysia)", "Kuala Lumpur", "KUL", "");
        arraylist.add(wp);
        wp = new Airport("Phnom Penh", "Phnom Penh", "Phnom Penh (Cambodia)", "Phnom Penh", "PNH", "");
        arraylist.add(wp);
        wp = new Airport("Yangon", "Yangon", "Yangon (Myamar)", "Yangon", "RGN", "");
        arraylist.add(wp);
        wp = new Airport("Vientiane", "Vientiane", "Vientiane (Laos)", "Vientiane", "VTE", "");
        arraylist.add(wp);
        wp = new Airport("Manila", "Manila", "Manila (Philippines)", "Manila", "MNL", "");
        arraylist.add(wp);
        wp = new Airport("Seoul", "Seoul", "Seoul (Hàn Quốc)", "Seoul", "SEL", "");
        arraylist.add(wp);
        wp = new Airport("Busan", "Busan", "Busan (Hàn Quốc)", "Busan", "PUS", "");
        arraylist.add(wp);
        wp = new Airport("Đài Bắc", "Dai Bac", "Đài Bắc (Đài Loan)", "Dai Bac", "TPE", "");
        arraylist.add(wp);
        wp = new Airport("Bắc Kinh", "Bắc Kinh", "Bắc Kinh", "Bac Kinh", "BJS", "");
        arraylist.add(wp);
        wp = new Airport("Hong Kong", "Hong Kong", "Hong Kong", "Hong Kong", "HKG", "");
        arraylist.add(wp);
        wp = new Airport("Thượng Hải", "Thuong Hai", "Thượng Hải", "Thuong Hai", "PVG", "");
        arraylist.add(wp);
        wp = new Airport("Narita", "Narita", "Narita (Nhật Bản)", "Narita", "NRT", "");
        arraylist.add(wp);
        wp = new Airport("New York", "New York", "New York", "New York", "JFK", "");
        arraylist.add(wp);
        wp = new Airport("Osaka", "Osaka", "Osaka", "Osaka", "KIX", "");
        arraylist.add(wp);
        wp = new Airport("Los Angeles", "Los Angeles", "Los Angeles", "Los Angeles", "LAX", "");
        arraylist.add(wp);
        wp = new Airport("San Francisco", "San Francisco", "San Francisco", "San Francisco", "SFO", "");
        arraylist.add(wp);
        wp = new Airport("Washington", "Washington", "Washington", "Washington", "WAS", "");
        arraylist.add(wp);
        wp = new Airport("Amsterdam", "Amsterdam", "Amsterdam", "Amsterdam", "AMS", "");
        arraylist.add(wp);
        wp = new Airport("London", "London", "London", "London", "LON", "");
        arraylist.add(wp);
        wp = new Airport("Paris", "Paris", "Paris", "Paris", "PAR", "");
        arraylist.add(wp);

        adapter = new AirPortAdapter(this, arraylist, 0);
        recyclerView.setAdapter(adapter);
        editsearch = findViewById(R.id.search);
        editsearch.addTextChangedListener(new TextWatcher() {

            @Override
            public void afterTextChanged(Editable arg0) {
                // TODO Auto-generated method stub
                String text = editsearch.getText().toString().toLowerCase(Locale.getDefault());
            }

            @Override
            public void beforeTextChanged(CharSequence arg0, int arg1,
                                          int arg2, int arg3) {
            }

            @Override
            public void onTextChanged(CharSequence arg0, int arg1, int arg2,
                                      int arg3) {
            }
        });
    }


//    @Override
//    protected void attachBaseContext(Context newBase) {
//        super.attachBaseContext(IconicsContextWrapper.wrap(newBase));
//    }


}
