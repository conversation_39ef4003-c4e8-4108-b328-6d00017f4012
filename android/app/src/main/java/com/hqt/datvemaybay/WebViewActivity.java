package com.hqt.datvemaybay;


import android.accounts.Account;
import android.accounts.AccountManager;
import android.annotation.SuppressLint;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.Toolbar;

import android.provider.Settings;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.WindowManager;
import android.webkit.CookieManager;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ProgressBar;
import android.widget.Toast;

import com.airbnb.lottie.LottieAnimationView;

import com.github.clans.fab.FloatingActionButton;
import com.github.clans.fab.FloatingActionMenu;
import com.google.firebase.iid.FirebaseInstanceId;
import com.hqt.util.AppConfigs;
import com.hqt.view.ui.BaseActivity;
import com.hqt.view.ui.HomeActivity;
import com.hqt.view.ui.LollipopFixedWebView;


import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.hqt.datvemaybay.Common.Email;
import static com.hqt.datvemaybay.Common.PhoneNumber;

@SuppressLint("SetJavaScriptEnabled")
public class WebViewActivity extends BaseActivity {
    AsyncTask<Void, Void, Void> mRegisterTask;
    private LollipopFixedWebView webView;
    ProgressBar progressBar;
    Boolean isConnectionExist = false, error = true;
    CheckInternet check;
    int appVer = 999;
    boolean loadErr = false;
    String toListCode = "";
    FloatingActionMenu fabMenu;
    FloatingActionButton fabFillter, fabAll;
    final int REQUEST_CODE_FILLTER = 1;
    Boolean isShowTip = false;
    SharedPreferences settings;
    String regId = "";
    Toolbar toolbar;
    String webLink = Common.WEBLINK;
    String webTitle = "12bay.vn";

    @Override
    protected int getLayoutId() {
        return R.layout.web_view;
    }

    @SuppressLint("AddJavascriptInterface")
    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        getToolbar().setTitle("12bay.vn");
        getToolbar().setNavigationIcon(R.drawable.ic_action_back_home);

        getToolbar().setNavigationOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                onBackPressed();
            }
        });
        progressBar = findViewById(R.id.progressBar);

        if (getIntent().getExtras() != null) {
            String promoLink = getIntent().getExtras().getString("promoUrl", null);
            if (promoLink != null && !promoLink.equals("")) {
                webLink = promoLink;
            }

            if (getIntent().hasExtra("webLink") && getIntent().hasExtra("webTitle")) {
                webLink = getIntent().getStringExtra("webLink");
                webTitle = getIntent().getStringExtra("webTitle");
                getToolbar().setTitle(webTitle);
                if (getIntent().hasExtra("webSubTitle")) {
                    getToolbar().setSubtitle(getIntent().getStringExtra("webSubTitle"));
                }
            }
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            getWindow().addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            getWindow().setStatusBarColor(getResources().getColor(R.color.primary_dark));
        }

        settings = getSharedPreferences("12BAY-APP-CONFIG", 0);
        toListCode = settings.getString("toListCode", "");
        isShowTip = settings.getBoolean("isShowTip", true);


        webView = findViewById(R.id.webView1);
        if (!webLink.contains("blog.12bay.vn")) {
            getWebviewVersionInfo();
        }
        WebSettings webSettings = webView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setAllowFileAccess(false);
        webSettings.setDomStorageEnabled(true);

        webView.addJavascriptInterface(new WebAppInterface(this), "Android");
        webView.addJavascriptInterface(new WebAppInterface(this), "AndroidWeekView");
        webView.getSettings().setAllowFileAccess(false);

        if (android.os.Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP)
            CookieManager.getInstance().setAcceptThirdPartyCookies(webView, true);

        webView.getSettings().setCacheMode(WebSettings.LOAD_DEFAULT);
        webView.setWebViewClient(new WebViewClient() {
            private int webViewPreviousState;
            private final int PAGE_STARTED = 0x1;

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {

                Intent i = Common.ConvertLinkAction(getApplicationContext(), Uri.parse(url));
                if (i.hasExtra("promoUrl")) {
                    view.loadUrl(Common.addUrlMobileParams(url));
                } else {
                    startActivity(i);
                    overridePendingTransition(R.anim.enter, R.anim.exit);
                }
                return true;
            }

            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                super.onPageStarted(view, url, favicon);

                progressBar.setVisibility(View.VISIBLE);
                webViewPreviousState = PAGE_STARTED;
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                if (webViewPreviousState == PAGE_STARTED) {

                }
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        progressBar.setVisibility(View.GONE);
                    }
                }, 1000);

            }

            @Override
            public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                loadErr = true;
                LottieAnimationView animationView = findViewById(R.id.animation_view);
                animationView.setAnimation(R.raw.antenna);
                animationView.loop(true);
                animationView.playAnimation();

                getToolbar().setTitle("12bay.vn");
                view.setVisibility(View.GONE);
                progressBar.setVisibility(View.GONE);

                Toast.makeText(getApplicationContext(), "Vui lòng kiểm tra lại kết nối", Toast.LENGTH_SHORT).show();
            }

        });

        if (isInternetConnected()) {
            if (Common.isOldChrome(webView)) {
                webView.loadUrl(Common.addUrlMobileParams(Common.getV2Link(webLink)));
            } else {
                webView.loadUrl(Common.addUrlMobileParams(webLink));
            }

            if (!Common.isLoaddingSetting) {
                googleGCM();
            }
        } else {
            Common.showAlertDialog(WebViewActivity.this, ":( Không có Internet",
                    "Xin vui lòng kiểm tra lại Wifi/3G để tiếp tục", false, true);
        }


        Intent appLinkIntent = getIntent();
        String appLinkAction = appLinkIntent.getAction();
        Uri appLinkData = appLinkIntent.getData();
        addBtnListener();
    }

    public void addBtnListener() {
        findViewById(R.id.btnHome).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                onBackPressed();
            }
        });
        findViewById(R.id.btnSetting).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                try {
                    Intent intent = new Intent(Settings.ACTION_WIRELESS_SETTINGS);
                    startActivity(intent);
                } catch (Exception e) {
                    AppConfigs.logException(e);
                    Toast.makeText(getApplicationContext(), "Vui lòng kiểm tra lại kết nối", Toast.LENGTH_SHORT).show();
                }
            }
        });
    }

    @Override
    public void onBackPressed() {
        if (webView.canGoBack() && !loadErr) {
            webView.goBack();
        } else {
            if (isTaskRoot()) {
                Intent intent = new Intent(this, HomeActivity.class);
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                startActivity(intent);
                finish();
            } else {
                super.onBackPressed();
            }

        }
    }

    public void googleGCM() {

        try {
            regId = FirebaseInstanceId.getInstance().getToken();
        } catch (Exception e) {
            AppConfigs.logException(e);
        }

        try {
            Account[] accounts = AccountManager.get(this).getAccountsByType("com.google");
            for (Account account : accounts) {
                Email = account.name;
                if (!Email.equals("")) break;

            }

        } catch (Exception e) {
            AppConfigs.logException(e);
        }


        if (regId != null && !regId.equals("")) {

            if (PhoneNumber == (null)) {
                PhoneNumber = "**********";
            }
            ServerUtilities.postAppSetting(this, "WEBVIEW");
        }
    }

    /**
     * Receiving push messages
     */


    @Override
    protected void onDestroy() {
        if (mRegisterTask != null) {
            mRegisterTask.cancel(true);
        }
        super.onDestroy();
    }


    @Override
    public void refreshLayout() {
        recreate();
    }

    public void getWebviewVersionInfo() {
        try {
            // Overridden UA string
            String alreadySetUA = webView.getSettings().getUserAgentString();

            // Next call to getUserAgentString() will get us the default
            webView.getSettings().setUserAgentString(null);
            String webViewVersion = webView.getSettings().getUserAgentString();

            Pattern pattern = Pattern.compile("Chrome/(\\d+)\\.");
            Matcher matcher = pattern.matcher(webViewVersion);

            if (matcher.find()) {
                int chromeVersion = Integer.valueOf(Integer.valueOf(matcher.group(1)));

                Bundle bun = new Bundle();
                bun.putString("user_agent", webViewVersion);
                bun.putInt("version", chromeVersion);
                bun.putInt("app_version", BuildConfig.VERSION_CODE);
                bun.putString("app_version", BuildConfig.VERSION_NAME);

                getFirebaseAnalytics().logEvent("WEBVIEW_BROWSER", bun);

                if (chromeVersion < 54) {
                    AlertDialog alertDialog = new AlertDialog.Builder(this).create();

                    alertDialog.setTitle("Thông báo");
                    alertDialog.setIcon(R.drawable.ic_bell_alert);
                    alertDialog.setMessage("Phiên bản phần mềm đã cũ\nVui lòng cập nhật để tiếp tục :)");
                    alertDialog.setButton(DialogInterface.BUTTON_POSITIVE, "Cập nhật", new DialogInterface.OnClickListener() {

                        public void onClick(DialogInterface dialog, int which) {

                            final String appPackageName = "com.google.android.webview";
                            try {
                                startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse("market://details?id=" + appPackageName)));
                            } catch (android.content.ActivityNotFoundException anfe) {
                                startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse("https://play.google.com/store/apps/details?id=" + appPackageName)));
                            }
                        }
                    });
                    alertDialog.setButton(DialogInterface.BUTTON_NEUTRAL, "Trở về", new DialogInterface.OnClickListener() {
                        public void onClick(DialogInterface dialog, int which) {

                        }
                    });
                    alertDialog.setCancelable(true);
                    alertDialog.show();

                }
            }

            webView.getSettings().setUserAgentString(alreadySetUA);

        } catch (Exception e) {
            AppConfigs.logException(e);
        }
    }
}