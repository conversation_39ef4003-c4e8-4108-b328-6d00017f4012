package com.hqt.datvemaybay

import android.app.ProgressDialog
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.Typeface
import android.os.Bundle
import android.util.TypedValue
import android.view.Menu
import android.view.View
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import com.android.volley.VolleyError
import com.db.williamchart.view.BarChartView
import com.hqt.util.AppConfigs
import com.hqt.util.SSLSendRequest
import com.hqt.util.SSLSendRequest.CallBackInterface
import com.hqt.view.ui.BaseActivity
import org.json.JSONArray
import org.json.JSONException
import org.json.JSONObject
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Collections
import java.util.Date

/**
 * Created by TN on 30/11/2015.
 */

class CheapPriceActivity : BaseActivity() {
    var mChartDi: BarChartView? = null
    var barchartDiBelow: BarChartView? = null
    var mChartVe: BarChartView? = null
    var barchartVeBelow: BarChartView? = null

    private val mDayDi = ArrayList<Calendar>()
    private val mValuesDi = ArrayList<ArrayList<Float>>()

    private val mDayVe = ArrayList<Calendar>()
    private val mValuesVe = ArrayList<ArrayList<Float>>()

    var jDepatureData: JSONArray? = null
    var jReturnData: JSONArray? = null
    private val arrayDepatureList = ArrayList<HashMap<String, String?>>()
    private val arrayReturnList = ArrayList<HashMap<String, String?>>()

    var q: String? = null
    var originCode: String? = null
    var destinationCode: String = ""

    var error: Boolean = true
    var isFistRun: Boolean = true
    var isShowTip: Boolean = true

    var isLoaddingVe: Int = 0
    var isLoaddingDi: Int = 0
    var idNgayDi: Int = 0
    var idNgayVe: Int = 2
    var isHaveFlightDi: Boolean = false
    var isHaveFlightVe: Boolean = false
    var onLoadMore: Boolean = false
    var getPrevious: Boolean = false
    var dialog: ProgressDialog? = null

    private var act: String? = null
    private var from: String? = null
    private var to: String? = null
    private var txtdepartureTime: String? = null
    private var txtreturnTime: String? = null
    private var adult = 0
    private var child = 0
    private var infant = 0
    private var departureTime: Date? = null
    private var returnTime: Date? = null
    private var isRoundTrip = false

    var next: Button? = null

    var maxLuotDi: Float = 0f
    var minLuotDi: Float = 0f
    var maxLuotVe: Float = 0f
    var minLuotVe: Float = 0f
    var idMinDi: Int = 0
    var idMinVe: Int = 0
    var n: Calendar? = null
    var dfm: SimpleDateFormat? = null
    var txtNgayVe: TextView? = null
    var txtNgayDi: TextView? = null
    var dayDi: Calendar? = null
    var dayVe: Calendar? = null
    var btnNextWeek: Button? = null
    var btnPreviousWeek: Button? = null
    var btnNextWeekVe: Button? = null
    var btnPreviousWeekVe: Button? = null
    var fontAwesome: Typeface? = null

    override fun getLayoutId(): Int {
        return R.layout.activity_barchart
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        mValuesVe.add(ArrayList())
        mValuesDi.add(ArrayList())
        fontAwesome = Typeface.createFromAsset(assets, "fonts/fontawesome-webfont.ttf")

        dfm = SimpleDateFormat("yyyy-MM-dd")
        n = Calendar.getInstance()




        minLuotDi = 90000f
        minLuotVe = minLuotDi
        maxLuotDi = 0f
        maxLuotVe = maxLuotDi

        txtNgayVe = findViewById(R.id.txtNgayVe)
        txtNgayDi = findViewById(R.id.txtNgayDi)
        btnNextWeek = findViewById(R.id.btnNextWeek)
        btnPreviousWeek = findViewById(R.id.btnPreviousWeek)
        btnNextWeekVe = findViewById(R.id.btnNextWeekVe)
        btnPreviousWeekVe = findViewById(R.id.btnPreviousWeekVe)
        next = findViewById(R.id.btnBookVe)

        //GET WWINDOWN HIEGHT
        val metrics = this.resources.displayMetrics
        val width = metrics.widthPixels
        val height = metrics.heightPixels

        //HANDEL INTENT
        val `in` = intent
        act = `in`.getStringExtra("act")
        from = `in`.getStringExtra("originCode")
        to = `in`.getStringExtra("destinationCode")
        txtdepartureTime = `in`.getStringExtra("departureTime")
        txtreturnTime = `in`.getStringExtra("returnTime")
        adult = `in`.getIntExtra("adult", 1)
        child = `in`.getIntExtra("child", 0)
        infant = `in`.getIntExtra("infant", 0)
        isRoundTrip = `in`.getBooleanExtra("isRoundTrip", false)




        btnNextWeek!!.setOnClickListener(View.OnClickListener {
            if (!onLoadMore) {
                nextWeek()
            }
        })
        btnPreviousWeek!!.setOnClickListener(View.OnClickListener {
            if (!onLoadMore) {
                previousWeek()
            }
        })

        btnNextWeekVe!!.setOnClickListener(View.OnClickListener {
            if (!onLoadMore) {
                nextWeek()
            }
        })
        btnPreviousWeekVe!!.setOnClickListener(View.OnClickListener {
            if (!onLoadMore) {
                previousWeek()
            }
        })


        if (toolbar != null) {
            toolbar!!.inflateMenu(R.menu.main)

            toolbar!!.setNavigationIcon(R.drawable.ic_action_back_home)
            toolbar!!.setTitleTextColor(Color.WHITE)
            toolbar!!.setSubtitleTextColor(Color.WHITE)


            val actionBar = supportActionBar
            if (actionBar != null) {
            }
        }

        departureTime = Common.stringToDate(txtdepartureTime, "yyyy-MM-dd").time

        dayDi = Calendar.getInstance()
        dayDi!!.setTime(departureTime)


        val b = from!!.split("\\s+".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
        val c = to!!.split("\\s+".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
        originCode = b[b.size - 1].replace(" ", "")
        destinationCode = c[c.size - 1].replace(" ", "")

        for (i in 0 until toolbar!!.childCount) {
            val view = toolbar!!.getChildAt(i)

            if (view is TextView) {
                view.setTypeface(fontAwesome)
            }
        }

        toolbar!!.title = "$originCode - $destinationCode"
        toolbar!!.subtitle = (Common.getAirPortName(
            originCode,
            true
        ) + " → " + Common.getAirPortName(destinationCode, true))


        if (isRoundTrip) {
            returnTime = Common.stringToDate(txtreturnTime, "yyyy-MM-dd").time

            genDateListDate(returnTime, mDayVe, mValuesVe)
            txtNgayVe!!.setText(Common.getFullDayLabel(dayVe) + " {faw_calendar}")
            idNgayVe = Common.getDayId(mDayVe, dayVe)

            (findViewById<View>(R.id.layout_chieuVe)).visibility =
                View.VISIBLE

            //SET RETURN TIME
            dayVe = Calendar.getInstance()
            dayVe!!.setTime(returnTime)
            txtNgayVe!!.setText(Common.getFullDayLabel(dayVe) + " {faw_calendar}")
        }

        genDateListDate(departureTime, mDayDi, mValuesDi)
        txtNgayDi!!.setText(Common.getFullDayLabel(dayDi) + " {faw_calendar}")
        idNgayDi = Common.getDayId(mDayDi, dayDi)

        if (isInternetConnected) {
            api
        } else {
            //Nếu không có kết nối với intenet thì
            Common.showAlertDialog(
                this@CheapPriceActivity, "Không có Internet",
                "Xin vui lòng kết nối Wifi/3G để tiếp tục", false, true
            )
        }

        next = findViewById(R.id.btnBookVe)
        next!!.setOnClickListener(object : View.OnClickListener {
            var err: Boolean = false

            override fun onClick(view: View) {
                val departTime = dfm!!.format(dayDi!!.getTime())
                var retTime: String? = ""
                if (isRoundTrip) {
                    retTime = dfm!!.format(dayVe!!.time)
                    val dif = dayVe!!.compareTo(dayDi)

                    if (dif <= 0) {
                        Toast.makeText(
                            application,
                            "Ngày trở về phải sau ngày khởi hành !",
                            Toast.LENGTH_LONG
                        ).show()
                        err = true
                    } else {
                        err = false
                    }
                }


                if (!err) {
                    val `in` = Intent(
                        applicationContext,
                        SearchResult::class.java
                    )
                    `in`.putExtra("originCode", from)
                    `in`.putExtra("destinationCode", to)
                    `in`.putExtra("departureTime", departTime)
                    `in`.putExtra("returnTime", retTime)
                    `in`.putExtra("adult", adult)
                    `in`.putExtra("child", child)
                    `in`.putExtra("infant", infant)
                    `in`.putExtra("isRoundTrip", isRoundTrip)

                    if (!isRoundTrip) {
                        startActivity(`in`)
                        overridePendingTransition(R.anim.enter, R.anim.exit)
                    } else {
                        if (dayVe!!.compareTo(dayDi) > 0) {
                            startActivity(`in`)
                            overridePendingTransition(R.anim.enter, R.anim.exit)
                        } else {
                            Toast.makeText(
                                applicationContext,
                                "Ngày trở về phải sau ngày khởi hành",
                                Toast.LENGTH_LONG
                            ).show()
                        }
                    }
                }
            }
        })
    }

    fun nextWeek() {
        try {
            getPrevious = false
            isFistRun = false
            onLoadMore = true

            val maxDay = Calendar.getInstance()
            maxDay.time = mDayDi[mDayDi.size - 1].time
            maxDay.add(Calendar.DATE, 3)
            departureTime = maxDay.time
            genDateListDate(departureTime, mDayDi, mValuesDi)


            if (isRoundTrip) {
                val maxDayVe = Calendar.getInstance()
                maxDayVe.time = mDayVe[mDayVe.size - 1].time
                maxDayVe.add(Calendar.DATE, 3)
                returnTime = maxDayVe.time
                genDateListDate(returnTime, mDayVe, mValuesVe)
            }

            api
        } catch (e: Exception) {
            AppConfigs.logException(e)
        }
    }

    fun previousWeek(): Boolean {
        getPrevious = true
        onLoadMore = true
        val now = Calendar.getInstance()
        now[Calendar.HOUR_OF_DAY] = 0
        now[Calendar.MINUTE] = 0
        now[Calendar.SECOND] = 0
        now[Calendar.MILLISECOND] = 0
        if ((Common.daysBetween(
                now.time,
                mDayDi[0].time
            ) <= 0) || (isRoundTrip && (Common.daysBetween(now.time, mDayVe[0].time) <= 0))
        ) {
            Toast.makeText(this, "Không quay về quá khứ được :)", Toast.LENGTH_SHORT).show()
            return true
        }

        isFistRun = false
        val minDay = Calendar.getInstance()
        minDay.time = mDayDi[0].time
        minDay.add(Calendar.DATE, -4)
        departureTime = minDay.time
        genDateListDate(departureTime, mDayDi, mValuesDi)


        if (isRoundTrip) {
            val minDayVe = Calendar.getInstance()
            minDayVe.time = mDayVe[0].time
            minDayVe.add(Calendar.DATE, -4)
            returnTime = minDayVe.time
            genDateListDate(returnTime, mDayVe, mValuesVe)
        }
        api
        return true
    }

    val api: Unit
        get() {
            dialog = ProgressDialog(this@CheapPriceActivity)
            dialog!!.setMessage("Đang tải dữ liệu ...\nVui lòng đợi !")
            dialog!!.isIndeterminate = false
            dialog!!.max = 100
            dialog!!.setCanceledOnTouchOutside(false)
            dialog!!.setProgressStyle(ProgressDialog.STYLE_SPINNER)
            dialog!!.show()

            getMonthPrices(departureTime, returnTime, isRoundTrip, originCode, destinationCode)
        }

    fun genDateListDate(
        cdate: Date?,
        mDay: ArrayList<Calendar>,
        mValues: ArrayList<ArrayList<Float>>
    ) {
        try {
            val dateFormat = SimpleDateFormat("yyyy-MM-dd")
            val n = Calendar.getInstance()
            val now = dateFormat.format(n.time)


            var c: Date? = Date()
            var no: Date? = Date()
            try {
                c = cdate
                no = dateFormat.parse(now)
            } catch (e: ParseException) {
                // TODO Auto-generated catch block
                e.printStackTrace()
            }

            val cal = Calendar.getInstance()
            cal.time = c
            n.time = no
            // so sanh ngay hien tai vs ngay chon.
            val d = Common.daysBetween(n.time, cal.time)
            if (d > 2) {
                // GEN 6 NGAY GAN - 2 truoc - 3 sau
                val d1 = Calendar.getInstance()
                d1.time = cal.time
                d1.add(Calendar.DATE, -2)
                mDay.add(d1)

                val d2 = Calendar.getInstance()
                d2.time = cal.time
                d2.add(Calendar.DATE, -1)
                mDay.add(d2)
                // NGAY CHON
                val d3 = Calendar.getInstance()
                d3.time = cal.time
                mDay.add(d3)

                val d4 = Calendar.getInstance()
                d4.time = cal.time
                d4.add(Calendar.DATE, 1)
                mDay.add(d4)

                val d5 = Calendar.getInstance()
                d5.time = cal.time
                d5.add(Calendar.DATE, 2)
                mDay.add(d5)

                val d6 = Calendar.getInstance()
                d6.time = cal.time
                d6.add(Calendar.DATE, 3)
                mDay.add(d6)
            } else {
                //NEU NGAY XUAT PHAT CACH NGAY XUAT PHAT <2 ngay thi ngay dau tien se la ngay hien tai thi lấy 6 ngày gần nhất
                val d1 = Calendar.getInstance()
                d1.time = n.time
                mDay.add(d1)

                val d2 = Calendar.getInstance()
                d2.time = n.time
                d2.add(Calendar.DATE, 1)
                mDay.add(d2)
                // NGAY CHON
                val d3 = Calendar.getInstance()
                d3.time = n.time
                d3.add(Calendar.DATE, 2)
                mDay.add(d3)

                val d4 = Calendar.getInstance()
                d4.time = n.time
                d4.add(Calendar.DATE, 3)
                mDay.add(d4)

                val d5 = Calendar.getInstance()
                d5.time = n.time
                d5.add(Calendar.DATE, 4)
                mDay.add(d5)

                val d6 = Calendar.getInstance()
                d6.time = n.time
                d6.add(Calendar.DATE, 5)
                mDay.add(d6)
            }


            Collections.sort(
                mDay
            ) { s1, s2 -> s1.compareTo(s2) }

            val dedupped: Set<Calendar> = LinkedHashSet(mDay)
            val temp = ArrayList(dedupped)

            mDay.clear()
            mValues[0].clear()
            for (i in temp.indices) {
                mDay.add(temp[i])
                mValues[0].add(-0.1f)
            }
        } catch (e: Exception) {
            AppConfigs.logException(e)
            Toast.makeText(this, "Có lỗi xẩy ra, Vui lòng thử lại sau", Toast.LENGTH_SHORT).show()
        }
    }

    fun showLuotVe(data: ArrayList<Float>?) {
        //        try {
//
//            Tooltip tooltip = new Tooltip(getApplicationContext(), R.layout.barchart_one_tooltip, R.id.value);
//
//            new Tooltip()
//            tooltip.setBackgroundColor(Color.parseColor("#f39c12"));
//            TextView va = ((TextView) tooltip.findViewById(R.id.value));
//            va.setTypeface(Typeface.createFromAsset(this.getAssets(), "fonts/Roboto-Regular.ttf"));
//
//            HorizontalScrollView horizontalScrollLuotve = findViewById(R.id.horizontalScrollLuotve);
//            horizontalScrollLuotve.removeAllViews();
//            LayoutInflater inflater = LayoutInflater.from(CheapPriceActivity.this);
//            inflater.inflate(R.layout.view_barchart_ve, horizontalScrollLuotve, true);
//
//
//            mChartVe = findViewById(R.id.barchartVe);
//
//            BarSet barSet = new BarSet(Common.getMlabel(mDayVe).toArray(new String[Common.getDayLabel(mDayVe).size()]), toPrimitive(data.toArray(new Float[data.size()])));
//            barSet.setColor(Color.parseColor("#68beff"));
//
//            mChartVe.addData(barSet);
//
//            Paint thresPaint = new Paint();
//            thresPaint.setColor(Color.RED);
//
//            thresPaint.setStyle(Paint.Style.FILL);
//            thresPaint.setAntiAlias(true);
//            thresPaint.setStrokeWidth(Tools.fromDpToPx(.75f));
//
//            mChartVe.setSetSpacing(Tools.fromDpToPx(10));
//            mChartVe.setBarSpacing(Tools.fromDpToPx(5));
//            mChartVe.setRoundCorners(Tools.fromDpToPx(2));
//            mChartVe.setBarBackgroundColor(Color.parseColor("#FFF1F1F1"));
//
//            LinearLayout.LayoutParams prams = new LinearLayout.LayoutParams(convDpToPx(this, (35 * Common.getDayLabel(mDayVe).size() + (15 * (Common.getDayLabel(mDayVe).size() - 1)) + (20 / 2))), convDpToPx(this, 160));
//            mChartVe.setLayoutParams(prams);
//
//
//            mChartVe.setXAxis(false)
//                    .setXLabels(XRenderer.LabelPosition.OUTSIDE)
//                    .setYAxis(false)
//                    .setYLabels(YRenderer.LabelPosition.NONE)
//                    // .setValueThreshold(minLuotVe + maxLuotVe/9, minLuotVe + maxLuotVe/9, thresPaint)
//                    .setLabelsColor(Color.parseColor("#003A6F"));
//
//            // Paint object used to draw Grid
//            Paint gridPaint = new Paint();
//            gridPaint.setColor(Color.parseColor("#727272"));
//            gridPaint.setStyle(Paint.Style.STROKE);
//            gridPaint.setAntiAlias(true);
//            gridPaint.setStrokeWidth(Tools.fromDpToPx(6));
//            gridPaint.setPathEffect(new DashPathEffect(new float[]{10, 10}, 0));
//            mChartVe.setGrid(0, 0, gridPaint);
//
//            mChartVe.setOnEntryClickListener(new OnEntryClickListener() {
//                @Override
//                public void onClick(int setIndex, int entryIndex, Rect rect) {
//
//                    txtNgayVe.setText(Common.getFullDayLabel(mDayVe.get(entryIndex)) + " {faw_calendar}");
//                    dayVe = mDayVe.get(entryIndex);
//
//                    idNgayVe = entryIndex;
//
//                    Tooltip tooltip = new Tooltip(getApplicationContext(), R.layout.barchart_one_tooltip, R.id.value);
//                    TextView va = (tooltip.findViewById(R.id.value));
//                    va.setTypeface(Typeface.createFromAsset(getApplication().getAssets(), "fonts/Roboto-Regular.ttf"));
//                    tooltip.setBackgroundColor(Color.parseColor("#f39c12"));
//
//                    tooltip.prepare(rect, data.get(entryIndex) - maxLuotVe / 9);
//                    tooltipsOn(mChartVe, data, maxLuotVe, idNgayVe);
//                    mChartVe.showTooltip(tooltip, true);
//                    showTooltip(mChartVe, idNgayVe);
//
//
//                }
//            });
//
//
//            Runnable chartOneAction = new Runnable() {
//                @Override
//                public void run() {
//                    tooltipsOn(mChartVe, data, maxLuotVe, idNgayVe);
//                    showTooltip(mChartVe, idNgayVe);
//
//                }
//            };
//
//            mChartVe.show(new Animation(500).withEndAction(chartOneAction));
//            // mChartVe.show(anim.setEndAction(chartOneAction));
//
//
//            //SHOW BELOW CHART
//            barchartVeBelow = findViewById(R.id.barchartVeBelow);
//            barchartVeBelow.reset();
//            BarSet barSet2 = new BarSet(Common.getDayLabel(mDayVe).toArray(new String[Common.getDayLabel(mDayVe).size()]), toPrimitive(mValuesVe.get(0).toArray(new Float[mValuesVe.get(0).size()])));
//            barSet2.setColor(Color.WHITE);
//            barchartVeBelow.addData(barSet2);
//            barchartVeBelow.setSetSpacing(Tools.fromDpToPx(10));
//            barchartVeBelow.setBarSpacing(Tools.fromDpToPx(5));
//
//            LinearLayout.LayoutParams prams2 = new LinearLayout.LayoutParams(convDpToPx(this, (35 * Common.getDayLabel(mDayVe).size() + (15 * (Common.getDayLabel(mDayVe).size() - 1)) + (20 / 2))), convDpToPx(this, 10));
//
//            barchartVeBelow.setLayoutParams(prams2);
//            barchartVeBelow.setXAxis(false)
//                    .setXLabels(XRenderer.LabelPosition.OUTSIDE)
//                    .setYAxis(false)
//                    .setYLabels(YRenderer.LabelPosition.NONE)
//                    .setLabelsColor(Color.parseColor("#003A6F"));
//
//            barchartVeBelow.show();
//
//            (findViewById(R.id.viewToolVe)).setVisibility(View.VISIBLE);
//
//        } catch (Exception e) {
//            AppConfigs.logException(e);
//            Toast.makeText(this, "Có lỗi xẩy ra, Vui lòng thử lại sau", Toast.LENGTH_SHORT).show();
//        }
    }

    fun showLuotDi(data: ArrayList<Float>?) {
        //        try {
//
//            final Tooltip tooltip = new Tooltip(getApplicationContext(), R.layout.barchart_one_tooltip, R.id.value);
//            tooltip.setBackgroundColor(Color.parseColor("#f39c12"));
//            TextView va = (tooltip.findViewById(R.id.value));
//            va.setTypeface(Typeface.createFromAsset(this.getAssets(), "fonts/Roboto-Regular.ttf"));
//
//
//            HorizontalScrollView horizontalScrollLuotDi = findViewById(R.id.horizontalScrollLuotDi);
//            horizontalScrollLuotDi.removeAllViews();
//            LayoutInflater inflater = LayoutInflater.from(CheapPriceActivity.this);
//            inflater.inflate(R.layout.view_barchart_di, horizontalScrollLuotDi, true);
//
//            mChartDi = findViewById(R.id.barchartDi);
//
//            BarSet barSet = new BarSet(Common.getMlabel(mDayDi).toArray(new String[Common.getDayLabel(mDayDi).size()]), toPrimitive(data.toArray(new Float[data.size()])));
//            barSet.setColor(Color.parseColor("#68beff"));
//            mChartDi.addData(barSet);
//
//            Paint thresPaint = new Paint();
//            thresPaint.setColor(Color.RED);
//
//            thresPaint.setStyle(Paint.Style.FILL);
//            thresPaint.setAntiAlias(true);
//            thresPaint.setStrokeWidth(Tools.fromDpToPx(.75f));
//
//            mChartDi.setSetSpacing(Tools.fromDpToPx(10));
//            mChartDi.setBarSpacing(Tools.fromDpToPx(5));
//            mChartDi.setRoundCorners(Tools.fromDpToPx(2));
//            mChartDi.setBarBackgroundColor(Color.parseColor("#FFF1F1F1"));
//
//            LinearLayout.LayoutParams prams = new LinearLayout.LayoutParams(convDpToPx(this, (35 * Common.getDayLabel(mDayDi).size() + (15 * (Common.getDayLabel(mDayDi).size() - 1)) + (20 / 2))), convDpToPx(this, 160));
//            mChartDi.setLayoutParams(prams);
//
//
//            mChartDi.setXAxis(false)
//                    .setXLabels(XRenderer.LabelPosition.OUTSIDE)
//                    .setYAxis(false)
//                    .setYLabels(YRenderer.LabelPosition.NONE)
//                    //    .setValueThreshold(minLuotDi + maxLuotDi/9, minLuotDi + maxLuotDi/9, thresPaint)
//                    .setLabelsColor(Color.parseColor("#003A6F"));
//            // Paint object used to draw Grid
//            Paint gridPaint = new Paint();
//            gridPaint.setColor(Color.parseColor("#727272"));
//            gridPaint.setStyle(Paint.Style.STROKE);
//            gridPaint.setAntiAlias(true);
//            gridPaint.setStrokeWidth(Tools.fromDpToPx(6));
//            gridPaint.setPathEffect(new DashPathEffect(new float[]{10, 10}, 0));
//            mChartDi.setGrid(0, 0, gridPaint);
//
//            // Animation customization
//
//
//            mChartDi.setOnEntryClickListener(new OnEntryClickListener() {
//                @Override
//                public void onClick(int setIndex, int entryIndex, Rect rect) {
//
//                    dayDi = mDayDi.get(entryIndex);
//                    txtNgayDi.setText(Common.getFullDayLabel(dayDi) + " {faw_calendar}");
//                    idNgayDi = entryIndex;
//
//
//                    Tooltip tooltip = new Tooltip(getApplicationContext(), R.layout.barchart_one_tooltip, R.id.value);
//                    TextView va = (tooltip.findViewById(R.id.value));
//                    va.setTypeface(Typeface.createFromAsset(getApplication().getAssets(), "fonts/Roboto-Regular.ttf"));
//                    tooltip.setBackgroundColor(Color.parseColor("#f39c12"));
//
//                    tooltip.prepare(rect, data.get(entryIndex) - maxLuotDi / 9);
//                    tooltipsOn(mChartDi, data, maxLuotDi, idNgayDi);
//                    mChartDi.showTooltip(tooltip, true);
//                    showTooltip(mChartDi, idNgayDi);
//
//
//                }
//            });
//
//
//            Runnable chartOneAction = new Runnable() {
//                @Override
//                public void run() {
//                    tooltipsOn(mChartDi, data, maxLuotDi, idNgayDi);
//                    showTooltip(mChartDi, idNgayDi);
//
//                }
//            };
//
//            mChartDi.show(new Animation(500).withEndAction(chartOneAction));
//            //SHOW BELOW CHART
//            barchartDiBelow = findViewById(R.id.barchartDiBelow);
//            barchartDiBelow.reset();
//            BarSet barSet2 = new BarSet(Common.getDayLabel(mDayDi).toArray(new String[Common.getDayLabel(mDayDi).size()]), toPrimitive(mValuesDi.get(0).toArray(new Float[mValuesDi.get(0).size()])));
//            barSet2.setColor(Color.WHITE);
//            barchartDiBelow.addData(barSet2);
//            barchartDiBelow.setSetSpacing(Tools.fromDpToPx(10));
//            barchartDiBelow.setBarSpacing(Tools.fromDpToPx(5));
//
//            LinearLayout.LayoutParams prams2 = new LinearLayout.LayoutParams(convDpToPx(this, (35 * Common.getDayLabel(mDayDi).size() + (15 * (Common.getDayLabel(mDayDi).size() - 1)) + (20 / 2))), convDpToPx(this, 10));
//
//            barchartDiBelow.setLayoutParams(prams2);
//            barchartDiBelow.setXAxis(false)
//                    .setXLabels(XRenderer.LabelPosition.OUTSIDE)
//                    .setYAxis(false)
//                    .setYLabels(YRenderer.LabelPosition.NONE)
//                    .setLabelsColor(Color.parseColor("#003A6F"));
//
//            barchartDiBelow.show();
//
//            (findViewById(R.id.viewToolDi)).setVisibility(View.VISIBLE);
//
//
//        } catch (Exception e) {
//            AppConfigs.logException(e);
//            Toast.makeText(this, "Có lỗi xẩy ra, Vui lòng thử lại sau", Toast.LENGTH_SHORT).show();
//        }
    }

    private fun showTooltip(mChar: BarChartView, id: Int) {
//        try {
//            Tooltip tip = new Tooltip(getApplicationContext(), R.layout.square_tooltip);
//            tip.setVerticalAlignment(Tooltip.Alignment.BOTTOM_TOP);
//            tip.setDimensions((int) Tools.fromDpToPx(30), (int) Tools.fromDpToPx(30));
//            tip.setMargins(0, 0, 0, (int) Tools.fromDpToPx(2));
//            tip.prepare(mChar.getEntriesArea(0).get(id), 0);
//            mChar.showTooltip(tip, true);
//        } catch (Exception e) {
//            AppConfigs.logException(e);
//        }
    }

    private fun tooltipsOn(
        mChart: BarChartView,
        data: ArrayList<Float>,
        max: Float,
        idSelect: Int
    ) {
//        try {
//            mChart.dismissAllTooltips();
//            ArrayList<ArrayList<Rect>> areas = new ArrayList<>();
//            areas.add(mChart.getEntriesArea(0));
//
//            for (int j = 0; j < areas.get(0).size(); j++) {
//
//                Tooltip tooltip = new Tooltip(getApplicationContext(), R.layout.barchart_one_tooltip, R.id.value);
//                TextView va = (tooltip.findViewById(R.id.value));
//                va.setTypeface(Typeface.createFromAsset(this.getAssets(), "fonts/Roboto-Regular.ttf"));
//                if (j == idSelect) tooltip.setBackgroundColor(Color.parseColor("#f39c12"));
//
//                tooltip.prepare(areas.get(0).get(j), data.get(j) - (max / 9));
//                mChart.showTooltip(tooltip, tưrue);
//            }
//        } catch (Exception e) {
//            AppConfigs.logException(e);
//            e.printStackTrace();
//        }
    }

    private fun veBieuDoVe(
        hMap: ArrayList<HashMap<String, String?>>,
        mLabel: ArrayList<String>,
        mValue: ArrayList<ArrayList<Float>>
    ) {
        try {
            val temp2 = ArrayList<Float>()
            for (item in hMap) {
                val date = item[TAG_DATE]
                val price = item[TAG_PRICE]!!.toInt()
                AppConfigs.Log("mLabel.get(i)", date)
                for (i in mLabel.indices) {
                    if (date == mLabel[i]) {
                        if (mValue[0][i] != -0.1f) {
                            if (price < mValue[0][i]) {
                                mValue[0][i] = price.toFloat()
                            }
                        } else {
                            mValue[0][i] = price.toFloat()
                        }
                        //TIM GIA TRI LON NHAT VA VI TRI NHO NHAT
                        break
                    }
                }
            }

            for (i in mValue[0].indices) {
                if (mValue[0][i] > maxLuotVe) {
                    maxLuotVe = mValue[0][i]
                }
                if ((mValue[0][i] < minLuotVe) && (mValue[0][i] != -0.1f)) {
                    minLuotVe = mValue[0][i]
                    idMinVe = i
                    // Log.w("compare",mValue.get(0).get(i)+"");
                }
            }

            //++ ADD THEM MAX/9 truong hop 0 thi van co chieu cao
            for (d in mValue[0]) {
                if (d != -0.1f) {
                    temp2.add(d + maxLuotVe / 9)
                } else temp2.add(0f)
            }
            if (temp2.size > 32) {
                if (!getPrevious) {
                    hMap.subList(0, 5).clear()
                    mDayVe.subList(0, 5).clear()
                    temp2.subList(0, 5).clear()
                    mValuesVe[0].subList(0, 5).clear()
                } else {
                    hMap.subList(hMap.size - 6, hMap.size - 1).clear()
                    mDayVe.subList(mDayVe.size - 6, mDayVe.size - 1).clear()
                    temp2.subList(temp2.size - 6, temp2.size - 1).clear()
                    mValuesVe[0].subList(mValuesVe[0].size - 6, mValuesVe[0].size - 1).clear()
                }
            }

            showLuotVe(temp2)
        } catch (e: Exception) {
            AppConfigs.logException(e)
            Toast.makeText(this, "Có lỗi xẩy ra, Vui lòng thử lại sau", Toast.LENGTH_SHORT).show()
        }
    }

    private fun veBieuDoDi(
        hMap: ArrayList<HashMap<String, String?>>,
        mLabel: ArrayList<String>,
        mValue: ArrayList<ArrayList<Float>>
    ) {
        try {
            val temp2 = ArrayList<Float>()
            for (item in hMap) {
                val date = item[TAG_DATE]
                val price = item[TAG_PRICE]!!.toInt()

                for (i in mLabel.indices) {
                    if (date == mLabel[i]) {
                        if (mValue[0][i] != -0.1f) {
                            if (price < mValue[0][i]) {
                                mValue[0][i] = price.toFloat()
                            }
                        } else {
                            mValue[0][i] = price.toFloat()
                        }
                        break
                    }
                }
            }


            for (i in mValue[0].indices) {
                if (mValue[0][i] > maxLuotDi) {
                    maxLuotDi = mValue[0][i]
                }
                if ((mValue[0][i] < minLuotDi) && (mValue[0][i] != -0.1f)) {
                    minLuotDi = mValue[0][i]
                    idMinDi = i
                }
            }

            //++ ADD THEM MAX/9 truong hop 0 thi van co chieu cao
            for (d in mValue[0]) {
                if (d != -0.1f) {
                    temp2.add(d + maxLuotDi / 9)
                } else temp2.add(0f)
            }


            if (temp2.size > 32) {
                if (!getPrevious) {
                    hMap.subList(0, 5).clear()
                    mDayDi.subList(0, 5).clear()
                    temp2.subList(0, 5).clear()
                    mValuesDi[0].subList(0, 5).clear()
                } else {
                    hMap.subList(hMap.size - 6, hMap.size - 1).clear()
                    mDayDi.subList(mDayDi.size - 6, mDayDi.size - 1).clear()
                    temp2.subList(temp2.size - 6, temp2.size - 1).clear()
                    mValuesDi[0].subList(mValuesDi[0].size - 6, mValuesDi[0].size - 1).clear()
                }
            }

            showLuotDi(temp2)
        } catch (e: Exception) {
            AppConfigs.logException(e)
            Toast.makeText(this, "Có lỗi xẩy ra, Vui lòng thử lại sau", Toast.LENGTH_SHORT).show()
        }
    }

    fun getMonthPrices(
        depatureDate: Date?,
        returnDate: Date?,
        isRoundTrip: Boolean,
        originCode: String?,
        destinationCode: String?
    ) {
        // loadedMonth = loadedMonth + "-" + month;

        val postParam = JSONObject()
        val route = JSONArray()
        try {
            postParam.put("departureDate", Common.dateToString(depatureDate, "yyyy-MM-dd"))
            postParam.put("returnDate", Common.dateToString(returnDate, "yyyy-MM-dd"))
            postParam.put("isRoundTrip", isRoundTrip)
            postParam.put("originCode", originCode)
            postParam.put("destinationCode", destinationCode)
            postParam.put("source", "ANDROID")
            postParam.put("key", Common.getKeyHash())
            postParam.put("ver", BuildConfig.VERSION_CODE.toString() + "")
        } catch (e: JSONException) {
            AppConfigs.logException(e)
        }
        (SSLSendRequest(this)).POST(
            false,
            "AirLines/PricesBoard/Week",
            postParam,
            object : CallBackInterface {
                override fun onSuccess(response: JSONObject, cached: Boolean) {
                    try {
                        val data = response.getJSONObject("data")
                        paserJsonDate(data)
                    } catch (e: JSONException) {
                        AppConfigs.logException(e)
                        e.printStackTrace()
                    }
                }

                override fun onFail(e: VolleyError) {
                    AppConfigs.logException(e)
                    e.printStackTrace()
                    onLoadMore = false
                }
            })
    }

    private fun paserJsonDate(jData: JSONObject): BooleanArray {
        try {
            var dataDi = false
            var dataVe = false

            jDepatureData = jData.getJSONArray("departure")
            jReturnData = jData.getJSONArray("return")


            for (i in 0 until jDepatureData!!.length()) {
                //NEU TRA VE CO KET QUA CHUYEN BAY
                isHaveFlightDi = true
                dataDi = true
                error = false
                val c = jDepatureData!!.getJSONObject(i)

                // Storing each json item in variable
                val date = c.getString("date")
                val price = c.getInt("price")

                // creating new HashMap
                val childData = HashMap<String, String?>()

                childData[TAG_DATE] = date
                childData[TAG_PRICE] =
                    Math.round((price / 1000).toFloat()).toString() + ""
                childData[TAG_FROM] = originCode
                childData[TAG_TO] = destinationCode


                arrayDepatureList.add(childData)
            }

            for (i in 0 until jReturnData!!.length()) {
                error = false
                dataVe = true
                isHaveFlightVe = true
                val c = jReturnData!!.getJSONObject(i)

                // Storing each json item in variable
                val date = c.getString("date")
                val price = c.getInt("price")

                // creating new HashMap
                val childData = HashMap<String, String?>()

                childData[TAG_DATE] = date
                childData[TAG_PRICE] =
                    Math.round((price / 1000).toFloat()).toString() + ""
                childData[TAG_FROM] = destinationCode
                childData[TAG_TO] = originCode


                arrayReturnList.add(childData)
            }
            val arr = booleanArrayOf(dataDi, dataVe)

            if (dialog!!.isShowing) dialog!!.dismiss()

            veBieuDoDi(arrayDepatureList, Common.getLabelForAddValue(mDayDi), mValuesDi)


            if (isRoundTrip) veBieuDoVe(
                arrayReturnList,
                Common.getLabelForAddValue(mDayVe),
                mValuesVe
            )

            if (!isFistRun && isShowTip) {
                Toast.makeText(
                    application,
                    "Trượt biểu đồ qua phải để xem thêm kết quả",
                    Toast.LENGTH_LONG
                ).show()
                isShowTip = false
            }
            onLoadMore = false

            return arr
        } catch (e: JSONException) {
            error = true
            AppConfigs.logException(e)
            e.printStackTrace()
            val arr = booleanArrayOf(false, false)
            return arr
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        // Inflate the menu; this adds items to the action bar if it is present.
        menuInflater.inflate(R.menu.main, menu)
        return true
    }

    override fun onPrepareOptionsMenu(menu: Menu): Boolean {
        val searchMenu = menu.findItem(R.id.action_search)
        searchMenu.setIcon(R.drawable.ic_action_call)
        return true
    }

    override fun onBackPressed() {
        super.onBackPressed()
        overridePendingTransition(R.anim.left_to_right, R.anim.right_to_left)
    } //    @Override
    //    protected void attachBaseContext(Context newBase) {
    //        super.attachBaseContext(IconicsContextWrapper.wrap(newBase));
    //    }


    companion object {
        private const val TAG_CHIEUDI = "arrayDepatureList"
        private const val TAG_CHIEUVE = "arrayReturnList"
        private const val TAG_DATE = "date"
        private const val TAG_PRICE = "price"
        private const val TAG_FROM = "from"
        private const val TAG_TO = "to"

        fun convDpToPx(context: Context, dp: Float): Int {
            val metrics = context.resources.displayMetrics
            return TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dp, metrics).toInt()
        }

        fun toPrimitive(array: Array<Float>?): FloatArray? {
            if (array == null) {
                return null
            } else if (array.size == 0) {
                return null
            }
            val result = FloatArray(array.size)
            for (i in array.indices) {
                result[i] = array[i]
            }
            return result
        }
    }
}