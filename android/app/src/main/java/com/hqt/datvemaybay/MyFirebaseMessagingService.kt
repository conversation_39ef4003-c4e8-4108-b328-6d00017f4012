package com.hqt.datvemaybay

import android.annotation.SuppressLint
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.PendingIntent.FLAG_IMMUTABLE
import android.app.PendingIntent.FLAG_MUTABLE
import android.app.UiModeManager
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Color
import android.media.RingtoneManager
import android.net.Uri
import android.os.Build
import android.util.Log
import android.view.View
import android.widget.RemoteViews
import androidx.appcompat.app.AppCompatDelegate
import androidx.core.app.NotificationCompat
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.google.firebase.iid.FirebaseInstanceId
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import com.hqt.util.AppConfigs
import com.hqt.util.SharedPrefs
import java.net.HttpURLConnection
import java.net.URL

class MyFirebaseMessagingService : FirebaseMessagingService() {
    override fun onCreate() {

        super.onCreate()
    }

    override fun onDestroy() {
        super.onDestroy()
    }

    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        try {
            if (remoteMessage.data.isNotEmpty()) {
                createNotification(remoteMessage)
                val action_link = remoteMessage.data["action_link"]

                if (action_link!!.contains("xu-ly-don-hang") || action_link.contains("don-hang")) {
                    val intent = Intent("bookingupdate")
                    intent.putExtra("message", remoteMessage.data["title"])
                    LocalBroadcastManager.getInstance(this).sendBroadcast(intent)
                }

            }
        } catch (e: Exception) {
            e.printStackTrace()
            AppConfigs.logException(e)
        }


        // Check if message contains a notification payload.
        if (remoteMessage.notification != null) {
            AppConfigs.Log(TAG,
                "Message Notification Body: " + remoteMessage.notification!!.clickAction + remoteMessage.data.toString() + remoteMessage.notification!!.body)
        }
    }

    @SuppressLint("InlinedApi") private fun createNotification(remoteMessage: RemoteMessage) {
        val data = remoteMessage.data
        val intent = Common.ConvertLinkAction(this, Uri.parse(data["action_link"]))


        if (!remoteMessage.data["track"].isNullOrBlank()) {
            intent.putExtra("track", remoteMessage.data["track"])
        }

        val channelId = getString(R.string.default_notification_channel_id)
        var picture_url: String? = ""
        try {
            if (data.containsKey("image")) {
                picture_url = data["image"]
            }
            if (picture_url == "" && remoteMessage.notification != null && remoteMessage.notification!!.imageUrl != null) {
                picture_url = remoteMessage.notification!!.imageUrl.toString()
            }

        } catch (e: Exception) {
            e.printStackTrace()
            picture_url = ""
        }

        val notificationLayout = RemoteViews(packageName, R.layout.notification_small)
        val notificationLayoutExpanded = RemoteViews(packageName, R.layout.notification_large)

        notificationLayout.setTextViewText(R.id.notification_small_title, remoteMessage.notification!!.title)
        notificationLayout.setTextViewText(R.id.notification_small_message, remoteMessage.notification!!.body)

        notificationLayoutExpanded.setTextViewText(R.id.notification_large_title, remoteMessage.notification!!.title)
        notificationLayoutExpanded.setTextViewText(R.id.notification_large_message, remoteMessage.notification!!.body)

        notificationLayout.setImageViewResource(R.id.notification_img, R.drawable.ic_logo_no_text)

        if (picture_url != "") {
            val bigPicture = getBitmapfromUrl(picture_url)
            notificationLayout.setImageViewBitmap(R.id.notification_img, bigPicture)
            notificationLayoutExpanded.setImageViewBitmap(R.id.notification_img, bigPicture)
            notificationLayoutExpanded.setViewVisibility(R.id.notification_img, View.VISIBLE)
        }

        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
        val pendingIntent: PendingIntent = PendingIntent.getActivity(this, 0, intent, PendingIntent.FLAG_IMMUTABLE)

        val builder: NotificationCompat.Builder = NotificationCompat.Builder(this, channelId)
        builder.setSmallIcon(R.mipmap.ic_launcher).setAutoCancel(true).setColor(Color.RED)
            .setLights(Color.RED, 1000, 300).setAutoCancel(true).setSubText("Thông báo").setContentText(data["body"])
            .setContentInfo(remoteMessage.notification!!.body).setContentTitle(remoteMessage.notification!!.title)
            .setCustomBigContentView(notificationLayoutExpanded).setCustomHeadsUpContentView(notificationLayout)
            .setSound(RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION))
            .setSmallIcon(R.mipmap.ic_launcher).setPriority(NotificationCompat.PRIORITY_MAX)
            .setDefaults(NotificationCompat.DEFAULT_ALL).setFullScreenIntent(pendingIntent, true)
            .setContentIntent(pendingIntent).setTicker(remoteMessage.notification!!.title)
            .setVibrate(longArrayOf(100, 200, 300, 400, 500)).setStyle(NotificationCompat.DecoratedCustomViewStyle())

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(channelId, "Thông báo 12bay", NotificationManager.IMPORTANCE_HIGH)
            channel.setShowBadge(true)
            channel.canShowBadge()
            channel.enableLights(true)
            channel.lightColor = Color.RED
            channel.enableVibration(true)
            channel.vibrationPattern = longArrayOf(100, 200, 300, 400, 500)
            notificationManager.createNotificationChannel(channel)
        }

        val id = (0..1000).random()
        notificationManager.notify(id, builder.build())
    }

    override fun onNewToken(token: String) {
        val refreshedToken = FirebaseInstanceId.getInstance().token
        AppConfigs.Log(TAG, "Refreshed token: $refreshedToken")
        if (refreshedToken != null && !refreshedToken.isEmpty()) {
            sendRegistrationToServer(refreshedToken)
        } else {
            sendRegistrationToServer(token)
        }
    }

    private fun sendRegistrationToServer(token: String) {
        Common.FCM_TOKEN = token
        SharedPrefs.getInstance().put("FCM-TOKEN", token) //ServerUtilities.postAppSetting(this, "FCM")
    }

    companion object {
        private const val TAG = "12bayFirebaseMsgService"
        fun getBitmapfromUrl(imageUrl: String?): Bitmap? {
            return try {
                if (imageUrl == null) {
                    return null
                } else if (imageUrl.length < 4) {
                    return null
                }
                val url = URL(imageUrl)
                val connection = url.openConnection() as HttpURLConnection
                connection.doInput = true
                connection.connect()
                val input = connection.inputStream
                BitmapFactory.decodeStream(input)
            } catch (e: Exception) { // TODO Auto-generated catch block
                e.printStackTrace()
                null
            }
        }
    }
}