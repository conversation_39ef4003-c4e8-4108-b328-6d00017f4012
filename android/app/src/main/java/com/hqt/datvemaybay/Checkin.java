package com.hqt.datvemaybay;

import com.android.volley.VolleyError;

import com.hqt.data.model.SliderItem;
import com.hqt.util.AppConfigs;
import com.hqt.util.SSLSendRequest;
import com.hqt.view.adapter.SliderAdapterCustom;
import com.hqt.view.ui.BaseActivity;
import com.mikepenz.iconics.context.IconicsContextWrapper;
import com.smarteist.autoimageslider.SliderView;

import android.content.Context;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;

import androidx.appcompat.app.AlertDialog;

import android.content.DialogInterface;
import android.content.Intent;

import androidx.appcompat.widget.AppCompatButton;

import android.text.InputFilter;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.EditText;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;
import android.widget.Toast;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

public class Checkin extends BaseActivity {
    static final int DATE_DIALOG_2 = 200;
    private EditText fistName, lastName, code;
    private AppCompatButton traCuu;
    private TextView diemDi;

    private String fName, lName, pnr, dep, car = "VN";
    private RadioGroup radio;
    private RadioButton radioVn, radioVJ, radioJT, radioQH, radioVU;
    private String from, to, tenHanhKhach, ngayBay, chuyenBay, gioBay, gioDen, status, TongDai = "19001100";

    final int REQUEST_CODE_FROM = 0;
    private View alertView;
    private AlertDialog myDialog;
    SliderView sliderView;
    SliderAdapterCustom sliderAdapter;

    @Override
    protected int getLayoutId() {
        return R.layout.check_in;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        getToolbar().setTitle("Checkin Online");
        getToolbar().setSubtitle("Làm thủ tục trực tuyến");
        setSupportActionBar(getToolbar());
        getSupportActionBar().setDisplayShowHomeEnabled(true);
        getToolbar().setNavigationIcon(R.drawable.ic_action_back_home);
        getToolbar().setNavigationOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                onBackPressed();
            }
        });


        sliderView = findViewById(R.id.sliderCheckin);
        sliderAdapter = new SliderAdapterCustom(this, false);
        sliderView.setSliderAdapter(sliderAdapter);

        radioVn = findViewById(R.id.checkVn);
        radioVJ = findViewById(R.id.checkVietJet);
        radioJT = findViewById(R.id.checkJetStar);
        radioQH = findViewById(R.id.checkQH);
        radioVU = findViewById(R.id.checkVU);
        traCuu = findViewById(R.id.btnCheckin);

        diemDi = findViewById(R.id.diemDi);
        fistName = findViewById(R.id.fName);
        lastName = findViewById(R.id.lName);
        code = findViewById(R.id.pnr);


        diemDi.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent i = new Intent(getApplicationContext(), AirportSearch.class);
                startActivityForResult(i, REQUEST_CODE_FROM);
            }
        });
        addButtonListener();
        Intent in = getIntent();
        if (in.hasExtra("pnr")) {
            pnr = in.getStringExtra("pnr");

            try {
                if (Common.curentBooking != null && pnr.equals(Common.curentBooking.getPnr())) {
                    diemDi.setText(Common.getAirPortName(Common.curentBooking.getOrigin_code(), false) + " - " + Common.curentBooking.getOrigin_code());
                    code.setText(Common.curentBooking.getPnr());

                    String fullName = Common.curentBooking.getPax_info().getAdult().get(0).getFullName();
                    String[] nameParts = fullName.split(" ");
                    lName = nameParts[0];
                    nameParts[0] = "";
                    fName = Common.join(" ", nameParts);

                    fistName.setText(fName.trim());
                    lastName.setText(lName.trim());

                    if (Common.curentBooking.getDeparture_f().getProvider().equals("VN")) {
                        radioVn.performClick();
                    } else if (Common.curentBooking.getDeparture_f().getProvider().equals("VJ")) {
                        radioVJ.performClick();
                    } else if (Common.curentBooking.getDeparture_f().getProvider().equals("BL")) {
                        radioJT.performClick();
                    } else if (Common.curentBooking.getDeparture_f().getProvider().equals("QH")) {
                        radioQH.performClick();
                    } else if (Common.curentBooking.getDeparture_f().getProvider().equals("VU")) {
                        radioVU.performClick();
                    }


                }
            } catch (Exception e) {
                AppConfigs.logException(e);
            }
        }


        getTopImage();

    }


    public void getTopImage() {

        (new SSLSendRequest(this)).GET(true, "AirLines/CheckinSlider/", new JSONObject(), new SSLSendRequest.CallBackInterface() {
            @Override
            public void onSuccess(JSONObject response, boolean cached) {
                try {
                    JSONArray listNew = response.getJSONArray("data");
                    sliderAdapter.renewItems(getListSlider(response.getJSONArray("data")));
                    sliderAdapter.notifyDataSetChanged();


                } catch (JSONException e) {
                    AppConfigs.logException(e);
                    e.printStackTrace();
                }
            }

            @Override
            public void onFail(VolleyError error) {

            }
        });

    }

    public void addButtonListener() {

        radioVJ.setOnClickListener(new OnClickListener() {

            @Override
            public void onClick(View v) {
                car = "VJ";
                TongDai = "19001886";
            }
        });
        radioQH.setOnClickListener(new OnClickListener() {

            @Override
            public void onClick(View v) {
                car = "QH";
                TongDai = "19001166";
            }
        });
        radioVU.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                car = "VU";
                TongDai = "19006686";
            }
        });

      
        radioVn.setOnClickListener(new OnClickListener() {

            @Override
            public void onClick(View v) {
                car = "VN";
                TongDai = "19001100";

            }
        });

        radio = findViewById(R.id.radio);
        traCuu.setOnClickListener(new OnClickListener() {

            @Override
            public void onClick(View v) {

                if (isInternetConnected()) {
                    dep = diemDi.getText().toString();
                    fName = fistName.getText().toString();
                    lName = lastName.getText().toString();
                    pnr = code.getText().toString();

                    String regexStr = "^[0-9]*$";

                    if (pnr.toString().trim().matches(regexStr)) {
                        radioVJ.performClick();
                    }

                    if (pnr.trim().isEmpty()) {
                        Toast.makeText(Checkin.this, "Chưa nhập mã đặt chỗ ", Toast.LENGTH_SHORT).show();
                    } else if (fName.trim().isEmpty()) {
                        Toast.makeText(Checkin.this, "Chưa nhập tên hành khách", Toast.LENGTH_SHORT).show();
                        fistName.requestFocus();
                    } else if ((lName.trim().isEmpty())) {
                        Toast.makeText(Checkin.this, "Chưa nhập họ hành khách", Toast.LENGTH_SHORT).show();
                        lastName.requestFocus();
                    } else if ((dep.trim().isEmpty())) {
                        Toast.makeText(Checkin.this, "Chưa nhập điểm khởi hành", Toast.LENGTH_SHORT).show();

                    } else {

                        String[] b = dep.split("\\s+");
                        String fromT = b[b.length - 1].replace(" ", "");
                        lName = lName.replace(" ", "+");
                        fName = fName.replace(" ", "+");
                        dep = fromT;

                        String urlCheckin = "https://ssl.12bay.vn/api/v1/AirLines/CheckIn/" + car + "/" + dep + "/" + URLEncoder.encode(pnr) + "/" + URLEncoder.encode(lName) + "/" + URLEncoder.encode(fName);

                        if (car.equals("VJ")) {
                            Intent intent = new Intent(getApplicationContext(), CheckinWeb.class);
                            intent.putExtra("urlCheckin", urlCheckin);
                            intent.putExtra("lName", lName);
                            intent.putExtra("fName", fName);
                            intent.putExtra("pnr", pnr);
                            intent.putExtra("car", "VJ");

                            if (intent.resolveActivity(getPackageManager()) != null) {
                                startActivity(intent);
                            }
                        } else if (car.equals("VN")) {
                            Intent intent = new Intent(getApplicationContext(), CheckinWeb.class);
                            intent.putExtra("urlCheckin", urlCheckin);
                            intent.putExtra("lName", lName);
                            intent.putExtra("fName", fName);
                            intent.putExtra("pnr", pnr);
                            intent.putExtra("car", "VN");
                            if (intent.resolveActivity(getPackageManager()) != null) {
                                startActivity(intent);
                            }
                        } else if (car.equals("BL")) {
                            Intent intent = new Intent(getApplicationContext(), CheckinWeb.class);
                            intent.putExtra("urlCheckin", urlCheckin);
                            intent.putExtra("lName", lName);
                            intent.putExtra("fName", fName);
                            intent.putExtra("pnr", pnr);
                            intent.putExtra("car", "BL");
                            startActivity(intent);
                        } else if (car.equals("QH")) {
                            Intent intent = new Intent(getApplicationContext(), CheckinWeb.class);
                            intent.putExtra("urlCheckin", urlCheckin);
                            intent.putExtra("lName", lName);
                            intent.putExtra("fName", fName);
                            intent.putExtra("pnr", pnr);
                            intent.putExtra("car", "QH");
                            startActivity(intent);
                        } else if (car.equals("VU")) {
                            Intent intent = new Intent(getApplicationContext(), CheckinWeb.class);
                            intent.putExtra("urlCheckin", urlCheckin);
                            intent.putExtra("lName", lName);
                            intent.putExtra("fName", fName);
                            intent.putExtra("pnr", pnr);
                            intent.putExtra("car", "VU");
                            startActivity(intent);
                        }


                    }
                } else {
                    //Nếu không có kết nối với intenet thì
                    Common.showAlertDialog(Checkin.this, "Không có Internet",
                            "Xin vui lòng kết nối Wifi/3G để tiếp tục", false, true);
                }


            }

        });


    }

    //GET SETTING


    private void showInfo() {

        AlertDialog.Builder builder = new AlertDialog.Builder(Checkin.this);

        LayoutInflater inflater = LayoutInflater.from(getApplicationContext());
        alertView = inflater.inflate(R.layout.check_view_layout, null);
        builder.setView(alertView);

        builder.setTitle("Thông tin đặt chỗ");
        builder.setIcon(R.drawable.ic_bell_alert);

        TextView txtChuyenBay = alertView.findViewById(R.id.chuyenBay);
        TextView txtGioBay = alertView.findViewById(R.id.gioBay);
        TextView txtGioDen = alertView.findViewById(R.id.gioDen);
        TextView txtNgayBay = alertView.findViewById(R.id.ngayBay);
        TextView txtTenHanhKhach = alertView.findViewById(R.id.tenHanhKhach);
        TextView txtTrangThai = alertView.findViewById(R.id.status);

        txtChuyenBay.setText(chuyenBay);
        txtGioBay.setText(gioBay);
        txtGioDen.setText(gioDen);
        txtNgayBay.setText(ngayBay);
        txtTenHanhKhach.setText(tenHanhKhach);
        txtTrangThai.setText(status);


        builder.setPositiveButton("OK", new DialogInterface.OnClickListener() {
                    public void onClick(DialogInterface dialog, int id) {

                        dialog.cancel();
                    }
                })
                .setNegativeButton("Gọi Tổng Đài", new DialogInterface.OnClickListener() {
                    public void onClick(DialogInterface dialog, int id) {


                        dialog.cancel();
                        String p = "";
                        Intent i = new Intent(Intent.ACTION_DIAL);
                        p = "tel:" + TongDai;

                        i.setData(Uri.parse(p));
                        startActivity(i);
                    }
                });

        builder.setCancelable(true);
        myDialog = builder.create();
        myDialog.show();

    }


    @Override
    public void onStart() {

        super.onStart();
    }

    @Override
    public void onStop() {

        super.onStop();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK && requestCode == REQUEST_CODE_FROM) {
            if (data.hasExtra("code")) {

                diemDi.setText(data.getExtras().getString("name") + " - " + data.getExtras().getString("code"));
            }
        }

    }

    public List<SliderItem> getListSlider(JSONArray jsonImages) {
        ArrayList<SliderItem> listSlider = new ArrayList();

        try {
            for (int i = 0; 0 < jsonImages.length() - 1; i++) {
                JSONObject image = jsonImages.getJSONObject(i);
                SliderItem slide = new SliderItem(image.getString("image"), image.getString("title"), image.getString("image"));
                listSlider.add(slide);
            }

        } catch (JSONException e) {
            e.printStackTrace();
        }
        return listSlider;
    }

}

