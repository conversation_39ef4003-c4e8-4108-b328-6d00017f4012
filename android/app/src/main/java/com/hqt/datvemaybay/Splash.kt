package com.hqt.datvemaybay

import android.annotation.SuppressLint
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import com.airbnb.lottie.LottieAnimationView
import com.android.installreferrer.api.InstallReferrerClient
import com.android.installreferrer.api.InstallReferrerStateListener
import com.android.installreferrer.api.ReferrerDetails
import com.google.firebase.dynamiclinks.FirebaseDynamicLinks
import com.hqt.util.AppConfigs
import com.hqt.util.AppController
import com.hqt.util.SharedPrefs
import com.hqt.view.ui.BaseActivity
import org.json.JSONObject
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

@SuppressLint("CustomSplashScreen")
class Splash : BaseActivity() {
    var appLinkData: Uri? = null
    override fun getLayoutId(): Int {
        return R.layout.splash
    }

    private lateinit var referrerClient: InstallReferrerClient

    @SuppressLint("WrongThread")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        if (!isTaskRoot
            && intent.hasCategory(Intent.CATEGORY_LAUNCHER)
            && intent.action != null
            && intent.action.equals(Intent.ACTION_MAIN)
        ) {
            finish()
            return
        }



        if (intent.extras != null && intent.extras?.getString("action_link", null) != null) {
            val promoLink = intent.extras?.getString("action_link", null)
            val notificationIntent = Common.ConvertLinkAction(this, Uri.parse(promoLink))
            val track = intent.extras?.getString("track", "")
            setUpTrackingClick(track!!)
            startActivity(notificationIntent)
            finish()


        } else {

            val animationView = findViewById<LottieAnimationView>(R.id.animation_view)
            animationView.setAnimation(R.raw.anim_12bay)
            animationView.playAnimation()


            FirebaseDynamicLinks.getInstance().getDynamicLink(intent)
                .addOnSuccessListener(this) { pendingDynamicLinkData ->
                    var deepLink: Uri? = null
                    if (pendingDynamicLinkData != null) {
                        deepLink = pendingDynamicLinkData.link
                        AppConfigs.Log("deepLink", deepLink.toString())
                    }
                }.addOnFailureListener(this) { e ->
                }


            appLinkData = intent.data

            if (appLinkData != null) {

                if (appLinkData.toString().contains("skipsplash")) {
                    SPLASH_TIME_OUT = 0
                }
            }


            Handler(Looper.getMainLooper()).postDelayed({

                val i2 = Common.ConvertLinkAction(applicationContext, appLinkData)
                startActivity(i2) //
                finish()


            }, SPLASH_TIME_OUT.toLong())
            Common.createShortcut(this)
        }

        checkReferral()
    }

    private fun setUpTrackingClick(track: String) {
        try {
            if (track != "") {
                val formatter = DateTimeFormatter.ofPattern("yyyyMMdd")
                val current = LocalDateTime.now().format(formatter)

                AppConfigs.setStringLocalCache(this, "track_code_$current", track)
                AppController.instance.trackingCode = track

                AppConfigs.Log(
                    "trackxx.trackingCode",
                    "track_code_$current" + AppController.instance.trackingCode
                )
            }

        } catch (e: Exception) {
            AppConfigs.logException(e)
        }
    }

    private fun checkReferral() {
        val isHasReferral =
            SharedPrefs.getInstance().get("isGetReferrer", Boolean::class.javaObjectType)
        SharedPrefs.getInstance().put("isGetReferrer", true)
        try {
            if (!isHasReferral) {
                referrerClient = InstallReferrerClient.newBuilder(this).build()
                referrerClient.startConnection(object : InstallReferrerStateListener {

                    override fun onInstallReferrerSetupFinished(responseCode: Int) {
                        try {
                            when (responseCode) {

                                InstallReferrerClient.InstallReferrerResponse.OK -> {

                                    val response: ReferrerDetails = referrerClient.installReferrer

                                    val referrerUrl: String = response.installReferrer
                                    val referrerClickTime: Long =
                                        response.referrerClickTimestampSeconds
                                    val appInstallTime: Long = response.installBeginTimestampSeconds
                                    val instantExperienceLaunched: Boolean =
                                        response.googlePlayInstantParam
                                    val js = JSONObject()
                                    js.put("referrerUrl", referrerUrl)
                                    js.put("referrerClickTime", referrerClickTime)
                                    js.put("appInstallTime", appInstallTime)
                                    js.put("instantExperienceLaunched", instantExperienceLaunched)

                                    var referredString = js.toString()

                                    SharedPrefs.getInstance().put("isGetReferrer", true)
                                    SharedPrefs.getInstance().put("ReferrerString", js)


                                }

                                InstallReferrerClient.InstallReferrerResponse.PERMISSION_ERROR -> {
                                    SharedPrefs.getInstance().put("isGetReferrer", true)
                                }

                                InstallReferrerClient.InstallReferrerResponse.SERVICE_DISCONNECTED -> {
                                    SharedPrefs.getInstance().put("isGetReferrer", true)
                                }

                                InstallReferrerClient.InstallReferrerResponse.FEATURE_NOT_SUPPORTED -> {
                                    SharedPrefs.getInstance().put("isGetReferrer", true)
                                }

                                InstallReferrerClient.InstallReferrerResponse.SERVICE_UNAVAILABLE -> {
                                    SharedPrefs.getInstance().put("isGetReferrer", true)
                                }
                            }
                        } catch (e: Exception) {
                            AppConfigs.logException(e)
                        }
                    }


                    override fun onInstallReferrerServiceDisconnected() { // Try to restart the connection on the next request to
                        // Google Play by calling the startConnection() method.
                    }
                })
            }
        } catch (e: Exception) {
            AppConfigs.logException(e)

        }
    }

    companion object {
        private var SPLASH_TIME_OUT = 4800
    }
}