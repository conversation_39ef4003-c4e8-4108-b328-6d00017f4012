package com.hqt.datvemaybay;

import static com.hqt.datvemaybay.Common.FCM_TOKEN;
import static com.hqt.datvemaybay.Common.isNewData;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.widget.Toast;

import com.android.volley.Request;
import com.android.volley.Response;
import com.android.volley.VolleyError;
import com.android.volley.toolbox.JsonObjectRequest;
import com.google.firebase.iid.FirebaseInstanceId;
import com.google.firebase.installations.FirebaseInstallations;
import com.google.firebase.remoteconfig.FirebaseRemoteConfig;
import com.hqt.util.AppConfigs;
import com.hqt.util.AppController;
import com.hqt.util.SSLSendRequest;
import com.hqt.util.SharedPrefs;


import org.json.JSONException;
import org.json.JSONObject;


public final class ServerUtilities {
    private static final int MAX_ATTEMPTS = 2;
    private static final int BACKOFF_MILLI_SECONDS = 2000;
    private static final Random random = new Random();
    private static FirebaseRemoteConfig mFirebaseRemoteConfig;
    // Remote Config keys
    private static final String PROMO_LINK = "promo_link_android";

    /**
     * Register this account/device pair within the server.
     */

    public static boolean checkInternet(Context context) {

        ConnectivityManager cm = (ConnectivityManager) context
                .getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo activeNetwork = cm.getActiveNetworkInfo();

        if (null != activeNetwork) {
            if (activeNetwork.getType() == ConnectivityManager.TYPE_WIFI || activeNetwork.getType() == ConnectivityManager.TYPE_MOBILE || activeNetwork.getType() == ConnectivityManager.TYPE_ETHERNET || activeNetwork.getType() == ConnectivityManager.TYPE_VPN)
                return true;

        }

        return false;
    }

    static void postAppSetting(Context e, String logFrom) {

        try {
            if (checkInternet(e)) {


                JSONObject postParam = new JSONObject();
                postParam.put("device_type", "ANDROID");
                postParam.put("uid", Common.Uid);
                postParam.put("gcm", Common.FCM_TOKEN);
                postParam.put("device_id", Common.DEVICE_ID);
                postParam.put("key", Common.getKeyHash());
                postParam.put("ver", BuildConfig.VERSION_CODE + "");
                postParam.put("logFrom", logFrom);
                postParam.put("device_name", Common.DeviceName);
                postParam.put("referral", SharedPrefs.getInstance().get("ReferrerString", JSONObject.class));


                (new SSLSendRequest(e)).POST(false, "AirLines/AppSetting", postParam, new SSLSendRequest.CallBackInterface() {
                    @Override
                    public void onSuccess(JSONObject response, boolean cached) {

                    }

                    @Override
                    public void onFail(VolleyError e) {
                        e.printStackTrace();

                    }
                });
            }
        } catch (JSONException ex) {
            AppConfigs.logException(ex);
            ex.printStackTrace();
        }

    }

}