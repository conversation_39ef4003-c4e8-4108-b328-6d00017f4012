package com.hqt.datvemaybay;


import org.json.JSONException;
import org.json.JSONObject;

import com.android.volley.VolleyError;
import com.hqt.util.AppConfigs;
import com.hqt.util.SSLSendRequest;
import com.hqt.view.ui.BaseActivity;
import com.mikepenz.iconics.context.IconicsContextWrapper;


import android.content.Context;
import android.net.Uri;
import android.os.Bundle;

import androidx.appcompat.app.AlertDialog;

import android.app.ProgressDialog;
import android.content.DialogInterface;
import android.content.Intent;

import androidx.appcompat.widget.AppCompatButton;
import androidx.coordinatorlayout.widget.CoordinatorLayout;

import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.EditText;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;
import android.widget.Toast;

import static com.hqt.datvemaybay.Common.isCheckVe;

public class CheckVe extends BaseActivity {
    static final int DATE_DIALOG_2 = 200;
    private EditText fistName, lastName, code;
    private AppCompatButton traCuu;
    private TextView diemDi;

    private String fName, lName, pnr, dep, car = "VN";
    private RadioGroup radio;
    private RadioButton radioVn, radioVJ, radioJT, radioVu;
    private String from, to, tenHanhKhach, ngayBay, chuyenBay, gioBay, gioDen, status, TongDai = "0439381835";


    final int REQUEST_CODE_FROM = 0;
    private View alertView;
    private AlertDialog myDialog;
    CoordinatorLayout coordinatorLayout;

    @Override
    protected int getLayoutId() {
        return R.layout.check_ve;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        getToolbar().setTitle("Kiểm tra mã đặt chỗ");
        getSupportActionBar().setDisplayShowHomeEnabled(true);
        coordinatorLayout = findViewById(R.id.coordinatorLayout);
        getToolbar().setNavigationIcon(R.drawable.ic_action_back_home);


        radioVn = findViewById(R.id.checkVn);
        radioVJ = findViewById(R.id.checkVietJet);
        radioJT = findViewById(R.id.checkJetStar);
        radioVu = findViewById(R.id.checkVu);
        traCuu = findViewById(R.id.button1);


        diemDi = findViewById(R.id.diemDi);
        fistName = findViewById(R.id.fName);
        lastName = findViewById(R.id.lName);
        code = findViewById(R.id.pnr);
        addButtonListener();

        Intent in = getIntent();
        if (in.hasExtra("pnr")) {
            pnr = in.getStringExtra("pnr");

            if (Common.curentBooking != null && pnr.equals(Common.curentBooking.getPnr())) {
                diemDi.setText(Common.getAirPortName(Common.curentBooking.getOrigin_code(), false) + " - " + Common.curentBooking.getOrigin_code());
                code.setText(Common.curentBooking.getPnr());
                try {
                    String fullName = Common.curentBooking.getPax_info().getAdult().get(0).getFullName();
                    String[] nameParts = fullName.split(" ");
                    lName = nameParts[0];
                    nameParts[0] = "";
                    fName = Common.join(" ", nameParts);
                    fistName.setText(fName.trim());
                    lastName.setText(lName.trim());

                    if (Common.curentBooking.getDeparture_f().getProvider().equals("VN")) {
                        radioVn.performClick();
                    } else if (Common.curentBooking.getDeparture_f().getProvider().equals("VJ")) {
                        radioVJ.performClick();
                    } else if (Common.curentBooking.getDeparture_f().getProvider().equals("BL")) {
                        radioJT.performClick();
                    } else if (Common.curentBooking.getDeparture_f().getProvider().equals("VU")) {
                        radioVu.performClick();
                    }

                } catch (Exception e) {

                }
            }
        }

        diemDi.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent i = new Intent(getApplicationContext(), AirportSearch.class);
                startActivityForResult(i, REQUEST_CODE_FROM);
            }
        });


        if (!isCheckVe) {
            new AlertDialog.Builder(this)
                    .setIcon(android.R.drawable.ic_dialog_alert)
                    .setTitle("Thông Báo")
                    .setMessage("Chức năng đang được cập nhật!\nBạn vui lòng quay lại sau.")
                    .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            finish();
                        }

                    }).show();
        }

    }


    public void addButtonListener() {
        radioVJ.setOnClickListener(new OnClickListener() {

            @Override
            public void onClick(View v) {
                // TODO Auto-generated method stub
                car = "VJ";
                TongDai = "19001886";

            }
        });

        radioJT.setOnClickListener(new OnClickListener() {

            @Override
            public void onClick(View v) {
                // TODO Auto-generated method stub
                car = "VN";
                TongDai = "19001550";


            }
        });

        radioVn.setOnClickListener(new OnClickListener() {

            @Override
            public void onClick(View v) {
                car = "VN";
                TongDai = "19001100";

            }
        });
        radioVu.setOnClickListener(new OnClickListener() {

            @Override
            public void onClick(View v) {
                car = "VU";
                TongDai = "19006686";

            }
        });

        radio = findViewById(R.id.radio);

        traCuu.setOnClickListener(new OnClickListener() {

            @Override
            public void onClick(View v) {


                if (isInternetConnected()) {


                    dep = diemDi.getText().toString();
                    fName = fistName.getText().toString();
                    lName = lastName.getText().toString();
                    pnr = code.getText().toString();

                    String regexStr = "^[0-9]*$";

                    if (pnr.toString().trim().matches(regexStr)) {
                        radioVJ.performClick();
                    }

                    if (pnr.trim().isEmpty()) {
                        Toast.makeText(CheckVe.this, "Chưa nhập mã đặt chỗ ", Toast.LENGTH_SHORT).show();
                    } else if (fName.trim().isEmpty()) {
                        Toast.makeText(CheckVe.this, "Chưa nhập tên hành khách", Toast.LENGTH_SHORT).show();
                        fistName.requestFocus();
                    } else if ((lName.trim().isEmpty())) {
                        Toast.makeText(CheckVe.this, "Chưa nhập họ hành khách", Toast.LENGTH_SHORT).show();
                        lastName.requestFocus();
                    } else if ((dep.trim().isEmpty())) {
                        Toast.makeText(CheckVe.this, "Chưa nhập điểm khởi hành", Toast.LENGTH_SHORT).show();

                    } else {

                        String[] b = dep.split("\\s+");
                        String fromT = b[b.length - 1].replace(" ", "");
                        lName = lName.replace(" ", "+");
                        fName = fName.replace(" ", "+");
                        dep = fromT;

                        checkVe();
                    }
                } else {
                    //Nếu không có kết nối với intenet thì
                    Common.showAlertDialog(CheckVe.this, "Không có Internet",
                            "Xin vui lòng kết nối Wifi/3G để tiếp tục", false, true);
                }
            }

        });
    }

    //GET SETTING
    public void checkVe() {
        ProgressDialog dialog;

        JSONObject rq = new JSONObject();
        dialog = new ProgressDialog(CheckVe.this);
        dialog.setMessage("Đang kiểm tra ...\n Vui lòng đợi!");
        dialog.setIndeterminate(false);
        dialog.setMax(100);
        dialog.setCancelable(false);
        dialog.setProgressStyle(ProgressDialog.STYLE_SPINNER);
        dialog.show();

        try {
            rq.put("airline", car);
            rq.put("pnr", pnr);
            rq.put("lName", lName);
            rq.put("fName", fName);
            rq.put("departure", dep);

        } catch (JSONException e) {
            AppConfigs.logException(e);
        }
        (new SSLSendRequest(this)).POST(false, "AirLines/PnrCheck", rq, new SSLSendRequest.CallBackInterface() {
            @Override
            public void onSuccess(JSONObject response, boolean cached) {
                try {

                    int retCode = response.getInt("status_code");
                    tenHanhKhach = response.getString("pax_name");
                    ngayBay = response.getString("depature_date");
                    chuyenBay = response.getString("trip");
                    gioBay = response.getString("depature_time");
                    gioDen = response.getString("arriver_date");
                    status = response.getString("status");

                    new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            if (dialog.isShowing()) dialog.dismiss();
                            showInfo();
                        }
                    }, 2000);


                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onFail(VolleyError error) {
                if (dialog.isShowing()) dialog.dismiss();
                Toast.makeText(getApplicationContext(), "Có lỗi xẩy ra, vui lòng thử lại", Toast.LENGTH_SHORT).show();
            }
        });
    }


    private void showInfo() {

        AlertDialog.Builder builder = new AlertDialog.Builder(CheckVe.this);

        LayoutInflater inflater = LayoutInflater.from(getApplicationContext());
        alertView = inflater.inflate(R.layout.check_view_layout, null);
        builder.setView(alertView);

        builder.setTitle("Thông tin đặt chỗ");
        builder.setIcon(R.drawable.ic_bell_alert);

        TextView txtChuyenBay = alertView.findViewById(R.id.chuyenBay);
        TextView txtGioBay = alertView.findViewById(R.id.gioBay);
        TextView txtGioDen = alertView.findViewById(R.id.gioDen);
        TextView txtNgayBay = alertView.findViewById(R.id.ngayBay);
        TextView txtTenHanhKhach = alertView.findViewById(R.id.tenHanhKhach);
        TextView txtTrangThai = alertView.findViewById(R.id.status);

        txtChuyenBay.setText(chuyenBay);
        txtGioBay.setText(gioBay);
        txtGioDen.setText(gioDen);
        txtNgayBay.setText(ngayBay);
        txtTenHanhKhach.setText(tenHanhKhach);
        txtTrangThai.setText(status);


//
//		mTracker.send(new HitBuilders.EventBuilder()
//				.setCategory("Check vé")
//				.setAction("Kiểm tra")
//				.setLabel(tenHanhKhach + " - " + pnr + " - " + status)
//				.build());


        builder.setPositiveButton("OK", new DialogInterface.OnClickListener() {
            public void onClick(DialogInterface dialog, int id) {

                dialog.cancel();
            }
        })
                .setNegativeButton("Gọi Tổng Đài", new DialogInterface.OnClickListener() {
                    public void onClick(DialogInterface dialog, int id) {

                        dialog.cancel();
                        String p = "";
                        Intent i = new Intent(Intent.ACTION_DIAL);
                        p = "tel:" + TongDai;

                        i.setData(Uri.parse(p));
                        startActivity(i);
                    }
                });

        builder.setCancelable(true);
        myDialog = builder.create();

        if (!CheckVe.this.isFinishing()) {
            myDialog.show();
        }


    }


    @Override
    public void onStart() {
        super.onStart();
    }

    @Override
    public void onStop() {
        super.onStop();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (resultCode == RESULT_OK && requestCode == REQUEST_CODE_FROM) {
            if (data.hasExtra("code")) {

                diemDi.setText(data.getExtras().getString("name") + " - " + data.getExtras().getString("code"));
            }
        }

    }

//    @Override
//    protected void attachBaseContext(Context newBase) {
//        super.attachBaseContext(IconicsContextWrapper.wrap(newBase));
//    }

}
