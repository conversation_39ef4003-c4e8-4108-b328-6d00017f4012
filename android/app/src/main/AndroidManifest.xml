<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:versionCode="252"
    android:versionName="4.7.1">

    <uses-sdk tools:overrideLibrary="com.facebook,android.support.customtabs,com.sanojpunchihewa.updatemanager" />

    <queries>
        <provider android:authorities="com.facebook.katana.provider.PlatformProvider" /> <!-- allows app to access Facebook app features -->
        <provider android:authorities="com.facebook.orca.provider.PlatformProvider" /> <!-- allows sharing to Messenger app -->
    </queries>

    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="com.samsung.android.providers.context.permission.WRITE_USE_APP_FEATURE_SURVEY" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />

    <uses-feature
        android:glEsVersion="0x00020000"
        android:required="true" />

    <application

        android:name="com.hqt.util.AppController"
        android:allowBackup="true"
        android:fullBackupContent="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:requestLegacyExternalStorage="true"
        android:theme="@style/MyMaterialTheme"
        android:usesCleartextTraffic="true"
        tools:replace="android:theme,android:allowBackup"
        tools:targetApi="m">
        <activity

            android:name="com.hqt.view.ui.demo.ui.DemoActivity"
            android:exported="false" />
        <activity
            android:name="com.hqt.view.ui.payment.NewPaymentActivity"
            android:exported="true"
            android:launchMode="singleTask">
            <intent-filter>
                <data
                    android:host="postbacksdkurl"
                    android:scheme="@string/protocol_scheme" />
            </intent-filter>
        </activity>
        <activity
            android:name="vn.payoo.paymentsdk.ui.home.PayooPaymentSDKActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:windowSoftInputMode="adjustResize"
            tools:ignore="AppLinkUrlError">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="postbacksdkurl"
                    android:scheme="payoosdk823" />
            </intent-filter>
        </activity>

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="@string/default_notification_channel_id" />
        <meta-data
            android:name="vn.payoo.sdk.MerchantId"
            android:value="@string/merchant_id" />
        <meta-data
            android:name="vn.payoo.sdk.SecretKey"
            android:value="@string/secret_key" />

        <activity
            android:name="com.hqt.view.ui.priceboard.MonthViewActivity"
            android:label="@string/title_activity_month_view"
            android:theme="@style/MyMaterialTheme.NoActionBar" />

        <meta-data
            android:name="firebase_crashlytics_collection_enabled"
            android:value="true" />
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="@string/google_maps_key" />

        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

        <meta-data
            android:name="asset_statements"
            android:resource="@string/asset_statements" />
        <meta-data
            android:name="com.facebook.sdk.ClientToken"
            android:value="28847c3ab27bce77ff1d239725a3017c" />
        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/facebook_app_id"
            tools:replace="android:value" />
        <meta-data
            android:name="com.facebook.sdk.ApplicationName"
            android:value="@string/app_name" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_icon_tran" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/primary" />

        <provider
            android:name="com.facebook.FacebookContentProvider"
            android:authorities="com.facebook.app.FacebookContentProvider983459756164264"
            android:exported="true" />

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />
        <meta-data
            android:name="com.google.android.gms.analytics.globalConfigResource"
            android:resource="@xml/global_tracker" />

        <activity
            android:name="com.hqt.view.ui.HomeActivity"
            android:configChanges="orientation|screenSize"
            android:exported="true"
            android:label="@string/app_name"
            android:launchMode="singleTop">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
            </intent-filter>
        </activity>
        <activity
            android:name=".Splash"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/splashTheme">
            <tools:validation testUrl="https://12bay.vn/" />

            <intent-filter>
                <action android:name="PUSH_CHEAP_TICKET" />

                <category android:name="android.intent.category.DEFAULT" />

                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />

                <action android:name="android.intent.action.VIEW" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="r.12bay.vn"
                    android:pathPrefix="/"
                    android:scheme="http" />
                <data
                    android:host="12bay.vn"
                    android:pathPrefix="/san-ve-re"
                    android:scheme="https" />
                <data
                    android:host="12bay.vn"
                    android:pathPrefix="/r"
                    android:scheme="https" />
                <data
                    android:host="12bay.vn"
                    android:pathPrefix="/lich-bay"
                    android:scheme="https" />
                <data
                    android:host="12bay.vn"
                    android:pathPrefix="/thanh-toan"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="12bay.vn"
                    android:pathPrefix="/chuyen-bay"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="12bay.vn"
                    android:pathPrefix="/theo-doi-gia"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="12bay.vn"
                    android:pathPattern="/^((?!ho-tro).)*$/"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="12bay.vn"
                    android:scheme="https" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.hqt.view.ui.account.ProfileEditActivity"
            android:configChanges="orientation|screenSize"
            android:label="@string/profile_edit"
            android:launchMode="singleTask" />
        <activity
            android:name="com.hqt.view.ui.SearchActivity"
            android:configChanges="orientation|screenSize"
            android:label="@string/trongNuoc"
            android:launchMode="singleTask">
            <tools:validation testUrl="https://12bay.vn/dat-ve" />
        </activity>

        <activity
            android:name="com.hqt.view.ui.search.ui.activity.SearchActivityV2"
            android:configChanges="orientation|screenSize"
            android:label="@string/trongNuoc"
            android:launchMode="singleTask">
            <tools:validation testUrl="https://12bay.vn/dat-ve" />
        </activity>
        <activity
            android:name=".BookView"
            android:configChanges="orientation|screenSize"
            android:label="@string/trongNuoc"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".AirportSearch"
            android:configChanges="orientation|screenSize"
            android:label="@string/airPortSearch"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".CheckVe"
            android:configChanges="orientation|screenSize"
            android:label="@string/checkVe"
            android:launchMode="singleTask"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name="com.hqt.view.ui.priceboard.PriceBoardActivity"
            android:configChanges="orientation|screenSize"
            android:label="Săn vé" />
        <activity
            android:name=".WebViewActivity"
            android:exported="false"
            android:label="@string/webviewname"
            android:launchMode="singleTask">
            <tools:validation testUrl="https://12bay.vn/san-ve-re" />

            <intent-filter>
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name=".PaymentActivity"
            android:configChanges="orientation|screenSize"
            android:exported="false"
            android:label="Thanh toán"
            android:launchMode="singleTask" />
        <activity
            android:name=".MonthWebViewActivity"
            android:configChanges="orientation|screenSize"
            android:exported="false"
            android:label="@string/webviewname" />
        <activity
            android:name=".ThanhToan"
            android:configChanges="orientation|screenSize"
            android:label="@string/thongTinThanhToan"
            android:launchMode="singleTask" />
        <activity
            android:name="com.hqt.view.ui.flighthistory.ui.activity.MapViewActivity"
            android:label="@string/map_activity_name" />
        <activity
            android:name=".PnrActivity"
            android:configChanges="orientation|screenSize"
            android:exported="false"
            android:label="Thông tin đơn hàng">
            <intent-filter>
                <action android:name="PUSH_HOLD_SUCCESS" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name=".CheapPriceActivity"
            android:configChanges="orientation|screenSize"
            android:label="@string/app_name" />
        <activity
            android:name=".SearchResult"
            android:configChanges="orientation|screenSize"
            android:label="@string/app_name" />
        <activity
            android:name="com.hqt.view.ui.search.ui.activity.SearchResultActivityV2"
            android:configChanges="orientation|screenSize"
            android:label="@string/app_name" />
        <activity
            android:name="com.hqt.util.amlich.AmLich"
            android:configChanges="orientation|screenSize"
            android:label="@string/app_name"
            android:launchMode="singleTask" />

        <activity
            android:name="com.hqt.view.ui.calender.ui.activity.LunarCalendarActivity"
            android:configChanges="orientation|screenSize"
            android:label="@string/app_name"
            android:launchMode="singleTask" />

        <activity
            android:name=".ResetPasswordActivity"
            android:configChanges="orientation|screenSize"
            android:label="@string/app_name"
            android:launchMode="singleTask" />
        <activity
            android:name=".CheckinWeb"
            android:configChanges="orientation|screenSize"
            android:exported="false"
            android:label="@string/app_name" />
        <activity
            android:name=".Checkin"
            android:configChanges="orientation|screenSize"
            android:label="@string/app_name" />
        <activity
            android:name="com.facebook.FacebookActivity"
            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
            android:label="@string/app_name"
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            tools:replace="android:theme" />
        <activity
            android:name="com.hqt.view.ui.reward.ui.activity.RewardActivity"
            android:configChanges="orientation|screenSize" />
        <activity
            android:name="com.hqt.view.ui.reward.ui.activity.RewardActivityV2"
            android:configChanges="orientation|screenSize" />
        <activity
            android:name="com.hqt.view.ui.train.TrainSearchActivity"
            android:configChanges="orientation|screenSize" />
        <activity
            android:name="com.hqt.view.ui.flightwaches.NewFlightWatchesActivity"
            android:configChanges="orientation|screenSize" />
        <activity
            android:name="com.hqt.view.ui.flightwaches.FlightWachesList"
            android:configChanges="orientation|screenSize" />
        <activity
            android:name="com.hqt.view.ui.train.TrainSelectActivity"
            android:configChanges="orientation|screenSize" />
        <activity android:name="com.hqt.view.ui.train.TrainBookingActivity" />
        <activity android:name="com.hqt.view.ui.booking.BookingActivity" />
        <activity
            android:name="com.hqt.view.ui.booking.ui.activity.BookingActivityV2"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:windowSoftInputMode="adjustPan" />
        <activity android:name="com.hqt.view.ui.train.TrainBookingViewActivity" />
        <activity android:name="com.hqt.view.ui.bus.BusBookingViewActivity" />
        <activity android:name="com.hqt.view.ui.bus.BusSearchActivity" />
        <activity
            android:name="com.hqt.view.ui.flightwaches.FlightWachesViewActivity"
            android:configChanges="orientation|screenSize"
            android:exported="false">
            <intent-filter>
                <action android:name="PRICE_UPDATE" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.hqt.view.ui.flighthistory.ui.activity.FlightHistoryActivity"
            android:theme="@style/MyMaterialTheme">
            <meta-data
                android:name="android.app.searchable"
                android:resource="@xml/searchable" />
        </activity>


        <activity
            android:name="com.hqt.view.ui.flighthistory.ui.activity.FlightHistoryActivityV2"
            android:theme="@style/MyMaterialTheme">
            <meta-data
                android:name="android.app.searchable"
                android:resource="@xml/searchable" />
        </activity>
        <activity
            android:name="com.hqt.view.ui.airport.AirportSearchActivity"
            android:label="@string/airPortSearch"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name="com.hqt.view.ui.search.ui.activity.AirportSearchActivityV2"
            android:windowSoftInputMode="adjustPan" />
        <activity android:name="com.hqt.view.ui.flightSearch.FlightSearchActivity" />
        <activity
            android:name="com.hqt.view.ui.seatmap.SelectSeatActivity"
            android:configChanges="orientation|screenSize"
            android:theme="@style/MyMaterialTheme.NoActionBar" />
        <activity
            android:name="com.hqt.view.ui.seatmap.ui.activity.SelectSeatActivityV2"
            android:configChanges="orientation|screenSize"
            android:theme="@style/MyMaterialTheme.NoActionBar" />
        <activity
            android:name="com.hqt.view.ui.tour.TourListActivity"
            android:configChanges="orientation|screenSize" />
        <activity
            android:name="com.hqt.view.ui.tour.TourDetailActivity"
            android:configChanges="orientation|screenSize" />
        <activity
            android:name="com.hqt.view.ui.addon.SelectAddOnActivity"
            android:configChanges="orientation|screenSize" />
        <activity
            android:name="com.hqt.view.ui.meal.ui.activity.SelectAddMealActivity"
            android:configChanges="orientation|screenSize" />
        <activity android:name="com.hqt.view.ui.bus.BusSelectActivity" />
        <activity
            android:name="com.hqt.view.ui.account.LoginActivity"
            android:exported="false"
            android:launchMode="singleTop">


        </activity>
        <activity android:name="com.hqt.view.ui.booking.FlightWeekViewActivity" />
        <activity
            android:name="com.hqt.view.ui.chart.WeeklyPriceChartActivity"
            android:label="Biểu đồ giá vé theo tuần"
            android:theme="@style/MyMaterialTheme.NoActionBar"
            android:exported="false" />

        <service
            android:name=".MyFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
    </application>

</manifest>