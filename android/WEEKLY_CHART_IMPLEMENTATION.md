# Weekly Cheapest Ticket Price Chart Implementation

## Overview
This document describes the implementation of the Weekly Cheapest Ticket Price Chart feature using **MPAndroidChart** library instead of the original williamchart library.

## Why MPAndroidChart?

MPAndroidChart was chosen as the replacement charting library because it offers:

- **Better Performance**: Optimized for large datasets and smooth animations
- **Rich Interactivity**: Built-in zoom, pan, and selection capabilities
- **Professional Tooltips**: Custom marker views for detailed information display
- **Extensive Customization**: Fine-grained control over chart appearance
- **Active Maintenance**: Regularly updated with bug fixes and new features
- **Better Documentation**: Comprehensive documentation and examples

## Key Features Implemented

### 1. Interactive Bar Chart
- **Zoom & Pan**: Users can zoom in/out and pan across weeks
- **Touch Selection**: Tap on bars to see detailed price information
- **Custom Tooltips**: Rich marker views showing week details and prices
- **Smooth Animations**: 800ms Y-axis animation on data load

### 2. Visual Enhancements
- **Color Coding**: Green bars for cheapest weeks, blue for regular prices
- **Value Labels**: Price values displayed on top of each bar (e.g., "1200k")
- **Formatted Axes**: Y-axis shows prices in thousands (k) format
- **Grid Lines**: Subtle grid lines for better readability

### 3. User Experience
- **Loading States**: Clear loading indicators while fetching data
- **Error Handling**: User-friendly error messages for failed requests
- **Responsive Design**: Adapts to different screen sizes
- **Accessibility**: Proper content descriptions and touch targets

## Technical Implementation

### Dependencies Added
```gradle
implementation 'com.github.PhilJay:MPAndroidChart:v3.1.0'
```

### Key Components

#### 1. WeeklyPriceChartView.kt
- Custom view extending LinearLayout
- Implements OnChartValueSelectedListener for touch interactions
- Manages chart lifecycle (loading, error, success states)
- Handles data formatting and visualization

#### 2. WeeklyPriceMarkerView.kt
- Custom marker view for detailed tooltips
- Shows week information, price, and route details
- Positioned above selected bars with proper offset

#### 3. WeeklyPriceProcessor.kt
- Processes monthly price data into weekly aggregates
- Identifies cheapest weeks across the dataset
- Converts data to chart-compatible format

### Chart Configuration

```kotlin
chartView.apply {
    // Interaction
    setTouchEnabled(true)
    setDragEnabled(true)
    setScaleEnabled(true)
    setPinchZoom(true)
    
    // Visual
    setDrawValueAboveBar(true)
    setHighlightFullBarEnabled(true)
    
    // Axes
    xAxis.position = XAxis.XAxisPosition.BOTTOM
    axisLeft.axisMinimum = 0f
    axisRight.isEnabled = false
}
```

## Usage Examples

### Basic Usage
```kotlin
val weeklyChart = WeeklyPriceChartView(context)
weeklyChart.setWeeklyData(weeklyPriceData, "SGN", "HAN")
```

### With Click Listener
```kotlin
weeklyChart.setOnBarClickListener { weekData ->
    showWeekDetails(weekData)
}
```

### Loading States
```kotlin
weeklyChart.showLoading()
// ... fetch data ...
weeklyChart.setWeeklyData(data, origin, destination)
// or on error:
weeklyChart.showError("Failed to load data")
```

## Integration Points

### 1. FlightWeekViewActivity
- Integrated chart below the calendar view
- Processes existing getMonthPrices() API response
- Maintains consistency with existing UI patterns

### 2. WeeklyPriceChartActivity
- Standalone activity for dedicated chart viewing
- Route selection with airport picker
- Refresh functionality for data updates

### 3. Home Navigation
- Added chart button to main services section
- Uses custom chart icon drawable
- Follows existing navigation patterns

## Data Flow

1. **API Call**: getMonthPrices() fetches monthly price data
2. **Processing**: WeeklyPriceProcessor aggregates daily prices to weekly minimums
3. **Visualization**: MPAndroidChart renders interactive bar chart
4. **Interaction**: Users tap bars to see detailed information

## Performance Considerations

- **Efficient Data Processing**: Weekly aggregation reduces chart complexity
- **Viewport Management**: Shows max 8 weeks at once for better performance
- **Memory Management**: Proper cleanup of chart resources
- **Smooth Animations**: Optimized animation duration (800ms)

## Testing Recommendations

### Unit Tests
- Test WeeklyPriceProcessor data aggregation logic
- Verify price formatting functions
- Test error handling scenarios

### Integration Tests
- Test chart rendering with various data sizes
- Verify touch interactions work correctly
- Test loading and error states

### UI Tests
- Test chart visibility and layout
- Verify marker view positioning
- Test accessibility features

## Future Enhancements

The MPAndroidChart implementation provides a solid foundation for:

- **Multiple Route Comparison**: Side-by-side charts for different routes
- **Historical Trends**: Line charts showing price trends over time
- **Price Alerts**: Visual indicators for price drop opportunities
- **Export Functionality**: Save chart images or data
- **Advanced Filtering**: Filter by price ranges or specific weeks

## Troubleshooting

### Common Issues

1. **Chart Not Displaying**
   - Check if data is properly formatted
   - Verify chart visibility is set to VISIBLE
   - Ensure layout dimensions are adequate

2. **Touch Events Not Working**
   - Verify OnChartValueSelectedListener is set
   - Check if touch is enabled on chart
   - Ensure marker view is properly configured

3. **Performance Issues**
   - Reduce visible X-range for large datasets
   - Disable unnecessary animations
   - Optimize data processing logic

### Debug Tips

```kotlin
// Enable chart debug mode
chartView.setLogEnabled(true)

// Check data validity
Log.d("Chart", "Data entries: ${barData.entryCount}")
Log.d("Chart", "X-axis labels: ${labels.joinToString()}")
```

## Conclusion

The MPAndroidChart implementation provides a robust, interactive, and visually appealing solution for displaying weekly flight price trends. The library's extensive feature set and active maintenance make it an excellent choice for this use case, offering users a professional charting experience while maintaining consistency with the existing 12BAY.VN application design.
