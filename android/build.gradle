// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    ext {
        kotlin_version = '1.9.21'
        minSdk = 14
        sdk = 29
        buildTools = "29.0.2"
        supportVersion = "29.1.0"
        libraryVersion = '1.2.3'
        daggerVersion = '2.48'
        pagingVersion = '3.2.1'
        retrofitVersion = '2.9.0'
        navigationVersion = '2.7.4'


        hiltVersion = '2.48'
        

    }
    repositories {
        mavenCentral()
        google()
    }


    dependencies {
        classpath 'com.android.tools.build:gradle:8.3.2'
        classpath 'com.google.gms:google-services:4.4.2'
        classpath 'com.github.dcendents:android-maven-gradle-plugin:1.4.1'
        classpath 'com.google.firebase:perf-plugin:1.4.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:1.9.21"
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.4'

        classpath "com.google.dagger:hilt-android-gradle-plugin:$hiltVersion"


    }
}

allprojects {
    repositories {

        mavenCentral()
        maven { url "https://jitpack.io" }
        jcenter()
        maven {
            url "https://plugins.gradle.org/m2/"
        }
        google()
        flatDir {
            dirs 'libs'
        }
    }
}
apply from: rootProject.file('dependencies.gradle')