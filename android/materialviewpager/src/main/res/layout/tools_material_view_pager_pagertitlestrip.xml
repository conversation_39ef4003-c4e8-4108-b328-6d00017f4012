<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal"
    >

    <FrameLayout
        android:layout_weight="1"
        android:layout_width="0dp"
        android:layout_height="match_parent">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:text="Tab 1"
            android:textAllCaps="true"
            android:gravity="center"
            android:textColor="@android:color/white" />

        <View
            android:layout_width="match_parent"
            android:layout_height="4dp"
            android:background="@android:color/white"
            android:layout_gravity="bottom"/>

    </FrameLayout>

    <TextView
        android:layout_weight="1"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:text="Tab 2"
        android:textAllCaps="true"
        android:gravity="center"
        android:textColor="@android:color/white" />

    <TextView
        android:layout_weight="1"
        android:layout_width="0dp"
        android:textAllCaps="true"
        android:layout_height="match_parent"
        android:text="Tab 3"
        android:gravity="center"
        android:textColor="@android:color/white" />

    <TextView
        android:layout_weight="1"
        android:layout_width="0dp"
        android:textAllCaps="true"
        android:layout_height="match_parent"
        android:text="Tab 4"
        android:gravity="center"
        android:textColor="@android:color/white" />

</LinearLayout>