<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true"
    android:orientation="vertical">

    <FrameLayout
        android:id="@+id/headerBackground"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:background="@color/colorPrimary"
        tools:layout_height="250dp">

        <FrameLayout
            android:id="@+id/headerBackgroundContainer"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <View
            android:id="@+id/statusBackground"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </FrameLayout>

    <FrameLayout
        android:id="@+id/viewpager_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <include layout="@layout/material_view_pager_viewpager" />

    </FrameLayout>

    <RelativeLayout
        android:id="@+id/toolbar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <View
            android:id="@+id/toolbar_layout_background"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <androidx.appcompat.widget.Toolbar
            android:fitsSystemWindows="true"
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minHeight="?attr/actionBarSize"
            android:theme="@style/MVP_AppTheme.ActionBarTheme"
            app:navigationIcon="?attr/homeAsUpIndicator"
            tools:background="#3FFF" />

        <FrameLayout
            android:id="@+id/pagerTitleStripContainer"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="160dp"
            tools:background="#3FFF" />

        <FrameLayout
            android:id="@+id/logoContainer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true" />

    </RelativeLayout>

</RelativeLayout>
