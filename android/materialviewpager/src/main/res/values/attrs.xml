<?xml version="1.0" encoding="utf-8"?>
<resources>

    <declare-styleable name="MaterialViewPager">
        <attr name="viewpager_header" format="reference" />
        <attr name="viewpager_logo" format="reference" />
        <attr name="viewpager_pagerTitleStrip" format="reference" />
        <attr name="viewpager_viewpager" format="reference" />
        <attr name="viewpager_logoMarginTop" format="dimension" />
        <attr name="viewpager_color" format="color" />
        <attr name="viewpager_headerHeight" format="dimension" />
        <attr name="viewpager_headerAlpha" format="float" />
        <attr name="viewpager_imageHeaderDarkLayerAlpha" format="float" />
        <attr name="viewpager_hideToolbarAndTitle" format="boolean" />
        <attr name="viewpager_hideLogoWithFade" format="boolean" />
        <attr name="viewpager_enableToolbarElevation" format="boolean" />
        <attr name="viewpager_parallaxHeaderFactor" format="float" />
        <attr name="viewpager_headerAdditionalHeight" format="dimension" />
        <attr name="viewpager_displayToolbarWhenSwipe" format="boolean" />
        <attr name="viewpager_transparentToolbar" format="boolean" />
        <attr name="viewpager_animatedHeaderImage" format="boolean" />
        <attr name="viewpager_disableToolbar" format="boolean" />

    </declare-styleable>
    <declare-styleable name="PagerSlidingTabStrip">
        <attr name="pstsIndicatorColor" format="color" />
        <attr name="pstsIndicatorHeight" format="dimension" />
        <attr name="pstsUnderlineColor" format="color" />
        <attr name="pstsUnderlineHeight" format="dimension" />
        <attr name="pstsDividerColor" format="color" />
        <attr name="pstsDividerWidth" format="dimension" />
        <attr name="pstsDividerPadding" format="dimension" />
        <attr name="pstsScrollOffset" format="dimension" />
        <attr name="pstsShouldExpand" format="boolean" />
        <attr name="pstsPaddingMiddle" format="boolean" />
        <attr name="pstsTabPaddingLeftRight" format="dimension" />
        <attr name="pstsTabBackground" format="reference" />
        <attr name="pstsTabTextSize" format="dimension" />
        <attr name="pstsTabTextColor" format="reference" />
        <attr name="pstsTabTextStyle" format="reference">
            <flag name="normal" value="0x0" />
            <flag name="bold" value="0x1" />
            <flag name="italic" value="0x2" />
        </attr>
        <attr name="pstsTabTextAllCaps" format="boolean" />
        <attr name="pstsTabTextAlpha" format="integer" />
        <attr name="pstsTabTextFontFamily" format="string" />
    </declare-styleable>

</resources>