<resources xmlns:tools="http://schemas.android.com/tools">


    <style name="MVP_AppBaseTheme" parent="@style/Theme.AppCompat.Light.NoActionBar">
    </style>

    <!-- Base application theme. -->
    <style name="MVP_AppTheme" parent="MVP_AppBaseTheme">

        <item name="android:textColorPrimary">@android:color/white</item>
        <item name="drawerArrowStyle">@style/MVP_DrawerArrowStyle</item>
        <item name="android:windowTranslucentStatus" tools:targetApi="19">true</item>

        <item name="android:windowContentOverlay">@null</item>
        <item name="windowActionBar">false</item>

        <!-- Toolbar Theme / Apply white arrow -->
        <item name="colorControlNormal">@android:color/white</item>
        <item name="actionBarTheme">@style/MVP_AppTheme.ActionBarTheme</item>

        <!-- Material Theme -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/accent_color</item>

        <item name="android:statusBarColor" tools:targetApi="21">@color/statusBarColor</item>
        <item name="android:navigationBarColor" tools:targetApi="21">@color/navigationBarColor</item>
        <item name="android:windowDrawsSystemBarBackgrounds" tools:targetApi="21">true</item>

    </style>

    <style name="MVP_AppTheme.ActionBarTheme" parent="@style/ThemeOverlay.AppCompat.ActionBar">
        <!-- White arrow -->
        <item name="colorControlNormal">@android:color/white</item>
    </style>

    <style name="MVP_DrawerArrowStyle" parent="Widget.AppCompat.DrawerArrowToggle">
        <item name="spinBars">true</item>
        <item name="color">@color/drawerArrowColor</item>
    </style>

</resources>
