apply plugin: 'com.android.library'

android {
    compileSdkVersion 33

    defaultConfig {
        minSdkVersion 17
        targetSdkVersion 33
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    namespace 'com.github.florent37.materialviewpager'
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    implementation(
            'com.android.support:appcompat-v7:' + project.supportVersion,
            'com.android.support:recyclerview-v7:' + project.supportVersion,
            'com.android.support:support-annotations:' + project.supportVersion
    )

    implementation 'com.flaviofaria:kenburnsview:1.0.7'
    implementation('com.jpardogo.materialtabstrip:library:1.1.0') {
        exclude module: 'support-v4'
    }
    implementation 'com.github.bumptech.glide:glide:4.16.0'
}

ext {
    bintrayRepo = 'maven'
    bintrayName = 'MaterialViewPager'
    orgName = 'florent37'

    publishedGroupId = 'com.github.florent37'
    libraryName = 'MaterialViewPager'
    artifact = 'materialviewpager'

    libraryDescription = 'MaterialViewPager'

    siteUrl = 'https://github.com/florent37/MaterialViewPager'
    gitUrl = 'https://github.com/florent37/MaterialViewPager.git'

    libraryVersion = rootProject.ext.libraryVersion

    developerId = 'florent37'
    developerName = 'Florent Champigny'
    developerEmail = '<EMAIL>'

    licenseName = 'The Apache Software License, Version 2.0'
    licenseUrl = 'http://www.apache.org/licenses/LICENSE-2.0.txt'
    allLicenses = ["Apache-2.0"]
}


apply from: rootProject.file('dependencies.gradle')