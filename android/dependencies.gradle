ext {
    minSdkVersion = 17
    compileSdkVersion = 32
    targetSdkVersion = 32

    hamcrestVersion = '1.3'
    junitVersion = '4.12'
    runnerVersion = '0.5'
    supportVersion = '28.0.0'


    supportV4 = 'androidx.legacy:legacy-support-v4:1.0.0'
    supportDesign = "com.android.support:design:$supportVersion"
    supportAnnotations = 'androidx.annotation:annotation:1.0.0'
    supportAppCompat = 'androidx.appcompat:appcompat:1.0.0'
    recyclerviewV7 = "com.android.support:recyclerview-v7:$supportVersion"

    // Test
    junit = "junit:junit:$junitVersion"
    testRunner = "com.android.support.test:runner:$runnerVersion"
    testRules = "com.android.support.test:rules:$runnerVersion"

    // Hamcrest
    hamcrestCore = "org.hamcrest:hamcrest-core:$hamcrestVersion"
    hamcrestLibrary = "org.hamcrest:hamcrest-library:$hamcrestVersion"
    hamcrestIntegration = "org.hamcrest:hamcrest-integration:$hamcrestVersion"

    // Other
    threeTenAbp = 'com.jakewharton.threetenabp:threetenabp:1.1.0'
    butterknife = 'com.jakewharton:butterknife:8.5.1'
    butterknifeCompiler = 'com.jakewharton:butterknife-compiler:8.5.1'

    testDep = [junit,
               hamcrestCore,
               hamcrestLibrary,
               hamcrestIntegration]

    androidTestDep = [junit,
                      testRunner,
                      testRules,
                      hamcrestCore,
                      hamcrestLibrary,
                      hamcrestIntegration]
}